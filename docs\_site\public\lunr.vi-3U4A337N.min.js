import{a as u}from"./chunk-OSRY5VT3.min.js";var o=u((t,r)=>{(function(e,i){typeof define=="function"&&define.amd?define(i):typeof t=="object"?r.exports=i():i()(e.lunr)})(t,function(){return function(e){if(typeof e>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof e.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");e.vi=function(){this.pipeline.reset(),this.pipeline.add(e.vi.stopWordFilter,e.vi.trimmer)},e.vi.wordCharacters="[A-Za-z\u0300\u0350\u0301\u0351\u0309\u0323\u0303\u0343\xC2\xE2\xCA\xEA\xD4\xF4\u0102-\u0103\u0110-\u0111\u01A0-\u01A1\u01AF-\u01B0]",e.vi.trimmer=e.trimmerSupport.generateTrimmer(e.vi.wordCharacters),e.Pipeline.registerFunction(e.vi.trimmer,"trimmer-vi"),e.vi.stopWordFilter=e.generateStopWordFilter("l\xE0 c\xE1i nh\u01B0ng m\xE0".split(" "))}})});export default o();
/*! Bundled license information:

lunr-languages/lunr.vi.js:
  (*!
   * Lunr languages, `Vietnamese` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2017, Keerati Thiwanruk
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.vi-3U4A337N.min.js.map
