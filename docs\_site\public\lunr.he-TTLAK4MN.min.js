import{a as u}from"./chunk-OSRY5VT3.min.js";var p=u((s,n)=>{(function(t,e){typeof define=="function"&&define.amd?define(e):typeof s=="object"?n.exports=e():e()(t.lunr)})(s,function(){return function(t){if(typeof t>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof t.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");t.he=function(){this.pipeline.reset(),this.pipeline.add(t.he.trimmer,t.he.stopWordFilter,t.he.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(t.he.stemmer))},t.he.wordCharacters="\u0590-\u05FF\u05D0-\u05EAa-zA-Z\uFF41-\uFF5A\uFF21-\uFF3A0-9\uFF10-\uFF19",t.he.trimmer=t.trimmerSupport.generateTrimmer(t.he.wordCharacters),t.Pipeline.registerFunction(t.he.trimmer,"trimmer-he"),t.he.stemmer=function(){var e=this,o="";return e.result=!1,e.execArray=["cleanWord"],e.stem=function(){var r=0;for(e.result=!1;r<e.execArray.length&&e.result!=!0;)e.result=e[e.execArray[r]](),r++},e.setCurrent=function(r){e.word=r},e.getCurrent=function(){return e.word},e.cleanWord=function(){var r="\u0591-\u05F4\u05D0-\u05EA",i=new RegExp("[^"+r+"]");return!!i.test(o)},function(r){return typeof r.update=="function"?r.update(function(i){return e.setCurrent(i),e.stem(),e.getCurrent()}):(e.setCurrent(r),e.stem(),e.getCurrent())}}(),t.Pipeline.registerFunction(t.he.stemmer,"stemmer-he"),t.he.stopWordFilter=t.generateStopWordFilter("\u05D0\u05D1\u05DC \u05D0\u05D5 \u05D0\u05D5\u05DC\u05D9 \u05D0\u05D5\u05EA\u05D5 \u05D0\u05D5\u05EA\u05D9 \u05D0\u05D5\u05EA\u05DA \u05D0\u05D5\u05EA\u05DD \u05D0\u05D5\u05EA\u05DF \u05D0\u05D5\u05EA\u05E0\u05D5 \u05D0\u05D6 \u05D0\u05D7\u05E8 \u05D0\u05D7\u05E8\u05D5\u05EA \u05D0\u05D7\u05E8\u05D9 \u05D0\u05D7\u05E8\u05D9\u05DB\u05DF \u05D0\u05D7\u05E8\u05D9\u05DD \u05D0\u05D7\u05E8\u05EA \u05D0\u05D9 \u05D0\u05D9\u05D6\u05D4 \u05D0\u05D9\u05DA \u05D0\u05D9\u05DF \u05D0\u05D9\u05E4\u05D4 \u05D0\u05DC \u05D0\u05DC\u05D4 \u05D0\u05DC\u05D5 \u05D0\u05DD \u05D0\u05E0\u05D7\u05E0\u05D5 \u05D0\u05E0\u05D9 \u05D0\u05E3 \u05D0\u05E4\u05E9\u05E8 \u05D0\u05EA \u05D0\u05EA\u05D4 \u05D0\u05EA\u05DB\u05DD \u05D0\u05EA\u05DB\u05DF \u05D0\u05EA\u05DD \u05D0\u05EA\u05DF \u05D1\u05D0\u05D9\u05D6\u05D4 \u05D1\u05D0\u05D9\u05D6\u05D5 \u05D1\u05D2\u05DC\u05DC \u05D1\u05D9\u05DF \u05D1\u05DC\u05D1\u05D3 \u05D1\u05E2\u05D1\u05D5\u05E8 \u05D1\u05E2\u05D6\u05E8\u05EA \u05D1\u05DB\u05DC \u05D1\u05DB\u05DF \u05D1\u05DC\u05D9 \u05D1\u05DE\u05D9\u05D3\u05D4 \u05D1\u05DE\u05E7\u05D5\u05DD \u05E9\u05D1\u05D5 \u05D1\u05E8\u05D5\u05D1 \u05D1\u05E9\u05D1\u05D9\u05DC \u05D1\u05E9\u05E2\u05D4 \u05E9 \u05D1\u05EA\u05D5\u05DA \u05D2\u05DD \u05D3\u05E8\u05DA \u05D4\u05D5\u05D0 \u05D4\u05D9\u05D0 \u05D4\u05D9\u05D4 \u05D4\u05D9\u05D9 \u05D4\u05D9\u05DB\u05DF \u05D4\u05D9\u05EA\u05D4 \u05D4\u05D9\u05EA\u05D9 \u05D4\u05DD \u05D4\u05DF \u05D4\u05E0\u05D4 \u05D4\u05E1\u05D9\u05D1\u05D4 \u05E9\u05D1\u05D2\u05DC\u05DC\u05D4 \u05D4\u05E8\u05D9 \u05D5\u05D0\u05D9\u05DC\u05D5 \u05D5\u05D0\u05EA \u05D6\u05D0\u05EA \u05D6\u05D4 \u05D6\u05D5\u05EA \u05D9\u05D4\u05D9\u05D4 \u05D9\u05D5\u05DB\u05DC \u05D9\u05D5\u05DB\u05DC\u05D5 \u05D9\u05D5\u05EA\u05E8 \u05DE\u05D3\u05D9 \u05D9\u05DB\u05D5\u05DC \u05D9\u05DB\u05D5\u05DC\u05D4 \u05D9\u05DB\u05D5\u05DC\u05D5\u05EA \u05D9\u05DB\u05D5\u05DC\u05D9\u05DD \u05D9\u05DB\u05DC \u05D9\u05DB\u05DC\u05D4 \u05D9\u05DB\u05DC\u05D5 \u05D9\u05E9 \u05DB\u05D0\u05DF \u05DB\u05D0\u05E9\u05E8 \u05DB\u05D5\u05DC\u05DD \u05DB\u05D5\u05DC\u05DF \u05DB\u05D6\u05D4 \u05DB\u05D9 \u05DB\u05D9\u05E6\u05D3 \u05DB\u05DA \u05DB\u05DC \u05DB\u05DC\u05DC \u05DB\u05DE\u05D5 \u05DB\u05DF \u05DB\u05E4\u05D9 \u05DB\u05E9 \u05DC\u05D0 \u05DC\u05D0\u05D5 \u05DC\u05D0\u05D9\u05D6\u05D5\u05EA\u05DA \u05DC\u05D0\u05DF \u05DC\u05D1\u05D9\u05DF \u05DC\u05D4 \u05DC\u05D4\u05D9\u05D5\u05EA \u05DC\u05D4\u05DD \u05DC\u05D4\u05DF \u05DC\u05D5 \u05DC\u05D6\u05D4 \u05DC\u05D6\u05D5\u05EA \u05DC\u05D9 \u05DC\u05DA \u05DC\u05DB\u05DD \u05DC\u05DB\u05DF \u05DC\u05DE\u05D4 \u05DC\u05DE\u05E2\u05DC\u05D4 \u05DC\u05DE\u05E2\u05DC\u05D4 \u05DE \u05DC\u05DE\u05D8\u05D4 \u05DC\u05DE\u05D8\u05D4 \u05DE \u05DC\u05DE\u05E2\u05D8 \u05DC\u05DE\u05E7\u05D5\u05DD \u05E9\u05D1\u05D5 \u05DC\u05DE\u05E8\u05D5\u05EA \u05DC\u05E0\u05D5 \u05DC\u05E2\u05D1\u05E8 \u05DC\u05E2\u05D9\u05DB\u05DF \u05DC\u05E4\u05D9\u05DB\u05DA \u05DC\u05E4\u05E0\u05D9 \u05DE\u05D0\u05D3 \u05DE\u05D0\u05D7\u05D5\u05E8\u05D9 \u05DE\u05D0\u05D9\u05D6\u05D5 \u05E1\u05D9\u05D1\u05D4 \u05DE\u05D0\u05D9\u05DF \u05DE\u05D0\u05D9\u05E4\u05D4 \u05DE\u05D1\u05DC\u05D9 \u05DE\u05D1\u05E2\u05D3 \u05DE\u05D3\u05D5\u05E2 \u05DE\u05D4 \u05DE\u05D4\u05D9\u05DB\u05DF \u05DE\u05D5\u05DC \u05DE\u05D7\u05D5\u05E5 \u05DE\u05D9 \u05DE\u05D9\u05D3\u05E2 \u05DE\u05DB\u05D0\u05DF \u05DE\u05DB\u05DC \u05DE\u05DB\u05DF \u05DE\u05DC\u05D1\u05D3 \u05DE\u05DF \u05DE\u05E0\u05D9\u05DF \u05DE\u05E1\u05D5\u05D2\u05DC \u05DE\u05E2\u05D8 \u05DE\u05E2\u05D8\u05D9\u05DD \u05DE\u05E2\u05DC \u05DE\u05E6\u05D3 \u05DE\u05E7\u05D5\u05DD \u05D1\u05D5 \u05DE\u05EA\u05D7\u05EA \u05DE\u05EA\u05D9 \u05E0\u05D2\u05D3 \u05E0\u05D2\u05E8 \u05E0\u05D5 \u05E2\u05D3 \u05E2\u05D6 \u05E2\u05DC \u05E2\u05DC\u05D9 \u05E2\u05DC\u05D9\u05D5 \u05E2\u05DC\u05D9\u05D4 \u05E2\u05DC\u05D9\u05D4\u05DD \u05E2\u05DC\u05D9\u05DA \u05E2\u05DC\u05D9\u05E0\u05D5 \u05E2\u05DD \u05E2\u05E6\u05DE\u05D4 \u05E2\u05E6\u05DE\u05D4\u05DD \u05E2\u05E6\u05DE\u05D4\u05DF \u05E2\u05E6\u05DE\u05D5 \u05E2\u05E6\u05DE\u05D9 \u05E2\u05E6\u05DE\u05DD \u05E2\u05E6\u05DE\u05DF \u05E2\u05E6\u05DE\u05E0\u05D5 \u05E4\u05D4 \u05E8\u05E7 \u05E9\u05D5\u05D1 \u05E9\u05DC \u05E9\u05DC\u05D4 \u05E9\u05DC\u05D4\u05DD \u05E9\u05DC\u05D4\u05DF \u05E9\u05DC\u05D5 \u05E9\u05DC\u05D9 \u05E9\u05DC\u05DA \u05E9\u05DC\u05DB\u05D4 \u05E9\u05DC\u05DB\u05DD \u05E9\u05DC\u05DB\u05DF \u05E9\u05DC\u05E0\u05D5 \u05E9\u05DD \u05EA\u05D4\u05D9\u05D4 \u05EA\u05D7\u05EA".split(" ")),t.Pipeline.registerFunction(t.he.stopWordFilter,"stopWordFilter-he")}})});export default p();
/*! Bundled license information:

lunr-languages/lunr.he.js:
  (*!
   * Lunr languages, `Hebrew` language
   * https://github.com/avisaradir/lunr-languages-he
   *
   * Copyright 2023, Adir Avisar
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Kazem Taghva, Rania Elkhoury, and Jeffrey Coombs (2005)
   * Meryeme Hadni, Abdelmonaime Lachkar, and S. Alaoui Ouatik (2012)
   *
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.he-TTLAK4MN.min.js.map
