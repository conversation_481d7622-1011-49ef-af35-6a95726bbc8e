import{a as S}from"./chunk-OSRY5VT3.min.js";var y=S((g,F)=>{(function(n,r){typeof define=="function"&&define.amd?define(r):typeof g=="object"?F.exports=r():r()(n.lunr)})(g,function(){return function(n){if(typeof n>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof n.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");n.de=function(){this.pipeline.reset(),this.pipeline.add(n.de.trimmer,n.de.stopWordFilter,n.de.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(n.de.stemmer))},n.de.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",n.de.trimmer=n.trimmerSupport.generateTrimmer(n.de.wordCharacters),n.Pipeline.registerFunction(n.de.trimmer,"trimmer-de"),n.de.stemmer=function(){var r=n.stemmerSupport.Among,v=n.stemmerSupport.SnowballProgram,c=new function(){var l=[new r("",-1,6),new r("U",0,2),new r("Y",0,1),new r("\xE4",0,3),new r("\xF6",0,4),new r("\xFC",0,5)],A=[new r("e",-1,2),new r("em",-1,1),new r("en",-1,2),new r("ern",-1,1),new r("er",-1,1),new r("s",-1,3),new r("es",5,2)],C=[new r("en",-1,1),new r("er",-1,1),new r("st",-1,2),new r("est",2,1)],B=[new r("ig",-1,1),new r("lich",-1,1)],D=[new r("end",-1,1),new r("ig",-1,2),new r("ung",-1,1),new r("lich",-1,3),new r("isch",-1,2),new r("ik",-1,2),new r("heit",-1,3),new r("keit",-1,4)],m=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,8,0,32,8],j=[117,30,5],q=[117,30,4],f,_,o,e=new v;this.setCurrent=function(i){e.setCurrent(i)},this.getCurrent=function(){return e.getCurrent()};function p(i,s,u){return e.eq_s(1,i)&&(e.ket=e.cursor,e.in_grouping(m,97,252))?(e.slice_from(s),e.cursor=u,!0):!1}function P(){for(var i=e.cursor,s,u,t,a;;)if(s=e.cursor,e.bra=s,e.eq_s(1,"\xDF"))e.ket=e.cursor,e.slice_from("ss");else{if(s>=e.limit)break;e.cursor=s+1}for(e.cursor=i;;)for(u=e.cursor;t=e.cursor,!(e.in_grouping(m,97,252)&&(a=e.cursor,e.bra=a,p("u","U",t)||(e.cursor=a,p("y","Y",t))));){if(t>=e.limit){e.cursor=u;return}e.cursor=t+1}}function k(){for(;!e.in_grouping(m,97,252);){if(e.cursor>=e.limit)return!0;e.cursor++}for(;!e.out_grouping(m,97,252);){if(e.cursor>=e.limit)return!0;e.cursor++}return!1}function x(){o=e.limit,_=o;var i=e.cursor+3;0<=i&&i<=e.limit&&(f=i,k()||(o=e.cursor,o<f&&(o=f),k()||(_=e.cursor)))}function E(){for(var i,s;;){if(s=e.cursor,e.bra=s,i=e.find_among(l,6),!i)return;switch(e.ket=e.cursor,i){case 1:e.slice_from("y");break;case 2:case 5:e.slice_from("u");break;case 3:e.slice_from("a");break;case 4:e.slice_from("o");break;case 6:if(e.cursor>=e.limit)return;e.cursor++;break}}}function h(){return o<=e.cursor}function w(){return _<=e.cursor}function z(){var i,s=e.limit-e.cursor,u,t,a;if(e.ket=e.cursor,i=e.find_among_b(A,7),i&&(e.bra=e.cursor,h()))switch(i){case 1:e.slice_del();break;case 2:e.slice_del(),e.ket=e.cursor,e.eq_s_b(1,"s")&&(e.bra=e.cursor,e.eq_s_b(3,"nis")&&e.slice_del());break;case 3:e.in_grouping_b(j,98,116)&&e.slice_del();break}if(e.cursor=e.limit-s,e.ket=e.cursor,i=e.find_among_b(C,4),i&&(e.bra=e.cursor,h()))switch(i){case 1:e.slice_del();break;case 2:if(e.in_grouping_b(q,98,116)){var b=e.cursor-3;e.limit_backward<=b&&b<=e.limit&&(e.cursor=b,e.slice_del())}break}if(e.cursor=e.limit-s,e.ket=e.cursor,i=e.find_among_b(D,8),i&&(e.bra=e.cursor,w()))switch(i){case 1:e.slice_del(),e.ket=e.cursor,e.eq_s_b(2,"ig")&&(e.bra=e.cursor,u=e.limit-e.cursor,e.eq_s_b(1,"e")||(e.cursor=e.limit-u,w()&&e.slice_del()));break;case 2:t=e.limit-e.cursor,e.eq_s_b(1,"e")||(e.cursor=e.limit-t,e.slice_del());break;case 3:if(e.slice_del(),e.ket=e.cursor,a=e.limit-e.cursor,!e.eq_s_b(2,"er")&&(e.cursor=e.limit-a,!e.eq_s_b(2,"en")))break;e.bra=e.cursor,h()&&e.slice_del();break;case 4:e.slice_del(),e.ket=e.cursor,i=e.find_among_b(B,2),i&&(e.bra=e.cursor,w()&&i==1&&e.slice_del());break}}this.stem=function(){var i=e.cursor;return P(),e.cursor=i,x(),e.limit_backward=i,e.cursor=e.limit,z(),e.cursor=e.limit_backward,E(),!0}};return function(d){return typeof d.update=="function"?d.update(function(l){return c.setCurrent(l),c.stem(),c.getCurrent()}):(c.setCurrent(d),c.stem(),c.getCurrent())}}(),n.Pipeline.registerFunction(n.de.stemmer,"stemmer-de"),n.de.stopWordFilter=n.generateStopWordFilter("aber alle allem allen aller alles als also am an ander andere anderem anderen anderer anderes anderm andern anderr anders auch auf aus bei bin bis bist da damit dann das dasselbe dazu da\xDF dein deine deinem deinen deiner deines dem demselben den denn denselben der derer derselbe derselben des desselben dessen dich die dies diese dieselbe dieselben diesem diesen dieser dieses dir doch dort du durch ein eine einem einen einer eines einig einige einigem einigen einiger einiges einmal er es etwas euch euer eure eurem euren eurer eures f\xFCr gegen gewesen hab habe haben hat hatte hatten hier hin hinter ich ihm ihn ihnen ihr ihre ihrem ihren ihrer ihres im in indem ins ist jede jedem jeden jeder jedes jene jenem jenen jener jenes jetzt kann kein keine keinem keinen keiner keines k\xF6nnen k\xF6nnte machen man manche manchem manchen mancher manches mein meine meinem meinen meiner meines mich mir mit muss musste nach nicht nichts noch nun nur ob oder ohne sehr sein seine seinem seinen seiner seines selbst sich sie sind so solche solchem solchen solcher solches soll sollte sondern sonst um und uns unse unsem unsen unser unses unter viel vom von vor war waren warst was weg weil weiter welche welchem welchen welcher welches wenn werde werden wie wieder will wir wird wirst wo wollen wollte w\xE4hrend w\xFCrde w\xFCrden zu zum zur zwar zwischen \xFCber".split(" ")),n.Pipeline.registerFunction(n.de.stopWordFilter,"stopWordFilter-de")}})});export default y();
/*! Bundled license information:

lunr-languages/lunr.de.js:
  (*!
   * Lunr languages, `German` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.de-XXPRKDAY.min.js.map
