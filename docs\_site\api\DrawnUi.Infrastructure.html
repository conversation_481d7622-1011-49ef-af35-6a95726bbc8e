<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Namespace DrawnUi.Infrastructure | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Namespace DrawnUi.Infrastructure | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Infrastructure">

  <h1 id="DrawnUi_Infrastructure" data-uid="DrawnUi.Infrastructure" class="text-break">Namespace DrawnUi.Infrastructure</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>

    <h3 id="classes">
Classes
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.FileDescriptor.html">FileDescriptor</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.Files.html">Files</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.Pdf.html">Pdf</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.Pendulum.html">Pendulum</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.PerpetualPendulum.html">PerpetualPendulum</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.RenderOnTimer.html">RenderOnTimer</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.SkSl.html">SkSl</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.Vector.html">Vector</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.VisualTransform.html">VisualTransform</a></dt>
      <dd><p>Will enhance this in the future to include more properties</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.VisualTreeChain.html">VisualTreeChain</a></dt>
      <dd></dd>
    </dl>
    <h3 id="structs">
Structs
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.ClosedRange-1.html">ClosedRange&lt;T&gt;</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.MeasuringConstraints.html">MeasuringConstraints</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.PdfPagePosition.html">PdfPagePosition</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.SkiaTouchResultContext.html">SkiaTouchResultContext</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.Spring.html">Spring</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.VisualTransformNative.html">VisualTransformNative</a></dt>
      <dd></dd>
    </dl>
    <h3 id="enums">
Enums
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.PaperFormat.html">PaperFormat</a></dt>
      <dd><p>Standard paper formats</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Infrastructure.StorageType.html">StorageType</a></dt>
      <dd></dd>
    </dl>


</article>

        <div class="contribution d-print-none">
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
