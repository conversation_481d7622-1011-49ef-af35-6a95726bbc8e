import{H as ne,O as re,S as se,T as oe,U as le,V as he,W as ce,X as de,Y as Et,Z as Tt,h as s,ia as qt,j as ct,ma as Dt,t as ae,v as L}from"./chunk-U3SD26FK.min.js";import"./chunk-OSRY5VT3.min.js";var zt=function(){var t=s(function(M,r,l,u){for(l=l||{},u=M.length;u--;l[M[u]]=r);return l},"o"),a=[1,3],p=[1,4],f=[1,5],o=[1,6],x=[1,7],_=[1,4,5,10,12,13,14,18,25,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],h=[1,4,5,10,12,13,14,18,25,28,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],c=[55,56,57],k=[2,36],q=[1,37],m=[1,36],y=[1,38],b=[1,35],T=[1,43],g=[1,41],st=[1,14],dt=[1,23],ut=[1,18],xt=[1,19],ft=[1,20],ot=[1,21],St=[1,22],lt=[1,24],i=[1,25],It=[1,26],wt=[1,27],Bt=[1,28],Rt=[1,29],W=[1,32],U=[1,33],A=[1,34],F=[1,39],P=[1,40],v=[1,42],C=[1,44],O=[1,62],H=[1,61],E=[4,5,8,10,12,13,14,18,44,47,49,55,56,57,63,64,65,66,67],Nt=[1,65],Wt=[1,66],Ut=[1,67],Qt=[1,68],Ot=[1,69],Ht=[1,70],Xt=[1,71],Mt=[1,72],Yt=[1,73],jt=[1,74],Gt=[1,75],Kt=[1,76],w=[4,5,6,7,8,9,10,11,12,13,14,15,18],G=[1,90],K=[1,91],Z=[1,92],J=[1,99],$=[1,93],tt=[1,96],et=[1,94],it=[1,95],at=[1,97],nt=[1,98],_t=[1,102],Zt=[10,55,56,57],R=[4,5,6,8,10,11,13,17,18,19,20,55,56,57],At={trace:s(function(){},"trace"),yy:{},symbols_:{error:2,idStringToken:3,ALPHA:4,NUM:5,NODE_STRING:6,DOWN:7,MINUS:8,DEFAULT:9,COMMA:10,COLON:11,AMP:12,BRKT:13,MULT:14,UNICODE_TEXT:15,styleComponent:16,UNIT:17,SPACE:18,STYLE:19,PCT:20,idString:21,style:22,stylesOpt:23,classDefStatement:24,CLASSDEF:25,start:26,eol:27,QUADRANT:28,document:29,line:30,statement:31,axisDetails:32,quadrantDetails:33,points:34,title:35,title_value:36,acc_title:37,acc_title_value:38,acc_descr:39,acc_descr_value:40,acc_descr_multiline_value:41,section:42,text:43,point_start:44,point_x:45,point_y:46,class_name:47,"X-AXIS":48,"AXIS-TEXT-DELIMITER":49,"Y-AXIS":50,QUADRANT_1:51,QUADRANT_2:52,QUADRANT_3:53,QUADRANT_4:54,NEWLINE:55,SEMI:56,EOF:57,alphaNumToken:58,textNoTagsToken:59,STR:60,MD_STR:61,alphaNum:62,PUNCTUATION:63,PLUS:64,EQUALS:65,DOT:66,UNDERSCORE:67,$accept:0,$end:1},terminals_:{2:"error",4:"ALPHA",5:"NUM",6:"NODE_STRING",7:"DOWN",8:"MINUS",9:"DEFAULT",10:"COMMA",11:"COLON",12:"AMP",13:"BRKT",14:"MULT",15:"UNICODE_TEXT",17:"UNIT",18:"SPACE",19:"STYLE",20:"PCT",25:"CLASSDEF",28:"QUADRANT",35:"title",36:"title_value",37:"acc_title",38:"acc_title_value",39:"acc_descr",40:"acc_descr_value",41:"acc_descr_multiline_value",42:"section",44:"point_start",45:"point_x",46:"point_y",47:"class_name",48:"X-AXIS",49:"AXIS-TEXT-DELIMITER",50:"Y-AXIS",51:"QUADRANT_1",52:"QUADRANT_2",53:"QUADRANT_3",54:"QUADRANT_4",55:"NEWLINE",56:"SEMI",57:"EOF",60:"STR",61:"MD_STR",63:"PUNCTUATION",64:"PLUS",65:"EQUALS",66:"DOT",67:"UNDERSCORE"},productions_:[0,[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[21,1],[21,2],[22,1],[22,2],[23,1],[23,3],[24,5],[26,2],[26,2],[26,2],[29,0],[29,2],[30,2],[31,0],[31,1],[31,2],[31,1],[31,1],[31,1],[31,2],[31,2],[31,2],[31,1],[31,1],[34,4],[34,5],[34,5],[34,6],[32,4],[32,3],[32,2],[32,4],[32,3],[32,2],[33,2],[33,2],[33,2],[33,2],[27,1],[27,1],[27,1],[43,1],[43,2],[43,1],[43,1],[62,1],[62,2],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[59,1],[59,1],[59,1]],performAction:s(function(r,l,u,d,S,e,ht){var n=e.length-1;switch(S){case 23:this.$=e[n];break;case 24:this.$=e[n-1]+""+e[n];break;case 26:this.$=e[n-1]+e[n];break;case 27:this.$=[e[n].trim()];break;case 28:e[n-2].push(e[n].trim()),this.$=e[n-2];break;case 29:this.$=e[n-4],d.addClass(e[n-2],e[n]);break;case 37:this.$=[];break;case 42:this.$=e[n].trim(),d.setDiagramTitle(this.$);break;case 43:this.$=e[n].trim(),d.setAccTitle(this.$);break;case 44:case 45:this.$=e[n].trim(),d.setAccDescription(this.$);break;case 46:d.addSection(e[n].substr(8)),this.$=e[n].substr(8);break;case 47:d.addPoint(e[n-3],"",e[n-1],e[n],[]);break;case 48:d.addPoint(e[n-4],e[n-3],e[n-1],e[n],[]);break;case 49:d.addPoint(e[n-4],"",e[n-2],e[n-1],e[n]);break;case 50:d.addPoint(e[n-5],e[n-4],e[n-2],e[n-1],e[n]);break;case 51:d.setXAxisLeftText(e[n-2]),d.setXAxisRightText(e[n]);break;case 52:e[n-1].text+=" \u27F6 ",d.setXAxisLeftText(e[n-1]);break;case 53:d.setXAxisLeftText(e[n]);break;case 54:d.setYAxisBottomText(e[n-2]),d.setYAxisTopText(e[n]);break;case 55:e[n-1].text+=" \u27F6 ",d.setYAxisBottomText(e[n-1]);break;case 56:d.setYAxisBottomText(e[n]);break;case 57:d.setQuadrant1Text(e[n]);break;case 58:d.setQuadrant2Text(e[n]);break;case 59:d.setQuadrant3Text(e[n]);break;case 60:d.setQuadrant4Text(e[n]);break;case 64:this.$={text:e[n],type:"text"};break;case 65:this.$={text:e[n-1].text+""+e[n],type:e[n-1].type};break;case 66:this.$={text:e[n],type:"text"};break;case 67:this.$={text:e[n],type:"markdown"};break;case 68:this.$=e[n];break;case 69:this.$=e[n-1]+""+e[n];break}},"anonymous"),table:[{18:a,26:1,27:2,28:p,55:f,56:o,57:x},{1:[3]},{18:a,26:8,27:2,28:p,55:f,56:o,57:x},{18:a,26:9,27:2,28:p,55:f,56:o,57:x},t(_,[2,33],{29:10}),t(h,[2,61]),t(h,[2,62]),t(h,[2,63]),{1:[2,30]},{1:[2,31]},t(c,k,{30:11,31:12,24:13,32:15,33:16,34:17,43:30,58:31,1:[2,32],4:q,5:m,10:y,12:b,13:T,14:g,18:st,25:dt,35:ut,37:xt,39:ft,41:ot,42:St,48:lt,50:i,51:It,52:wt,53:Bt,54:Rt,60:W,61:U,63:A,64:F,65:P,66:v,67:C}),t(_,[2,34]),{27:45,55:f,56:o,57:x},t(c,[2,37]),t(c,k,{24:13,32:15,33:16,34:17,43:30,58:31,31:46,4:q,5:m,10:y,12:b,13:T,14:g,18:st,25:dt,35:ut,37:xt,39:ft,41:ot,42:St,48:lt,50:i,51:It,52:wt,53:Bt,54:Rt,60:W,61:U,63:A,64:F,65:P,66:v,67:C}),t(c,[2,39]),t(c,[2,40]),t(c,[2,41]),{36:[1,47]},{38:[1,48]},{40:[1,49]},t(c,[2,45]),t(c,[2,46]),{18:[1,50]},{4:q,5:m,10:y,12:b,13:T,14:g,43:51,58:31,60:W,61:U,63:A,64:F,65:P,66:v,67:C},{4:q,5:m,10:y,12:b,13:T,14:g,43:52,58:31,60:W,61:U,63:A,64:F,65:P,66:v,67:C},{4:q,5:m,10:y,12:b,13:T,14:g,43:53,58:31,60:W,61:U,63:A,64:F,65:P,66:v,67:C},{4:q,5:m,10:y,12:b,13:T,14:g,43:54,58:31,60:W,61:U,63:A,64:F,65:P,66:v,67:C},{4:q,5:m,10:y,12:b,13:T,14:g,43:55,58:31,60:W,61:U,63:A,64:F,65:P,66:v,67:C},{4:q,5:m,10:y,12:b,13:T,14:g,43:56,58:31,60:W,61:U,63:A,64:F,65:P,66:v,67:C},{4:q,5:m,8:O,10:y,12:b,13:T,14:g,18:H,44:[1,57],47:[1,58],58:60,59:59,63:A,64:F,65:P,66:v,67:C},t(E,[2,64]),t(E,[2,66]),t(E,[2,67]),t(E,[2,70]),t(E,[2,71]),t(E,[2,72]),t(E,[2,73]),t(E,[2,74]),t(E,[2,75]),t(E,[2,76]),t(E,[2,77]),t(E,[2,78]),t(E,[2,79]),t(E,[2,80]),t(_,[2,35]),t(c,[2,38]),t(c,[2,42]),t(c,[2,43]),t(c,[2,44]),{3:64,4:Nt,5:Wt,6:Ut,7:Qt,8:Ot,9:Ht,10:Xt,11:Mt,12:Yt,13:jt,14:Gt,15:Kt,21:63},t(c,[2,53],{59:59,58:60,4:q,5:m,8:O,10:y,12:b,13:T,14:g,18:H,49:[1,77],63:A,64:F,65:P,66:v,67:C}),t(c,[2,56],{59:59,58:60,4:q,5:m,8:O,10:y,12:b,13:T,14:g,18:H,49:[1,78],63:A,64:F,65:P,66:v,67:C}),t(c,[2,57],{59:59,58:60,4:q,5:m,8:O,10:y,12:b,13:T,14:g,18:H,63:A,64:F,65:P,66:v,67:C}),t(c,[2,58],{59:59,58:60,4:q,5:m,8:O,10:y,12:b,13:T,14:g,18:H,63:A,64:F,65:P,66:v,67:C}),t(c,[2,59],{59:59,58:60,4:q,5:m,8:O,10:y,12:b,13:T,14:g,18:H,63:A,64:F,65:P,66:v,67:C}),t(c,[2,60],{59:59,58:60,4:q,5:m,8:O,10:y,12:b,13:T,14:g,18:H,63:A,64:F,65:P,66:v,67:C}),{45:[1,79]},{44:[1,80]},t(E,[2,65]),t(E,[2,81]),t(E,[2,82]),t(E,[2,83]),{3:82,4:Nt,5:Wt,6:Ut,7:Qt,8:Ot,9:Ht,10:Xt,11:Mt,12:Yt,13:jt,14:Gt,15:Kt,18:[1,81]},t(w,[2,23]),t(w,[2,1]),t(w,[2,2]),t(w,[2,3]),t(w,[2,4]),t(w,[2,5]),t(w,[2,6]),t(w,[2,7]),t(w,[2,8]),t(w,[2,9]),t(w,[2,10]),t(w,[2,11]),t(w,[2,12]),t(c,[2,52],{58:31,43:83,4:q,5:m,10:y,12:b,13:T,14:g,60:W,61:U,63:A,64:F,65:P,66:v,67:C}),t(c,[2,55],{58:31,43:84,4:q,5:m,10:y,12:b,13:T,14:g,60:W,61:U,63:A,64:F,65:P,66:v,67:C}),{46:[1,85]},{45:[1,86]},{4:G,5:K,6:Z,8:J,11:$,13:tt,16:89,17:et,18:it,19:at,20:nt,22:88,23:87},t(w,[2,24]),t(c,[2,51],{59:59,58:60,4:q,5:m,8:O,10:y,12:b,13:T,14:g,18:H,63:A,64:F,65:P,66:v,67:C}),t(c,[2,54],{59:59,58:60,4:q,5:m,8:O,10:y,12:b,13:T,14:g,18:H,63:A,64:F,65:P,66:v,67:C}),t(c,[2,47],{22:88,16:89,23:100,4:G,5:K,6:Z,8:J,11:$,13:tt,17:et,18:it,19:at,20:nt}),{46:[1,101]},t(c,[2,29],{10:_t}),t(Zt,[2,27],{16:103,4:G,5:K,6:Z,8:J,11:$,13:tt,17:et,18:it,19:at,20:nt}),t(R,[2,25]),t(R,[2,13]),t(R,[2,14]),t(R,[2,15]),t(R,[2,16]),t(R,[2,17]),t(R,[2,18]),t(R,[2,19]),t(R,[2,20]),t(R,[2,21]),t(R,[2,22]),t(c,[2,49],{10:_t}),t(c,[2,48],{22:88,16:89,23:104,4:G,5:K,6:Z,8:J,11:$,13:tt,17:et,18:it,19:at,20:nt}),{4:G,5:K,6:Z,8:J,11:$,13:tt,16:89,17:et,18:it,19:at,20:nt,22:105},t(R,[2,26]),t(c,[2,50],{10:_t}),t(Zt,[2,28],{16:103,4:G,5:K,6:Z,8:J,11:$,13:tt,17:et,18:it,19:at,20:nt})],defaultActions:{8:[2,30],9:[2,31]},parseError:s(function(r,l){if(l.recoverable)this.trace(r);else{var u=new Error(r);throw u.hash=l,u}},"parseError"),parse:s(function(r){var l=this,u=[0],d=[],S=[null],e=[],ht=this.table,n="",pt=0,Jt=0,$t=0,ve=2,te=1,Ce=e.slice.call(arguments,1),D=Object.create(this.lexer),Y={yy:{}};for(var Ft in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Ft)&&(Y.yy[Ft]=this.yy[Ft]);D.setInput(r,Y.yy),Y.yy.lexer=D,Y.yy.parser=this,typeof D.yylloc>"u"&&(D.yylloc={});var Pt=D.yylloc;e.push(Pt);var Le=D.options&&D.options.ranges;typeof Y.yy.parseError=="function"?this.parseError=Y.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Ee(B){u.length=u.length-2*B,S.length=S.length-B,e.length=e.length-B}s(Ee,"popStack");function ee(){var B;return B=d.pop()||D.lex()||te,typeof B!="number"&&(B instanceof Array&&(d=B,B=d.pop()),B=l.symbols_[B]||B),B}s(ee,"lex");for(var V,vt,j,N,Ne,Ct,rt={},yt,X,ie,bt;;){if(j=u[u.length-1],this.defaultActions[j]?N=this.defaultActions[j]:((V===null||typeof V>"u")&&(V=ee()),N=ht[j]&&ht[j][V]),typeof N>"u"||!N.length||!N[0]){var Lt="";bt=[];for(yt in ht[j])this.terminals_[yt]&&yt>ve&&bt.push("'"+this.terminals_[yt]+"'");D.showPosition?Lt="Parse error on line "+(pt+1)+`:
`+D.showPosition()+`
Expecting `+bt.join(", ")+", got '"+(this.terminals_[V]||V)+"'":Lt="Parse error on line "+(pt+1)+": Unexpected "+(V==te?"end of input":"'"+(this.terminals_[V]||V)+"'"),this.parseError(Lt,{text:D.match,token:this.terminals_[V]||V,line:D.yylineno,loc:Pt,expected:bt})}if(N[0]instanceof Array&&N.length>1)throw new Error("Parse Error: multiple actions possible at state: "+j+", token: "+V);switch(N[0]){case 1:u.push(V),S.push(D.yytext),e.push(D.yylloc),u.push(N[1]),V=null,vt?(V=vt,vt=null):(Jt=D.yyleng,n=D.yytext,pt=D.yylineno,Pt=D.yylloc,$t>0&&$t--);break;case 2:if(X=this.productions_[N[1]][1],rt.$=S[S.length-X],rt._$={first_line:e[e.length-(X||1)].first_line,last_line:e[e.length-1].last_line,first_column:e[e.length-(X||1)].first_column,last_column:e[e.length-1].last_column},Le&&(rt._$.range=[e[e.length-(X||1)].range[0],e[e.length-1].range[1]]),Ct=this.performAction.apply(rt,[n,Jt,pt,Y.yy,N[1],S,e].concat(Ce)),typeof Ct<"u")return Ct;X&&(u=u.slice(0,-1*X*2),S=S.slice(0,-1*X),e=e.slice(0,-1*X)),u.push(this.productions_[N[1]][0]),S.push(rt.$),e.push(rt._$),ie=ht[u[u.length-2]][u[u.length-1]],u.push(ie);break;case 3:return!0}}return!0},"parse")},Pe=function(){var M={EOF:1,parseError:s(function(l,u){if(this.yy.parser)this.yy.parser.parseError(l,u);else throw new Error(l)},"parseError"),setInput:s(function(r,l){return this.yy=l||this.yy||{},this._input=r,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:s(function(){var r=this._input[0];this.yytext+=r,this.yyleng++,this.offset++,this.match+=r,this.matched+=r;var l=r.match(/(?:\r\n?|\n).*/g);return l?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),r},"input"),unput:s(function(r){var l=r.length,u=r.split(/(?:\r\n?|\n)/g);this._input=r+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-l),this.offset-=l;var d=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),u.length-1&&(this.yylineno-=u.length-1);var S=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:u?(u.length===d.length?this.yylloc.first_column:0)+d[d.length-u.length].length-u[0].length:this.yylloc.first_column-l},this.options.ranges&&(this.yylloc.range=[S[0],S[0]+this.yyleng-l]),this.yyleng=this.yytext.length,this},"unput"),more:s(function(){return this._more=!0,this},"more"),reject:s(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:s(function(r){this.unput(this.match.slice(r))},"less"),pastInput:s(function(){var r=this.matched.substr(0,this.matched.length-this.match.length);return(r.length>20?"...":"")+r.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:s(function(){var r=this.match;return r.length<20&&(r+=this._input.substr(0,20-r.length)),(r.substr(0,20)+(r.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:s(function(){var r=this.pastInput(),l=new Array(r.length+1).join("-");return r+this.upcomingInput()+`
`+l+"^"},"showPosition"),test_match:s(function(r,l){var u,d,S;if(this.options.backtrack_lexer&&(S={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(S.yylloc.range=this.yylloc.range.slice(0))),d=r[0].match(/(?:\r\n?|\n).*/g),d&&(this.yylineno+=d.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:d?d[d.length-1].length-d[d.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+r[0].length},this.yytext+=r[0],this.match+=r[0],this.matches=r,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(r[0].length),this.matched+=r[0],u=this.performAction.call(this,this.yy,this,l,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),u)return u;if(this._backtrack){for(var e in S)this[e]=S[e];return!1}return!1},"test_match"),next:s(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var r,l,u,d;this._more||(this.yytext="",this.match="");for(var S=this._currentRules(),e=0;e<S.length;e++)if(u=this._input.match(this.rules[S[e]]),u&&(!l||u[0].length>l[0].length)){if(l=u,d=e,this.options.backtrack_lexer){if(r=this.test_match(u,S[e]),r!==!1)return r;if(this._backtrack){l=!1;continue}else return!1}else if(!this.options.flex)break}return l?(r=this.test_match(l,S[d]),r!==!1?r:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:s(function(){var l=this.next();return l||this.lex()},"lex"),begin:s(function(l){this.conditionStack.push(l)},"begin"),popState:s(function(){var l=this.conditionStack.length-1;return l>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:s(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:s(function(l){return l=this.conditionStack.length-1-Math.abs(l||0),l>=0?this.conditionStack[l]:"INITIAL"},"topState"),pushState:s(function(l){this.begin(l)},"pushState"),stateStackSize:s(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:s(function(l,u,d,S){var e=S;switch(d){case 0:break;case 1:break;case 2:return 55;case 3:break;case 4:return this.begin("title"),35;break;case 5:return this.popState(),"title_value";break;case 6:return this.begin("acc_title"),37;break;case 7:return this.popState(),"acc_title_value";break;case 8:return this.begin("acc_descr"),39;break;case 9:return this.popState(),"acc_descr_value";break;case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 48;case 14:return 50;case 15:return 49;case 16:return 51;case 17:return 52;case 18:return 53;case 19:return 54;case 20:return 25;case 21:this.begin("md_string");break;case 22:return"MD_STR";case 23:this.popState();break;case 24:this.begin("string");break;case 25:this.popState();break;case 26:return"STR";case 27:this.begin("class_name");break;case 28:return this.popState(),47;break;case 29:return this.begin("point_start"),44;break;case 30:return this.begin("point_x"),45;break;case 31:this.popState();break;case 32:this.popState(),this.begin("point_y");break;case 33:return this.popState(),46;break;case 34:return 28;case 35:return 4;case 36:return 11;case 37:return 64;case 38:return 10;case 39:return 65;case 40:return 65;case 41:return 14;case 42:return 13;case 43:return 67;case 44:return 66;case 45:return 12;case 46:return 8;case 47:return 5;case 48:return 18;case 49:return 56;case 50:return 63;case 51:return 57}},"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?: *x-axis *)/i,/^(?: *y-axis *)/i,/^(?: *--+> *)/i,/^(?: *quadrant-1 *)/i,/^(?: *quadrant-2 *)/i,/^(?: *quadrant-3 *)/i,/^(?: *quadrant-4 *)/i,/^(?:classDef\b)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?::::)/i,/^(?:^\w+)/i,/^(?:\s*:\s*\[\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?:\s*\] *)/i,/^(?:\s*,\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?: *quadrantChart *)/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s)/i,/^(?:;)/i,/^(?:[!"#$%&'*+,-.`?\\_/])/i,/^(?:$)/i],conditions:{class_name:{rules:[28],inclusive:!1},point_y:{rules:[33],inclusive:!1},point_x:{rules:[32],inclusive:!1},point_start:{rules:[30,31],inclusive:!1},acc_descr_multiline:{rules:[11,12],inclusive:!1},acc_descr:{rules:[9],inclusive:!1},acc_title:{rules:[7],inclusive:!1},title:{rules:[5],inclusive:!1},md_string:{rules:[22,23],inclusive:!1},string:{rules:[25,26],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,6,8,10,13,14,15,16,17,18,19,20,21,24,27,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],inclusive:!0}}};return M}();At.lexer=Pe;function gt(){this.yy={}}return s(gt,"Parser"),gt.prototype=At,At.Parser=gt,new gt}();zt.parser=zt;var De=zt,I=ae(),ze=class{constructor(){this.classes=new Map,this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData()}static{s(this,"QuadrantBuilder")}getDefaultData(){return{titleText:"",quadrant1Text:"",quadrant2Text:"",quadrant3Text:"",quadrant4Text:"",xAxisLeftText:"",xAxisRightText:"",yAxisBottomText:"",yAxisTopText:"",points:[]}}getDefaultConfig(){return{showXAxis:!0,showYAxis:!0,showTitle:!0,chartHeight:L.quadrantChart?.chartWidth||500,chartWidth:L.quadrantChart?.chartHeight||500,titlePadding:L.quadrantChart?.titlePadding||10,titleFontSize:L.quadrantChart?.titleFontSize||20,quadrantPadding:L.quadrantChart?.quadrantPadding||5,xAxisLabelPadding:L.quadrantChart?.xAxisLabelPadding||5,yAxisLabelPadding:L.quadrantChart?.yAxisLabelPadding||5,xAxisLabelFontSize:L.quadrantChart?.xAxisLabelFontSize||16,yAxisLabelFontSize:L.quadrantChart?.yAxisLabelFontSize||16,quadrantLabelFontSize:L.quadrantChart?.quadrantLabelFontSize||16,quadrantTextTopPadding:L.quadrantChart?.quadrantTextTopPadding||5,pointTextPadding:L.quadrantChart?.pointTextPadding||5,pointLabelFontSize:L.quadrantChart?.pointLabelFontSize||12,pointRadius:L.quadrantChart?.pointRadius||5,xAxisPosition:L.quadrantChart?.xAxisPosition||"top",yAxisPosition:L.quadrantChart?.yAxisPosition||"left",quadrantInternalBorderStrokeWidth:L.quadrantChart?.quadrantInternalBorderStrokeWidth||1,quadrantExternalBorderStrokeWidth:L.quadrantChart?.quadrantExternalBorderStrokeWidth||2}}getDefaultThemeConfig(){return{quadrant1Fill:I.quadrant1Fill,quadrant2Fill:I.quadrant2Fill,quadrant3Fill:I.quadrant3Fill,quadrant4Fill:I.quadrant4Fill,quadrant1TextFill:I.quadrant1TextFill,quadrant2TextFill:I.quadrant2TextFill,quadrant3TextFill:I.quadrant3TextFill,quadrant4TextFill:I.quadrant4TextFill,quadrantPointFill:I.quadrantPointFill,quadrantPointTextFill:I.quadrantPointTextFill,quadrantXAxisTextFill:I.quadrantXAxisTextFill,quadrantYAxisTextFill:I.quadrantYAxisTextFill,quadrantTitleFill:I.quadrantTitleFill,quadrantInternalBorderStrokeFill:I.quadrantInternalBorderStrokeFill,quadrantExternalBorderStrokeFill:I.quadrantExternalBorderStrokeFill}}clear(){this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData(),this.classes=new Map,ct.info("clear called")}setData(t){this.data={...this.data,...t}}addPoints(t){this.data.points=[...t,...this.data.points]}addClass(t,a){this.classes.set(t,a)}setConfig(t){ct.trace("setConfig called with: ",t),this.config={...this.config,...t}}setThemeConfig(t){ct.trace("setThemeConfig called with: ",t),this.themeConfig={...this.themeConfig,...t}}calculateSpace(t,a,p,f){let o=this.config.xAxisLabelPadding*2+this.config.xAxisLabelFontSize,x={top:t==="top"&&a?o:0,bottom:t==="bottom"&&a?o:0},_=this.config.yAxisLabelPadding*2+this.config.yAxisLabelFontSize,h={left:this.config.yAxisPosition==="left"&&p?_:0,right:this.config.yAxisPosition==="right"&&p?_:0},c=this.config.titleFontSize+this.config.titlePadding*2,k={top:f?c:0},q=this.config.quadrantPadding+h.left,m=this.config.quadrantPadding+x.top+k.top,y=this.config.chartWidth-this.config.quadrantPadding*2-h.left-h.right,b=this.config.chartHeight-this.config.quadrantPadding*2-x.top-x.bottom-k.top,T=y/2,g=b/2;return{xAxisSpace:x,yAxisSpace:h,titleSpace:k,quadrantSpace:{quadrantLeft:q,quadrantTop:m,quadrantWidth:y,quadrantHalfWidth:T,quadrantHeight:b,quadrantHalfHeight:g}}}getAxisLabels(t,a,p,f){let{quadrantSpace:o,titleSpace:x}=f,{quadrantHalfHeight:_,quadrantHeight:h,quadrantLeft:c,quadrantHalfWidth:k,quadrantTop:q,quadrantWidth:m}=o,y=!!this.data.xAxisRightText,b=!!this.data.yAxisTopText,T=[];return this.data.xAxisLeftText&&a&&T.push({text:this.data.xAxisLeftText,fill:this.themeConfig.quadrantXAxisTextFill,x:c+(y?k/2:0),y:t==="top"?this.config.xAxisLabelPadding+x.top:this.config.xAxisLabelPadding+q+h+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:y?"center":"left",horizontalPos:"top",rotation:0}),this.data.xAxisRightText&&a&&T.push({text:this.data.xAxisRightText,fill:this.themeConfig.quadrantXAxisTextFill,x:c+k+(y?k/2:0),y:t==="top"?this.config.xAxisLabelPadding+x.top:this.config.xAxisLabelPadding+q+h+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:y?"center":"left",horizontalPos:"top",rotation:0}),this.data.yAxisBottomText&&p&&T.push({text:this.data.yAxisBottomText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+c+m+this.config.quadrantPadding,y:q+h-(b?_/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:b?"center":"left",horizontalPos:"top",rotation:-90}),this.data.yAxisTopText&&p&&T.push({text:this.data.yAxisTopText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+c+m+this.config.quadrantPadding,y:q+_-(b?_/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:b?"center":"left",horizontalPos:"top",rotation:-90}),T}getQuadrants(t){let{quadrantSpace:a}=t,{quadrantHalfHeight:p,quadrantLeft:f,quadrantHalfWidth:o,quadrantTop:x}=a,_=[{text:{text:this.data.quadrant1Text,fill:this.themeConfig.quadrant1TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:f+o,y:x,width:o,height:p,fill:this.themeConfig.quadrant1Fill},{text:{text:this.data.quadrant2Text,fill:this.themeConfig.quadrant2TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:f,y:x,width:o,height:p,fill:this.themeConfig.quadrant2Fill},{text:{text:this.data.quadrant3Text,fill:this.themeConfig.quadrant3TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:f,y:x+p,width:o,height:p,fill:this.themeConfig.quadrant3Fill},{text:{text:this.data.quadrant4Text,fill:this.themeConfig.quadrant4TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:f+o,y:x+p,width:o,height:p,fill:this.themeConfig.quadrant4Fill}];for(let h of _)h.text.x=h.x+h.width/2,this.data.points.length===0?(h.text.y=h.y+h.height/2,h.text.horizontalPos="middle"):(h.text.y=h.y+this.config.quadrantTextTopPadding,h.text.horizontalPos="top");return _}getQuadrantPoints(t){let{quadrantSpace:a}=t,{quadrantHeight:p,quadrantLeft:f,quadrantTop:o,quadrantWidth:x}=a,_=Dt().domain([0,1]).range([f,x+f]),h=Dt().domain([0,1]).range([p+o,o]);return this.data.points.map(k=>{let q=this.classes.get(k.className);return q&&(k={...q,...k}),{x:_(k.x),y:h(k.y),fill:k.color??this.themeConfig.quadrantPointFill,radius:k.radius??this.config.pointRadius,text:{text:k.text,fill:this.themeConfig.quadrantPointTextFill,x:_(k.x),y:h(k.y)+this.config.pointTextPadding,verticalPos:"center",horizontalPos:"top",fontSize:this.config.pointLabelFontSize,rotation:0},strokeColor:k.strokeColor??this.themeConfig.quadrantPointFill,strokeWidth:k.strokeWidth??"0px"}})}getBorders(t){let a=this.config.quadrantExternalBorderStrokeWidth/2,{quadrantSpace:p}=t,{quadrantHalfHeight:f,quadrantHeight:o,quadrantLeft:x,quadrantHalfWidth:_,quadrantTop:h,quadrantWidth:c}=p;return[{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:x-a,y1:h,x2:x+c+a,y2:h},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:x+c,y1:h+a,x2:x+c,y2:h+o-a},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:x-a,y1:h+o,x2:x+c+a,y2:h+o},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:x,y1:h+a,x2:x,y2:h+o-a},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:x+_,y1:h+a,x2:x+_,y2:h+o-a},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:x+a,y1:h+f,x2:x+c-a,y2:h+f}]}getTitle(t){if(t)return{text:this.data.titleText,fill:this.themeConfig.quadrantTitleFill,fontSize:this.config.titleFontSize,horizontalPos:"top",verticalPos:"center",rotation:0,y:this.config.titlePadding,x:this.config.chartWidth/2}}build(){let t=this.config.showXAxis&&!!(this.data.xAxisLeftText||this.data.xAxisRightText),a=this.config.showYAxis&&!!(this.data.yAxisTopText||this.data.yAxisBottomText),p=this.config.showTitle&&!!this.data.titleText,f=this.data.points.length>0?"bottom":this.config.xAxisPosition,o=this.calculateSpace(f,t,a,p);return{points:this.getQuadrantPoints(o),quadrants:this.getQuadrants(o),axisLabels:this.getAxisLabels(f,t,a,o),borderLines:this.getBorders(o),title:this.getTitle(p)}}},mt=class extends Error{static{s(this,"InvalidStyleError")}constructor(t,a,p){super(`value for ${t} ${a} is invalid, please use a valid ${p}`),this.name="InvalidStyleError"}};function Vt(t){return!/^#?([\dA-Fa-f]{6}|[\dA-Fa-f]{3})$/.test(t)}s(Vt,"validateHexCode");function ue(t){return!/^\d+$/.test(t)}s(ue,"validateNumber");function xe(t){return!/^\d+px$/.test(t)}s(xe,"validateSizeInPixels");var Ve=Tt();function Q(t){return ne(t.trim(),Ve)}s(Q,"textSanitizer");var z=new ze;function fe(t){z.setData({quadrant1Text:Q(t.text)})}s(fe,"setQuadrant1Text");function ge(t){z.setData({quadrant2Text:Q(t.text)})}s(ge,"setQuadrant2Text");function pe(t){z.setData({quadrant3Text:Q(t.text)})}s(pe,"setQuadrant3Text");function ye(t){z.setData({quadrant4Text:Q(t.text)})}s(ye,"setQuadrant4Text");function be(t){z.setData({xAxisLeftText:Q(t.text)})}s(be,"setXAxisLeftText");function Te(t){z.setData({xAxisRightText:Q(t.text)})}s(Te,"setXAxisRightText");function qe(t){z.setData({yAxisTopText:Q(t.text)})}s(qe,"setYAxisTopText");function me(t){z.setData({yAxisBottomText:Q(t.text)})}s(me,"setYAxisBottomText");function kt(t){let a={};for(let p of t){let[f,o]=p.trim().split(/\s*:\s*/);if(f==="radius"){if(ue(o))throw new mt(f,o,"number");a.radius=parseInt(o)}else if(f==="color"){if(Vt(o))throw new mt(f,o,"hex code");a.color=o}else if(f==="stroke-color"){if(Vt(o))throw new mt(f,o,"hex code");a.strokeColor=o}else if(f==="stroke-width"){if(xe(o))throw new mt(f,o,"number of pixels (eg. 10px)");a.strokeWidth=o}else throw new Error(`style named ${f} is not supported.`)}return a}s(kt,"parseStyles");function ke(t,a,p,f,o){let x=kt(o);z.addPoints([{x:p,y:f,text:Q(t.text),className:a,...x}])}s(ke,"addPoint");function Se(t,a){z.addClass(t,kt(a))}s(Se,"addClass");function _e(t){z.setConfig({chartWidth:t})}s(_e,"setWidth");function Ae(t){z.setConfig({chartHeight:t})}s(Ae,"setHeight");function Fe(){let t=Tt(),{themeVariables:a,quadrantChart:p}=t;return p&&z.setConfig(p),z.setThemeConfig({quadrant1Fill:a.quadrant1Fill,quadrant2Fill:a.quadrant2Fill,quadrant3Fill:a.quadrant3Fill,quadrant4Fill:a.quadrant4Fill,quadrant1TextFill:a.quadrant1TextFill,quadrant2TextFill:a.quadrant2TextFill,quadrant3TextFill:a.quadrant3TextFill,quadrant4TextFill:a.quadrant4TextFill,quadrantPointFill:a.quadrantPointFill,quadrantPointTextFill:a.quadrantPointTextFill,quadrantXAxisTextFill:a.quadrantXAxisTextFill,quadrantYAxisTextFill:a.quadrantYAxisTextFill,quadrantExternalBorderStrokeFill:a.quadrantExternalBorderStrokeFill,quadrantInternalBorderStrokeFill:a.quadrantInternalBorderStrokeFill,quadrantTitleFill:a.quadrantTitleFill}),z.setData({titleText:Et()}),z.build()}s(Fe,"getQuadrantData");var Ie=s(function(){z.clear(),se()},"clear"),we={setWidth:_e,setHeight:Ae,setQuadrant1Text:fe,setQuadrant2Text:ge,setQuadrant3Text:pe,setQuadrant4Text:ye,setXAxisLeftText:be,setXAxisRightText:Te,setYAxisTopText:qe,setYAxisBottomText:me,parseStyles:kt,addPoint:ke,addClass:Se,getQuadrantData:Fe,clear:Ie,setAccTitle:oe,getAccTitle:le,setDiagramTitle:de,getDiagramTitle:Et,getAccDescription:ce,setAccDescription:he},Be=s((t,a,p,f)=>{function o(i){return i==="top"?"hanging":"middle"}s(o,"getDominantBaseLine");function x(i){return i==="left"?"start":"middle"}s(x,"getTextAnchor");function _(i){return`translate(${i.x}, ${i.y}) rotate(${i.rotation||0})`}s(_,"getTransformation");let h=Tt();ct.debug(`Rendering quadrant chart
`+t);let c=h.securityLevel,k;c==="sandbox"&&(k=qt("#i"+a));let m=(c==="sandbox"?qt(k.nodes()[0].contentDocument.body):qt("body")).select(`[id="${a}"]`),y=m.append("g").attr("class","main"),b=h.quadrantChart?.chartWidth??500,T=h.quadrantChart?.chartHeight??500;re(m,T,b,h.quadrantChart?.useMaxWidth??!0),m.attr("viewBox","0 0 "+b+" "+T),f.db.setHeight(T),f.db.setWidth(b);let g=f.db.getQuadrantData(),st=y.append("g").attr("class","quadrants"),dt=y.append("g").attr("class","border"),ut=y.append("g").attr("class","data-points"),xt=y.append("g").attr("class","labels"),ft=y.append("g").attr("class","title");g.title&&ft.append("text").attr("x",0).attr("y",0).attr("fill",g.title.fill).attr("font-size",g.title.fontSize).attr("dominant-baseline",o(g.title.horizontalPos)).attr("text-anchor",x(g.title.verticalPos)).attr("transform",_(g.title)).text(g.title.text),g.borderLines&&dt.selectAll("line").data(g.borderLines).enter().append("line").attr("x1",i=>i.x1).attr("y1",i=>i.y1).attr("x2",i=>i.x2).attr("y2",i=>i.y2).style("stroke",i=>i.strokeFill).style("stroke-width",i=>i.strokeWidth);let ot=st.selectAll("g.quadrant").data(g.quadrants).enter().append("g").attr("class","quadrant");ot.append("rect").attr("x",i=>i.x).attr("y",i=>i.y).attr("width",i=>i.width).attr("height",i=>i.height).attr("fill",i=>i.fill),ot.append("text").attr("x",0).attr("y",0).attr("fill",i=>i.text.fill).attr("font-size",i=>i.text.fontSize).attr("dominant-baseline",i=>o(i.text.horizontalPos)).attr("text-anchor",i=>x(i.text.verticalPos)).attr("transform",i=>_(i.text)).text(i=>i.text.text),xt.selectAll("g.label").data(g.axisLabels).enter().append("g").attr("class","label").append("text").attr("x",0).attr("y",0).text(i=>i.text).attr("fill",i=>i.fill).attr("font-size",i=>i.fontSize).attr("dominant-baseline",i=>o(i.horizontalPos)).attr("text-anchor",i=>x(i.verticalPos)).attr("transform",i=>_(i));let lt=ut.selectAll("g.data-point").data(g.points).enter().append("g").attr("class","data-point");lt.append("circle").attr("cx",i=>i.x).attr("cy",i=>i.y).attr("r",i=>i.radius).attr("fill",i=>i.fill).attr("stroke",i=>i.strokeColor).attr("stroke-width",i=>i.strokeWidth),lt.append("text").attr("x",0).attr("y",0).text(i=>i.text.text).attr("fill",i=>i.text.fill).attr("font-size",i=>i.text.fontSize).attr("dominant-baseline",i=>o(i.text.horizontalPos)).attr("text-anchor",i=>x(i.text.verticalPos)).attr("transform",i=>_(i.text))},"draw"),Re={draw:Be},Oe={parser:De,db:we,renderer:Re,styles:s(()=>"","styles")};export{Oe as diagram};
//# sourceMappingURL=quadrantDiagram-OS5C2QUG-BN35C5UH.min.js.map
