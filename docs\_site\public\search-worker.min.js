import{a as W,d as D}from"./chunk-OSRY5VT3.min.js";var ee=W((Z,Y)=>{(function(){var t=function(e){var r=new t.Builder;return r.pipeline.add(t.trimmer,t.<PERSON>,t.stemmer),r.searchPipeline.add(t.stemmer),e.call(r,r),r.build()};t.version="2.3.9";t.utils={},t.utils.warn=function(e){return function(r){e.console&&console.warn&&console.warn(r)}}(this),t.utils.asString=function(e){return e==null?"":e.toString()},t.utils.clone=function(e){if(e==null)return e;for(var r=Object.create(null),n=Object.keys(e),i=0;i<n.length;i++){var s=n[i],o=e[s];if(Array.isArray(o)){r[s]=o.slice();continue}if(typeof o=="string"||typeof o=="number"||typeof o=="boolean"){r[s]=o;continue}throw new TypeError("clone is not deep and does not support nested objects")}return r},t.FieldRef=function(e,r,n){this.docRef=e,this.fieldName=r,this._stringValue=n},t.FieldRef.joiner="/",t.FieldRef.fromString=function(e){var r=e.indexOf(t.FieldRef.joiner);if(r===-1)throw"malformed field ref string";var n=e.slice(0,r),i=e.slice(r+1);return new t.FieldRef(i,n,e)},t.FieldRef.prototype.toString=function(){return this._stringValue==null&&(this._stringValue=this.fieldName+t.FieldRef.joiner+this.docRef),this._stringValue};t.Set=function(e){if(this.elements=Object.create(null),e){this.length=e.length;for(var r=0;r<this.length;r++)this.elements[e[r]]=!0}else this.length=0},t.Set.complete={intersect:function(e){return e},union:function(){return this},contains:function(){return!0}},t.Set.empty={intersect:function(){return this},union:function(e){return e},contains:function(){return!1}},t.Set.prototype.contains=function(e){return!!this.elements[e]},t.Set.prototype.intersect=function(e){var r,n,i,s=[];if(e===t.Set.complete)return this;if(e===t.Set.empty)return e;this.length<e.length?(r=this,n=e):(r=e,n=this),i=Object.keys(r.elements);for(var o=0;o<i.length;o++){var u=i[o];u in n.elements&&s.push(u)}return new t.Set(s)},t.Set.prototype.union=function(e){return e===t.Set.complete?t.Set.complete:e===t.Set.empty?this:new t.Set(Object.keys(this.elements).concat(Object.keys(e.elements)))},t.idf=function(e,r){var n=0;for(var i in e)i!="_index"&&(n+=Object.keys(e[i]).length);var s=(r-n+.5)/(n+.5);return Math.log(1+Math.abs(s))},t.Token=function(e,r){this.str=e||"",this.metadata=r||{}},t.Token.prototype.toString=function(){return this.str},t.Token.prototype.update=function(e){return this.str=e(this.str,this.metadata),this},t.Token.prototype.clone=function(e){return e=e||function(r){return r},new t.Token(e(this.str,this.metadata),this.metadata)};t.tokenizer=function(e,r){if(e==null||e==null)return[];if(Array.isArray(e))return e.map(function(d){return new t.Token(t.utils.asString(d).toLowerCase(),t.utils.clone(r))});for(var n=e.toString().toLowerCase(),i=n.length,s=[],o=0,u=0;o<=i;o++){var l=n.charAt(o),a=o-u;if(l.match(t.tokenizer.separator)||o==i){if(a>0){var f=t.utils.clone(r)||{};f.position=[u,a],f.index=s.length,s.push(new t.Token(n.slice(u,o),f))}u=o+1}}return s},t.tokenizer.separator=/[\s\-]+/;t.Pipeline=function(){this._stack=[]},t.Pipeline.registeredFunctions=Object.create(null),t.Pipeline.registerFunction=function(e,r){r in this.registeredFunctions&&t.utils.warn("Overwriting existing registered function: "+r),e.label=r,t.Pipeline.registeredFunctions[e.label]=e},t.Pipeline.warnIfFunctionNotRegistered=function(e){var r=e.label&&e.label in this.registeredFunctions;r||t.utils.warn(`Function is not registered with pipeline. This may cause problems when serialising the index.
`,e)},t.Pipeline.load=function(e){var r=new t.Pipeline;return e.forEach(function(n){var i=t.Pipeline.registeredFunctions[n];if(i)r.add(i);else throw new Error("Cannot load unregistered function: "+n)}),r},t.Pipeline.prototype.add=function(){var e=Array.prototype.slice.call(arguments);e.forEach(function(r){t.Pipeline.warnIfFunctionNotRegistered(r),this._stack.push(r)},this)},t.Pipeline.prototype.after=function(e,r){t.Pipeline.warnIfFunctionNotRegistered(r);var n=this._stack.indexOf(e);if(n==-1)throw new Error("Cannot find existingFn");n=n+1,this._stack.splice(n,0,r)},t.Pipeline.prototype.before=function(e,r){t.Pipeline.warnIfFunctionNotRegistered(r);var n=this._stack.indexOf(e);if(n==-1)throw new Error("Cannot find existingFn");this._stack.splice(n,0,r)},t.Pipeline.prototype.remove=function(e){var r=this._stack.indexOf(e);r!=-1&&this._stack.splice(r,1)},t.Pipeline.prototype.run=function(e){for(var r=this._stack.length,n=0;n<r;n++){for(var i=this._stack[n],s=[],o=0;o<e.length;o++){var u=i(e[o],o,e);if(!(u==null||u===""))if(Array.isArray(u))for(var l=0;l<u.length;l++)s.push(u[l]);else s.push(u)}e=s}return e},t.Pipeline.prototype.runString=function(e,r){var n=new t.Token(e,r);return this.run([n]).map(function(i){return i.toString()})},t.Pipeline.prototype.reset=function(){this._stack=[]},t.Pipeline.prototype.toJSON=function(){return this._stack.map(function(e){return t.Pipeline.warnIfFunctionNotRegistered(e),e.label})};t.Vector=function(e){this._magnitude=0,this.elements=e||[]},t.Vector.prototype.positionForIndex=function(e){if(this.elements.length==0)return 0;for(var r=0,n=this.elements.length/2,i=n-r,s=Math.floor(i/2),o=this.elements[s*2];i>1&&(o<e&&(r=s),o>e&&(n=s),o!=e);)i=n-r,s=r+Math.floor(i/2),o=this.elements[s*2];if(o==e||o>e)return s*2;if(o<e)return(s+1)*2},t.Vector.prototype.insert=function(e,r){this.upsert(e,r,function(){throw"duplicate index"})},t.Vector.prototype.upsert=function(e,r,n){this._magnitude=0;var i=this.positionForIndex(e);this.elements[i]==e?this.elements[i+1]=n(this.elements[i+1],r):this.elements.splice(i,0,e,r)},t.Vector.prototype.magnitude=function(){if(this._magnitude)return this._magnitude;for(var e=0,r=this.elements.length,n=1;n<r;n+=2){var i=this.elements[n];e+=i*i}return this._magnitude=Math.sqrt(e)},t.Vector.prototype.dot=function(e){for(var r=0,n=this.elements,i=e.elements,s=n.length,o=i.length,u=0,l=0,a=0,f=0;a<s&&f<o;)u=n[a],l=i[f],u<l?a+=2:u>l?f+=2:u==l&&(r+=n[a+1]*i[f+1],a+=2,f+=2);return r},t.Vector.prototype.similarity=function(e){return this.dot(e)/this.magnitude()||0},t.Vector.prototype.toArray=function(){for(var e=new Array(this.elements.length/2),r=1,n=0;r<this.elements.length;r+=2,n++)e[n]=this.elements[r];return e},t.Vector.prototype.toJSON=function(){return this.elements};t.stemmer=function(){var e={ational:"ate",tional:"tion",enci:"ence",anci:"ance",izer:"ize",bli:"ble",alli:"al",entli:"ent",eli:"e",ousli:"ous",ization:"ize",ation:"ate",ator:"ate",alism:"al",iveness:"ive",fulness:"ful",ousness:"ous",aliti:"al",iviti:"ive",biliti:"ble",logi:"log"},r={icate:"ic",ative:"",alize:"al",iciti:"ic",ical:"ic",ful:"",ness:""},n="[^aeiou]",i="[aeiouy]",s=n+"[^aeiouy]*",o=i+"[aeiou]*",u="^("+s+")?"+o+s,l="^("+s+")?"+o+s+"("+o+")?$",a="^("+s+")?"+o+s+o+s,f="^("+s+")?"+i,d=new RegExp(u),h=new RegExp(a),g=new RegExp(l),p=new RegExp(f),m=/^(.+?)(ss|i)es$/,_=/^(.+?)([^s])s$/,v=/^(.+?)eed$/,S=/^(.+?)(ed|ing)$/,x=/.$/,H=/(at|bl|iz)$/,I=new RegExp("([^aeiouylsz])\\1$"),B=new RegExp("^"+s+i+"[^aeiouwxy]$"),P=/^(.+?[^aeiou])y$/,T=/^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/,L=/^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/,F=/^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/,N=/^(.+?)(s|t)(ion)$/,C=/^(.+?)e$/,j=/ll$/,U=new RegExp("^"+s+i+"[^aeiouwxy]$"),K=function(c){var O,E,k,y,Q,A,M;if(c.length<3)return c;if(k=c.substr(0,1),k=="y"&&(c=k.toUpperCase()+c.substr(1)),y=m,Q=_,y.test(c)?c=c.replace(y,"$1$2"):Q.test(c)&&(c=c.replace(Q,"$1$2")),y=v,Q=S,y.test(c)){var b=y.exec(c);y=d,y.test(b[1])&&(y=x,c=c.replace(y,""))}else if(Q.test(c)){var b=Q.exec(c);O=b[1],Q=p,Q.test(O)&&(c=O,Q=H,A=I,M=B,Q.test(c)?c=c+"e":A.test(c)?(y=x,c=c.replace(y,"")):M.test(c)&&(c=c+"e"))}if(y=P,y.test(c)){var b=y.exec(c);O=b[1],c=O+"i"}if(y=T,y.test(c)){var b=y.exec(c);O=b[1],E=b[2],y=d,y.test(O)&&(c=O+e[E])}if(y=L,y.test(c)){var b=y.exec(c);O=b[1],E=b[2],y=d,y.test(O)&&(c=O+r[E])}if(y=F,Q=N,y.test(c)){var b=y.exec(c);O=b[1],y=h,y.test(O)&&(c=O)}else if(Q.test(c)){var b=Q.exec(c);O=b[1]+b[2],Q=h,Q.test(O)&&(c=O)}if(y=C,y.test(c)){var b=y.exec(c);O=b[1],y=h,Q=g,A=U,(y.test(O)||Q.test(O)&&!A.test(O))&&(c=O)}return y=j,Q=h,y.test(c)&&Q.test(c)&&(y=x,c=c.replace(y,"")),k=="y"&&(c=k.toLowerCase()+c.substr(1)),c};return function(R){return R.update(K)}}(),t.Pipeline.registerFunction(t.stemmer,"stemmer");t.generateStopWordFilter=function(e){var r=e.reduce(function(n,i){return n[i]=i,n},{});return function(n){if(n&&r[n.toString()]!==n.toString())return n}},t.stopWordFilter=t.generateStopWordFilter(["a","able","about","across","after","all","almost","also","am","among","an","and","any","are","as","at","be","because","been","but","by","can","cannot","could","dear","did","do","does","either","else","ever","every","for","from","get","got","had","has","have","he","her","hers","him","his","how","however","i","if","in","into","is","it","its","just","least","let","like","likely","may","me","might","most","must","my","neither","no","nor","not","of","off","often","on","only","or","other","our","own","rather","said","say","says","she","should","since","so","some","than","that","the","their","them","then","there","these","they","this","tis","to","too","twas","us","wants","was","we","were","what","when","where","which","while","who","whom","why","will","with","would","yet","you","your"]),t.Pipeline.registerFunction(t.stopWordFilter,"stopWordFilter");t.trimmer=function(e){return e.update(function(r){return r.replace(/^\W+/,"").replace(/\W+$/,"")})},t.Pipeline.registerFunction(t.trimmer,"trimmer");t.TokenSet=function(){this.final=!1,this.edges={},this.id=t.TokenSet._nextId,t.TokenSet._nextId+=1},t.TokenSet._nextId=1,t.TokenSet.fromArray=function(e){for(var r=new t.TokenSet.Builder,n=0,i=e.length;n<i;n++)r.insert(e[n]);return r.finish(),r.root},t.TokenSet.fromClause=function(e){return"editDistance"in e?t.TokenSet.fromFuzzyString(e.term,e.editDistance):t.TokenSet.fromString(e.term)},t.TokenSet.fromFuzzyString=function(e,r){for(var n=new t.TokenSet,i=[{node:n,editsRemaining:r,str:e}];i.length;){var s=i.pop();if(s.str.length>0){var o=s.str.charAt(0),u;o in s.node.edges?u=s.node.edges[o]:(u=new t.TokenSet,s.node.edges[o]=u),s.str.length==1&&(u.final=!0),i.push({node:u,editsRemaining:s.editsRemaining,str:s.str.slice(1)})}if(s.editsRemaining!=0){if("*"in s.node.edges)var l=s.node.edges["*"];else{var l=new t.TokenSet;s.node.edges["*"]=l}if(s.str.length==0&&(l.final=!0),i.push({node:l,editsRemaining:s.editsRemaining-1,str:s.str}),s.str.length>1&&i.push({node:s.node,editsRemaining:s.editsRemaining-1,str:s.str.slice(1)}),s.str.length==1&&(s.node.final=!0),s.str.length>=1){if("*"in s.node.edges)var a=s.node.edges["*"];else{var a=new t.TokenSet;s.node.edges["*"]=a}s.str.length==1&&(a.final=!0),i.push({node:a,editsRemaining:s.editsRemaining-1,str:s.str.slice(1)})}if(s.str.length>1){var f=s.str.charAt(0),d=s.str.charAt(1),h;d in s.node.edges?h=s.node.edges[d]:(h=new t.TokenSet,s.node.edges[d]=h),s.str.length==1&&(h.final=!0),i.push({node:h,editsRemaining:s.editsRemaining-1,str:f+s.str.slice(2)})}}}return n},t.TokenSet.fromString=function(e){for(var r=new t.TokenSet,n=r,i=0,s=e.length;i<s;i++){var o=e[i],u=i==s-1;if(o=="*")r.edges[o]=r,r.final=u;else{var l=new t.TokenSet;l.final=u,r.edges[o]=l,r=l}}return n},t.TokenSet.prototype.toArray=function(){for(var e=[],r=[{prefix:"",node:this}];r.length;){var n=r.pop(),i=Object.keys(n.node.edges),s=i.length;n.node.final&&(n.prefix.charAt(0),e.push(n.prefix));for(var o=0;o<s;o++){var u=i[o];r.push({prefix:n.prefix.concat(u),node:n.node.edges[u]})}}return e},t.TokenSet.prototype.toString=function(){if(this._str)return this._str;for(var e=this.final?"1":"0",r=Object.keys(this.edges).sort(),n=r.length,i=0;i<n;i++){var s=r[i],o=this.edges[s];e=e+s+o.id}return e},t.TokenSet.prototype.intersect=function(e){for(var r=new t.TokenSet,n=void 0,i=[{qNode:e,output:r,node:this}];i.length;){n=i.pop();for(var s=Object.keys(n.qNode.edges),o=s.length,u=Object.keys(n.node.edges),l=u.length,a=0;a<o;a++)for(var f=s[a],d=0;d<l;d++){var h=u[d];if(h==f||f=="*"){var g=n.node.edges[h],p=n.qNode.edges[f],m=g.final&&p.final,_=void 0;h in n.output.edges?(_=n.output.edges[h],_.final=_.final||m):(_=new t.TokenSet,_.final=m,n.output.edges[h]=_),i.push({qNode:p,output:_,node:g})}}}return r},t.TokenSet.Builder=function(){this.previousWord="",this.root=new t.TokenSet,this.uncheckedNodes=[],this.minimizedNodes={}},t.TokenSet.Builder.prototype.insert=function(e){var r,n=0;if(e<this.previousWord)throw new Error("Out of order word insertion");for(var i=0;i<e.length&&i<this.previousWord.length&&e[i]==this.previousWord[i];i++)n++;this.minimize(n),this.uncheckedNodes.length==0?r=this.root:r=this.uncheckedNodes[this.uncheckedNodes.length-1].child;for(var i=n;i<e.length;i++){var s=new t.TokenSet,o=e[i];r.edges[o]=s,this.uncheckedNodes.push({parent:r,char:o,child:s}),r=s}r.final=!0,this.previousWord=e},t.TokenSet.Builder.prototype.finish=function(){this.minimize(0)},t.TokenSet.Builder.prototype.minimize=function(e){for(var r=this.uncheckedNodes.length-1;r>=e;r--){var n=this.uncheckedNodes[r],i=n.child.toString();i in this.minimizedNodes?n.parent.edges[n.char]=this.minimizedNodes[i]:(n.child._str=i,this.minimizedNodes[i]=n.child),this.uncheckedNodes.pop()}};t.Index=function(e){this.invertedIndex=e.invertedIndex,this.fieldVectors=e.fieldVectors,this.tokenSet=e.tokenSet,this.fields=e.fields,this.pipeline=e.pipeline},t.Index.prototype.search=function(e){return this.query(function(r){var n=new t.QueryParser(e,r);n.parse()})},t.Index.prototype.query=function(e){for(var r=new t.Query(this.fields),n=Object.create(null),i=Object.create(null),s=Object.create(null),o=Object.create(null),u=Object.create(null),l=0;l<this.fields.length;l++)i[this.fields[l]]=new t.Vector;e.call(r,r);for(var l=0;l<r.clauses.length;l++){var a=r.clauses[l],f=null,d=t.Set.empty;a.usePipeline?f=this.pipeline.runString(a.term,{fields:a.fields}):f=[a.term];for(var h=0;h<f.length;h++){var g=f[h];a.term=g;var p=t.TokenSet.fromClause(a),m=this.tokenSet.intersect(p).toArray();if(m.length===0&&a.presence===t.Query.presence.REQUIRED){for(var _=0;_<a.fields.length;_++){var v=a.fields[_];o[v]=t.Set.empty}break}for(var S=0;S<m.length;S++)for(var x=m[S],H=this.invertedIndex[x],I=H._index,_=0;_<a.fields.length;_++){var v=a.fields[_],B=H[v],P=Object.keys(B),T=x+"/"+v,L=new t.Set(P);if(a.presence==t.Query.presence.REQUIRED&&(d=d.union(L),o[v]===void 0&&(o[v]=t.Set.complete)),a.presence==t.Query.presence.PROHIBITED){u[v]===void 0&&(u[v]=t.Set.empty),u[v]=u[v].union(L);continue}if(i[v].upsert(I,a.boost,function(ye,_e){return ye+_e}),!s[T]){for(var F=0;F<P.length;F++){var N=P[F],C=new t.FieldRef(N,v),j=B[N],U;(U=n[C])===void 0?n[C]=new t.MatchData(x,v,j):U.add(x,v,j)}s[T]=!0}}}if(a.presence===t.Query.presence.REQUIRED)for(var _=0;_<a.fields.length;_++){var v=a.fields[_];o[v]=o[v].intersect(d)}}for(var K=t.Set.complete,R=t.Set.empty,l=0;l<this.fields.length;l++){var v=this.fields[l];o[v]&&(K=K.intersect(o[v])),u[v]&&(R=R.union(u[v]))}var c=Object.keys(n),O=[],E=Object.create(null);if(r.isNegated()){c=Object.keys(this.fieldVectors);for(var l=0;l<c.length;l++){var C=c[l],k=t.FieldRef.fromString(C);n[C]=new t.MatchData}}for(var l=0;l<c.length;l++){var k=t.FieldRef.fromString(c[l]),y=k.docRef;if(K.contains(y)&&!R.contains(y)){var Q=this.fieldVectors[k],A=i[k.fieldName].similarity(Q),M;if((M=E[y])!==void 0)M.score+=A,M.matchData.combine(n[k]);else{var b={ref:y,score:A,matchData:n[k]};E[y]=b,O.push(b)}}}return O.sort(function(de,pe){return pe.score-de.score})},t.Index.prototype.toJSON=function(){var e=Object.keys(this.invertedIndex).sort().map(function(n){return[n,this.invertedIndex[n]]},this),r=Object.keys(this.fieldVectors).map(function(n){return[n,this.fieldVectors[n].toJSON()]},this);return{version:t.version,fields:this.fields,fieldVectors:r,invertedIndex:e,pipeline:this.pipeline.toJSON()}},t.Index.load=function(e){var r={},n={},i=e.fieldVectors,s=Object.create(null),o=e.invertedIndex,u=new t.TokenSet.Builder,l=t.Pipeline.load(e.pipeline);e.version!=t.version&&t.utils.warn("Version mismatch when loading serialised index. Current version of lunr '"+t.version+"' does not match serialized index '"+e.version+"'");for(var a=0;a<i.length;a++){var f=i[a],d=f[0],h=f[1];n[d]=new t.Vector(h)}for(var a=0;a<o.length;a++){var f=o[a],g=f[0],p=f[1];u.insert(g),s[g]=p}return u.finish(),r.fields=e.fields,r.fieldVectors=n,r.invertedIndex=s,r.tokenSet=u.root,r.pipeline=l,new t.Index(r)};t.Builder=function(){this._ref="id",this._fields=Object.create(null),this._documents=Object.create(null),this.invertedIndex=Object.create(null),this.fieldTermFrequencies={},this.fieldLengths={},this.tokenizer=t.tokenizer,this.pipeline=new t.Pipeline,this.searchPipeline=new t.Pipeline,this.documentCount=0,this._b=.75,this._k1=1.2,this.termIndex=0,this.metadataWhitelist=[]},t.Builder.prototype.ref=function(e){this._ref=e},t.Builder.prototype.field=function(e,r){if(/\//.test(e))throw new RangeError("Field '"+e+"' contains illegal character '/'");this._fields[e]=r||{}},t.Builder.prototype.b=function(e){e<0?this._b=0:e>1?this._b=1:this._b=e},t.Builder.prototype.k1=function(e){this._k1=e},t.Builder.prototype.add=function(e,r){var n=e[this._ref],i=Object.keys(this._fields);this._documents[n]=r||{},this.documentCount+=1;for(var s=0;s<i.length;s++){var o=i[s],u=this._fields[o].extractor,l=u?u(e):e[o],a=this.tokenizer(l,{fields:[o]}),f=this.pipeline.run(a),d=new t.FieldRef(n,o),h=Object.create(null);this.fieldTermFrequencies[d]=h,this.fieldLengths[d]=0,this.fieldLengths[d]+=f.length;for(var g=0;g<f.length;g++){var p=f[g];if(h[p]==null&&(h[p]=0),h[p]+=1,this.invertedIndex[p]==null){var m=Object.create(null);m._index=this.termIndex,this.termIndex+=1;for(var _=0;_<i.length;_++)m[i[_]]=Object.create(null);this.invertedIndex[p]=m}this.invertedIndex[p][o][n]==null&&(this.invertedIndex[p][o][n]=Object.create(null));for(var v=0;v<this.metadataWhitelist.length;v++){var S=this.metadataWhitelist[v],x=p.metadata[S];this.invertedIndex[p][o][n][S]==null&&(this.invertedIndex[p][o][n][S]=[]),this.invertedIndex[p][o][n][S].push(x)}}}},t.Builder.prototype.calculateAverageFieldLengths=function(){for(var e=Object.keys(this.fieldLengths),r=e.length,n={},i={},s=0;s<r;s++){var o=t.FieldRef.fromString(e[s]),u=o.fieldName;i[u]||(i[u]=0),i[u]+=1,n[u]||(n[u]=0),n[u]+=this.fieldLengths[o]}for(var l=Object.keys(this._fields),s=0;s<l.length;s++){var a=l[s];n[a]=n[a]/i[a]}this.averageFieldLength=n},t.Builder.prototype.createFieldVectors=function(){for(var e={},r=Object.keys(this.fieldTermFrequencies),n=r.length,i=Object.create(null),s=0;s<n;s++){for(var o=t.FieldRef.fromString(r[s]),u=o.fieldName,l=this.fieldLengths[o],a=new t.Vector,f=this.fieldTermFrequencies[o],d=Object.keys(f),h=d.length,g=this._fields[u].boost||1,p=this._documents[o.docRef].boost||1,m=0;m<h;m++){var _=d[m],v=f[_],S=this.invertedIndex[_]._index,x,H,I;i[_]===void 0?(x=t.idf(this.invertedIndex[_],this.documentCount),i[_]=x):x=i[_],H=x*((this._k1+1)*v)/(this._k1*(1-this._b+this._b*(l/this.averageFieldLength[u]))+v),H*=g,H*=p,I=Math.round(H*1e3)/1e3,a.insert(S,I)}e[o]=a}this.fieldVectors=e},t.Builder.prototype.createTokenSet=function(){this.tokenSet=t.TokenSet.fromArray(Object.keys(this.invertedIndex).sort())},t.Builder.prototype.build=function(){return this.calculateAverageFieldLengths(),this.createFieldVectors(),this.createTokenSet(),new t.Index({invertedIndex:this.invertedIndex,fieldVectors:this.fieldVectors,tokenSet:this.tokenSet,fields:Object.keys(this._fields),pipeline:this.searchPipeline})},t.Builder.prototype.use=function(e){var r=Array.prototype.slice.call(arguments,1);r.unshift(this),e.apply(this,r)},t.MatchData=function(e,r,n){for(var i=Object.create(null),s=Object.keys(n||{}),o=0;o<s.length;o++){var u=s[o];i[u]=n[u].slice()}this.metadata=Object.create(null),e!==void 0&&(this.metadata[e]=Object.create(null),this.metadata[e][r]=i)},t.MatchData.prototype.combine=function(e){for(var r=Object.keys(e.metadata),n=0;n<r.length;n++){var i=r[n],s=Object.keys(e.metadata[i]);this.metadata[i]==null&&(this.metadata[i]=Object.create(null));for(var o=0;o<s.length;o++){var u=s[o],l=Object.keys(e.metadata[i][u]);this.metadata[i][u]==null&&(this.metadata[i][u]=Object.create(null));for(var a=0;a<l.length;a++){var f=l[a];this.metadata[i][u][f]==null?this.metadata[i][u][f]=e.metadata[i][u][f]:this.metadata[i][u][f]=this.metadata[i][u][f].concat(e.metadata[i][u][f])}}}},t.MatchData.prototype.add=function(e,r,n){if(!(e in this.metadata)){this.metadata[e]=Object.create(null),this.metadata[e][r]=n;return}if(!(r in this.metadata[e])){this.metadata[e][r]=n;return}for(var i=Object.keys(n),s=0;s<i.length;s++){var o=i[s];o in this.metadata[e][r]?this.metadata[e][r][o]=this.metadata[e][r][o].concat(n[o]):this.metadata[e][r][o]=n[o]}},t.Query=function(e){this.clauses=[],this.allFields=e},t.Query.wildcard=new String("*"),t.Query.wildcard.NONE=0,t.Query.wildcard.LEADING=1,t.Query.wildcard.TRAILING=2,t.Query.presence={OPTIONAL:1,REQUIRED:2,PROHIBITED:3},t.Query.prototype.clause=function(e){return"fields"in e||(e.fields=this.allFields),"boost"in e||(e.boost=1),"usePipeline"in e||(e.usePipeline=!0),"wildcard"in e||(e.wildcard=t.Query.wildcard.NONE),e.wildcard&t.Query.wildcard.LEADING&&e.term.charAt(0)!=t.Query.wildcard&&(e.term="*"+e.term),e.wildcard&t.Query.wildcard.TRAILING&&e.term.slice(-1)!=t.Query.wildcard&&(e.term=""+e.term+"*"),"presence"in e||(e.presence=t.Query.presence.OPTIONAL),this.clauses.push(e),this},t.Query.prototype.isNegated=function(){for(var e=0;e<this.clauses.length;e++)if(this.clauses[e].presence!=t.Query.presence.PROHIBITED)return!1;return!0},t.Query.prototype.term=function(e,r){if(Array.isArray(e))return e.forEach(function(i){this.term(i,t.utils.clone(r))},this),this;var n=r||{};return n.term=e.toString(),this.clause(n),this},t.QueryParseError=function(e,r,n){this.name="QueryParseError",this.message=e,this.start=r,this.end=n},t.QueryParseError.prototype=new Error,t.QueryLexer=function(e){this.lexemes=[],this.str=e,this.length=e.length,this.pos=0,this.start=0,this.escapeCharPositions=[]},t.QueryLexer.prototype.run=function(){for(var e=t.QueryLexer.lexText;e;)e=e(this)},t.QueryLexer.prototype.sliceString=function(){for(var e=[],r=this.start,n=this.pos,i=0;i<this.escapeCharPositions.length;i++)n=this.escapeCharPositions[i],e.push(this.str.slice(r,n)),r=n+1;return e.push(this.str.slice(r,this.pos)),this.escapeCharPositions.length=0,e.join("")},t.QueryLexer.prototype.emit=function(e){this.lexemes.push({type:e,str:this.sliceString(),start:this.start,end:this.pos}),this.start=this.pos},t.QueryLexer.prototype.escapeCharacter=function(){this.escapeCharPositions.push(this.pos-1),this.pos+=1},t.QueryLexer.prototype.next=function(){if(this.pos>=this.length)return t.QueryLexer.EOS;var e=this.str.charAt(this.pos);return this.pos+=1,e},t.QueryLexer.prototype.width=function(){return this.pos-this.start},t.QueryLexer.prototype.ignore=function(){this.start==this.pos&&(this.pos+=1),this.start=this.pos},t.QueryLexer.prototype.backup=function(){this.pos-=1},t.QueryLexer.prototype.acceptDigitRun=function(){var e,r;do e=this.next(),r=e.charCodeAt(0);while(r>47&&r<58);e!=t.QueryLexer.EOS&&this.backup()},t.QueryLexer.prototype.more=function(){return this.pos<this.length},t.QueryLexer.EOS="EOS",t.QueryLexer.FIELD="FIELD",t.QueryLexer.TERM="TERM",t.QueryLexer.EDIT_DISTANCE="EDIT_DISTANCE",t.QueryLexer.BOOST="BOOST",t.QueryLexer.PRESENCE="PRESENCE",t.QueryLexer.lexField=function(e){return e.backup(),e.emit(t.QueryLexer.FIELD),e.ignore(),t.QueryLexer.lexText},t.QueryLexer.lexTerm=function(e){if(e.width()>1&&(e.backup(),e.emit(t.QueryLexer.TERM)),e.ignore(),e.more())return t.QueryLexer.lexText},t.QueryLexer.lexEditDistance=function(e){return e.ignore(),e.acceptDigitRun(),e.emit(t.QueryLexer.EDIT_DISTANCE),t.QueryLexer.lexText},t.QueryLexer.lexBoost=function(e){return e.ignore(),e.acceptDigitRun(),e.emit(t.QueryLexer.BOOST),t.QueryLexer.lexText},t.QueryLexer.lexEOS=function(e){e.width()>0&&e.emit(t.QueryLexer.TERM)},t.QueryLexer.termSeparator=t.tokenizer.separator,t.QueryLexer.lexText=function(e){for(;;){var r=e.next();if(r==t.QueryLexer.EOS)return t.QueryLexer.lexEOS;if(r.charCodeAt(0)==92){e.escapeCharacter();continue}if(r==":")return t.QueryLexer.lexField;if(r=="~")return e.backup(),e.width()>0&&e.emit(t.QueryLexer.TERM),t.QueryLexer.lexEditDistance;if(r=="^")return e.backup(),e.width()>0&&e.emit(t.QueryLexer.TERM),t.QueryLexer.lexBoost;if(r=="+"&&e.width()===1||r=="-"&&e.width()===1)return e.emit(t.QueryLexer.PRESENCE),t.QueryLexer.lexText;if(r.match(t.QueryLexer.termSeparator))return t.QueryLexer.lexTerm}},t.QueryParser=function(e,r){this.lexer=new t.QueryLexer(e),this.query=r,this.currentClause={},this.lexemeIdx=0},t.QueryParser.prototype.parse=function(){this.lexer.run(),this.lexemes=this.lexer.lexemes;for(var e=t.QueryParser.parseClause;e;)e=e(this);return this.query},t.QueryParser.prototype.peekLexeme=function(){return this.lexemes[this.lexemeIdx]},t.QueryParser.prototype.consumeLexeme=function(){var e=this.peekLexeme();return this.lexemeIdx+=1,e},t.QueryParser.prototype.nextClause=function(){var e=this.currentClause;this.query.clause(e),this.currentClause={}},t.QueryParser.parseClause=function(e){var r=e.peekLexeme();if(r!=null)switch(r.type){case t.QueryLexer.PRESENCE:return t.QueryParser.parsePresence;case t.QueryLexer.FIELD:return t.QueryParser.parseField;case t.QueryLexer.TERM:return t.QueryParser.parseTerm;default:var n="expected either a field or a term, found "+r.type;throw r.str.length>=1&&(n+=" with value '"+r.str+"'"),new t.QueryParseError(n,r.start,r.end)}},t.QueryParser.parsePresence=function(e){var r=e.consumeLexeme();if(r!=null){switch(r.str){case"-":e.currentClause.presence=t.Query.presence.PROHIBITED;break;case"+":e.currentClause.presence=t.Query.presence.REQUIRED;break;default:var n="unrecognised presence operator'"+r.str+"'";throw new t.QueryParseError(n,r.start,r.end)}var i=e.peekLexeme();if(i==null){var n="expecting term or field, found nothing";throw new t.QueryParseError(n,r.start,r.end)}switch(i.type){case t.QueryLexer.FIELD:return t.QueryParser.parseField;case t.QueryLexer.TERM:return t.QueryParser.parseTerm;default:var n="expecting term or field, found '"+i.type+"'";throw new t.QueryParseError(n,i.start,i.end)}}},t.QueryParser.parseField=function(e){var r=e.consumeLexeme();if(r!=null){if(e.query.allFields.indexOf(r.str)==-1){var n=e.query.allFields.map(function(o){return"'"+o+"'"}).join(", "),i="unrecognised field '"+r.str+"', possible fields: "+n;throw new t.QueryParseError(i,r.start,r.end)}e.currentClause.fields=[r.str];var s=e.peekLexeme();if(s==null){var i="expecting term, found nothing";throw new t.QueryParseError(i,r.start,r.end)}switch(s.type){case t.QueryLexer.TERM:return t.QueryParser.parseTerm;default:var i="expecting term, found '"+s.type+"'";throw new t.QueryParseError(i,s.start,s.end)}}},t.QueryParser.parseTerm=function(e){var r=e.consumeLexeme();if(r!=null){e.currentClause.term=r.str.toLowerCase(),r.str.indexOf("*")!=-1&&(e.currentClause.usePipeline=!1);var n=e.peekLexeme();if(n==null){e.nextClause();return}switch(n.type){case t.QueryLexer.TERM:return e.nextClause(),t.QueryParser.parseTerm;case t.QueryLexer.FIELD:return e.nextClause(),t.QueryParser.parseField;case t.QueryLexer.EDIT_DISTANCE:return t.QueryParser.parseEditDistance;case t.QueryLexer.BOOST:return t.QueryParser.parseBoost;case t.QueryLexer.PRESENCE:return e.nextClause(),t.QueryParser.parsePresence;default:var i="Unexpected lexeme type '"+n.type+"'";throw new t.QueryParseError(i,n.start,n.end)}}},t.QueryParser.parseEditDistance=function(e){var r=e.consumeLexeme();if(r!=null){var n=parseInt(r.str,10);if(isNaN(n)){var i="edit distance must be numeric";throw new t.QueryParseError(i,r.start,r.end)}e.currentClause.editDistance=n;var s=e.peekLexeme();if(s==null){e.nextClause();return}switch(s.type){case t.QueryLexer.TERM:return e.nextClause(),t.QueryParser.parseTerm;case t.QueryLexer.FIELD:return e.nextClause(),t.QueryParser.parseField;case t.QueryLexer.EDIT_DISTANCE:return t.QueryParser.parseEditDistance;case t.QueryLexer.BOOST:return t.QueryParser.parseBoost;case t.QueryLexer.PRESENCE:return e.nextClause(),t.QueryParser.parsePresence;default:var i="Unexpected lexeme type '"+s.type+"'";throw new t.QueryParseError(i,s.start,s.end)}}},t.QueryParser.parseBoost=function(e){var r=e.consumeLexeme();if(r!=null){var n=parseInt(r.str,10);if(isNaN(n)){var i="boost must be numeric";throw new t.QueryParseError(i,r.start,r.end)}e.currentClause.boost=n;var s=e.peekLexeme();if(s==null){e.nextClause();return}switch(s.type){case t.QueryLexer.TERM:return e.nextClause(),t.QueryParser.parseTerm;case t.QueryLexer.FIELD:return e.nextClause(),t.QueryParser.parseField;case t.QueryLexer.EDIT_DISTANCE:return t.QueryParser.parseEditDistance;case t.QueryLexer.BOOST:return t.QueryParser.parseBoost;case t.QueryLexer.PRESENCE:return e.nextClause(),t.QueryParser.parsePresence;default:var i="Unexpected lexeme type '"+s.type+"'";throw new t.QueryParseError(i,s.start,s.end)}}},function(e,r){typeof define=="function"&&define.amd?define(r):typeof Z=="object"?Y.exports=r():e.lunr=r()}(this,function(){return t})})()});var re=W((V,te)=>{(function(t,e){typeof define=="function"&&define.amd?define(e):typeof V=="object"?te.exports=e():e()(t.lunr)})(V,function(){return function(t){t.stemmerSupport={Among:function(e,r,n,i){if(this.toCharArray=function(s){for(var o=s.length,u=new Array(o),l=0;l<o;l++)u[l]=s.charCodeAt(l);return u},!e&&e!=""||!r&&r!=0||!n)throw"Bad Among initialisation: s:"+e+", substring_i: "+r+", result: "+n;this.s_size=e.length,this.s=this.toCharArray(e),this.substring_i=r,this.result=n,this.method=i},SnowballProgram:function(){var e;return{bra:0,ket:0,limit:0,cursor:0,limit_backward:0,setCurrent:function(r){e=r,this.cursor=0,this.limit=r.length,this.limit_backward=0,this.bra=this.cursor,this.ket=this.limit},getCurrent:function(){var r=e;return e=null,r},in_grouping:function(r,n,i){if(this.cursor<this.limit){var s=e.charCodeAt(this.cursor);if(s<=i&&s>=n&&(s-=n,r[s>>3]&1<<(s&7)))return this.cursor++,!0}return!1},in_grouping_b:function(r,n,i){if(this.cursor>this.limit_backward){var s=e.charCodeAt(this.cursor-1);if(s<=i&&s>=n&&(s-=n,r[s>>3]&1<<(s&7)))return this.cursor--,!0}return!1},out_grouping:function(r,n,i){if(this.cursor<this.limit){var s=e.charCodeAt(this.cursor);if(s>i||s<n)return this.cursor++,!0;if(s-=n,!(r[s>>3]&1<<(s&7)))return this.cursor++,!0}return!1},out_grouping_b:function(r,n,i){if(this.cursor>this.limit_backward){var s=e.charCodeAt(this.cursor-1);if(s>i||s<n)return this.cursor--,!0;if(s-=n,!(r[s>>3]&1<<(s&7)))return this.cursor--,!0}return!1},eq_s:function(r,n){if(this.limit-this.cursor<r)return!1;for(var i=0;i<r;i++)if(e.charCodeAt(this.cursor+i)!=n.charCodeAt(i))return!1;return this.cursor+=r,!0},eq_s_b:function(r,n){if(this.cursor-this.limit_backward<r)return!1;for(var i=0;i<r;i++)if(e.charCodeAt(this.cursor-r+i)!=n.charCodeAt(i))return!1;return this.cursor-=r,!0},find_among:function(r,n){for(var i=0,s=n,o=this.cursor,u=this.limit,l=0,a=0,f=!1;;){for(var d=i+(s-i>>1),h=0,g=l<a?l:a,p=r[d],m=g;m<p.s_size;m++){if(o+g==u){h=-1;break}if(h=e.charCodeAt(o+g)-p.s[m],h)break;g++}if(h<0?(s=d,a=g):(i=d,l=g),s-i<=1){if(i>0||s==i||f)break;f=!0}}for(;;){var p=r[i];if(l>=p.s_size){if(this.cursor=o+p.s_size,!p.method)return p.result;var _=p.method();if(this.cursor=o+p.s_size,_)return p.result}if(i=p.substring_i,i<0)return 0}},find_among_b:function(r,n){for(var i=0,s=n,o=this.cursor,u=this.limit_backward,l=0,a=0,f=!1;;){for(var d=i+(s-i>>1),h=0,g=l<a?l:a,p=r[d],m=p.s_size-1-g;m>=0;m--){if(o-g==u){h=-1;break}if(h=e.charCodeAt(o-1-g)-p.s[m],h)break;g++}if(h<0?(s=d,a=g):(i=d,l=g),s-i<=1){if(i>0||s==i||f)break;f=!0}}for(;;){var p=r[i];if(l>=p.s_size){if(this.cursor=o-p.s_size,!p.method)return p.result;var _=p.method();if(this.cursor=o-p.s_size,_)return p.result}if(i=p.substring_i,i<0)return 0}},replace_s:function(r,n,i){var s=i.length-(n-r),o=e.substring(0,r),u=e.substring(n);return e=o+i+u,this.limit+=s,this.cursor>=n?this.cursor+=s:this.cursor>r&&(this.cursor=r),s},slice_check:function(){if(this.bra<0||this.bra>this.ket||this.ket>this.limit||this.limit>e.length)throw"faulty slice operation"},slice_from:function(r){this.slice_check(),this.replace_s(this.bra,this.ket,r)},slice_del:function(){this.slice_from("")},insert:function(r,n,i){var s=this.replace_s(r,n,i);r<=this.bra&&(this.bra+=s),r<=this.ket&&(this.ket+=s)},slice_to:function(){return this.slice_check(),e.substring(this.bra,this.ket)},eq_v_b:function(r){return this.eq_s_b(r.length,r)}}}},t.trimmerSupport={generateTrimmer:function(e){var r=new RegExp("^[^"+e+"]+"),n=new RegExp("[^"+e+"]+$");return function(i){return typeof i.update=="function"?i.update(function(s){return s.replace(r,"").replace(n,"")}):i.replace(r,"").replace(n,"")}}}}})});var ne=W((z,ie)=>{(function(t,e){typeof define=="function"&&define.amd?define(e):typeof z=="object"?ie.exports=e():e()(t.lunr)})(z,function(){return function(t){function e(){var r={"[\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D\u5341\u767E\u5343\u4E07\u5104\u5146]":"M","[\u4E00-\u9FA0\u3005\u3006\u30F5\u30F6]":"H","[\u3041-\u3093]":"I","[\u30A1-\u30F4\u30FC\uFF71-\uFF9D\uFF9E\uFF70]":"K","[a-zA-Z\uFF41-\uFF5A\uFF21-\uFF3A]":"A","[0-9\uFF10-\uFF19]":"N"};this.chartype_=[];for(var n in r){var i=new RegExp(n);this.chartype_.push([i,r[n]])}return this.BIAS__=-332,this.BC1__={HH:6,II:2461,KH:406,OH:-1378},this.BC2__={AA:-3267,AI:2744,AN:-878,HH:-4070,HM:-1711,HN:4012,HO:3761,IA:1327,IH:-1184,II:-1332,IK:1721,IO:5492,KI:3831,KK:-8741,MH:-3132,MK:3334,OO:-2920},this.BC3__={HH:996,HI:626,HK:-721,HN:-1307,HO:-836,IH:-301,KK:2762,MK:1079,MM:4034,OA:-1652,OH:266},this.BP1__={BB:295,OB:304,OO:-125,UB:352},this.BP2__={BO:60,OO:-1762},this.BQ1__={BHH:1150,BHM:1521,BII:-1158,BIM:886,BMH:1208,BNH:449,BOH:-91,BOO:-2597,OHI:451,OIH:-296,OKA:1851,OKH:-1020,OKK:904,OOO:2965},this.BQ2__={BHH:118,BHI:-1159,BHM:466,BIH:-919,BKK:-1720,BKO:864,OHH:-1139,OHM:-181,OIH:153,UHI:-1146},this.BQ3__={BHH:-792,BHI:2664,BII:-299,BKI:419,BMH:937,BMM:8335,BNN:998,BOH:775,OHH:2174,OHM:439,OII:280,OKH:1798,OKI:-793,OKO:-2242,OMH:-2402,OOO:11699},this.BQ4__={BHH:-3895,BIH:3761,BII:-4654,BIK:1348,BKK:-1806,BMI:-3385,BOO:-12396,OAH:926,OHH:266,OHK:-2036,ONN:-973},this.BW1__={",\u3068":660,",\u540C":727,B1\u3042:1404,B1\u540C:542,"\u3001\u3068":660,"\u3001\u540C":727,"\u300D\u3068":1682,\u3042\u3063:1505,\u3044\u3046:1743,\u3044\u3063:-2055,\u3044\u308B:672,\u3046\u3057:-4817,\u3046\u3093:665,\u304B\u3089:3472,\u304C\u3089:600,\u3053\u3046:-790,\u3053\u3068:2083,\u3053\u3093:-1262,\u3055\u3089:-4143,\u3055\u3093:4573,\u3057\u305F:2641,\u3057\u3066:1104,\u3059\u3067:-3399,\u305D\u3053:1977,\u305D\u308C:-871,\u305F\u3061:1122,\u305F\u3081:601,\u3063\u305F:3463,\u3064\u3044:-802,\u3066\u3044:805,\u3066\u304D:1249,\u3067\u304D:1127,\u3067\u3059:3445,\u3067\u306F:844,\u3068\u3044:-4915,\u3068\u307F:1922,\u3069\u3053:3887,\u306A\u3044:5713,\u306A\u3063:3015,\u306A\u3069:7379,\u306A\u3093:-1113,\u306B\u3057:2468,\u306B\u306F:1498,\u306B\u3082:1671,\u306B\u5BFE:-912,\u306E\u4E00:-501,\u306E\u4E2D:741,\u307E\u305B:2448,\u307E\u3067:1711,\u307E\u307E:2600,\u307E\u308B:-2155,\u3084\u3080:-1947,\u3088\u3063:-2565,\u308C\u305F:2369,\u308C\u3067:-913,\u3092\u3057:1860,\u3092\u898B:731,\u4EA1\u304F:-1886,\u4EAC\u90FD:2558,\u53D6\u308A:-2784,\u5927\u304D:-2604,\u5927\u962A:1497,\u5E73\u65B9:-2314,\u5F15\u304D:-1336,\u65E5\u672C:-195,\u672C\u5F53:-2423,\u6BCE\u65E5:-2113,\u76EE\u6307:-724,\uFF22\uFF11\u3042:1404,\uFF22\uFF11\u540C:542,"\uFF63\u3068":1682},this.BW2__={"..":-11822,11:-669,"\u2015\u2015":-5730,"\u2212\u2212":-13175,\u3044\u3046:-1609,\u3046\u304B:2490,\u304B\u3057:-1350,\u304B\u3082:-602,\u304B\u3089:-7194,\u304B\u308C:4612,\u304C\u3044:853,\u304C\u3089:-3198,\u304D\u305F:1941,\u304F\u306A:-1597,\u3053\u3068:-8392,\u3053\u306E:-4193,\u3055\u305B:4533,\u3055\u308C:13168,\u3055\u3093:-3977,\u3057\u3044:-1819,\u3057\u304B:-545,\u3057\u305F:5078,\u3057\u3066:972,\u3057\u306A:939,\u305D\u306E:-3744,\u305F\u3044:-1253,\u305F\u305F:-662,\u305F\u3060:-3857,\u305F\u3061:-786,\u305F\u3068:1224,\u305F\u306F:-939,\u3063\u305F:4589,\u3063\u3066:1647,\u3063\u3068:-2094,\u3066\u3044:6144,\u3066\u304D:3640,\u3066\u304F:2551,\u3066\u306F:-3110,\u3066\u3082:-3065,\u3067\u3044:2666,\u3067\u304D:-1528,\u3067\u3057:-3828,\u3067\u3059:-4761,\u3067\u3082:-4203,\u3068\u3044:1890,\u3068\u3053:-1746,\u3068\u3068:-2279,\u3068\u306E:720,\u3068\u307F:5168,\u3068\u3082:-3941,\u306A\u3044:-2488,\u306A\u304C:-1313,\u306A\u3069:-6509,\u306A\u306E:2614,\u306A\u3093:3099,\u306B\u304A:-1615,\u306B\u3057:2748,\u306B\u306A:2454,\u306B\u3088:-7236,\u306B\u5BFE:-14943,\u306B\u5F93:-4688,\u306B\u95A2:-11388,\u306E\u304B:2093,\u306E\u3067:-7059,\u306E\u306B:-6041,\u306E\u306E:-6125,\u306F\u3044:1073,\u306F\u304C:-1033,\u306F\u305A:-2532,\u3070\u308C:1813,\u307E\u3057:-1316,\u307E\u3067:-6621,\u307E\u308C:5409,\u3081\u3066:-3153,\u3082\u3044:2230,\u3082\u306E:-10713,\u3089\u304B:-944,\u3089\u3057:-1611,\u3089\u306B:-1897,\u308A\u3057:651,\u308A\u307E:1620,\u308C\u305F:4270,\u308C\u3066:849,\u308C\u3070:4114,\u308D\u3046:6067,\u308F\u308C:7901,\u3092\u901A:-11877,\u3093\u3060:728,\u3093\u306A:-4115,\u4E00\u4EBA:602,\u4E00\u65B9:-1375,\u4E00\u65E5:970,\u4E00\u90E8:-1051,\u4E0A\u304C:-4479,\u4F1A\u793E:-1116,\u51FA\u3066:2163,\u5206\u306E:-7758,\u540C\u515A:970,\u540C\u65E5:-913,\u5927\u962A:-2471,\u59D4\u54E1:-1250,\u5C11\u306A:-1050,\u5E74\u5EA6:-8669,\u5E74\u9593:-1626,\u5E9C\u770C:-2363,\u624B\u6A29:-1982,\u65B0\u805E:-4066,\u65E5\u65B0:-722,\u65E5\u672C:-7068,\u65E5\u7C73:3372,\u66DC\u65E5:-601,\u671D\u9BAE:-2355,\u672C\u4EBA:-2697,\u6771\u4EAC:-1543,\u7136\u3068:-1384,\u793E\u4F1A:-1276,\u7ACB\u3066:-990,\u7B2C\u306B:-1612,\u7C73\u56FD:-4268,"\uFF11\uFF11":-669},this.BW3__={\u3042\u305F:-2194,\u3042\u308A:719,\u3042\u308B:3846,"\u3044.":-1185,"\u3044\u3002":-1185,\u3044\u3044:5308,\u3044\u3048:2079,\u3044\u304F:3029,\u3044\u305F:2056,\u3044\u3063:1883,\u3044\u308B:5600,\u3044\u308F:1527,\u3046\u3061:1117,\u3046\u3068:4798,\u3048\u3068:1454,"\u304B.":2857,"\u304B\u3002":2857,\u304B\u3051:-743,\u304B\u3063:-4098,\u304B\u306B:-669,\u304B\u3089:6520,\u304B\u308A:-2670,"\u304C,":1816,"\u304C\u3001":1816,\u304C\u304D:-4855,\u304C\u3051:-1127,\u304C\u3063:-913,\u304C\u3089:-4977,\u304C\u308A:-2064,\u304D\u305F:1645,\u3051\u3069:1374,\u3053\u3068:7397,\u3053\u306E:1542,\u3053\u308D:-2757,\u3055\u3044:-714,\u3055\u3092:976,"\u3057,":1557,"\u3057\u3001":1557,\u3057\u3044:-3714,\u3057\u305F:3562,\u3057\u3066:1449,\u3057\u306A:2608,\u3057\u307E:1200,"\u3059.":-1310,"\u3059\u3002":-1310,\u3059\u308B:6521,"\u305A,":3426,"\u305A\u3001":3426,\u305A\u306B:841,\u305D\u3046:428,"\u305F.":8875,"\u305F\u3002":8875,\u305F\u3044:-594,\u305F\u306E:812,\u305F\u308A:-1183,\u305F\u308B:-853,"\u3060.":4098,"\u3060\u3002":4098,\u3060\u3063:1004,\u3063\u305F:-4748,\u3063\u3066:300,\u3066\u3044:6240,\u3066\u304A:855,\u3066\u3082:302,\u3067\u3059:1437,\u3067\u306B:-1482,\u3067\u306F:2295,\u3068\u3046:-1387,\u3068\u3057:2266,\u3068\u306E:541,\u3068\u3082:-3543,\u3069\u3046:4664,\u306A\u3044:1796,\u306A\u304F:-903,\u306A\u3069:2135,"\u306B,":-1021,"\u306B\u3001":-1021,\u306B\u3057:1771,\u306B\u306A:1906,\u306B\u306F:2644,"\u306E,":-724,"\u306E\u3001":-724,\u306E\u5B50:-1e3,"\u306F,":1337,"\u306F\u3001":1337,\u3079\u304D:2181,\u307E\u3057:1113,\u307E\u3059:6943,\u307E\u3063:-1549,\u307E\u3067:6154,\u307E\u308C:-793,\u3089\u3057:1479,\u3089\u308C:6820,\u308B\u308B:3818,"\u308C,":854,"\u308C\u3001":854,\u308C\u305F:1850,\u308C\u3066:1375,\u308C\u3070:-3246,\u308C\u308B:1091,\u308F\u308C:-605,\u3093\u3060:606,\u3093\u3067:798,\u30AB\u6708:990,\u4F1A\u8B70:860,\u5165\u308A:1232,\u5927\u4F1A:2217,\u59CB\u3081:1681,\u5E02:965,\u65B0\u805E:-5055,"\u65E5,":974,"\u65E5\u3001":974,\u793E\u4F1A:2024,\uFF76\u6708:990},this.TC1__={AAA:1093,HHH:1029,HHM:580,HII:998,HOH:-390,HOM:-331,IHI:1169,IOH:-142,IOI:-1015,IOM:467,MMH:187,OOI:-1832},this.TC2__={HHO:2088,HII:-1023,HMM:-1154,IHI:-1965,KKH:703,OII:-2649},this.TC3__={AAA:-294,HHH:346,HHI:-341,HII:-1088,HIK:731,HOH:-1486,IHH:128,IHI:-3041,IHO:-1935,IIH:-825,IIM:-1035,IOI:-542,KHH:-1216,KKA:491,KKH:-1217,KOK:-1009,MHH:-2694,MHM:-457,MHO:123,MMH:-471,NNH:-1689,NNO:662,OHO:-3393},this.TC4__={HHH:-203,HHI:1344,HHK:365,HHM:-122,HHN:182,HHO:669,HIH:804,HII:679,HOH:446,IHH:695,IHO:-2324,IIH:321,III:1497,IIO:656,IOO:54,KAK:4845,KKA:3386,KKK:3065,MHH:-405,MHI:201,MMH:-241,MMM:661,MOM:841},this.TQ1__={BHHH:-227,BHHI:316,BHIH:-132,BIHH:60,BIII:1595,BNHH:-744,BOHH:225,BOOO:-908,OAKK:482,OHHH:281,OHIH:249,OIHI:200,OIIH:-68},this.TQ2__={BIHH:-1401,BIII:-1033,BKAK:-543,BOOO:-5591},this.TQ3__={BHHH:478,BHHM:-1073,BHIH:222,BHII:-504,BIIH:-116,BIII:-105,BMHI:-863,BMHM:-464,BOMH:620,OHHH:346,OHHI:1729,OHII:997,OHMH:481,OIHH:623,OIIH:1344,OKAK:2792,OKHH:587,OKKA:679,OOHH:110,OOII:-685},this.TQ4__={BHHH:-721,BHHM:-3604,BHII:-966,BIIH:-607,BIII:-2181,OAAA:-2763,OAKK:180,OHHH:-294,OHHI:2446,OHHO:480,OHIH:-1573,OIHH:1935,OIHI:-493,OIIH:626,OIII:-4007,OKAK:-8156},this.TW1__={\u306B\u3064\u3044:-4681,\u6771\u4EAC\u90FD:2026},this.TW2__={\u3042\u308B\u7A0B:-2049,\u3044\u3063\u305F:-1256,\u3053\u308D\u304C:-2434,\u3057\u3087\u3046:3873,\u305D\u306E\u5F8C:-4430,\u3060\u3063\u3066:-1049,\u3066\u3044\u305F:1833,\u3068\u3057\u3066:-4657,\u3068\u3082\u306B:-4517,\u3082\u306E\u3067:1882,\u4E00\u6C17\u306B:-792,\u521D\u3081\u3066:-1512,\u540C\u6642\u306B:-8097,\u5927\u304D\u306A:-1255,\u5BFE\u3057\u3066:-2721,\u793E\u4F1A\u515A:-3216},this.TW3__={\u3044\u305F\u3060:-1734,\u3057\u3066\u3044:1314,\u3068\u3057\u3066:-4314,\u306B\u3064\u3044:-5483,\u306B\u3068\u3063:-5989,\u306B\u5F53\u305F:-6247,"\u306E\u3067,":-727,"\u306E\u3067\u3001":-727,\u306E\u3082\u306E:-600,\u308C\u304B\u3089:-3752,\u5341\u4E8C\u6708:-2287},this.TW4__={"\u3044\u3046.":8576,"\u3044\u3046\u3002":8576,\u304B\u3089\u306A:-2348,\u3057\u3066\u3044:2958,"\u305F\u304C,":1516,"\u305F\u304C\u3001":1516,\u3066\u3044\u308B:1538,\u3068\u3044\u3046:1349,\u307E\u3057\u305F:5543,\u307E\u305B\u3093:1097,\u3088\u3046\u3068:-4258,\u3088\u308B\u3068:5865},this.UC1__={A:484,K:93,M:645,O:-505},this.UC2__={A:819,H:1059,I:409,M:3987,N:5775,O:646},this.UC3__={A:-1370,I:2311},this.UC4__={A:-2643,H:1809,I:-1032,K:-3450,M:3565,N:3876,O:6646},this.UC5__={H:313,I:-1238,K:-799,M:539,O:-831},this.UC6__={H:-506,I:-253,K:87,M:247,O:-387},this.UP1__={O:-214},this.UP2__={B:69,O:935},this.UP3__={B:189},this.UQ1__={BH:21,BI:-12,BK:-99,BN:142,BO:-56,OH:-95,OI:477,OK:410,OO:-2422},this.UQ2__={BH:216,BI:113,OK:1759},this.UQ3__={BA:-479,BH:42,BI:1913,BK:-7198,BM:3160,BN:6427,BO:14761,OI:-827,ON:-3212},this.UW1__={",":156,"\u3001":156,"\u300C":-463,\u3042:-941,\u3046:-127,\u304C:-553,\u304D:121,\u3053:505,\u3067:-201,\u3068:-547,\u3069:-123,\u306B:-789,\u306E:-185,\u306F:-847,\u3082:-466,\u3084:-470,\u3088:182,\u3089:-292,\u308A:208,\u308C:169,\u3092:-446,\u3093:-137,"\u30FB":-135,\u4E3B:-402,\u4EAC:-268,\u533A:-912,\u5348:871,\u56FD:-460,\u5927:561,\u59D4:729,\u5E02:-411,\u65E5:-141,\u7406:361,\u751F:-408,\u770C:-386,\u90FD:-718,"\uFF62":-463,"\uFF65":-135},this.UW2__={",":-829,"\u3001":-829,"\u3007":892,"\u300C":-645,"\u300D":3145,\u3042:-538,\u3044:505,\u3046:134,\u304A:-502,\u304B:1454,\u304C:-856,\u304F:-412,\u3053:1141,\u3055:878,\u3056:540,\u3057:1529,\u3059:-675,\u305B:300,\u305D:-1011,\u305F:188,\u3060:1837,\u3064:-949,\u3066:-291,\u3067:-268,\u3068:-981,\u3069:1273,\u306A:1063,\u306B:-1764,\u306E:130,\u306F:-409,\u3072:-1273,\u3079:1261,\u307E:600,\u3082:-1263,\u3084:-402,\u3088:1639,\u308A:-579,\u308B:-694,\u308C:571,\u3092:-2516,\u3093:2095,\u30A2:-587,\u30AB:306,\u30AD:568,\u30C3:831,\u4E09:-758,\u4E0D:-2150,\u4E16:-302,\u4E2D:-968,\u4E3B:-861,\u4E8B:492,\u4EBA:-123,\u4F1A:978,\u4FDD:362,\u5165:548,\u521D:-3025,\u526F:-1566,\u5317:-3414,\u533A:-422,\u5927:-1769,\u5929:-865,\u592A:-483,\u5B50:-1519,\u5B66:760,\u5B9F:1023,\u5C0F:-2009,\u5E02:-813,\u5E74:-1060,\u5F37:1067,\u624B:-1519,\u63FA:-1033,\u653F:1522,\u6587:-1355,\u65B0:-1682,\u65E5:-1815,\u660E:-1462,\u6700:-630,\u671D:-1843,\u672C:-1650,\u6771:-931,\u679C:-665,\u6B21:-2378,\u6C11:-180,\u6C17:-1740,\u7406:752,\u767A:529,\u76EE:-1584,\u76F8:-242,\u770C:-1165,\u7ACB:-763,\u7B2C:810,\u7C73:509,\u81EA:-1353,\u884C:838,\u897F:-744,\u898B:-3874,\u8ABF:1010,\u8B70:1198,\u8FBC:3041,\u958B:1758,\u9593:-1257,"\uFF62":-645,"\uFF63":3145,\uFF6F:831,\uFF71:-587,\uFF76:306,\uFF77:568},this.UW3__={",":4889,1:-800,"\u2212":-1723,"\u3001":4889,\u3005:-2311,"\u3007":5827,"\u300D":2670,"\u3013":-3573,\u3042:-2696,\u3044:1006,\u3046:2342,\u3048:1983,\u304A:-4864,\u304B:-1163,\u304C:3271,\u304F:1004,\u3051:388,\u3052:401,\u3053:-3552,\u3054:-3116,\u3055:-1058,\u3057:-395,\u3059:584,\u305B:3685,\u305D:-5228,\u305F:842,\u3061:-521,\u3063:-1444,\u3064:-1081,\u3066:6167,\u3067:2318,\u3068:1691,\u3069:-899,\u306A:-2788,\u306B:2745,\u306E:4056,\u306F:4555,\u3072:-2171,\u3075:-1798,\u3078:1199,\u307B:-5516,\u307E:-4384,\u307F:-120,\u3081:1205,\u3082:2323,\u3084:-788,\u3088:-202,\u3089:727,\u308A:649,\u308B:5905,\u308C:2773,\u308F:-1207,\u3092:6620,\u3093:-518,\u30A2:551,\u30B0:1319,\u30B9:874,\u30C3:-1350,\u30C8:521,\u30E0:1109,\u30EB:1591,\u30ED:2201,\u30F3:278,"\u30FB":-3794,\u4E00:-1619,\u4E0B:-1759,\u4E16:-2087,\u4E21:3815,\u4E2D:653,\u4E3B:-758,\u4E88:-1193,\u4E8C:974,\u4EBA:2742,\u4ECA:792,\u4ED6:1889,\u4EE5:-1368,\u4F4E:811,\u4F55:4265,\u4F5C:-361,\u4FDD:-2439,\u5143:4858,\u515A:3593,\u5168:1574,\u516C:-3030,\u516D:755,\u5171:-1880,\u5186:5807,\u518D:3095,\u5206:457,\u521D:2475,\u5225:1129,\u524D:2286,\u526F:4437,\u529B:365,\u52D5:-949,\u52D9:-1872,\u5316:1327,\u5317:-1038,\u533A:4646,\u5343:-2309,\u5348:-783,\u5354:-1006,\u53E3:483,\u53F3:1233,\u5404:3588,\u5408:-241,\u540C:3906,\u548C:-837,\u54E1:4513,\u56FD:642,\u578B:1389,\u5834:1219,\u5916:-241,\u59BB:2016,\u5B66:-1356,\u5B89:-423,\u5B9F:-1008,\u5BB6:1078,\u5C0F:-513,\u5C11:-3102,\u5DDE:1155,\u5E02:3197,\u5E73:-1804,\u5E74:2416,\u5E83:-1030,\u5E9C:1605,\u5EA6:1452,\u5EFA:-2352,\u5F53:-3885,\u5F97:1905,\u601D:-1291,\u6027:1822,\u6238:-488,\u6307:-3973,\u653F:-2013,\u6559:-1479,\u6570:3222,\u6587:-1489,\u65B0:1764,\u65E5:2099,\u65E7:5792,\u6628:-661,\u6642:-1248,\u66DC:-951,\u6700:-937,\u6708:4125,\u671F:360,\u674E:3094,\u6751:364,\u6771:-805,\u6838:5156,\u68EE:2438,\u696D:484,\u6C0F:2613,\u6C11:-1694,\u6C7A:-1073,\u6CD5:1868,\u6D77:-495,\u7121:979,\u7269:461,\u7279:-3850,\u751F:-273,\u7528:914,\u753A:1215,\u7684:7313,\u76F4:-1835,\u7701:792,\u770C:6293,\u77E5:-1528,\u79C1:4231,\u7A0E:401,\u7ACB:-960,\u7B2C:1201,\u7C73:7767,\u7CFB:3066,\u7D04:3663,\u7D1A:1384,\u7D71:-4229,\u7DCF:1163,\u7DDA:1255,\u8005:6457,\u80FD:725,\u81EA:-2869,\u82F1:785,\u898B:1044,\u8ABF:-562,\u8CA1:-733,\u8CBB:1777,\u8ECA:1835,\u8ECD:1375,\u8FBC:-1504,\u901A:-1136,\u9078:-681,\u90CE:1026,\u90E1:4404,\u90E8:1200,\u91D1:2163,\u9577:421,\u958B:-1432,\u9593:1302,\u95A2:-1282,\u96E8:2009,\u96FB:-1045,\u975E:2066,\u99C5:1620,"\uFF11":-800,"\uFF63":2670,"\uFF65":-3794,\uFF6F:-1350,\uFF71:551,\uFF78\uFF9E:1319,\uFF7D:874,\uFF84:521,\uFF91:1109,\uFF99:1591,\uFF9B:2201,\uFF9D:278},this.UW4__={",":3930,".":3508,"\u2015":-4841,"\u3001":3930,"\u3002":3508,"\u3007":4999,"\u300C":1895,"\u300D":3798,"\u3013":-5156,\u3042:4752,\u3044:-3435,\u3046:-640,\u3048:-2514,\u304A:2405,\u304B:530,\u304C:6006,\u304D:-4482,\u304E:-3821,\u304F:-3788,\u3051:-4376,\u3052:-4734,\u3053:2255,\u3054:1979,\u3055:2864,\u3057:-843,\u3058:-2506,\u3059:-731,\u305A:1251,\u305B:181,\u305D:4091,\u305F:5034,\u3060:5408,\u3061:-3654,\u3063:-5882,\u3064:-1659,\u3066:3994,\u3067:7410,\u3068:4547,\u306A:5433,\u306B:6499,\u306C:1853,\u306D:1413,\u306E:7396,\u306F:8578,\u3070:1940,\u3072:4249,\u3073:-4134,\u3075:1345,\u3078:6665,\u3079:-744,\u307B:1464,\u307E:1051,\u307F:-2082,\u3080:-882,\u3081:-5046,\u3082:4169,\u3083:-2666,\u3084:2795,\u3087:-1544,\u3088:3351,\u3089:-2922,\u308A:-9726,\u308B:-14896,\u308C:-2613,\u308D:-4570,\u308F:-1783,\u3092:13150,\u3093:-2352,\u30AB:2145,\u30B3:1789,\u30BB:1287,\u30C3:-724,\u30C8:-403,\u30E1:-1635,\u30E9:-881,\u30EA:-541,\u30EB:-856,\u30F3:-3637,"\u30FB":-4371,\u30FC:-11870,\u4E00:-2069,\u4E2D:2210,\u4E88:782,\u4E8B:-190,\u4E95:-1768,\u4EBA:1036,\u4EE5:544,\u4F1A:950,\u4F53:-1286,\u4F5C:530,\u5074:4292,\u5148:601,\u515A:-2006,\u5171:-1212,\u5185:584,\u5186:788,\u521D:1347,\u524D:1623,\u526F:3879,\u529B:-302,\u52D5:-740,\u52D9:-2715,\u5316:776,\u533A:4517,\u5354:1013,\u53C2:1555,\u5408:-1834,\u548C:-681,\u54E1:-910,\u5668:-851,\u56DE:1500,\u56FD:-619,\u5712:-1200,\u5730:866,\u5834:-1410,\u5841:-2094,\u58EB:-1413,\u591A:1067,\u5927:571,\u5B50:-4802,\u5B66:-1397,\u5B9A:-1057,\u5BFA:-809,\u5C0F:1910,\u5C4B:-1328,\u5C71:-1500,\u5CF6:-2056,\u5DDD:-2667,\u5E02:2771,\u5E74:374,\u5E81:-4556,\u5F8C:456,\u6027:553,\u611F:916,\u6240:-1566,\u652F:856,\u6539:787,\u653F:2182,\u6559:704,\u6587:522,\u65B9:-856,\u65E5:1798,\u6642:1829,\u6700:845,\u6708:-9066,\u6728:-485,\u6765:-442,\u6821:-360,\u696D:-1043,\u6C0F:5388,\u6C11:-2716,\u6C17:-910,\u6CA2:-939,\u6E08:-543,\u7269:-735,\u7387:672,\u7403:-1267,\u751F:-1286,\u7523:-1101,\u7530:-2900,\u753A:1826,\u7684:2586,\u76EE:922,\u7701:-3485,\u770C:2997,\u7A7A:-867,\u7ACB:-2112,\u7B2C:788,\u7C73:2937,\u7CFB:786,\u7D04:2171,\u7D4C:1146,\u7D71:-1169,\u7DCF:940,\u7DDA:-994,\u7F72:749,\u8005:2145,\u80FD:-730,\u822C:-852,\u884C:-792,\u898F:792,\u8B66:-1184,\u8B70:-244,\u8C37:-1e3,\u8CDE:730,\u8ECA:-1481,\u8ECD:1158,\u8F2A:-1433,\u8FBC:-3370,\u8FD1:929,\u9053:-1291,\u9078:2596,\u90CE:-4866,\u90FD:1192,\u91CE:-1100,\u9280:-2213,\u9577:357,\u9593:-2344,\u9662:-2297,\u969B:-2604,\u96FB:-878,\u9818:-1659,\u984C:-792,\u9928:-1984,\u9996:1749,\u9AD8:2120,"\uFF62":1895,"\uFF63":3798,"\uFF65":-4371,\uFF6F:-724,\uFF70:-11870,\uFF76:2145,\uFF7A:1789,\uFF7E:1287,\uFF84:-403,\uFF92:-1635,\uFF97:-881,\uFF98:-541,\uFF99:-856,\uFF9D:-3637},this.UW5__={",":465,".":-299,1:-514,E2:-32768,"]":-2762,"\u3001":465,"\u3002":-299,"\u300C":363,\u3042:1655,\u3044:331,\u3046:-503,\u3048:1199,\u304A:527,\u304B:647,\u304C:-421,\u304D:1624,\u304E:1971,\u304F:312,\u3052:-983,\u3055:-1537,\u3057:-1371,\u3059:-852,\u3060:-1186,\u3061:1093,\u3063:52,\u3064:921,\u3066:-18,\u3067:-850,\u3068:-127,\u3069:1682,\u306A:-787,\u306B:-1224,\u306E:-635,\u306F:-578,\u3079:1001,\u307F:502,\u3081:865,\u3083:3350,\u3087:854,\u308A:-208,\u308B:429,\u308C:504,\u308F:419,\u3092:-1264,\u3093:327,\u30A4:241,\u30EB:451,\u30F3:-343,\u4E2D:-871,\u4EAC:722,\u4F1A:-1153,\u515A:-654,\u52D9:3519,\u533A:-901,\u544A:848,\u54E1:2104,\u5927:-1296,\u5B66:-548,\u5B9A:1785,\u5D50:-1304,\u5E02:-2991,\u5E2D:921,\u5E74:1763,\u601D:872,\u6240:-814,\u6319:1618,\u65B0:-1682,\u65E5:218,\u6708:-4353,\u67FB:932,\u683C:1356,\u6A5F:-1508,\u6C0F:-1347,\u7530:240,\u753A:-3912,\u7684:-3149,\u76F8:1319,\u7701:-1052,\u770C:-4003,\u7814:-997,\u793E:-278,\u7A7A:-813,\u7D71:1955,\u8005:-2233,\u8868:663,\u8A9E:-1073,\u8B70:1219,\u9078:-1018,\u90CE:-368,\u9577:786,\u9593:1191,\u984C:2368,\u9928:-689,"\uFF11":-514,\uFF25\uFF12:-32768,"\uFF62":363,\uFF72:241,\uFF99:451,\uFF9D:-343},this.UW6__={",":227,".":808,1:-270,E1:306,"\u3001":227,"\u3002":808,\u3042:-307,\u3046:189,\u304B:241,\u304C:-73,\u304F:-121,\u3053:-200,\u3058:1782,\u3059:383,\u305F:-428,\u3063:573,\u3066:-1014,\u3067:101,\u3068:-105,\u306A:-253,\u306B:-149,\u306E:-417,\u306F:-236,\u3082:-206,\u308A:187,\u308B:-135,\u3092:195,\u30EB:-673,\u30F3:-496,\u4E00:-277,\u4E2D:201,\u4EF6:-800,\u4F1A:624,\u524D:302,\u533A:1792,\u54E1:-1212,\u59D4:798,\u5B66:-960,\u5E02:887,\u5E83:-695,\u5F8C:535,\u696D:-697,\u76F8:753,\u793E:-507,\u798F:974,\u7A7A:-822,\u8005:1811,\u9023:463,\u90CE:1082,"\uFF11":-270,\uFF25\uFF11:306,\uFF99:-673,\uFF9D:-496},this}e.prototype.ctype_=function(r){for(var n in this.chartype_)if(r.match(this.chartype_[n][0]))return this.chartype_[n][1];return"O"},e.prototype.ts_=function(r){return r||0},e.prototype.segment=function(r){if(r==null||r==null||r=="")return[];var n=[],i=["B3","B2","B1"],s=["O","O","O"],o=r.split("");for(d=0;d<o.length;++d)i.push(o[d]),s.push(this.ctype_(o[d]));i.push("E1"),i.push("E2"),i.push("E3"),s.push("O"),s.push("O"),s.push("O");for(var u=i[3],l="U",a="U",f="U",d=4;d<i.length-3;++d){var h=this.BIAS__,g=i[d-3],p=i[d-2],m=i[d-1],_=i[d],v=i[d+1],S=i[d+2],x=s[d-3],H=s[d-2],I=s[d-1],B=s[d],P=s[d+1],T=s[d+2];h+=this.ts_(this.UP1__[l]),h+=this.ts_(this.UP2__[a]),h+=this.ts_(this.UP3__[f]),h+=this.ts_(this.BP1__[l+a]),h+=this.ts_(this.BP2__[a+f]),h+=this.ts_(this.UW1__[g]),h+=this.ts_(this.UW2__[p]),h+=this.ts_(this.UW3__[m]),h+=this.ts_(this.UW4__[_]),h+=this.ts_(this.UW5__[v]),h+=this.ts_(this.UW6__[S]),h+=this.ts_(this.BW1__[p+m]),h+=this.ts_(this.BW2__[m+_]),h+=this.ts_(this.BW3__[_+v]),h+=this.ts_(this.TW1__[g+p+m]),h+=this.ts_(this.TW2__[p+m+_]),h+=this.ts_(this.TW3__[m+_+v]),h+=this.ts_(this.TW4__[_+v+S]),h+=this.ts_(this.UC1__[x]),h+=this.ts_(this.UC2__[H]),h+=this.ts_(this.UC3__[I]),h+=this.ts_(this.UC4__[B]),h+=this.ts_(this.UC5__[P]),h+=this.ts_(this.UC6__[T]),h+=this.ts_(this.BC1__[H+I]),h+=this.ts_(this.BC2__[I+B]),h+=this.ts_(this.BC3__[B+P]),h+=this.ts_(this.TC1__[x+H+I]),h+=this.ts_(this.TC2__[H+I+B]),h+=this.ts_(this.TC3__[I+B+P]),h+=this.ts_(this.TC4__[B+P+T]),h+=this.ts_(this.UQ1__[l+x]),h+=this.ts_(this.UQ2__[a+H]),h+=this.ts_(this.UQ3__[f+I]),h+=this.ts_(this.BQ1__[a+H+I]),h+=this.ts_(this.BQ2__[a+I+B]),h+=this.ts_(this.BQ3__[f+H+I]),h+=this.ts_(this.BQ4__[f+I+B]),h+=this.ts_(this.TQ1__[a+x+H+I]),h+=this.ts_(this.TQ2__[a+H+I+B]),h+=this.ts_(this.TQ3__[f+x+H+I]),h+=this.ts_(this.TQ4__[f+H+I+B]);var L="O";h>0&&(n.push(u),u="",L="B"),l=a,a=f,f=L,u+=i[d]}return n.push(u),n},t.TinySegmenter=e}})});var oe=W(($,se)=>{(function(t,e){typeof define=="function"&&define.amd?define(e):typeof $=="object"?se.exports=e():e()(t.lunr)})($,function(){return function(t){t.multiLanguage=function(){for(var e=Array.prototype.slice.call(arguments),r=e.join("-"),n="",i=[],s=[],o=0;o<e.length;++o)e[o]=="en"?(n+="\\w",i.unshift(t.stopWordFilter),i.push(t.stemmer),s.push(t.stemmer)):(n+=t[e[o]].wordCharacters,t[e[o]].stopWordFilter&&i.unshift(t[e[o]].stopWordFilter),t[e[o]].stemmer&&(i.push(t[e[o]].stemmer),s.push(t[e[o]].stemmer)));var u=t.trimmerSupport.generateTrimmer(n);return t.Pipeline.registerFunction(u,"lunr-multi-trimmer-"+r),i.unshift(u),function(){this.pipeline.reset(),this.pipeline.add.apply(this.pipeline,i),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add.apply(this.searchPipeline,s))}}}})});var w=D(ee()),le=D(re()),fe=D(ne()),ce=D(oe());function J(t){return new Promise((e,r)=>{t.oncomplete=t.onsuccess=()=>e(t.result),t.onabort=t.onerror=()=>r(t.error)})}function X(t,e){let r=indexedDB.open(t);r.onupgradeneeded=()=>r.result.createObjectStore(e);let n=J(r);return(i,s)=>n.then(o=>s(o.transaction(e,i).objectStore(e)))}var q;function ue(){return q||(q=X("keyval-store","keyval")),q}function ae(t,e=ue()){return e("readonly",r=>J(r.get(t)))}function he(t,e,r=ue()){return r("readwrite",n=>(n.put(e,t),J(n.transaction)))}var G;async function ve({lunrLanguages:t}){let{index:e,data:r}=await n();G=i=>e.search(i).map(({ref:s})=>r[s]),postMessage({e:"index-ready"});async function n(){let i=await fetch("../index.json"),s=i.headers.get("etag"),o=await i.json(),u=X("docfx","lunr");if(t&&t.length>0&&((0,ce.default)(w.default),(0,le.default)(w.default),t.includes("ja")&&(0,fe.default)(w.default),await Promise.all(t.map(me))),s){let a=JSON.parse(await ae("index",u)||"{}");if(a&&a.etag===s)return{index:w.default.Index.load(a),data:o}}let l=(0,w.default)(function(){w.default.tokenizer.separator=/[\s\-.()]+/,this.ref("href"),this.field("title",{boost:50}),this.field("keywords",{boost:40}),this.field("summary",{boost:20}),t&&t.length>0&&this.use(w.default.multiLanguage(...t));for(let a in o)this.add(o[a])});return s&&await he("index",JSON.stringify(Object.assign(l.toJSON(),{etag:s})),u),{index:l,data:o}}}onmessage=function(t){t.data.q&&G?postMessage({e:"query-ready",d:G(t.data.q)}):t.data.init&&ve(t.data.init).catch(console.error)};var ge={ar:()=>import("./lunr.ar-A6ZT2INA.min.js"),da:()=>import("./lunr.da-WWM276CR.min.js"),de:()=>import("./lunr.de-XXPRKDAY.min.js"),du:()=>import("./lunr.du-NO4L2LL3.min.js"),el:()=>import("./lunr.el-5ZSSJVMA.min.js"),es:()=>import("./lunr.es-ZH6Q76E6.min.js"),fi:()=>import("./lunr.fi-S7WJSBCP.min.js"),fr:()=>import("./lunr.fr-H2QNBELV.min.js"),he:()=>import("./lunr.he-TTLAK4MN.min.js"),hi:()=>import("./lunr.hi-PWWMAGLU.min.js"),hu:()=>import("./lunr.hu-DLG2DSVM.min.js"),hy:()=>import("./lunr.hy-FFQJAR7M.min.js"),it:()=>import("./lunr.it-VQNLJLPR.min.js"),ja:()=>import("./lunr.ja-J6QHZSR2.min.js"),jp:()=>import("./lunr.jp-M45D3XJE.min.js"),kn:()=>import("./lunr.kn-ASLXFRTC.min.js"),ko:()=>import("./lunr.ko-RHF2BDE4.min.js"),nl:()=>import("./lunr.nl-2BITG354.min.js"),no:()=>import("./lunr.no-WPLSHWFO.min.js"),pt:()=>import("./lunr.pt-V2XEBELC.min.js"),ro:()=>import("./lunr.ro-O76266FJ.min.js"),ru:()=>import("./lunr.ru-G56UDXYH.min.js"),sa:()=>import("./lunr.sa-LD5PRAIS.min.js"),sv:()=>import("./lunr.sv-7VRY4UDB.min.js"),ta:()=>import("./lunr.ta-OWB7AURB.min.js"),te:()=>import("./lunr.te-JGGL3BFP.min.js"),th:()=>import("./lunr.th-O4JBL3IY.min.js"),tr:()=>import("./lunr.tr-WXUV733C.min.js"),vi:()=>import("./lunr.vi-3U4A337N.min.js")};async function me(t){if(t!=="en"){let{default:e}=await ge[t]();e(w.default)}}
/*! Bundled license information:

lunr/lunr.js:
  (**
   * lunr - http://lunrjs.com - A bit like Solr, but much smaller and not as bright - 2.3.9
   * Copyright (C) 2020 Oliver Nightingale
   * @license MIT
   *)
  (*!
   * lunr.utils
   * Copyright (C) 2020 Oliver Nightingale
   *)
  (*!
   * lunr.Set
   * Copyright (C) 2020 Oliver Nightingale
   *)
  (*!
   * lunr.tokenizer
   * Copyright (C) 2020 Oliver Nightingale
   *)
  (*!
   * lunr.Pipeline
   * Copyright (C) 2020 Oliver Nightingale
   *)
  (*!
   * lunr.Vector
   * Copyright (C) 2020 Oliver Nightingale
   *)
  (*!
   * lunr.stemmer
   * Copyright (C) 2020 Oliver Nightingale
   * Includes code from - http://tartarus.org/~martin/PorterStemmer/js.txt
   *)
  (*!
   * lunr.stopWordFilter
   * Copyright (C) 2020 Oliver Nightingale
   *)
  (*!
   * lunr.trimmer
   * Copyright (C) 2020 Oliver Nightingale
   *)
  (*!
   * lunr.TokenSet
   * Copyright (C) 2020 Oliver Nightingale
   *)
  (*!
   * lunr.Index
   * Copyright (C) 2020 Oliver Nightingale
   *)
  (*!
   * lunr.Builder
   * Copyright (C) 2020 Oliver Nightingale
   *)

lunr-languages/lunr.stemmer.support.js:
  (*!
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=search-worker.min.js.map
