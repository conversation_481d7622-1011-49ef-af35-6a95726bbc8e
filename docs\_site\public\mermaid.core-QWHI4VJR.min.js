import{a as yr}from"./chunk-EDJWACL4.min.js";import{a as Xr}from"./chunk-5IIW54K6.min.js";import{d as hr}from"./chunk-IQQ46AC6.min.js";import{a as Nr,b as Vr}from"./chunk-N4YULA37.min.js";import{a as jr}from"./chunk-JL3VILNY.min.js";import"./chunk-TLYS76Q7.min.js";import"./chunk-CLIYZZ5Y.min.js";import"./chunk-N6ME3NZU.min.js";import"./chunk-V55NTXQN.min.js";import{b as Ur}from"./chunk-BD4P4Z7J.min.js";import{a as Yr}from"./chunk-AUO2PXKS.min.js";import{b as Gr,j as vr,l as Hr,m as N,n as qr,o as Br}from"./chunk-PYPO7LRM.min.js";import"./chunk-CM5D5KZN.min.js";import{A as Rr,B as gr,C as Fr,D as P,E as Or,F as q,I as zr,O as $r,Q as Pr,ca as B,da as K,g as _r,h as a,ia as I,j as f,k as lr,l as Lr,n as Ar,o as ur,p as mr,q as fr,r as Mr,s as Cr,u as W,x as pr,y as Ir,z as kr}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import"./chunk-OSRY5VT3.min.js";var Q="comm",Z="rule",J="decl";var Wr="@import";var Kr="@keyframes";var Qr="@layer";var wr=Math.abs,Y=String.fromCharCode;function rr(r){return r.trim()}function j(r,e,t){return r.replace(e,t)}function Zr(r,e,t){return r.indexOf(e,t)}function z(r,e){return r.charCodeAt(e)|0}function F(r,e,t){return r.slice(e,t)}function w(r){return r.length}function Jr(r){return r.length}function V(r,e){return e.push(r),r}var er=1,U=1,re=0,b=0,v=0,H="";function tr(r,e,t,i,o,n,d,s){return{value:r,root:e,parent:t,type:i,props:o,children:n,line:er,column:U,length:d,return:"",siblings:s}}function ee(){return v}function te(){return v=b>0?z(H,--b):0,U--,v===10&&(U=1,er--),v}function E(){return v=b<re?z(H,b++):0,U++,v===10&&(U=1,er++),v}function k(){return z(H,b)}function X(){return b}function ar(r,e){return F(H,r,e)}function G(r){switch(r){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function ae(r){return er=U=1,re=w(H=r),b=0,[]}function ie(r){return H="",r}function ir(r){return rr(ar(b-1,xr(r===91?r+2:r===40?r+1:r)))}function oe(r){for(;(v=k())&&v<33;)E();return G(r)>2||G(v)>3?"":" "}function ne(r,e){for(;--e&&E()&&!(v<48||v>102||v>57&&v<65||v>70&&v<97););return ar(r,X()+(e<6&&k()==32&&E()==32))}function xr(r){for(;E();)switch(v){case r:return b;case 34:case 39:r!==34&&r!==39&&xr(v);break;case 40:r===41&&xr(r);break;case 92:E();break}return b}function se(r,e){for(;E()&&r+v!==57;)if(r+v===84&&k()===47)break;return"/*"+ar(e,b-1)+"*"+Y(r===47?r:E())}function ce(r){for(;!G(k());)E();return ar(r,b)}function ue(r){return ie(or("",null,null,null,[""],r=ae(r),0,[0],r))}function or(r,e,t,i,o,n,d,s,c){for(var p=0,h=0,l=d,y=0,L=0,D=0,m=1,M=1,S=1,g=0,T="",C=o,A=n,x=i,u=T;M;)switch(D=g,g=E()){case 40:if(D!=108&&z(u,l-1)==58){Zr(u+=j(ir(g),"&","&\f"),"&\f",wr(p?s[p-1]:0))!=-1&&(S=-1);break}case 34:case 39:case 91:u+=ir(g);break;case 9:case 10:case 13:case 32:u+=oe(D);break;case 92:u+=ne(X()-1,7);continue;case 47:switch(k()){case 42:case 47:V(nt(se(E(),X()),e,t,c),c),(G(D||1)==5||G(k()||1)==5)&&w(u)&&F(u,-1,void 0)!==" "&&(u+=" ");break;default:u+="/"}break;case 123*m:s[p++]=w(u)*S;case 125*m:case 59:case 0:switch(g){case 0:case 125:M=0;case 59+h:S==-1&&(u=j(u,/\f/g,"")),L>0&&(w(u)-l||m===0&&D===47)&&V(L>32?le(u+";",i,t,l-1,c):le(j(u," ","")+";",i,t,l-2,c),c);break;case 59:u+=";";default:if(V(x=de(u,e,t,p,h,o,s,T,C=[],A=[],l,n),n),g===123)if(h===0)or(u,e,x,x,C,n,l,s,A);else switch(y===99&&z(u,3)===110?100:y){case 100:case 108:case 109:case 115:or(r,x,x,i&&V(de(r,x,x,0,0,o,s,T,o,C=[],l,A),A),o,A,l,s,i?C:A);break;default:or(u,x,x,x,[""],A,0,s,A)}}p=h=L=0,m=S=1,T=u="",l=d;break;case 58:l=1+w(u),L=D;default:if(m<1){if(g==123)--m;else if(g==125&&m++==0&&te()==125)continue}switch(u+=Y(g),g*m){case 38:S=h>0?1:(u+="\f",-1);break;case 44:s[p++]=(w(u)-1)*S,S=1;break;case 64:k()===45&&(u+=ir(E())),y=k(),h=l=w(T=u+=ce(X())),g++;break;case 45:D===45&&w(u)==2&&(m=0)}}return n}function de(r,e,t,i,o,n,d,s,c,p,h,l){for(var y=o-1,L=o===0?n:[""],D=Jr(L),m=0,M=0,S=0;m<i;++m)for(var g=0,T=F(r,y+1,y=wr(M=d[m])),C=r;g<D;++g)(C=rr(M>0?L[g]+" "+T:j(T,/&\f/g,L[g])))&&(c[S++]=C);return tr(r,e,t,o===0?Z:s,c,p,h,l)}function nt(r,e,t,i){return tr(r,e,t,Q,Y(ee()),F(r,2,-2),0,i)}function le(r,e,t,i,o){return tr(r,e,t,J,F(r,0,i),F(r,i+1,-1),i,o)}function nr(r,e){for(var t="",i=0;i<r.length;i++)t+=e(r[i],i,r,e)||"";return t}function me(r,e,t,i){switch(r.type){case Qr:if(r.children.length)break;case Wr:case J:return r.return=r.return||r.value;case Q:return"";case Kr:return r.return=r.value+"{"+nr(r.children,i)+"}";case Z:if(!w(r.value=r.props.join(",")))return""}return w(t=nr(r.children,i))?r.return=r.value+"{"+t+"}":""}var he="c4",st=a(r=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(r),"detector"),ct=a(async()=>{let{diagram:r}=await import("./c4Diagram-6F5ED5ID-X53KYE5F.min.js");return{id:he,diagram:r}},"loader"),dt={id:he,detector:st,loader:ct},lt=dt,ye="flowchart",ut=a((r,e)=>e?.flowchart?.defaultRenderer==="dagre-wrapper"||e?.flowchart?.defaultRenderer==="elk"?!1:/^\s*graph/.test(r),"detector"),mt=a(async()=>{let{diagram:r}=await import("./flowDiagram-7ASYPVHJ-DABBKNEC.min.js");return{id:ye,diagram:r}},"loader"),ft={id:ye,detector:ut,loader:mt},pt=ft,we="flowchart-v2",gt=a((r,e)=>e?.flowchart?.defaultRenderer==="dagre-d3"?!1:(e?.flowchart?.defaultRenderer==="elk"&&(e.layout="elk"),/^\s*graph/.test(r)&&e?.flowchart?.defaultRenderer==="dagre-wrapper"?!0:/^\s*flowchart/.test(r)),"detector"),vt=a(async()=>{let{diagram:r}=await import("./flowDiagram-7ASYPVHJ-DABBKNEC.min.js");return{id:we,diagram:r}},"loader"),ht={id:we,detector:gt,loader:vt},yt=ht,xe="er",wt=a(r=>/^\s*erDiagram/.test(r),"detector"),xt=a(async()=>{let{diagram:r}=await import("./erDiagram-6RL3IURR-PEYW6AVI.min.js");return{id:xe,diagram:r}},"loader"),bt={id:xe,detector:wt,loader:xt},Et=bt,be="gitGraph",Dt=a(r=>/^\s*gitGraph/.test(r),"detector"),St=a(async()=>{let{diagram:r}=await import("./gitGraphDiagram-NRZ2UAAF-WVTRWY3E.min.js");return{id:be,diagram:r}},"loader"),Tt={id:be,detector:Dt,loader:St},_t=Tt,Ee="gantt",Lt=a(r=>/^\s*gantt/.test(r),"detector"),At=a(async()=>{let{diagram:r}=await import("./ganttDiagram-NTVNEXSI-JVQ2N4MZ.min.js");return{id:Ee,diagram:r}},"loader"),Mt={id:Ee,detector:Lt,loader:At},Ct=Mt,De="info",It=a(r=>/^\s*info/.test(r),"detector"),kt=a(async()=>{let{diagram:r}=await import("./infoDiagram-A4XQUW5V-SKLVFWJI.min.js");return{id:De,diagram:r}},"loader"),Rt={id:De,detector:It,loader:kt},Se="pie",Ft=a(r=>/^\s*pie/.test(r),"detector"),Ot=a(async()=>{let{diagram:r}=await import("./pieDiagram-YF2LJOPJ-ITGVNBO2.min.js");return{id:Se,diagram:r}},"loader"),zt={id:Se,detector:Ft,loader:Ot},Te="quadrantChart",$t=a(r=>/^\s*quadrantChart/.test(r),"detector"),Pt=a(async()=>{let{diagram:r}=await import("./quadrantDiagram-OS5C2QUG-BN35C5UH.min.js");return{id:Te,diagram:r}},"loader"),Nt={id:Te,detector:$t,loader:Pt},Vt=Nt,_e="xychart",Ut=a(r=>/^\s*xychart-beta/.test(r),"detector"),Gt=a(async()=>{let{diagram:r}=await import("./xychartDiagram-6QU3TZC5-MQVPM64I.min.js");return{id:_e,diagram:r}},"loader"),Ht={id:_e,detector:Ut,loader:Gt},qt=Ht,Le="requirement",Bt=a(r=>/^\s*requirement(Diagram)?/.test(r),"detector"),Yt=a(async()=>{let{diagram:r}=await import("./requirementDiagram-MIRIMTAZ-CXICLXCG.min.js");return{id:Le,diagram:r}},"loader"),jt={id:Le,detector:Bt,loader:Yt},Xt=jt,Ae="sequence",Wt=a(r=>/^\s*sequenceDiagram/.test(r),"detector"),Kt=a(async()=>{let{diagram:r}=await import("./sequenceDiagram-G6AWOVSC-UJVWCU2P.min.js");return{id:Ae,diagram:r}},"loader"),Qt={id:Ae,detector:Wt,loader:Kt},Zt=Qt,Me="class",Jt=a((r,e)=>e?.class?.defaultRenderer==="dagre-wrapper"?!1:/^\s*classDiagram/.test(r),"detector"),ra=a(async()=>{let{diagram:r}=await import("./classDiagram-LNE6IOMH-VZ67B4ZP.min.js");return{id:Me,diagram:r}},"loader"),ea={id:Me,detector:Jt,loader:ra},ta=ea,Ce="classDiagram",aa=a((r,e)=>/^\s*classDiagram/.test(r)&&e?.class?.defaultRenderer==="dagre-wrapper"?!0:/^\s*classDiagram-v2/.test(r),"detector"),ia=a(async()=>{let{diagram:r}=await import("./classDiagram-v2-MQ7JQ4JX-4JTAVB6L.min.js");return{id:Ce,diagram:r}},"loader"),oa={id:Ce,detector:aa,loader:ia},na=oa,Ie="state",sa=a((r,e)=>e?.state?.defaultRenderer==="dagre-wrapper"?!1:/^\s*stateDiagram/.test(r),"detector"),ca=a(async()=>{let{diagram:r}=await import("./stateDiagram-MAYHULR4-UPNPJ5ZA.min.js");return{id:Ie,diagram:r}},"loader"),da={id:Ie,detector:sa,loader:ca},la=da,ke="stateDiagram",ua=a((r,e)=>!!(/^\s*stateDiagram-v2/.test(r)||/^\s*stateDiagram/.test(r)&&e?.state?.defaultRenderer==="dagre-wrapper"),"detector"),ma=a(async()=>{let{diagram:r}=await import("./stateDiagram-v2-4JROLMXI-COTI64PW.min.js");return{id:ke,diagram:r}},"loader"),fa={id:ke,detector:ua,loader:ma},pa=fa,Re="journey",ga=a(r=>/^\s*journey/.test(r),"detector"),va=a(async()=>{let{diagram:r}=await import("./journeyDiagram-G5WM74LC-AHZ7GKR5.min.js");return{id:Re,diagram:r}},"loader"),ha={id:Re,detector:ga,loader:va},ya=ha,wa=a((r,e,t)=>{f.debug(`rendering svg for syntax error
`);let i=Xr(e),o=i.append("g");i.attr("viewBox","0 0 2412 512"),$r(i,100,512,!0),o.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),o.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),o.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),o.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),o.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),o.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),o.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),o.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${t}`)},"draw"),Fe={draw:wa},xa=Fe,ba={db:{},renderer:Fe,parser:{parse:a(()=>{},"parse")}},Ea=ba,Oe="flowchart-elk",Da=a((r,e={})=>/^\s*flowchart-elk/.test(r)||/^\s*flowchart|graph/.test(r)&&e?.flowchart?.defaultRenderer==="elk"?(e.layout="elk",!0):!1,"detector"),Sa=a(async()=>{let{diagram:r}=await import("./flowDiagram-7ASYPVHJ-DABBKNEC.min.js");return{id:Oe,diagram:r}},"loader"),Ta={id:Oe,detector:Da,loader:Sa},_a=Ta,ze="timeline",La=a(r=>/^\s*timeline/.test(r),"detector"),Aa=a(async()=>{let{diagram:r}=await import("./timeline-definition-U7ZMHBDA-I7GF7M6N.min.js");return{id:ze,diagram:r}},"loader"),Ma={id:ze,detector:La,loader:Aa},Ca=Ma,$e="mindmap",Ia=a(r=>/^\s*mindmap/.test(r),"detector"),ka=a(async()=>{let{diagram:r}=await import("./mindmap-definition-GWI6TPTV-XCX7U2FR.min.js");return{id:$e,diagram:r}},"loader"),Ra={id:$e,detector:Ia,loader:ka},Fa=Ra,Pe="kanban",Oa=a(r=>/^\s*kanban/.test(r),"detector"),za=a(async()=>{let{diagram:r}=await import("./kanban-definition-QRCXZQQD-MKSHYOCX.min.js");return{id:Pe,diagram:r}},"loader"),$a={id:Pe,detector:Oa,loader:za},Pa=$a,Ne="sankey",Na=a(r=>/^\s*sankey-beta/.test(r),"detector"),Va=a(async()=>{let{diagram:r}=await import("./sankeyDiagram-Y46BX6SQ-LTJNBPUP.min.js");return{id:Ne,diagram:r}},"loader"),Ua={id:Ne,detector:Na,loader:Va},Ga=Ua,Ve="packet",Ha=a(r=>/^\s*packet-beta/.test(r),"detector"),qa=a(async()=>{let{diagram:r}=await import("./diagram-QW4FP2JN-UOF7FAFC.min.js");return{id:Ve,diagram:r}},"loader"),Ba={id:Ve,detector:Ha,loader:qa},Ue="block",Ya=a(r=>/^\s*block-beta/.test(r),"detector"),ja=a(async()=>{let{diagram:r}=await import("./blockDiagram-ZHA2E4KO-IZKTV5IP.min.js");return{id:Ue,diagram:r}},"loader"),Xa={id:Ue,detector:Ya,loader:ja},Wa=Xa,Ge="architecture",Ka=a(r=>/^\s*architecture/.test(r),"detector"),Qa=a(async()=>{let{diagram:r}=await import("./architectureDiagram-UYN6MBPD-WBU2OYNU.min.js");return{id:Ge,diagram:r}},"loader"),Za={id:Ge,detector:Ka,loader:Qa},Ja=Za,fe=!1,cr=a(()=>{fe||(fe=!0,B("error",Ea,r=>r.toLowerCase().trim()==="error"),B("---",{db:{clear:a(()=>{},"clear")},styles:{},renderer:{draw:a(()=>{},"draw")},parser:{parse:a(()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")},"parse")},init:a(()=>null,"init")},r=>r.toLowerCase().trimStart().startsWith("---")),fr(lt,Pa,na,ta,Et,Ct,Rt,zt,Xt,Zt,_a,yt,pt,Fa,Ca,_t,pa,la,ya,Vt,Ga,Ba,qt,Wa,Ja))},"addDiagrams"),ri=a(async()=>{f.debug("Loading registered diagrams");let e=(await Promise.allSettled(Object.entries(ur).map(async([t,{detector:i,loader:o}])=>{if(o)try{K(t)}catch{try{let{diagram:n,id:d}=await o();B(d,n,i)}catch(n){throw f.error(`Failed to load external diagram with key ${t}. Removing from detectors.`),delete ur[t],n}}}))).filter(t=>t.status==="rejected");if(e.length>0){f.error(`Failed to load ${e.length} external diagrams`);for(let t of e)f.error(t);throw new Error(`Failed to load ${e.length} external diagrams`)}},"loadRegisteredDiagrams"),ei="graphics-document document";function He(r,e){r.attr("role",ei),e!==""&&r.attr("aria-roledescription",e)}a(He,"setA11yDiagramInfo");function qe(r,e,t,i){if(r.insert!==void 0){if(t){let o=`chart-desc-${i}`;r.attr("aria-describedby",o),r.insert("desc",":first-child").attr("id",o).text(t)}if(e){let o=`chart-title-${i}`;r.attr("aria-labelledby",o),r.insert("title",":first-child").attr("id",o).text(e)}}}a(qe,"addSVGa11yTitleDescription");var Er=class Be{constructor(e,t,i,o,n){this.type=e,this.text=t,this.db=i,this.parser=o,this.renderer=n}static{a(this,"Diagram")}static async fromText(e,t={}){let i=P(),o=mr(e,i);e=qr(e)+`
`;try{K(o)}catch{let p=Mr(o);if(!p)throw new Ar(`Diagram ${o} not found.`);let{id:h,diagram:l}=await p();B(h,l)}let{db:n,parser:d,renderer:s,init:c}=K(o);return d.parser&&(d.parser.yy=n),n.clear?.(),c?.(i),t.title&&n.setDiagramTitle?.(t.title),await d.parse(e),new Be(o,e,n,d,s)}async render(e,t){await this.renderer.draw(this.text,e,t,this)}getParser(){return this.parser}getType(){return this.type}},pe=[],ti=a(()=>{pe.forEach(r=>{r()}),pe=[]},"attachFunctions"),ai=a(r=>r.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart(),"cleanupComments");function Ye(r){let e=r.match(Lr);if(!e)return{text:r,metadata:{}};let t=Vr(e[1],{schema:Nr})??{};t=typeof t=="object"&&!Array.isArray(t)?t:{};let i={};return t.displayMode&&(i.displayMode=t.displayMode.toString()),t.title&&(i.title=t.title.toString()),t.config&&(i.config=t.config),{text:r.slice(e[0].length),metadata:i}}a(Ye,"extractFrontMatter");var ii=a(r=>r.replace(/\r\n?/g,`
`).replace(/<(\w+)([^>]*)>/g,(e,t,i)=>"<"+t+i.replace(/="([^"]*)"/g,"='$1'")+">"),"cleanupText"),oi=a(r=>{let{text:e,metadata:t}=Ye(r),{displayMode:i,title:o,config:n={}}=t;return i&&(n.gantt||(n.gantt={}),n.gantt.displayMode=i),{title:o,config:n,text:e}},"processFrontmatter"),ni=a(r=>{let e=N.detectInit(r)??{},t=N.detectDirective(r,"wrap");return Array.isArray(t)?e.wrap=t.some(({type:i})=>i==="wrap"):t?.type==="wrap"&&(e.wrap=!0),{text:Gr(r),directive:e}},"processDirectives");function Sr(r){let e=ii(r),t=oi(e),i=ni(t.text),o=Hr(t.config,i.directive);return r=ai(i.text),{code:r,title:t.title,config:o}}a(Sr,"preprocessDiagram");function je(r){let e=new TextEncoder().encode(r),t=Array.from(e,i=>String.fromCodePoint(i)).join("");return btoa(t)}a(je,"toBase64");var si=5e4,ci="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa",di="sandbox",li="loose",ui="http://www.w3.org/2000/svg",mi="http://www.w3.org/1999/xlink",fi="http://www.w3.org/1999/xhtml",pi="100%",gi="100%",vi="border:0;margin:0;",hi="margin:0",yi="allow-top-navigation-by-user-activation allow-popups",wi='The "iframe" tag is not supported by your browser.',xi=["foreignobject"],bi=["dominant-baseline"];function Tr(r){let e=Sr(r);return q(),Or(e.config??{}),e}a(Tr,"processAndSetConfigs");async function Xe(r,e){cr();try{let{code:t,config:i}=Tr(r);return{diagramType:(await Ke(t)).type,config:i}}catch(t){if(e?.suppressErrors)return!1;throw t}}a(Xe,"parse");var ge=a((r,e,t=[])=>`
.${r} ${e} { ${t.join(" !important; ")} !important; }`,"cssImportantStyles"),Ei=a((r,e=new Map)=>{let t="";if(r.themeCSS!==void 0&&(t+=`
${r.themeCSS}`),r.fontFamily!==void 0&&(t+=`
:root { --mermaid-font-family: ${r.fontFamily}}`),r.altFontFamily!==void 0&&(t+=`
:root { --mermaid-alt-font-family: ${r.altFontFamily}}`),e instanceof Map){let d=r.htmlLabels??r.flowchart?.htmlLabels?["> *","span"]:["rect","polygon","ellipse","circle","path"];e.forEach(s=>{hr(s.styles)||d.forEach(c=>{t+=ge(s.id,c,s.styles)}),hr(s.textStyles)||(t+=ge(s.id,"tspan",(s?.textStyles||[]).map(c=>c.replace("color","fill"))))})}return t},"createCssStyles"),Di=a((r,e,t,i)=>{let o=Ei(r,t),n=Pr(e,o,r.themeVariables);return nr(ue(`${i}{${n}}`),me)},"createUserStyles"),Si=a((r="",e,t)=>{let i=r;return!t&&!e&&(i=i.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),i=Br(i),i=i.replace(/<br>/g,"<br/>"),i},"cleanUpSvgCode"),Ti=a((r="",e)=>{let t=e?.viewBox?.baseVal?.height?e.viewBox.baseVal.height+"px":gi,i=je(`<body style="${hi}">${r}</body>`);return`<iframe style="width:${pi};height:${t};${vi}" src="data:text/html;charset=UTF-8;base64,${i}" sandbox="${yi}">
  ${wi}
</iframe>`},"putIntoIFrame"),ve=a((r,e,t,i,o)=>{let n=r.append("div");n.attr("id",t),i&&n.attr("style",i);let d=n.append("svg").attr("id",e).attr("width","100%").attr("xmlns",ui);return o&&d.attr("xmlns:xlink",o),d.append("g"),r},"appendDivSvgG");function Dr(r,e){return r.append("iframe").attr("id",e).attr("style","width: 100%; height: 100%;").attr("sandbox","")}a(Dr,"sandboxedIframe");var _i=a((r,e,t,i)=>{r.getElementById(e)?.remove(),r.getElementById(t)?.remove(),r.getElementById(i)?.remove()},"removeExistingElements"),Li=a(async function(r,e,t){cr();let i=Tr(e);e=i.code;let o=P();f.debug(o),e.length>(o?.maxTextSize??si)&&(e=ci);let n="#"+r,d="i"+r,s="#"+d,c="d"+r,p="#"+c,h=a(()=>{let dr=I(y?s:p).node();dr&&"remove"in dr&&dr.remove()},"removeTempElements"),l=I("body"),y=o.securityLevel===di,L=o.securityLevel===li,D=o.fontFamily;if(t!==void 0){if(t&&(t.innerHTML=""),y){let _=Dr(I(t),d);l=I(_.nodes()[0].contentDocument.body),l.node().style.margin=0}else l=I(t);ve(l,r,c,`font-family: ${D}`,mi)}else{if(_i(document,r,c,d),y){let _=Dr(I("body"),d);l=I(_.nodes()[0].contentDocument.body),l.node().style.margin=0}else l=I("body");ve(l,r,c)}let m,M;try{m=await Er.fromText(e,{title:i.title})}catch(_){if(o.suppressErrorRendering)throw h(),_;m=await Er.fromText("error"),M=_}let S=l.select(p).node(),g=m.type,T=S.firstChild,C=T.firstChild,A=m.renderer.getClasses?.(e,m),x=Di(o,g,A,n),u=document.createElement("style");u.innerHTML=x,T.insertBefore(u,C);try{await m.renderer.draw(e,r,yr,m)}catch(_){throw o.suppressErrorRendering?h():xa.draw(e,r,yr),_}let at=l.select(`${p} svg`),it=m.db.getAccTitle?.(),ot=m.db.getAccDescription?.();Qe(g,at,it,ot),l.select(`[id="${r}"]`).selectAll("foreignobject > *").attr("xmlns",fi);let O=l.select(p).node().innerHTML;if(f.debug("config.arrowMarkerAbsolute",o.arrowMarkerAbsolute),O=Si(O,y,zr(o.arrowMarkerAbsolute)),y){let _=l.select(p+" svg").node();O=Ti(O,_)}else L||(O=_r.sanitize(O,{ADD_TAGS:xi,ADD_ATTR:bi,HTML_INTEGRATION_POINTS:{foreignobject:!0}}));if(ti(),M)throw M;return h(),{diagramType:g,svg:O,bindFunctions:m.db.bindFunctions}},"render");function We(r={}){let e=Cr({},r);e?.fontFamily&&!e.themeVariables?.fontFamily&&(e.themeVariables||(e.themeVariables={}),e.themeVariables.fontFamily=e.fontFamily),kr(e),e?.theme&&e.theme in W?e.themeVariables=W[e.theme].getThemeVariables(e.themeVariables):e&&(e.themeVariables=W.default.getThemeVariables(e.themeVariables));let t=typeof e=="object"?Ir(e):gr();lr(t.logLevel),cr()}a(We,"initialize");var Ke=a((r,e={})=>{let{code:t}=Sr(r);return Er.fromText(t,e)},"getDiagramFromText");function Qe(r,e,t,i){He(e,r),qe(e,t,i,e.attr("id"))}a(Qe,"addA11yInfo");var $=Object.freeze({render:Li,parse:Xe,getDiagramFromText:Ke,initialize:We,getConfig:P,setConfig:Fr,getSiteConfig:gr,updateSiteConfig:Rr,reset:a(()=>{q()},"reset"),globalReset:a(()=>{q(pr)},"globalReset"),defaultConfig:pr});lr(P().logLevel);q(P());var Ai=a((r,e,t)=>{f.warn(r),vr(r)?(t&&t(r.str,r.hash),e.push({...r,message:r.str,error:r})):(t&&t(r),r instanceof Error&&e.push({str:r.message,message:r.message,hash:r.name,error:r}))},"handleError"),Ze=a(async function(r={querySelector:".mermaid"}){try{await Mi(r)}catch(e){if(vr(e)&&f.error(e.str),R.parseError&&R.parseError(e),!r.suppressErrors)throw f.error("Use the suppressErrors option to suppress these errors"),e}},"run"),Mi=a(async function({postRenderCallback:r,querySelector:e,nodes:t}={querySelector:".mermaid"}){let i=$.getConfig();f.debug(`${r?"":"No "}Callback function found`);let o;if(t)o=t;else if(e)o=document.querySelectorAll(e);else throw new Error("Nodes and querySelector are both undefined");f.debug(`Found ${o.length} diagrams`),i?.startOnLoad!==void 0&&(f.debug("Start On Load: "+i?.startOnLoad),$.updateSiteConfig({startOnLoad:i?.startOnLoad}));let n=new N.InitIDGenerator(i.deterministicIds,i.deterministicIDSeed),d,s=[];for(let c of Array.from(o)){if(f.info("Rendering diagram: "+c.id),c.getAttribute("data-processed"))continue;c.setAttribute("data-processed","true");let p=`mermaid-${n.next()}`;d=c.innerHTML,d=Yr(N.entityDecode(d)).trim().replace(/<br\s*\/?>/gi,"<br/>");let h=N.detectInit(d);h&&f.debug("Detected early reinit: ",h);try{let{svg:l,bindFunctions:y}=await tt(p,d,c);c.innerHTML=l,r&&await r(p),y&&y(c)}catch(l){Ai(l,s,R.parseError)}}if(s.length>0)throw s[0]},"runThrowsErrors"),Je=a(function(r){$.initialize(r)},"initialize"),Ci=a(async function(r,e,t){f.warn("mermaid.init is deprecated. Please use run instead."),r&&Je(r);let i={postRenderCallback:t,querySelector:".mermaid"};typeof e=="string"?i.querySelector=e:e&&(e instanceof HTMLElement?i.nodes=[e]:i.nodes=e),await Ze(i)},"init"),Ii=a(async(r,{lazyLoad:e=!0}={})=>{cr(),fr(...r),e===!1&&await ri()},"registerExternalDiagrams"),rt=a(function(){if(R.startOnLoad){let{startOnLoad:r}=$.getConfig();r&&R.run().catch(e=>f.error("Mermaid failed to initialize",e))}},"contentLoaded");typeof document<"u"&&window.addEventListener("load",rt,!1);var ki=a(function(r){R.parseError=r},"setParseErrorHandler"),sr=[],br=!1,et=a(async()=>{if(!br){for(br=!0;sr.length>0;){let r=sr.shift();if(r)try{await r()}catch(e){f.error("Error executing queue",e)}}br=!1}},"executeQueue"),Ri=a(async(r,e)=>new Promise((t,i)=>{let o=a(()=>new Promise((n,d)=>{$.parse(r,e).then(s=>{n(s),t(s)},s=>{f.error("Error parsing",s),R.parseError?.(s),d(s),i(s)})}),"performCall");sr.push(o),et().catch(i)}),"parse"),tt=a((r,e,t)=>new Promise((i,o)=>{let n=a(()=>new Promise((d,s)=>{$.render(r,e,t).then(c=>{d(c),i(c)},c=>{f.error("Error parsing",c),R.parseError?.(c),s(c),o(c)})}),"performCall");sr.push(n),et().catch(o)}),"render"),R={startOnLoad:!0,mermaidAPI:$,parse:Ri,render:tt,init:Ci,run:Ze,registerExternalDiagrams:Ii,registerLayoutLoaders:jr,initialize:Je,parseError:void 0,contentLoaded:rt,setParseErrorHandler:ki,detectType:mr,registerIconPacks:Ur},lo=R;export{lo as default};
/*! Bundled license information:

mermaid/dist/mermaid.core.mjs:
  (*! Check if previously processed *)
  (*!
   * Wait for document loaded before starting the execution
   *)
*/
//# sourceMappingURL=mermaid.core-QWHI4VJR.min.js.map
