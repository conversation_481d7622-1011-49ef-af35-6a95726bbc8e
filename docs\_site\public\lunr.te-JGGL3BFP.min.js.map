{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.te.js"], "sourcesContent": ["/*!\n * Lunr languages, `Hindi` language\n * https://github.com/MiKr13/lunr-languages\n *\n * Copyright 2023, India\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.te = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.te.trimmer,\n        lunr.te.stopWordFilter,\n        lunr.te.stemmer\n      );\n\n      // change the tokenizer for japanese one\n      // if (isLunr2) { // for lunr version 2.0.0\n      //   this.tokenizer = lunr.hi.tokenizer;\n      // } else {\n      //   if (lunr.tokenizer) { // for lunr version 0.6.0\n      //     lunr.tokenizer = lunr.hi.tokenizer;\n      //   }\n      //   if (this.tokenizerFn) { // for lunr version 0.7.0 -> 1.0.0\n      //     this.tokenizerFn = lunr.hi.tokenizer;\n      //   }\n      // }\n\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.te.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.te.wordCharacters = \"\\u0C00-\\u0C04\\u0C05-\\u0C14\\u0C15-\\u0C39\\u0C3E-\\u0C4C\\u0C55-\\u0C56\\u0C58-\\u0C5A\\u0C60-\\u0C61\\u0C62-\\u0C63\\u0C66-\\u0C6F\\u0C78-\\u0C7F\\u0C3C\\u0C3D\\u0C4D\\u0C5D\\u0C77\\u0C64\\u0C65\";\n    lunr.te.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.te.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.te.trimmer, 'trimmer-te');\n    /* lunr stop word filter */\n    lunr.te.stopWordFilter = lunr.generateStopWordFilter(\n      'అందరూ అందుబాటులో అడగండి అడగడం అడ్డంగా అనుగుణంగా అనుమతించు అనుమతిస్తుంది అయితే ఇప్పటికే ఉన్నారు ఎక్కడైనా ఎప్పుడు ఎవరైనా ఎవరో ఏ ఏదైనా ఏమైనప్పటికి ఒక ఒకరు కనిపిస్తాయి కాదు కూడా గా గురించి చుట్టూ చేయగలిగింది తగిన తర్వాత దాదాపు దూరంగా నిజంగా పై ప్రకారం ప్రక్కన మధ్య మరియు మరొక మళ్ళీ మాత్రమే మెచ్చుకో వద్ద వెంట వేరుగా వ్యతిరేకంగా సంబంధం'.split(' '));\n    /* lunr stemmer function */\n    lunr.te.stemmer = (function() {\n\n      return function(word) {\n        // for lunr version 2\n        if (typeof word.update === \"function\") {\n          return word.update(function(word) {\n            return word;\n          })\n        } else { // for lunr version <= 1\n          return word;\n        }\n\n      }\n    })();\n\n    var segmenter = lunr.wordcut;\n    segmenter.init();\n    lunr.te.tokenizer = function(obj) {\n      if (!arguments.length || obj == null || obj == undefined) return []\n      if (Array.isArray(obj)) return obj.map(function(t) {\n        return isLunr2 ? new lunr.Token(t.toLowerCase()) : t.toLowerCase()\n      });\n\n      var str = obj.toString().toLowerCase().replace(/^\\s+/, '');\n      return segmenter.cut(str).split('|');\n    }\n\n    lunr.Pipeline.registerFunction(lunr.te.stemmer, 'stemmer-te');\n    lunr.Pipeline.registerFunction(lunr.te.stopWordFilter, 'stopWordFilter-te');\n\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAcI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,+KACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAC5B,8tDAA6U,MAAM,GAAG,CAAC,EAEzVA,EAAK,GAAG,QAAW,UAAW,CAE5B,OAAO,SAASC,EAAM,CAEpB,OAAI,OAAOA,EAAK,QAAW,WAClBA,EAAK,OAAO,SAASA,EAAM,CAChC,OAAOA,CACT,CAAC,EAEMA,CAGX,CACF,EAAG,EAEH,IAAIC,EAAYF,EAAK,QACrBE,EAAU,KAAK,EACfF,EAAK,GAAG,UAAY,SAASG,EAAK,CAChC,GAAI,CAAC,UAAU,QAAUA,GAAO,MAAQA,GAAO,KAAW,MAAO,CAAC,EAClE,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOA,EAAI,IAAI,SAASC,EAAG,CACjD,OAAO,QAAU,IAAIJ,EAAK,MAAMI,EAAE,YAAY,CAAC,EAAIA,EAAE,YAAY,CACnE,CAAC,EAED,IAAIC,EAAMF,EAAI,SAAS,EAAE,YAAY,EAAE,QAAQ,OAAQ,EAAE,EACzD,OAAOD,EAAU,IAAIG,CAAG,EAAE,MAAM,GAAG,CACrC,EAEAL,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAC5DA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAE5E,CACF,CAAC", "names": ["require_lunr_te", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "word", "segmenter", "obj", "t", "str"]}