<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>DrawnUi.Maui | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="DrawnUi.Maui | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/index.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="drawnuimaui">DrawnUi.Maui</h1>

<p><strong>A lightweight library for .NET MAUI built on top of SkiaSharp, to layout and draw your UI on a Skia canvas.</strong></p>
<ul>
<li>Provides infrastructure to create and render drawn controls with gestures and animations, comes with some pre-built controls.</li>
<li>Profits from hardware-accelerated rendering on iOS • MacCatalyst • Android • Windows</li>
<li>Free to use under the MIT license, a nuget package is available.</li>
<li>To consume inside a usual MAUI app - wrap drawn controls inside <code>Canvas</code> views.</li>
<li>To create a totally drawn apps with just one <code>Canvas</code> as root view - <code>SkiaShell</code>, <code>SkiaViewSwitcher</code> are provided for navigation with modals, popups, toasts etc.</li>
<li>Drawn controls are virtual: no native views/handlers created, UI-thread is not required to accessed and modify them.</li>
</ul>
<h2 id="-live-examples">🚀 Live Examples</h2>
<ul>
<li><strong><a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo">Engine Demo</a></strong> - A totally drawn app demo with recycled cells, camera etc</li>
<li><strong><a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter">Space Shooter Game</a></strong> - Arcade game etude built with DrawnUi</li>
<li><strong><a href="https://github.com/taublast/ShadersCarousel/">Shaders Carousel</a></strong> - A totally drawn app with making use of SkiaSharp v3 shaders</li>
<li><strong><a href="https://github.com/taublast/DrawnUi.Maui/tree/main/src/Maui/Samples/Sandbox">Sandbox project</a></strong> - Experiment with pre-built drawn controls and more</li>
</ul>
<hr>
<h2 id="-onboarding">🎯 Onboarding</h2>
<p><strong>Q: What is the difference between DrawnUi and other drawn frameworks?</strong><br>
A: Not really comparable since DrawnUI is just a library for <strong>.NET MAUI</strong>, to let you draw UI instead of using native views.</p>
<p><strong>Q: Why choose drawn over native UI?</strong><br>
A: Rather a freedom choice to draw what you want and how you see it.<br>
It also can bemore performant to draw a complex UI on just one canvas instead of composing it with many native views.</p>
<p><strong>Q: Do I need to know how to draw on a canvas??</strong><br>
A: No, you can start by using prebuilt drawn controls and customize them. All controls are initially designed to be subclassed, customized, and almost every method is virtual.</p>
<p><strong>Q: Can I still use XAML?</strong><br>
A: Yes you can use both XAML and code-behind to create your UI.</p>
<p><strong>Q: Can I avoid using XAML at all costs?</strong><br>
A: Yes feel free to use code-behind to create your UI, up to using background thread to access and modify drawn controls properties.</p>
<p><strong>Q: How do I create custom controls with DrawnUI?</strong><br>
A: Inherit from <code>SkiaControl</code> for basic controls or <code>SkiaLayout</code> for containers etc. Override the <code>Paint</code> method to draw with SkiaSharp.</p>
<p><strong>Q: Can I embed native MAUI controls inside DrawnUI?</strong><br>
A: Yes! Use <code>SkiaMauiElement</code> to embed native MAUI controls like WebView inside your DrawnUI canvas. This allows you to combine the best of both worlds.</p>
<p><strong>Q: Possible to make games with DrawnUI?</strong><br>
A: Well, since you draw, why not just draw a game instead of a business app. DrawnUI comes with gaming helpers and custom accelerated platform views to assure a smooth display-synched rendering.</p>
<hr>
<p><strong>Can't find the answer to your question?</strong> → <strong><a href="https://github.com/taublast/DrawnUi/discussions">Ask in GitHub Discussions</a></strong> - The community is here to help!</p>
<h2 id="knowedge-base">💡Knowedge Base</h2>
<ul>
<li><strong><a href="faq.html">FAQ</a></strong> - Frequently asked questions and answers</li>
<li><strong><a href="https://github.com/taublast/DrawnUi.Maui/issues">GitHub Issues</a></strong> - Report bugs or ask questions</li>
<li><strong><a href="https://taublast.github.io/posts/MauiJuly/">Background Article</a></strong> - Why DrawnUi was created</li>
</ul>
<hr>
<p><strong>Ready to get started?</strong> → <strong><a href="getting-started.html">Install and Setup Guide</a></strong></p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/index.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
