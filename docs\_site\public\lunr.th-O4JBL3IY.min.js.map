{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.th.js"], "sourcesContent": ["/*!\n * Lunr languages, `Thai` language\n * https://github.com/MihaiValentin/lunr-languages\n *\n * Copyright 2017, Keerati Thiwanruk\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /*\n    Thai tokenization is the same to Japanense, which does not take into account spaces.\n    So, it uses the same logic to assign tokenization function due to different Lunr versions.\n    */\n    var isLunr2 = lunr.version[0] == \"2\";\n\n    /* register specific locale function */\n    lunr.th = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        /*lunr.th.stopWordFilter,*/\n        lunr.th.trimmer\n      );\n\n      if (isLunr2) { // for lunr version 2.0.0\n        this.tokenizer = lunr.th.tokenizer;\n      } else {\n        if (lunr.tokenizer) { // for lunr version 0.6.0\n          lunr.tokenizer = lunr.th.tokenizer;\n        }\n        if (this.tokenizerFn) { // for lunr version 0.7.0 -> 1.0.0\n          this.tokenizerFn = lunr.th.tokenizer;\n        }\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.th.wordCharacters = \"[\\u0e00-\\u0e7f]\";\n    lunr.th.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.th.wordCharacters);\n    lunr.Pipeline.registerFunction(lunr.th.trimmer, 'trimmer-th');\n\n    var segmenter = lunr.wordcut;\n    segmenter.init();\n    lunr.th.tokenizer = function(obj) {\n      //console.log(obj);\n      if (!arguments.length || obj == null || obj == undefined) return []\n      if (Array.isArray(obj)) return obj.map(function(t) {\n        return isLunr2 ? new lunr.Token(t) : t\n      })\n\n      var str = obj.toString().replace(/^\\s+/, '');\n      return segmenter.cut(str).split('|');\n    }\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAO1H,IAAIC,EAAUD,EAAK,QAAQ,CAAC,GAAK,IAGjCA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IAEZA,EAAK,GAAG,OACV,EAEIC,EACF,KAAK,UAAYD,EAAK,GAAG,WAErBA,EAAK,YACPA,EAAK,UAAYA,EAAK,GAAG,WAEvB,KAAK,cACP,KAAK,YAAcA,EAAK,GAAG,WAGjC,EAGAA,EAAK,GAAG,eAAiB,kBACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAC5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5D,IAAIE,EAAYF,EAAK,QACrBE,EAAU,KAAK,EACfF,EAAK,GAAG,UAAY,SAASG,EAAK,CAEhC,GAAI,CAAC,UAAU,QAAUA,GAAO,MAAQA,GAAO,KAAW,MAAO,CAAC,EAClE,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOA,EAAI,IAAI,SAASC,EAAG,CACjD,OAAOH,EAAU,IAAID,EAAK,MAAMI,CAAC,EAAIA,CACvC,CAAC,EAED,IAAIC,EAAMF,EAAI,SAAS,EAAE,QAAQ,OAAQ,EAAE,EAC3C,OAAOD,EAAU,IAAIG,CAAG,EAAE,MAAM,GAAG,CACrC,CACF,CACF,CAAC", "names": ["require_lunr_th", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "isLunr2", "segmenter", "obj", "t", "str"]}