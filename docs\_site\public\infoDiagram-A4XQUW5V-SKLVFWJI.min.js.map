{"version": 3, "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-A4XQUW5V.mjs"], "sourcesContent": ["import {\n  version\n} from \"./chunk-K6PMAZHR.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-EJ4ZWXGL.mjs\";\nimport {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/diagrams/info/infoParser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"info\", input);\n    log.debug(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/info/infoDb.ts\nvar DEFAULT_INFO_DB = { version };\nvar getVersion = /* @__PURE__ */ __name(() => DEFAULT_INFO_DB.version, \"getVersion\");\nvar db = {\n  getVersion\n};\n\n// src/diagrams/info/infoRenderer.ts\nvar draw = /* @__PURE__ */ __name((text, id, version2) => {\n  log.debug(\"rendering info diagram\\n\" + text);\n  const svg = selectSvgElement(id);\n  configureSvgSize(svg, 100, 400, true);\n  const group = svg.append(\"g\");\n  group.append(\"text\").attr(\"x\", 100).attr(\"y\", 40).attr(\"class\", \"version\").attr(\"font-size\", 32).style(\"text-anchor\", \"middle\").text(`v${version2}`);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/info/infoDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer\n};\nexport {\n  diagram\n};\n"], "mappings": "8fAcA,IAAIA,EAAS,CACX,MAAuBC,EAAO,MAAOC,GAAU,CAC7C,IAAMC,EAAM,MAAMC,EAAM,OAAQF,CAAK,EACrCG,EAAI,MAAMF,CAAG,CACf,EAAG,OAAO,CACZ,EAGIG,EAAkB,CAAE,QAAAC,CAAQ,EAC5BC,EAA6BP,EAAO,IAAMK,EAAgB,QAAS,YAAY,EAC/EG,EAAK,CACP,WAAAD,CACF,EAGIE,EAAuBT,EAAO,CAACU,EAAMC,EAAIC,IAAa,CACxDR,EAAI,MAAM;AAAA,EAA6BM,CAAI,EAC3C,IAAMG,EAAMC,EAAiBH,CAAE,EAC/BI,EAAiBF,EAAK,IAAK,IAAK,EAAI,EACtBA,EAAI,OAAO,GAAG,EACtB,OAAO,MAAM,EAAE,KAAK,IAAK,GAAG,EAAE,KAAK,IAAK,EAAE,EAAE,KAAK,QAAS,SAAS,EAAE,KAAK,YAAa,EAAE,EAAE,MAAM,cAAe,QAAQ,EAAE,KAAK,IAAID,CAAQ,EAAE,CACrJ,EAAG,MAAM,EACLI,EAAW,CAAE,KAAAP,CAAK,EAGlBQ,EAAU,CACZ,OAAAlB,EACA,GAAAS,EACA,SAAAQ,CACF", "names": ["parser", "__name", "input", "ast", "parse", "log", "DEFAULT_INFO_DB", "version", "getVersion", "db", "draw", "text", "id", "version2", "svg", "selectSvgElement", "configureSvgSize", "renderer", "diagram"]}