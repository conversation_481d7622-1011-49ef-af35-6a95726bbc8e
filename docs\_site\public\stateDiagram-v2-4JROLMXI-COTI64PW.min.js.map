{"version": 3, "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-4JROLMXI.mjs"], "sourcesContent": ["import {\n  stateDb_default,\n  stateDiagram_default,\n  stateRenderer_v3_unified_default,\n  styles_default\n} from \"./chunk-7U56Z5CX.mjs\";\nimport \"./chunk-5HRBRIJM.mjs\";\nimport \"./chunk-BO7VGL7K.mjs\";\nimport \"./chunk-66SQ7PYY.mjs\";\nimport \"./chunk-7NZE2EM7.mjs\";\nimport \"./chunk-OPO4IU42.mjs\";\nimport \"./chunk-3JNJP5BE.mjs\";\nimport \"./chunk-3X56UNUX.mjs\";\nimport \"./chunk-6JOS74DS.mjs\";\nimport \"./chunk-7DKRZKHE.mjs\";\nimport {\n  __name\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/diagrams/state/stateDiagram-v2.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  db: stateDb_default,\n  renderer: stateRenderer_v3_unified_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n    stateDb_default.clear();\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "mappings": "weAoBA,IAAIA,EAAU,CACZ,OAAQC,EACR,GAAIC,EACJ,SAAUC,EACV,OAAQC,EACR,KAAsBC,EAAQC,GAAQ,CAC/BA,EAAI,QACPA,EAAI,MAAQ,CAAC,GAEfA,EAAI,MAAM,oBAAsBA,EAAI,oBACpCJ,EAAgB,MAAM,CACxB,EAAG,MAAM,CACX", "names": ["diagram", "stateDiagram_default", "stateDb_default", "stateRenderer_v3_unified_default", "styles_default", "__name", "cnf"]}