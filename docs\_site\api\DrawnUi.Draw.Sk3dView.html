<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class Sk3dView | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class Sk3dView | DrawnUi Documentation ">
      
      <meta name="description" content="Custom implementation of Android&#39;s Camera 3D helper for SkiaSharp">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.Sk3dView">



  <h1 id="DrawnUi_Draw_Sk3dView" data-uid="DrawnUi.Draw.Sk3dView" class="text-break">
Class Sk3dView  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L10"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>Custom implementation of Android's Camera 3D helper for SkiaSharp</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class Sk3dView</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">Sk3dView</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_Sk3dView__ctor_" data-uid="DrawnUi.Draw.Sk3dView.#ctor*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView__ctor" data-uid="DrawnUi.Draw.Sk3dView.#ctor">
  Sk3dView()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L20"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Sk3dView()</code></pre>
  </div>













  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_Sk3dView_CameraDistance" data-uid="DrawnUi.Draw.Sk3dView.CameraDistance">
  CameraDistance
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L34"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>3D camera distance (8 inches in pixels, similar to Android implementation)</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float CameraDistance</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Sk3dView_Invalidated" data-uid="DrawnUi.Draw.Sk3dView.Invalidated">
  Invalidated
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L25"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected bool Invalidated</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_Sk3dView_Matrix_" data-uid="DrawnUi.Draw.Sk3dView.Matrix*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_Matrix" data-uid="DrawnUi.Draw.Sk3dView.Matrix">
  Matrix
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L179"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the current transformation matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKMatrix Matrix { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix">SKMatrix</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_Sk3dView_ApplyToCanvas_" data-uid="DrawnUi.Draw.Sk3dView.ApplyToCanvas*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_ApplyToCanvas_SkiaSharp_SKCanvas_" data-uid="DrawnUi.Draw.Sk3dView.ApplyToCanvas(SkiaSharp.SKCanvas)">
  ApplyToCanvas(SKCanvas)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L78"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Applies the current 3D transformation to the canvas</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ApplyToCanvas(SKCanvas canvas)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>canvas</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas">SKCanvas</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Sk3dView_OnReset_" data-uid="DrawnUi.Draw.Sk3dView.OnReset*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_OnReset" data-uid="DrawnUi.Draw.Sk3dView.OnReset">
  OnReset()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L107"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnReset()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Sk3dView_OnRestore_" data-uid="DrawnUi.Draw.Sk3dView.OnRestore*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_OnRestore" data-uid="DrawnUi.Draw.Sk3dView.OnRestore">
  OnRestore()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L109"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnRestore()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Sk3dView_Reset_" data-uid="DrawnUi.Draw.Sk3dView.Reset*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_Reset" data-uid="DrawnUi.Draw.Sk3dView.Reset">
  Reset()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L89"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Resets the current state and clears all saved states</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Reset()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Sk3dView_Restore_" data-uid="DrawnUi.Draw.Sk3dView.Restore*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_Restore" data-uid="DrawnUi.Draw.Sk3dView.Restore">
  Restore()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L56"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Restores the previously saved transformation state</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Restore()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Sk3dView_RotateXDegrees_" data-uid="DrawnUi.Draw.Sk3dView.RotateXDegrees*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_RotateXDegrees_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.RotateXDegrees(System.Single)">
  RotateXDegrees(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L114"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Rotates around the X axis</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RotateXDegrees(float degrees)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>degrees</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Sk3dView_RotateYDegrees_" data-uid="DrawnUi.Draw.Sk3dView.RotateYDegrees*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_RotateYDegrees_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.RotateYDegrees(System.Single)">
  RotateYDegrees(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L123"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Rotates around the Y axis</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RotateYDegrees(float degrees)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>degrees</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Sk3dView_RotateZDegrees_" data-uid="DrawnUi.Draw.Sk3dView.RotateZDegrees*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_RotateZDegrees_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.RotateZDegrees(System.Single)">
  RotateZDegrees(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L132"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Rotates around the Z axis</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RotateZDegrees(float degrees)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>degrees</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Sk3dView_Save_" data-uid="DrawnUi.Draw.Sk3dView.Save*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_Save" data-uid="DrawnUi.Draw.Sk3dView.Save">
  Save()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L39"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Saves the current transformation state</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Save()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Sk3dView_Translate_" data-uid="DrawnUi.Draw.Sk3dView.Translate*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_Translate_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.Translate(System.Single,System.Single,System.Single)">
  Translate(float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L168"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Translates along all axes</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Translate(float x, float y, float z)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>x</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>y</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>z</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Sk3dView_TranslateX_" data-uid="DrawnUi.Draw.Sk3dView.TranslateX*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_TranslateX_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.TranslateX(System.Single)">
  TranslateX(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L141"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Translates along the X axis</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void TranslateX(float value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Sk3dView_TranslateY_" data-uid="DrawnUi.Draw.Sk3dView.TranslateY*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_TranslateY_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.TranslateY(System.Single)">
  TranslateY(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L150"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Translates along the Y axis</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void TranslateY(float value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Sk3dView_TranslateZ_" data-uid="DrawnUi.Draw.Sk3dView.TranslateZ*"></a>

  <h3 id="DrawnUi_Draw_Sk3dView_TranslateZ_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.TranslateZ(System.Single)">
  TranslateZ(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L159"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Translates along the Z axis</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void TranslateZ(float value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>













</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L10" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
