import{a as me}from"./chunk-5IIW54K6.min.js";import{a as pe,b as fe}from"./chunk-N4YULA37.min.js";import{e as be,h as ye,k as ke}from"./chunk-N6ME3NZU.min.js";import"./chunk-V55NTXQN.min.js";import"./chunk-BD4P4Z7J.min.js";import"./chunk-AUO2PXKS.min.js";import"./chunk-PYPO7LRM.min.js";import"./chunk-CM5D5KZN.min.js";import{H as B,P as de,Z as M,d as ge,e as te,f as ne,h as o,j as Y,v as W}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import"./chunk-OSRY5VT3.min.js";var ie=function(){var e=o(function(O,i,n,s){for(n=n||{},s=O.length;s--;n[O[s]]=i);return n},"o"),u=[1,4],p=[1,13],r=[1,12],d=[1,15],_=[1,16],y=[1,20],l=[1,19],D=[6,7,8],I=[1,26],g=[1,24],w=[1,25],m=[6,7,11],U=[1,31],N=[6,7,11,24],j=[1,6,13,16,17,20,23],k=[1,35],A=[1,36],L=[1,6,7,11,13,16,17,20,23],H=[1,38],T={trace:o(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,KANBAN:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,shapeData:15,ICON:16,CLASS:17,nodeWithId:18,nodeWithoutId:19,NODE_DSTART:20,NODE_DESCR:21,NODE_DEND:22,NODE_ID:23,SHAPE_DATA:24,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"KANBAN",11:"EOF",13:"SPACELIST",16:"ICON",17:"CLASS",20:"NODE_DSTART",21:"NODE_DESCR",22:"NODE_DEND",23:"NODE_ID",24:"SHAPE_DATA"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,3],[12,2],[12,2],[12,2],[12,1],[12,2],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[19,3],[18,1],[18,4],[15,2],[15,1]],performAction:o(function(i,n,s,a,h,t,R){var c=t.length-1;switch(h){case 6:case 7:return a;case 8:a.getLogger().trace("Stop NL ");break;case 9:a.getLogger().trace("Stop EOF ");break;case 11:a.getLogger().trace("Stop NL2 ");break;case 12:a.getLogger().trace("Stop EOF2 ");break;case 15:a.getLogger().info("Node: ",t[c-1].id),a.addNode(t[c-2].length,t[c-1].id,t[c-1].descr,t[c-1].type,t[c]);break;case 16:a.getLogger().info("Node: ",t[c].id),a.addNode(t[c-1].length,t[c].id,t[c].descr,t[c].type);break;case 17:a.getLogger().trace("Icon: ",t[c]),a.decorateNode({icon:t[c]});break;case 18:case 23:a.decorateNode({class:t[c]});break;case 19:a.getLogger().trace("SPACELIST");break;case 20:a.getLogger().trace("Node: ",t[c-1].id),a.addNode(0,t[c-1].id,t[c-1].descr,t[c-1].type,t[c]);break;case 21:a.getLogger().trace("Node: ",t[c].id),a.addNode(0,t[c].id,t[c].descr,t[c].type);break;case 22:a.decorateNode({icon:t[c]});break;case 27:a.getLogger().trace("node found ..",t[c-2]),this.$={id:t[c-1],descr:t[c-1],type:a.getType(t[c-2],t[c])};break;case 28:this.$={id:t[c],descr:t[c],type:0};break;case 29:a.getLogger().trace("node found ..",t[c-3]),this.$={id:t[c-3],descr:t[c-1],type:a.getType(t[c-2],t[c])};break;case 30:this.$=t[c-1]+t[c];break;case 31:this.$=t[c];break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:u},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:u},{6:p,7:[1,10],9:9,12:11,13:r,14:14,16:d,17:_,18:17,19:18,20:y,23:l},e(D,[2,3]),{1:[2,2]},e(D,[2,4]),e(D,[2,5]),{1:[2,6],6:p,12:21,13:r,14:14,16:d,17:_,18:17,19:18,20:y,23:l},{6:p,9:22,12:11,13:r,14:14,16:d,17:_,18:17,19:18,20:y,23:l},{6:I,7:g,10:23,11:w},e(m,[2,24],{18:17,19:18,14:27,16:[1,28],17:[1,29],20:y,23:l}),e(m,[2,19]),e(m,[2,21],{15:30,24:U}),e(m,[2,22]),e(m,[2,23]),e(N,[2,25]),e(N,[2,26]),e(N,[2,28],{20:[1,32]}),{21:[1,33]},{6:I,7:g,10:34,11:w},{1:[2,7],6:p,12:21,13:r,14:14,16:d,17:_,18:17,19:18,20:y,23:l},e(j,[2,14],{7:k,11:A}),e(L,[2,8]),e(L,[2,9]),e(L,[2,10]),e(m,[2,16],{15:37,24:U}),e(m,[2,17]),e(m,[2,18]),e(m,[2,20],{24:H}),e(N,[2,31]),{21:[1,39]},{22:[1,40]},e(j,[2,13],{7:k,11:A}),e(L,[2,11]),e(L,[2,12]),e(m,[2,15],{24:H}),e(N,[2,30]),{22:[1,41]},e(N,[2,27]),e(N,[2,29])],defaultActions:{2:[2,1],6:[2,2]},parseError:o(function(i,n){if(n.recoverable)this.trace(i);else{var s=new Error(i);throw s.hash=n,s}},"parseError"),parse:o(function(i){var n=this,s=[0],a=[],h=[null],t=[],R=this.table,c="",z=0,oe=0,ce=0,_e=2,le=1,Se=t.slice.call(arguments,1),b=Object.create(this.lexer),P={yy:{}};for(var q in this.yy)Object.prototype.hasOwnProperty.call(this.yy,q)&&(P.yy[q]=this.yy[q]);b.setInput(i,P.yy),P.yy.lexer=b,P.yy.parser=this,typeof b.yylloc>"u"&&(b.yylloc={});var Q=b.yylloc;t.push(Q);var Ne=b.options&&b.options.ranges;typeof P.yy.parseError=="function"?this.parseError=P.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function xe(S){s.length=s.length-2*S,h.length=h.length-S,t.length=t.length-S}o(xe,"popStack");function he(){var S;return S=a.pop()||b.lex()||le,typeof S!="number"&&(S instanceof Array&&(a=S,S=a.pop()),S=n.symbols_[S]||S),S}o(he,"lex");for(var E,Z,V,x,He,$,G={},X,C,ue,K;;){if(V=s[s.length-1],this.defaultActions[V]?x=this.defaultActions[V]:((E===null||typeof E>"u")&&(E=he()),x=R[V]&&R[V][E]),typeof x>"u"||!x.length||!x[0]){var ee="";K=[];for(X in R[V])this.terminals_[X]&&X>_e&&K.push("'"+this.terminals_[X]+"'");b.showPosition?ee="Parse error on line "+(z+1)+`:
`+b.showPosition()+`
Expecting `+K.join(", ")+", got '"+(this.terminals_[E]||E)+"'":ee="Parse error on line "+(z+1)+": Unexpected "+(E==le?"end of input":"'"+(this.terminals_[E]||E)+"'"),this.parseError(ee,{text:b.match,token:this.terminals_[E]||E,line:b.yylineno,loc:Q,expected:K})}if(x[0]instanceof Array&&x.length>1)throw new Error("Parse Error: multiple actions possible at state: "+V+", token: "+E);switch(x[0]){case 1:s.push(E),h.push(b.yytext),t.push(b.yylloc),s.push(x[1]),E=null,Z?(E=Z,Z=null):(oe=b.yyleng,c=b.yytext,z=b.yylineno,Q=b.yylloc,ce>0&&ce--);break;case 2:if(C=this.productions_[x[1]][1],G.$=h[h.length-C],G._$={first_line:t[t.length-(C||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(C||1)].first_column,last_column:t[t.length-1].last_column},Ne&&(G._$.range=[t[t.length-(C||1)].range[0],t[t.length-1].range[1]]),$=this.performAction.apply(G,[c,oe,z,P.yy,x[1],h,t].concat(Se)),typeof $<"u")return $;C&&(s=s.slice(0,-1*C*2),h=h.slice(0,-1*C),t=t.slice(0,-1*C)),s.push(this.productions_[x[1]][0]),h.push(G.$),t.push(G._$),ue=R[s[s.length-2]][s[s.length-1]],s.push(ue);break;case 3:return!0}}return!0},"parse")},J=function(){var O={EOF:1,parseError:o(function(n,s){if(this.yy.parser)this.yy.parser.parseError(n,s);else throw new Error(n)},"parseError"),setInput:o(function(i,n){return this.yy=n||this.yy||{},this._input=i,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:o(function(){var i=this._input[0];this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i;var n=i.match(/(?:\r\n?|\n).*/g);return n?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},"input"),unput:o(function(i){var n=i.length,s=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-n),this.offset-=n;var a=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var h=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===a.length?this.yylloc.first_column:0)+a[a.length-s.length].length-s[0].length:this.yylloc.first_column-n},this.options.ranges&&(this.yylloc.range=[h[0],h[0]+this.yyleng-n]),this.yyleng=this.yytext.length,this},"unput"),more:o(function(){return this._more=!0,this},"more"),reject:o(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:o(function(i){this.unput(this.match.slice(i))},"less"),pastInput:o(function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:o(function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:o(function(){var i=this.pastInput(),n=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+n+"^"},"showPosition"),test_match:o(function(i,n){var s,a,h;if(this.options.backtrack_lexer&&(h={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(h.yylloc.range=this.yylloc.range.slice(0))),a=i[0].match(/(?:\r\n?|\n).*/g),a&&(this.yylineno+=a.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:a?a[a.length-1].length-a[a.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+i[0].length},this.yytext+=i[0],this.match+=i[0],this.matches=i,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(i[0].length),this.matched+=i[0],s=this.performAction.call(this,this.yy,this,n,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),s)return s;if(this._backtrack){for(var t in h)this[t]=h[t];return!1}return!1},"test_match"),next:o(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var i,n,s,a;this._more||(this.yytext="",this.match="");for(var h=this._currentRules(),t=0;t<h.length;t++)if(s=this._input.match(this.rules[h[t]]),s&&(!n||s[0].length>n[0].length)){if(n=s,a=t,this.options.backtrack_lexer){if(i=this.test_match(s,h[t]),i!==!1)return i;if(this._backtrack){n=!1;continue}else return!1}else if(!this.options.flex)break}return n?(i=this.test_match(n,h[a]),i!==!1?i:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:o(function(){var n=this.next();return n||this.lex()},"lex"),begin:o(function(n){this.conditionStack.push(n)},"begin"),popState:o(function(){var n=this.conditionStack.length-1;return n>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:o(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:o(function(n){return n=this.conditionStack.length-1-Math.abs(n||0),n>=0?this.conditionStack[n]:"INITIAL"},"topState"),pushState:o(function(n){this.begin(n)},"pushState"),stateStackSize:o(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:o(function(n,s,a,h){var t=h;switch(a){case 0:return this.pushState("shapeData"),s.yytext="",24;break;case 1:return this.pushState("shapeDataStr"),24;break;case 2:return this.popState(),24;break;case 3:let R=/\n\s*/g;return s.yytext=s.yytext.replace(R,"<br/>"),24;break;case 4:return 24;case 5:this.popState();break;case 6:return n.getLogger().trace("Found comment",s.yytext),6;break;case 7:return 8;case 8:this.begin("CLASS");break;case 9:return this.popState(),17;break;case 10:this.popState();break;case 11:n.getLogger().trace("Begin icon"),this.begin("ICON");break;case 12:return n.getLogger().trace("SPACELINE"),6;break;case 13:return 7;case 14:return 16;case 15:n.getLogger().trace("end icon"),this.popState();break;case 16:return n.getLogger().trace("Exploding node"),this.begin("NODE"),20;break;case 17:return n.getLogger().trace("Cloud"),this.begin("NODE"),20;break;case 18:return n.getLogger().trace("Explosion Bang"),this.begin("NODE"),20;break;case 19:return n.getLogger().trace("Cloud Bang"),this.begin("NODE"),20;break;case 20:return this.begin("NODE"),20;break;case 21:return this.begin("NODE"),20;break;case 22:return this.begin("NODE"),20;break;case 23:return this.begin("NODE"),20;break;case 24:return 13;case 25:return 23;case 26:return 11;case 27:this.begin("NSTR2");break;case 28:return"NODE_DESCR";case 29:this.popState();break;case 30:n.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 31:return n.getLogger().trace("description:",s.yytext),"NODE_DESCR";break;case 32:this.popState();break;case 33:return this.popState(),n.getLogger().trace("node end ))"),"NODE_DEND";break;case 34:return this.popState(),n.getLogger().trace("node end )"),"NODE_DEND";break;case 35:return this.popState(),n.getLogger().trace("node end ...",s.yytext),"NODE_DEND";break;case 36:return this.popState(),n.getLogger().trace("node end (("),"NODE_DEND";break;case 37:return this.popState(),n.getLogger().trace("node end (-"),"NODE_DEND";break;case 38:return this.popState(),n.getLogger().trace("node end (-"),"NODE_DEND";break;case 39:return this.popState(),n.getLogger().trace("node end (("),"NODE_DEND";break;case 40:return this.popState(),n.getLogger().trace("node end (("),"NODE_DEND";break;case 41:return n.getLogger().trace("Long description:",s.yytext),21;break;case 42:return n.getLogger().trace("Long description:",s.yytext),21;break}},"anonymous"),rules:[/^(?:@\{)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^\"]+)/i,/^(?:[^}^"]+)/i,/^(?:\})/i,/^(?:\s*%%.*)/i,/^(?:kanban\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}@]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{shapeDataEndBracket:{rules:[],inclusive:!1},shapeDataStr:{rules:[2,3],inclusive:!1},shapeData:{rules:[1,4,5],inclusive:!1},CLASS:{rules:[9,10],inclusive:!1},ICON:{rules:[14,15],inclusive:!1},NSTR2:{rules:[28,29],inclusive:!1},NSTR:{rules:[31,32],inclusive:!1},NODE:{rules:[27,30,33,34,35,36,37,38,39,40,41,42],inclusive:!1},INITIAL:{rules:[0,6,7,8,11,12,13,16,17,18,19,20,21,22,23,24,25,26],inclusive:!0}}};return O}();T.lexer=J;function F(){this.yy={}}return o(F,"Parser"),F.prototype=T,T.Parser=F,new F}();ie.parser=ie;var ve=ie,v=[],se=[],re=0,ae={},De=o(()=>{v=[],se=[],re=0,ae={}},"clear"),Le=o(e=>{if(v.length===0)return null;let u=v[0].level,p=null;for(let r=v.length-1;r>=0;r--)if(v[r].level===u&&!p&&(p=v[r]),v[r].level<u)throw new Error('Items without section detected, found section ("'+v[r].label+'")');return e===p?.level?null:p},"getSection"),Ee=o(function(){return se},"getSections"),Oe=o(function(){let e=[],u=[],p=Ee(),r=M();for(let d of p){let _={id:d.id,label:B(d.label??"",r),isGroup:!0,ticket:d.ticket,shape:"kanbanSection",level:d.level,look:r.look};u.push(_);let y=v.filter(l=>l.parentId===d.id);for(let l of y){let D={id:l.id,parentId:d.id,label:B(l.label??"",r),isGroup:!1,ticket:l?.ticket,priority:l?.priority,assigned:l?.assigned,icon:l?.icon,shape:"kanbanItem",level:l.level,rx:5,ry:5,cssStyles:["text-align: left"]};u.push(D)}}return{nodes:u,edges:e,other:{},config:M()}},"getData"),Ie=o((e,u,p,r,d)=>{let _=M(),y=_.mindmap?.padding??W.mindmap.padding;switch(r){case f.ROUNDED_RECT:case f.RECT:case f.HEXAGON:y*=2}let l={id:B(u,_)||"kbn"+re++,level:e,label:B(p,_),width:_.mindmap?.maxNodeWidth??W.mindmap.maxNodeWidth,padding:y,isGroup:!1};if(d!==void 0){let I;d.includes(`
`)?I=d+`
`:I=`{
`+d+`
}`;let g=fe(I,{schema:pe});if(g.shape&&(g.shape!==g.shape.toLowerCase()||g.shape.includes("_")))throw new Error(`No such shape: ${g.shape}. Shape names should be lowercase.`);g?.shape&&g.shape==="kanbanItem"&&(l.shape=g?.shape),g?.label&&(l.label=g?.label),g?.icon&&(l.icon=g?.icon.toString()),g?.assigned&&(l.assigned=g?.assigned.toString()),g?.ticket&&(l.ticket=g?.ticket.toString()),g?.priority&&(l.priority=g?.priority)}let D=Le(e);D?l.parentId=D.id||"kbn"+re++:se.push(l),v.push(l)},"addNode"),f={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},Ce=o((e,u)=>{switch(Y.debug("In get type",e,u),e){case"[":return f.RECT;case"(":return u===")"?f.ROUNDED_RECT:f.CLOUD;case"((":return f.CIRCLE;case")":return f.CLOUD;case"))":return f.BANG;case"{{":return f.HEXAGON;default:return f.DEFAULT}},"getType"),we=o((e,u)=>{ae[e]=u},"setElementForId"),Ae=o(e=>{if(!e)return;let u=M(),p=v[v.length-1];e.icon&&(p.icon=B(e.icon,u)),e.class&&(p.cssClasses=B(e.class,u))},"decorateNode"),Te=o(e=>{switch(e){case f.DEFAULT:return"no-border";case f.RECT:return"rect";case f.ROUNDED_RECT:return"rounded-rect";case f.CIRCLE:return"circle";case f.CLOUD:return"cloud";case f.BANG:return"bang";case f.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),Re=o(()=>Y,"getLogger"),Pe=o(e=>ae[e],"getElementById"),Ve={clear:De,addNode:Ie,getSections:Ee,getData:Oe,nodeType:f,getType:Ce,setElementForId:we,decorateNode:Ae,type2Str:Te,getLogger:Re,getElementById:Pe},Be=Ve,je=o(async(e,u,p,r)=>{Y.debug(`Rendering kanban diagram
`+e);let _=r.db.getData(),y=M();y.htmlLabels=!1;let l=me(u),D=l.append("g");D.attr("class","sections");let I=l.append("g");I.attr("class","items");let g=_.nodes.filter(k=>k.isGroup),w=0,m=10,U=[],N=25;for(let k of g){let A=y?.kanban?.sectionWidth||200;w=w+1,k.x=A*w+(w-1)*m/2,k.width=A,k.y=0,k.height=A*3,k.rx=5,k.ry=5,k.cssClasses=k.cssClasses+" section-"+w;let L=await be(D,k);N=Math.max(N,L?.labelBBox?.height),U.push(L)}let j=0;for(let k of g){let A=U[j];j=j+1;let L=y?.kanban?.sectionWidth||200,H=-L*3/2+N,T=H,J=_.nodes.filter(i=>i.parentId===k.id);for(let i of J){if(i.isGroup)throw new Error("Groups within groups are not allowed in Kanban diagrams");i.x=k.x,i.width=L-1.5*m;let s=(await ye(I,i,{config:y})).node().getBBox();i.y=T+s.height/2,await ke(i),T=i.y+s.height/2+m/2}let F=A.cluster.select("rect"),O=Math.max(T-H+3*m,50)+(N-25);F.attr("height",O)}de(void 0,l,y.mindmap?.padding??W.kanban.padding,y.mindmap?.useMaxWidth??W.kanban.useMaxWidth)},"draw"),Fe={draw:je},Ge=o(e=>{let u="";for(let r=0;r<e.THEME_COLOR_LIMIT;r++)e["lineColor"+r]=e["lineColor"+r]||e["cScaleInv"+r],ge(e["lineColor"+r])?e["lineColor"+r]=te(e["lineColor"+r],20):e["lineColor"+r]=ne(e["lineColor"+r],20);let p=o((r,d)=>e.darkMode?ne(r,d):te(r,d),"adjuster");for(let r=0;r<e.THEME_COLOR_LIMIT;r++){let d=""+(17-3*r);u+=`
    .section-${r-1} rect, .section-${r-1} path, .section-${r-1} circle, .section-${r-1} polygon, .section-${r-1} path  {
      fill: ${p(e["cScale"+r],10)};
      stroke: ${p(e["cScale"+r],10)};

    }
    .section-${r-1} text {
     fill: ${e["cScaleLabel"+r]};
    }
    .node-icon-${r-1} {
      font-size: 40px;
      color: ${e["cScaleLabel"+r]};
    }
    .section-edge-${r-1}{
      stroke: ${e["cScale"+r]};
    }
    .edge-depth-${r-1}{
      stroke-width: ${d};
    }
    .section-${r-1} line {
      stroke: ${e["cScaleInv"+r]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.background};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }

  .kanban-ticket-link {
    fill: ${e.background};
    stroke: ${e.nodeBorder};
    text-decoration: underline;
  }
    `}return u},"genSections"),Me=o(e=>`
  .edge {
    stroke-width: 3;
  }
  ${Ge(e)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${e.git0};
  }
  .section-root text {
    fill: ${e.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .cluster-label, .label {
    color: ${e.textColor};
    fill: ${e.textColor};
    }
  .kanban-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles"),Ue=Me,$e={db:Be,renderer:Fe,parser:ve,styles:Ue};export{$e as diagram};
//# sourceMappingURL=kanban-definition-QRCXZQQD-MKSHYOCX.min.js.map
