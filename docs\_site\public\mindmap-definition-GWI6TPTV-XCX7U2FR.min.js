import{a as ct}from"./chunk-WMZJ2DJX.min.js";import{a as Rt}from"./chunk-5IIW54K6.min.js";import{d as Ct}from"./chunk-AUO2PXKS.min.js";import{k as It}from"./chunk-PYPO7LRM.min.js";import"./chunk-CM5D5KZN.min.js";import{H as tt,P as Ot,Z as at,d as Nt,e as mt,f as Dt,h as w,ia as At,j as Q,v as q}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import{a as lt,d as bt}from"./chunk-OSRY5VT3.min.js";var ut=lt((et,gt)=>{(function(A,o){typeof et=="object"&&typeof gt=="object"?gt.exports=o():typeof define=="function"&&define.amd?define([],o):typeof et=="object"?et.layoutBase=o():A.layoutBase=o()})(et,function(){return function(E){var A={};function o(i){if(A[i])return A[i].exports;var e=A[i]={i,l:!1,exports:{}};return E[i].call(e.exports,e,e.exports,o),e.l=!0,e.exports}return o.m=E,o.c=A,o.i=function(i){return i},o.d=function(i,e,t){o.o(i,e)||Object.defineProperty(i,e,{configurable:!1,enumerable:!0,get:t})},o.n=function(i){var e=i&&i.__esModule?function(){return i.default}:function(){return i};return o.d(e,"a",e),e},o.o=function(i,e){return Object.prototype.hasOwnProperty.call(i,e)},o.p="",o(o.s=26)}([function(E,A,o){"use strict";function i(){}i.QUALITY=1,i.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,i.DEFAULT_INCREMENTAL=!1,i.DEFAULT_ANIMATION_ON_LAYOUT=!0,i.DEFAULT_ANIMATION_DURING_LAYOUT=!1,i.DEFAULT_ANIMATION_PERIOD=50,i.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,i.DEFAULT_GRAPH_MARGIN=15,i.NODE_DIMENSIONS_INCLUDE_LABELS=!1,i.SIMPLE_NODE_SIZE=40,i.SIMPLE_NODE_HALF_SIZE=i.SIMPLE_NODE_SIZE/2,i.EMPTY_COMPOUND_NODE_SIZE=40,i.MIN_EDGE_LENGTH=1,i.WORLD_BOUNDARY=1e6,i.INITIAL_WORLD_BOUNDARY=i.WORLD_BOUNDARY/1e3,i.WORLD_CENTER_X=1200,i.WORLD_CENTER_Y=900,E.exports=i},function(E,A,o){"use strict";var i=o(2),e=o(8),t=o(9);function r(f,a,d){i.call(this,d),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=d,this.bendpoints=[],this.source=f,this.target=a}r.prototype=Object.create(i.prototype);for(var l in i)r[l]=i[l];r.prototype.getSource=function(){return this.source},r.prototype.getTarget=function(){return this.target},r.prototype.isInterGraph=function(){return this.isInterGraph},r.prototype.getLength=function(){return this.length},r.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},r.prototype.getBendpoints=function(){return this.bendpoints},r.prototype.getLca=function(){return this.lca},r.prototype.getSourceInLca=function(){return this.sourceInLca},r.prototype.getTargetInLca=function(){return this.targetInLca},r.prototype.getOtherEnd=function(f){if(this.source===f)return this.target;if(this.target===f)return this.source;throw"Node is not incident with this edge"},r.prototype.getOtherEndInGraph=function(f,a){for(var d=this.getOtherEnd(f),s=a.getGraphManager().getRoot();;){if(d.getOwner()==a)return d;if(d.getOwner()==s)break;d=d.getOwner().getParent()}return null},r.prototype.updateLength=function(){var f=new Array(4);this.isOverlapingSourceAndTarget=e.getIntersection(this.target.getRect(),this.source.getRect(),f),this.isOverlapingSourceAndTarget||(this.lengthX=f[0]-f[2],this.lengthY=f[1]-f[3],Math.abs(this.lengthX)<1&&(this.lengthX=t.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=t.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},r.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=t.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=t.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},E.exports=r},function(E,A,o){"use strict";function i(e){this.vGraphObject=e}E.exports=i},function(E,A,o){"use strict";var i=o(2),e=o(10),t=o(13),r=o(0),l=o(16),f=o(4);function a(s,c,u,v){u==null&&v==null&&(v=c),i.call(this,v),s.graphManager!=null&&(s=s.graphManager),this.estimatedSize=e.MIN_VALUE,this.inclusionTreeDepth=e.MAX_VALUE,this.vGraphObject=v,this.edges=[],this.graphManager=s,u!=null&&c!=null?this.rect=new t(c.x,c.y,u.width,u.height):this.rect=new t}a.prototype=Object.create(i.prototype);for(var d in i)a[d]=i[d];a.prototype.getEdges=function(){return this.edges},a.prototype.getChild=function(){return this.child},a.prototype.getOwner=function(){return this.owner},a.prototype.getWidth=function(){return this.rect.width},a.prototype.setWidth=function(s){this.rect.width=s},a.prototype.getHeight=function(){return this.rect.height},a.prototype.setHeight=function(s){this.rect.height=s},a.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},a.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},a.prototype.getCenter=function(){return new f(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},a.prototype.getLocation=function(){return new f(this.rect.x,this.rect.y)},a.prototype.getRect=function(){return this.rect},a.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},a.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},a.prototype.setRect=function(s,c){this.rect.x=s.x,this.rect.y=s.y,this.rect.width=c.width,this.rect.height=c.height},a.prototype.setCenter=function(s,c){this.rect.x=s-this.rect.width/2,this.rect.y=c-this.rect.height/2},a.prototype.setLocation=function(s,c){this.rect.x=s,this.rect.y=c},a.prototype.moveBy=function(s,c){this.rect.x+=s,this.rect.y+=c},a.prototype.getEdgeListToNode=function(s){var c=[],u,v=this;return v.edges.forEach(function(L){if(L.target==s){if(L.source!=v)throw"Incorrect edge source!";c.push(L)}}),c},a.prototype.getEdgesBetween=function(s){var c=[],u,v=this;return v.edges.forEach(function(L){if(!(L.source==v||L.target==v))throw"Incorrect edge source and/or target";(L.target==s||L.source==s)&&c.push(L)}),c},a.prototype.getNeighborsList=function(){var s=new Set,c=this;return c.edges.forEach(function(u){if(u.source==c)s.add(u.target);else{if(u.target!=c)throw"Incorrect incidency!";s.add(u.source)}}),s},a.prototype.withChildren=function(){var s=new Set,c,u;if(s.add(this),this.child!=null)for(var v=this.child.getNodes(),L=0;L<v.length;L++)c=v[L],u=c.withChildren(),u.forEach(function(T){s.add(T)});return s},a.prototype.getNoOfChildren=function(){var s=0,c;if(this.child==null)s=1;else for(var u=this.child.getNodes(),v=0;v<u.length;v++)c=u[v],s+=c.getNoOfChildren();return s==0&&(s=1),s},a.prototype.getEstimatedSize=function(){if(this.estimatedSize==e.MIN_VALUE)throw"assert failed";return this.estimatedSize},a.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},a.prototype.scatter=function(){var s,c,u=-r.INITIAL_WORLD_BOUNDARY,v=r.INITIAL_WORLD_BOUNDARY;s=r.WORLD_CENTER_X+l.nextDouble()*(v-u)+u;var L=-r.INITIAL_WORLD_BOUNDARY,T=r.INITIAL_WORLD_BOUNDARY;c=r.WORLD_CENTER_Y+l.nextDouble()*(T-L)+L,this.rect.x=s,this.rect.y=c},a.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var s=this.getChild();if(s.updateBounds(!0),this.rect.x=s.getLeft(),this.rect.y=s.getTop(),this.setWidth(s.getRight()-s.getLeft()),this.setHeight(s.getBottom()-s.getTop()),r.NODE_DIMENSIONS_INCLUDE_LABELS){var c=s.getRight()-s.getLeft(),u=s.getBottom()-s.getTop();this.labelWidth>c&&(this.rect.x-=(this.labelWidth-c)/2,this.setWidth(this.labelWidth)),this.labelHeight>u&&(this.labelPos=="center"?this.rect.y-=(this.labelHeight-u)/2:this.labelPos=="top"&&(this.rect.y-=this.labelHeight-u),this.setHeight(this.labelHeight))}}},a.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==e.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},a.prototype.transform=function(s){var c=this.rect.x;c>r.WORLD_BOUNDARY?c=r.WORLD_BOUNDARY:c<-r.WORLD_BOUNDARY&&(c=-r.WORLD_BOUNDARY);var u=this.rect.y;u>r.WORLD_BOUNDARY?u=r.WORLD_BOUNDARY:u<-r.WORLD_BOUNDARY&&(u=-r.WORLD_BOUNDARY);var v=new f(c,u),L=s.inverseTransformPoint(v);this.setLocation(L.x,L.y)},a.prototype.getLeft=function(){return this.rect.x},a.prototype.getRight=function(){return this.rect.x+this.rect.width},a.prototype.getTop=function(){return this.rect.y},a.prototype.getBottom=function(){return this.rect.y+this.rect.height},a.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},E.exports=a},function(E,A,o){"use strict";function i(e,t){e==null&&t==null?(this.x=0,this.y=0):(this.x=e,this.y=t)}i.prototype.getX=function(){return this.x},i.prototype.getY=function(){return this.y},i.prototype.setX=function(e){this.x=e},i.prototype.setY=function(e){this.y=e},i.prototype.getDifference=function(e){return new DimensionD(this.x-e.x,this.y-e.y)},i.prototype.getCopy=function(){return new i(this.x,this.y)},i.prototype.translate=function(e){return this.x+=e.width,this.y+=e.height,this},E.exports=i},function(E,A,o){"use strict";var i=o(2),e=o(10),t=o(0),r=o(6),l=o(3),f=o(1),a=o(13),d=o(12),s=o(11);function c(v,L,T){i.call(this,T),this.estimatedSize=e.MIN_VALUE,this.margin=t.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=v,L!=null&&L instanceof r?this.graphManager=L:L!=null&&L instanceof Layout&&(this.graphManager=L.graphManager)}c.prototype=Object.create(i.prototype);for(var u in i)c[u]=i[u];c.prototype.getNodes=function(){return this.nodes},c.prototype.getEdges=function(){return this.edges},c.prototype.getGraphManager=function(){return this.graphManager},c.prototype.getParent=function(){return this.parent},c.prototype.getLeft=function(){return this.left},c.prototype.getRight=function(){return this.right},c.prototype.getTop=function(){return this.top},c.prototype.getBottom=function(){return this.bottom},c.prototype.isConnected=function(){return this.isConnected},c.prototype.add=function(v,L,T){if(L==null&&T==null){var y=v;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(y)>-1)throw"Node already in graph!";return y.owner=this,this.getNodes().push(y),y}else{var O=v;if(!(this.getNodes().indexOf(L)>-1&&this.getNodes().indexOf(T)>-1))throw"Source or target not in graph!";if(!(L.owner==T.owner&&L.owner==this))throw"Both owners must be this graph!";return L.owner!=T.owner?null:(O.source=L,O.target=T,O.isInterGraph=!1,this.getEdges().push(O),L.edges.push(O),T!=L&&T.edges.push(O),O)}},c.prototype.remove=function(v){var L=v;if(v instanceof l){if(L==null)throw"Node is null!";if(!(L.owner!=null&&L.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var T=L.edges.slice(),y,O=T.length,D=0;D<O;D++)y=T[D],y.isInterGraph?this.graphManager.remove(y):y.source.owner.remove(y);var R=this.nodes.indexOf(L);if(R==-1)throw"Node not in owner node list!";this.nodes.splice(R,1)}else if(v instanceof f){var y=v;if(y==null)throw"Edge is null!";if(!(y.source!=null&&y.target!=null))throw"Source and/or target is null!";if(!(y.source.owner!=null&&y.target.owner!=null&&y.source.owner==this&&y.target.owner==this))throw"Source and/or target owner is invalid!";var n=y.source.edges.indexOf(y),h=y.target.edges.indexOf(y);if(!(n>-1&&h>-1))throw"Source and/or target doesn't know this edge!";y.source.edges.splice(n,1),y.target!=y.source&&y.target.edges.splice(h,1);var R=y.source.owner.getEdges().indexOf(y);if(R==-1)throw"Not in owner's edge list!";y.source.owner.getEdges().splice(R,1)}},c.prototype.updateLeftTop=function(){for(var v=e.MAX_VALUE,L=e.MAX_VALUE,T,y,O,D=this.getNodes(),R=D.length,n=0;n<R;n++){var h=D[n];T=h.getTop(),y=h.getLeft(),v>T&&(v=T),L>y&&(L=y)}return v==e.MAX_VALUE?null:(D[0].getParent().paddingLeft!=null?O=D[0].getParent().paddingLeft:O=this.margin,this.left=L-O,this.top=v-O,new d(this.left,this.top))},c.prototype.updateBounds=function(v){for(var L=e.MAX_VALUE,T=-e.MAX_VALUE,y=e.MAX_VALUE,O=-e.MAX_VALUE,D,R,n,h,g,p=this.nodes,N=p.length,m=0;m<N;m++){var C=p[m];v&&C.child!=null&&C.updateBounds(),D=C.getLeft(),R=C.getRight(),n=C.getTop(),h=C.getBottom(),L>D&&(L=D),T<R&&(T=R),y>n&&(y=n),O<h&&(O=h)}var I=new a(L,y,T-L,O-y);L==e.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),p[0].getParent().paddingLeft!=null?g=p[0].getParent().paddingLeft:g=this.margin,this.left=I.x-g,this.right=I.x+I.width+g,this.top=I.y-g,this.bottom=I.y+I.height+g},c.calculateBounds=function(v){for(var L=e.MAX_VALUE,T=-e.MAX_VALUE,y=e.MAX_VALUE,O=-e.MAX_VALUE,D,R,n,h,g=v.length,p=0;p<g;p++){var N=v[p];D=N.getLeft(),R=N.getRight(),n=N.getTop(),h=N.getBottom(),L>D&&(L=D),T<R&&(T=R),y>n&&(y=n),O<h&&(O=h)}var m=new a(L,y,T-L,O-y);return m},c.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},c.prototype.getEstimatedSize=function(){if(this.estimatedSize==e.MIN_VALUE)throw"assert failed";return this.estimatedSize},c.prototype.calcEstimatedSize=function(){for(var v=0,L=this.nodes,T=L.length,y=0;y<T;y++){var O=L[y];v+=O.calcEstimatedSize()}return v==0?this.estimatedSize=t.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=v/Math.sqrt(this.nodes.length),this.estimatedSize},c.prototype.updateConnected=function(){var v=this;if(this.nodes.length==0){this.isConnected=!0;return}var L=new s,T=new Set,y=this.nodes[0],O,D,R=y.withChildren();for(R.forEach(function(m){L.push(m),T.add(m)});L.length!==0;){y=L.shift(),O=y.getEdges();for(var n=O.length,h=0;h<n;h++){var g=O[h];if(D=g.getOtherEndInGraph(y,this),D!=null&&!T.has(D)){var p=D.withChildren();p.forEach(function(m){L.push(m),T.add(m)})}}}if(this.isConnected=!1,T.size>=this.nodes.length){var N=0;T.forEach(function(m){m.owner==v&&N++}),N==this.nodes.length&&(this.isConnected=!0)}},E.exports=c},function(E,A,o){"use strict";var i,e=o(1);function t(r){i=o(5),this.layout=r,this.graphs=[],this.edges=[]}t.prototype.addRoot=function(){var r=this.layout.newGraph(),l=this.layout.newNode(null),f=this.add(r,l);return this.setRootGraph(f),this.rootGraph},t.prototype.add=function(r,l,f,a,d){if(f==null&&a==null&&d==null){if(r==null)throw"Graph is null!";if(l==null)throw"Parent node is null!";if(this.graphs.indexOf(r)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(r),r.parent!=null)throw"Already has a parent!";if(l.child!=null)throw"Already has a child!";return r.parent=l,l.child=r,r}else{d=f,a=l,f=r;var s=a.getOwner(),c=d.getOwner();if(!(s!=null&&s.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(c!=null&&c.getGraphManager()==this))throw"Target not in this graph mgr!";if(s==c)return f.isInterGraph=!1,s.add(f,a,d);if(f.isInterGraph=!0,f.source=a,f.target=d,this.edges.indexOf(f)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(f),!(f.source!=null&&f.target!=null))throw"Edge source and/or target is null!";if(!(f.source.edges.indexOf(f)==-1&&f.target.edges.indexOf(f)==-1))throw"Edge already in source and/or target incidency list!";return f.source.edges.push(f),f.target.edges.push(f),f}},t.prototype.remove=function(r){if(r instanceof i){var l=r;if(l.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(l==this.rootGraph||l.parent!=null&&l.parent.graphManager==this))throw"Invalid parent node!";var f=[];f=f.concat(l.getEdges());for(var a,d=f.length,s=0;s<d;s++)a=f[s],l.remove(a);var c=[];c=c.concat(l.getNodes());var u;d=c.length;for(var s=0;s<d;s++)u=c[s],l.remove(u);l==this.rootGraph&&this.setRootGraph(null);var v=this.graphs.indexOf(l);this.graphs.splice(v,1),l.parent=null}else if(r instanceof e){if(a=r,a==null)throw"Edge is null!";if(!a.isInterGraph)throw"Not an inter-graph edge!";if(!(a.source!=null&&a.target!=null))throw"Source and/or target is null!";if(!(a.source.edges.indexOf(a)!=-1&&a.target.edges.indexOf(a)!=-1))throw"Source and/or target doesn't know this edge!";var v=a.source.edges.indexOf(a);if(a.source.edges.splice(v,1),v=a.target.edges.indexOf(a),a.target.edges.splice(v,1),!(a.source.owner!=null&&a.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(a.source.owner.getGraphManager().edges.indexOf(a)==-1)throw"Not in owner graph manager's edge list!";var v=a.source.owner.getGraphManager().edges.indexOf(a);a.source.owner.getGraphManager().edges.splice(v,1)}},t.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},t.prototype.getGraphs=function(){return this.graphs},t.prototype.getAllNodes=function(){if(this.allNodes==null){for(var r=[],l=this.getGraphs(),f=l.length,a=0;a<f;a++)r=r.concat(l[a].getNodes());this.allNodes=r}return this.allNodes},t.prototype.resetAllNodes=function(){this.allNodes=null},t.prototype.resetAllEdges=function(){this.allEdges=null},t.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},t.prototype.getAllEdges=function(){if(this.allEdges==null){for(var r=[],l=this.getGraphs(),f=l.length,a=0;a<l.length;a++)r=r.concat(l[a].getEdges());r=r.concat(this.edges),this.allEdges=r}return this.allEdges},t.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},t.prototype.setAllNodesToApplyGravitation=function(r){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=r},t.prototype.getRoot=function(){return this.rootGraph},t.prototype.setRootGraph=function(r){if(r.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=r,r.parent==null&&(r.parent=this.layout.newNode("Root node"))},t.prototype.getLayout=function(){return this.layout},t.prototype.isOneAncestorOfOther=function(r,l){if(!(r!=null&&l!=null))throw"assert failed";if(r==l)return!0;var f=r.getOwner(),a;do{if(a=f.getParent(),a==null)break;if(a==l)return!0;if(f=a.getOwner(),f==null)break}while(!0);f=l.getOwner();do{if(a=f.getParent(),a==null)break;if(a==r)return!0;if(f=a.getOwner(),f==null)break}while(!0);return!1},t.prototype.calcLowestCommonAncestors=function(){for(var r,l,f,a,d,s=this.getAllEdges(),c=s.length,u=0;u<c;u++){if(r=s[u],l=r.source,f=r.target,r.lca=null,r.sourceInLca=l,r.targetInLca=f,l==f){r.lca=l.getOwner();continue}for(a=l.getOwner();r.lca==null;){for(r.targetInLca=f,d=f.getOwner();r.lca==null;){if(d==a){r.lca=d;break}if(d==this.rootGraph)break;if(r.lca!=null)throw"assert failed";r.targetInLca=d.getParent(),d=r.targetInLca.getOwner()}if(a==this.rootGraph)break;r.lca==null&&(r.sourceInLca=a.getParent(),a=r.sourceInLca.getOwner())}if(r.lca==null)throw"assert failed"}},t.prototype.calcLowestCommonAncestor=function(r,l){if(r==l)return r.getOwner();var f=r.getOwner();do{if(f==null)break;var a=l.getOwner();do{if(a==null)break;if(a==f)return a;a=a.getParent().getOwner()}while(!0);f=f.getParent().getOwner()}while(!0);return f},t.prototype.calcInclusionTreeDepths=function(r,l){r==null&&l==null&&(r=this.rootGraph,l=1);for(var f,a=r.getNodes(),d=a.length,s=0;s<d;s++)f=a[s],f.inclusionTreeDepth=l,f.child!=null&&this.calcInclusionTreeDepths(f.child,l+1)},t.prototype.includesInvalidEdge=function(){for(var r,l=this.edges.length,f=0;f<l;f++)if(r=this.edges[f],this.isOneAncestorOfOther(r.source,r.target))return!0;return!1},E.exports=t},function(E,A,o){"use strict";var i=o(0);function e(){}for(var t in i)e[t]=i[t];e.MAX_ITERATIONS=2500,e.DEFAULT_EDGE_LENGTH=50,e.DEFAULT_SPRING_STRENGTH=.45,e.DEFAULT_REPULSION_STRENGTH=4500,e.DEFAULT_GRAVITY_STRENGTH=.4,e.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,e.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,e.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,e.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,e.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,e.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,e.COOLING_ADAPTATION_FACTOR=.33,e.ADAPTATION_LOWER_NODE_LIMIT=1e3,e.ADAPTATION_UPPER_NODE_LIMIT=5e3,e.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,e.MAX_NODE_DISPLACEMENT=e.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,e.MIN_REPULSION_DIST=e.DEFAULT_EDGE_LENGTH/10,e.CONVERGENCE_CHECK_PERIOD=100,e.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,e.MIN_EDGE_LENGTH=1,e.GRID_CALCULATION_CHECK_PERIOD=10,E.exports=e},function(E,A,o){"use strict";var i=o(12);function e(){}e.calcSeparationAmount=function(t,r,l,f){if(!t.intersects(r))throw"assert failed";var a=new Array(2);this.decideDirectionsForOverlappingNodes(t,r,a),l[0]=Math.min(t.getRight(),r.getRight())-Math.max(t.x,r.x),l[1]=Math.min(t.getBottom(),r.getBottom())-Math.max(t.y,r.y),t.getX()<=r.getX()&&t.getRight()>=r.getRight()?l[0]+=Math.min(r.getX()-t.getX(),t.getRight()-r.getRight()):r.getX()<=t.getX()&&r.getRight()>=t.getRight()&&(l[0]+=Math.min(t.getX()-r.getX(),r.getRight()-t.getRight())),t.getY()<=r.getY()&&t.getBottom()>=r.getBottom()?l[1]+=Math.min(r.getY()-t.getY(),t.getBottom()-r.getBottom()):r.getY()<=t.getY()&&r.getBottom()>=t.getBottom()&&(l[1]+=Math.min(t.getY()-r.getY(),r.getBottom()-t.getBottom()));var d=Math.abs((r.getCenterY()-t.getCenterY())/(r.getCenterX()-t.getCenterX()));r.getCenterY()===t.getCenterY()&&r.getCenterX()===t.getCenterX()&&(d=1);var s=d*l[0],c=l[1]/d;l[0]<c?c=l[0]:s=l[1],l[0]=-1*a[0]*(c/2+f),l[1]=-1*a[1]*(s/2+f)},e.decideDirectionsForOverlappingNodes=function(t,r,l){t.getCenterX()<r.getCenterX()?l[0]=-1:l[0]=1,t.getCenterY()<r.getCenterY()?l[1]=-1:l[1]=1},e.getIntersection2=function(t,r,l){var f=t.getCenterX(),a=t.getCenterY(),d=r.getCenterX(),s=r.getCenterY();if(t.intersects(r))return l[0]=f,l[1]=a,l[2]=d,l[3]=s,!0;var c=t.getX(),u=t.getY(),v=t.getRight(),L=t.getX(),T=t.getBottom(),y=t.getRight(),O=t.getWidthHalf(),D=t.getHeightHalf(),R=r.getX(),n=r.getY(),h=r.getRight(),g=r.getX(),p=r.getBottom(),N=r.getRight(),m=r.getWidthHalf(),C=r.getHeightHalf(),I=!1,x=!1;if(f===d){if(a>s)return l[0]=f,l[1]=u,l[2]=d,l[3]=p,!1;if(a<s)return l[0]=f,l[1]=T,l[2]=d,l[3]=n,!1}else if(a===s){if(f>d)return l[0]=c,l[1]=a,l[2]=h,l[3]=s,!1;if(f<d)return l[0]=v,l[1]=a,l[2]=R,l[3]=s,!1}else{var U=t.height/t.width,P=r.height/r.width,_=(s-a)/(d-f),M=void 0,G=void 0,S=void 0,F=void 0,X=void 0,b=void 0;if(-U===_?f>d?(l[0]=L,l[1]=T,I=!0):(l[0]=v,l[1]=u,I=!0):U===_&&(f>d?(l[0]=c,l[1]=u,I=!0):(l[0]=y,l[1]=T,I=!0)),-P===_?d>f?(l[2]=g,l[3]=p,x=!0):(l[2]=h,l[3]=n,x=!0):P===_&&(d>f?(l[2]=R,l[3]=n,x=!0):(l[2]=N,l[3]=p,x=!0)),I&&x)return!1;if(f>d?a>s?(M=this.getCardinalDirection(U,_,4),G=this.getCardinalDirection(P,_,2)):(M=this.getCardinalDirection(-U,_,3),G=this.getCardinalDirection(-P,_,1)):a>s?(M=this.getCardinalDirection(-U,_,1),G=this.getCardinalDirection(-P,_,3)):(M=this.getCardinalDirection(U,_,2),G=this.getCardinalDirection(P,_,4)),!I)switch(M){case 1:F=u,S=f+-D/_,l[0]=S,l[1]=F;break;case 2:S=y,F=a+O*_,l[0]=S,l[1]=F;break;case 3:F=T,S=f+D/_,l[0]=S,l[1]=F;break;case 4:S=L,F=a+-O*_,l[0]=S,l[1]=F;break}if(!x)switch(G){case 1:b=n,X=d+-C/_,l[2]=X,l[3]=b;break;case 2:X=N,b=s+m*_,l[2]=X,l[3]=b;break;case 3:b=p,X=d+C/_,l[2]=X,l[3]=b;break;case 4:X=g,b=s+-m*_,l[2]=X,l[3]=b;break}}return!1},e.getCardinalDirection=function(t,r,l){return t>r?l:1+l%4},e.getIntersection=function(t,r,l,f){if(f==null)return this.getIntersection2(t,r,l);var a=t.x,d=t.y,s=r.x,c=r.y,u=l.x,v=l.y,L=f.x,T=f.y,y=void 0,O=void 0,D=void 0,R=void 0,n=void 0,h=void 0,g=void 0,p=void 0,N=void 0;return D=c-d,n=a-s,g=s*d-a*c,R=T-v,h=u-L,p=L*v-u*T,N=D*h-R*n,N===0?null:(y=(n*p-h*g)/N,O=(R*g-D*p)/N,new i(y,O))},e.angleOfVector=function(t,r,l,f){var a=void 0;return t!==l?(a=Math.atan((f-r)/(l-t)),l<t?a+=Math.PI:f<r&&(a+=this.TWO_PI)):f<r?a=this.ONE_AND_HALF_PI:a=this.HALF_PI,a},e.doIntersect=function(t,r,l,f){var a=t.x,d=t.y,s=r.x,c=r.y,u=l.x,v=l.y,L=f.x,T=f.y,y=(s-a)*(T-v)-(L-u)*(c-d);if(y===0)return!1;var O=((T-v)*(L-a)+(u-L)*(T-d))/y,D=((d-c)*(L-a)+(s-a)*(T-d))/y;return 0<O&&O<1&&0<D&&D<1},e.HALF_PI=.5*Math.PI,e.ONE_AND_HALF_PI=1.5*Math.PI,e.TWO_PI=2*Math.PI,e.THREE_PI=3*Math.PI,E.exports=e},function(E,A,o){"use strict";function i(){}i.sign=function(e){return e>0?1:e<0?-1:0},i.floor=function(e){return e<0?Math.ceil(e):Math.floor(e)},i.ceil=function(e){return e<0?Math.floor(e):Math.ceil(e)},E.exports=i},function(E,A,o){"use strict";function i(){}i.MAX_VALUE=2147483647,i.MIN_VALUE=-2147483648,E.exports=i},function(E,A,o){"use strict";var i=function(){function a(d,s){for(var c=0;c<s.length;c++){var u=s[c];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(d,u.key,u)}}return function(d,s,c){return s&&a(d.prototype,s),c&&a(d,c),d}}();function e(a,d){if(!(a instanceof d))throw new TypeError("Cannot call a class as a function")}var t=function(d){return{value:d,next:null,prev:null}},r=function(d,s,c,u){return d!==null?d.next=s:u.head=s,c!==null?c.prev=s:u.tail=s,s.prev=d,s.next=c,u.length++,s},l=function(d,s){var c=d.prev,u=d.next;return c!==null?c.next=u:s.head=u,u!==null?u.prev=c:s.tail=c,d.prev=d.next=null,s.length--,d},f=function(){function a(d){var s=this;e(this,a),this.length=0,this.head=null,this.tail=null,d?.forEach(function(c){return s.push(c)})}return i(a,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(s,c){return r(c.prev,t(s),c,this)}},{key:"insertAfter",value:function(s,c){return r(c,t(s),c.next,this)}},{key:"insertNodeBefore",value:function(s,c){return r(c.prev,s,c,this)}},{key:"insertNodeAfter",value:function(s,c){return r(c,s,c.next,this)}},{key:"push",value:function(s){return r(this.tail,t(s),null,this)}},{key:"unshift",value:function(s){return r(null,t(s),this.head,this)}},{key:"remove",value:function(s){return l(s,this)}},{key:"pop",value:function(){return l(this.tail,this).value}},{key:"popNode",value:function(){return l(this.tail,this)}},{key:"shift",value:function(){return l(this.head,this).value}},{key:"shiftNode",value:function(){return l(this.head,this)}},{key:"get_object_at",value:function(s){if(s<=this.length()){for(var c=1,u=this.head;c<s;)u=u.next,c++;return u.value}}},{key:"set_object_at",value:function(s,c){if(s<=this.length()){for(var u=1,v=this.head;u<s;)v=v.next,u++;v.value=c}}}]),a}();E.exports=f},function(E,A,o){"use strict";function i(e,t,r){this.x=null,this.y=null,e==null&&t==null&&r==null?(this.x=0,this.y=0):typeof e=="number"&&typeof t=="number"&&r==null?(this.x=e,this.y=t):e.constructor.name=="Point"&&t==null&&r==null&&(r=e,this.x=r.x,this.y=r.y)}i.prototype.getX=function(){return this.x},i.prototype.getY=function(){return this.y},i.prototype.getLocation=function(){return new i(this.x,this.y)},i.prototype.setLocation=function(e,t,r){e.constructor.name=="Point"&&t==null&&r==null?(r=e,this.setLocation(r.x,r.y)):typeof e=="number"&&typeof t=="number"&&r==null&&(parseInt(e)==e&&parseInt(t)==t?this.move(e,t):(this.x=Math.floor(e+.5),this.y=Math.floor(t+.5)))},i.prototype.move=function(e,t){this.x=e,this.y=t},i.prototype.translate=function(e,t){this.x+=e,this.y+=t},i.prototype.equals=function(e){if(e.constructor.name=="Point"){var t=e;return this.x==t.x&&this.y==t.y}return this==e},i.prototype.toString=function(){return new i().constructor.name+"[x="+this.x+",y="+this.y+"]"},E.exports=i},function(E,A,o){"use strict";function i(e,t,r,l){this.x=0,this.y=0,this.width=0,this.height=0,e!=null&&t!=null&&r!=null&&l!=null&&(this.x=e,this.y=t,this.width=r,this.height=l)}i.prototype.getX=function(){return this.x},i.prototype.setX=function(e){this.x=e},i.prototype.getY=function(){return this.y},i.prototype.setY=function(e){this.y=e},i.prototype.getWidth=function(){return this.width},i.prototype.setWidth=function(e){this.width=e},i.prototype.getHeight=function(){return this.height},i.prototype.setHeight=function(e){this.height=e},i.prototype.getRight=function(){return this.x+this.width},i.prototype.getBottom=function(){return this.y+this.height},i.prototype.intersects=function(e){return!(this.getRight()<e.x||this.getBottom()<e.y||e.getRight()<this.x||e.getBottom()<this.y)},i.prototype.getCenterX=function(){return this.x+this.width/2},i.prototype.getMinX=function(){return this.getX()},i.prototype.getMaxX=function(){return this.getX()+this.width},i.prototype.getCenterY=function(){return this.y+this.height/2},i.prototype.getMinY=function(){return this.getY()},i.prototype.getMaxY=function(){return this.getY()+this.height},i.prototype.getWidthHalf=function(){return this.width/2},i.prototype.getHeightHalf=function(){return this.height/2},E.exports=i},function(E,A,o){"use strict";var i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function e(){}e.lastID=0,e.createID=function(t){return e.isPrimitive(t)?t:(t.uniqueID!=null||(t.uniqueID=e.getString(),e.lastID++),t.uniqueID)},e.getString=function(t){return t==null&&(t=e.lastID),"Object#"+t},e.isPrimitive=function(t){var r=typeof t>"u"?"undefined":i(t);return t==null||r!="object"&&r!="function"},E.exports=e},function(E,A,o){"use strict";function i(u){if(Array.isArray(u)){for(var v=0,L=Array(u.length);v<u.length;v++)L[v]=u[v];return L}else return Array.from(u)}var e=o(0),t=o(6),r=o(3),l=o(1),f=o(5),a=o(4),d=o(17),s=o(27);function c(u){s.call(this),this.layoutQuality=e.QUALITY,this.createBendsAsNeeded=e.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=e.DEFAULT_INCREMENTAL,this.animationOnLayout=e.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=e.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=e.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=e.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new t(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,u!=null&&(this.isRemoteUse=u)}c.RANDOM_SEED=1,c.prototype=Object.create(s.prototype),c.prototype.getGraphManager=function(){return this.graphManager},c.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},c.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},c.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},c.prototype.newGraphManager=function(){var u=new t(this);return this.graphManager=u,u},c.prototype.newGraph=function(u){return new f(null,this.graphManager,u)},c.prototype.newNode=function(u){return new r(this.graphManager,u)},c.prototype.newEdge=function(u){return new l(null,null,u)},c.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},c.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var u;return this.checkLayoutSuccess()?u=!1:u=this.layout(),e.ANIMATE==="during"?!1:(u&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,u)},c.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},c.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var u,v=this.graphManager.getAllEdges(),L=0;L<v.length;L++)u=v[L];for(var T,y=this.graphManager.getRoot().getNodes(),L=0;L<y.length;L++)T=y[L];this.update(this.graphManager.getRoot())}},c.prototype.update=function(u){if(u==null)this.update2();else if(u instanceof r){var v=u;if(v.getChild()!=null)for(var L=v.getChild().getNodes(),T=0;T<L.length;T++)update(L[T]);if(v.vGraphObject!=null){var y=v.vGraphObject;y.update(v)}}else if(u instanceof l){var O=u;if(O.vGraphObject!=null){var D=O.vGraphObject;D.update(O)}}else if(u instanceof f){var R=u;if(R.vGraphObject!=null){var n=R.vGraphObject;n.update(R)}}},c.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=e.QUALITY,this.animationDuringLayout=e.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=e.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=e.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=e.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=e.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=e.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},c.prototype.transform=function(u){if(u==null)this.transform(new a(0,0));else{var v=new d,L=this.graphManager.getRoot().updateLeftTop();if(L!=null){v.setWorldOrgX(u.x),v.setWorldOrgY(u.y),v.setDeviceOrgX(L.x),v.setDeviceOrgY(L.y);for(var T=this.getAllNodes(),y,O=0;O<T.length;O++)y=T[O],y.transform(v)}}},c.prototype.positionNodesRandomly=function(u){if(u==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var v,L,T=u.getNodes(),y=0;y<T.length;y++)v=T[y],L=v.getChild(),L==null||L.getNodes().length==0?v.scatter():(this.positionNodesRandomly(L),v.updateBounds())},c.prototype.getFlatForest=function(){for(var u=[],v=!0,L=this.graphManager.getRoot().getNodes(),T=!0,y=0;y<L.length;y++)L[y].getChild()!=null&&(T=!1);if(!T)return u;var O=new Set,D=[],R=new Map,n=[];for(n=n.concat(L);n.length>0&&v;){for(D.push(n[0]);D.length>0&&v;){var h=D[0];D.splice(0,1),O.add(h);for(var g=h.getEdges(),y=0;y<g.length;y++){var p=g[y].getOtherEnd(h);if(R.get(h)!=p)if(!O.has(p))D.push(p),R.set(p,h);else{v=!1;break}}}if(!v)u=[];else{var N=[].concat(i(O));u.push(N);for(var y=0;y<N.length;y++){var m=N[y],C=n.indexOf(m);C>-1&&n.splice(C,1)}O=new Set,R=new Map}}return u},c.prototype.createDummyNodesForBendpoints=function(u){for(var v=[],L=u.source,T=this.graphManager.calcLowestCommonAncestor(u.source,u.target),y=0;y<u.bendpoints.length;y++){var O=this.newNode(null);O.setRect(new Point(0,0),new Dimension(1,1)),T.add(O);var D=this.newEdge(null);this.graphManager.add(D,L,O),v.add(O),L=O}var D=this.newEdge(null);return this.graphManager.add(D,L,u.target),this.edgeToDummyNodes.set(u,v),u.isInterGraph()?this.graphManager.remove(u):T.remove(u),v},c.prototype.createBendpointsFromDummyNodes=function(){var u=[];u=u.concat(this.graphManager.getAllEdges()),u=[].concat(i(this.edgeToDummyNodes.keys())).concat(u);for(var v=0;v<u.length;v++){var L=u[v];if(L.bendpoints.length>0){for(var T=this.edgeToDummyNodes.get(L),y=0;y<T.length;y++){var O=T[y],D=new a(O.getCenterX(),O.getCenterY()),R=L.bendpoints.get(y);R.x=D.x,R.y=D.y,O.getOwner().remove(O)}this.graphManager.add(L,L.source,L.target)}}},c.transform=function(u,v,L,T){if(L!=null&&T!=null){var y=v;if(u<=50){var O=v/L;y-=(v-O)/50*(50-u)}else{var D=v*T;y+=(D-v)/50*(u-50)}return y}else{var R,n;return u<=50?(R=9*v/500,n=v/10):(R=9*v/50,n=-8*v),R*u+n}},c.findCenterOfTree=function(u){var v=[];v=v.concat(u);var L=[],T=new Map,y=!1,O=null;(v.length==1||v.length==2)&&(y=!0,O=v[0]);for(var D=0;D<v.length;D++){var R=v[D],n=R.getNeighborsList().size;T.set(R,R.getNeighborsList().size),n==1&&L.push(R)}var h=[];for(h=h.concat(L);!y;){var g=[];g=g.concat(h),h=[];for(var D=0;D<v.length;D++){var R=v[D],p=v.indexOf(R);p>=0&&v.splice(p,1);var N=R.getNeighborsList();N.forEach(function(I){if(L.indexOf(I)<0){var x=T.get(I),U=x-1;U==1&&h.push(I),T.set(I,U)}})}L=L.concat(h),(v.length==1||v.length==2)&&(y=!0,O=v[0])}return O},c.prototype.setGraphManager=function(u){this.graphManager=u},E.exports=c},function(E,A,o){"use strict";function i(){}i.seed=1,i.x=0,i.nextDouble=function(){return i.x=Math.sin(i.seed++)*1e4,i.x-Math.floor(i.x)},E.exports=i},function(E,A,o){"use strict";var i=o(4);function e(t,r){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}e.prototype.getWorldOrgX=function(){return this.lworldOrgX},e.prototype.setWorldOrgX=function(t){this.lworldOrgX=t},e.prototype.getWorldOrgY=function(){return this.lworldOrgY},e.prototype.setWorldOrgY=function(t){this.lworldOrgY=t},e.prototype.getWorldExtX=function(){return this.lworldExtX},e.prototype.setWorldExtX=function(t){this.lworldExtX=t},e.prototype.getWorldExtY=function(){return this.lworldExtY},e.prototype.setWorldExtY=function(t){this.lworldExtY=t},e.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},e.prototype.setDeviceOrgX=function(t){this.ldeviceOrgX=t},e.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},e.prototype.setDeviceOrgY=function(t){this.ldeviceOrgY=t},e.prototype.getDeviceExtX=function(){return this.ldeviceExtX},e.prototype.setDeviceExtX=function(t){this.ldeviceExtX=t},e.prototype.getDeviceExtY=function(){return this.ldeviceExtY},e.prototype.setDeviceExtY=function(t){this.ldeviceExtY=t},e.prototype.transformX=function(t){var r=0,l=this.lworldExtX;return l!=0&&(r=this.ldeviceOrgX+(t-this.lworldOrgX)*this.ldeviceExtX/l),r},e.prototype.transformY=function(t){var r=0,l=this.lworldExtY;return l!=0&&(r=this.ldeviceOrgY+(t-this.lworldOrgY)*this.ldeviceExtY/l),r},e.prototype.inverseTransformX=function(t){var r=0,l=this.ldeviceExtX;return l!=0&&(r=this.lworldOrgX+(t-this.ldeviceOrgX)*this.lworldExtX/l),r},e.prototype.inverseTransformY=function(t){var r=0,l=this.ldeviceExtY;return l!=0&&(r=this.lworldOrgY+(t-this.ldeviceOrgY)*this.lworldExtY/l),r},e.prototype.inverseTransformPoint=function(t){var r=new i(this.inverseTransformX(t.x),this.inverseTransformY(t.y));return r},E.exports=e},function(E,A,o){"use strict";function i(s){if(Array.isArray(s)){for(var c=0,u=Array(s.length);c<s.length;c++)u[c]=s[c];return u}else return Array.from(s)}var e=o(15),t=o(7),r=o(0),l=o(8),f=o(9);function a(){e.call(this),this.useSmartIdealEdgeLengthCalculation=t.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=t.DEFAULT_EDGE_LENGTH,this.springConstant=t.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=t.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=t.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=t.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=t.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*t.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=t.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=t.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=t.MAX_ITERATIONS}a.prototype=Object.create(e.prototype);for(var d in e)a[d]=e[d];a.prototype.initParameters=function(){e.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=t.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},a.prototype.calcIdealEdgeLengths=function(){for(var s,c,u,v,L,T,y=this.getGraphManager().getAllEdges(),O=0;O<y.length;O++)s=y[O],s.idealLength=this.idealEdgeLength,s.isInterGraph&&(u=s.getSource(),v=s.getTarget(),L=s.getSourceInLca().getEstimatedSize(),T=s.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(s.idealLength+=L+T-2*r.SIMPLE_NODE_SIZE),c=s.getLca().getInclusionTreeDepth(),s.idealLength+=t.DEFAULT_EDGE_LENGTH*t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(u.getInclusionTreeDepth()+v.getInclusionTreeDepth()-2*c))},a.prototype.initSpringEmbedder=function(){var s=this.getAllNodes().length;this.incremental?(s>t.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*t.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(s-t.ADAPTATION_LOWER_NODE_LIMIT)/(t.ADAPTATION_UPPER_NODE_LIMIT-t.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-t.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=t.MAX_NODE_DISPLACEMENT_INCREMENTAL):(s>t.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(t.COOLING_ADAPTATION_FACTOR,1-(s-t.ADAPTATION_LOWER_NODE_LIMIT)/(t.ADAPTATION_UPPER_NODE_LIMIT-t.ADAPTATION_LOWER_NODE_LIMIT)*(1-t.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=t.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},a.prototype.calcSpringForces=function(){for(var s=this.getAllEdges(),c,u=0;u<s.length;u++)c=s[u],this.calcSpringForce(c,c.idealLength)},a.prototype.calcRepulsionForces=function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,u,v,L,T,y=this.getAllNodes(),O;if(this.useFRGridVariant)for(this.totalIterations%t.GRID_CALCULATION_CHECK_PERIOD==1&&s&&this.updateGrid(),O=new Set,u=0;u<y.length;u++)L=y[u],this.calculateRepulsionForceOfANode(L,O,s,c),O.add(L);else for(u=0;u<y.length;u++)for(L=y[u],v=u+1;v<y.length;v++)T=y[v],L.getOwner()==T.getOwner()&&this.calcRepulsionForce(L,T)},a.prototype.calcGravitationalForces=function(){for(var s,c=this.getAllNodesToApplyGravitation(),u=0;u<c.length;u++)s=c[u],this.calcGravitationalForce(s)},a.prototype.moveNodes=function(){for(var s=this.getAllNodes(),c,u=0;u<s.length;u++)c=s[u],c.move()},a.prototype.calcSpringForce=function(s,c){var u=s.getSource(),v=s.getTarget(),L,T,y,O;if(this.uniformLeafNodeSizes&&u.getChild()==null&&v.getChild()==null)s.updateLengthSimple();else if(s.updateLength(),s.isOverlapingSourceAndTarget)return;L=s.getLength(),L!=0&&(T=this.springConstant*(L-c),y=T*(s.lengthX/L),O=T*(s.lengthY/L),u.springForceX+=y,u.springForceY+=O,v.springForceX-=y,v.springForceY-=O)},a.prototype.calcRepulsionForce=function(s,c){var u=s.getRect(),v=c.getRect(),L=new Array(2),T=new Array(4),y,O,D,R,n,h,g;if(u.intersects(v)){l.calcSeparationAmount(u,v,L,t.DEFAULT_EDGE_LENGTH/2),h=2*L[0],g=2*L[1];var p=s.noOfChildren*c.noOfChildren/(s.noOfChildren+c.noOfChildren);s.repulsionForceX-=p*h,s.repulsionForceY-=p*g,c.repulsionForceX+=p*h,c.repulsionForceY+=p*g}else this.uniformLeafNodeSizes&&s.getChild()==null&&c.getChild()==null?(y=v.getCenterX()-u.getCenterX(),O=v.getCenterY()-u.getCenterY()):(l.getIntersection(u,v,T),y=T[2]-T[0],O=T[3]-T[1]),Math.abs(y)<t.MIN_REPULSION_DIST&&(y=f.sign(y)*t.MIN_REPULSION_DIST),Math.abs(O)<t.MIN_REPULSION_DIST&&(O=f.sign(O)*t.MIN_REPULSION_DIST),D=y*y+O*O,R=Math.sqrt(D),n=this.repulsionConstant*s.noOfChildren*c.noOfChildren/D,h=n*y/R,g=n*O/R,s.repulsionForceX-=h,s.repulsionForceY-=g,c.repulsionForceX+=h,c.repulsionForceY+=g},a.prototype.calcGravitationalForce=function(s){var c,u,v,L,T,y,O,D;c=s.getOwner(),u=(c.getRight()+c.getLeft())/2,v=(c.getTop()+c.getBottom())/2,L=s.getCenterX()-u,T=s.getCenterY()-v,y=Math.abs(L)+s.getWidth()/2,O=Math.abs(T)+s.getHeight()/2,s.getOwner()==this.graphManager.getRoot()?(D=c.getEstimatedSize()*this.gravityRangeFactor,(y>D||O>D)&&(s.gravitationForceX=-this.gravityConstant*L,s.gravitationForceY=-this.gravityConstant*T)):(D=c.getEstimatedSize()*this.compoundGravityRangeFactor,(y>D||O>D)&&(s.gravitationForceX=-this.gravityConstant*L*this.compoundGravityConstant,s.gravitationForceY=-this.gravityConstant*T*this.compoundGravityConstant))},a.prototype.isConverged=function(){var s,c=!1;return this.totalIterations>this.maxIterations/3&&(c=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),s=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,s||c},a.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},a.prototype.calcNoOfChildrenForAllNodes=function(){for(var s,c=this.graphManager.getAllNodes(),u=0;u<c.length;u++)s=c[u],s.noOfChildren=s.getNoOfChildren()},a.prototype.calcGrid=function(s){var c=0,u=0;c=parseInt(Math.ceil((s.getRight()-s.getLeft())/this.repulsionRange)),u=parseInt(Math.ceil((s.getBottom()-s.getTop())/this.repulsionRange));for(var v=new Array(c),L=0;L<c;L++)v[L]=new Array(u);for(var L=0;L<c;L++)for(var T=0;T<u;T++)v[L][T]=new Array;return v},a.prototype.addNodeToGrid=function(s,c,u){var v=0,L=0,T=0,y=0;v=parseInt(Math.floor((s.getRect().x-c)/this.repulsionRange)),L=parseInt(Math.floor((s.getRect().width+s.getRect().x-c)/this.repulsionRange)),T=parseInt(Math.floor((s.getRect().y-u)/this.repulsionRange)),y=parseInt(Math.floor((s.getRect().height+s.getRect().y-u)/this.repulsionRange));for(var O=v;O<=L;O++)for(var D=T;D<=y;D++)this.grid[O][D].push(s),s.setGridCoordinates(v,L,T,y)},a.prototype.updateGrid=function(){var s,c,u=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),s=0;s<u.length;s++)c=u[s],this.addNodeToGrid(c,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},a.prototype.calculateRepulsionForceOfANode=function(s,c,u,v){if(this.totalIterations%t.GRID_CALCULATION_CHECK_PERIOD==1&&u||v){var L=new Set;s.surrounding=new Array;for(var T,y=this.grid,O=s.startX-1;O<s.finishX+2;O++)for(var D=s.startY-1;D<s.finishY+2;D++)if(!(O<0||D<0||O>=y.length||D>=y[0].length)){for(var R=0;R<y[O][D].length;R++)if(T=y[O][D][R],!(s.getOwner()!=T.getOwner()||s==T)&&!c.has(T)&&!L.has(T)){var n=Math.abs(s.getCenterX()-T.getCenterX())-(s.getWidth()/2+T.getWidth()/2),h=Math.abs(s.getCenterY()-T.getCenterY())-(s.getHeight()/2+T.getHeight()/2);n<=this.repulsionRange&&h<=this.repulsionRange&&L.add(T)}}s.surrounding=[].concat(i(L))}for(O=0;O<s.surrounding.length;O++)this.calcRepulsionForce(s,s.surrounding[O])},a.prototype.calcRepulsionRange=function(){return 0},E.exports=a},function(E,A,o){"use strict";var i=o(1),e=o(7);function t(l,f,a){i.call(this,l,f,a),this.idealLength=e.DEFAULT_EDGE_LENGTH}t.prototype=Object.create(i.prototype);for(var r in i)t[r]=i[r];E.exports=t},function(E,A,o){"use strict";var i=o(3);function e(r,l,f,a){i.call(this,r,l,f,a),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}e.prototype=Object.create(i.prototype);for(var t in i)e[t]=i[t];e.prototype.setGridCoordinates=function(r,l,f,a){this.startX=r,this.finishX=l,this.startY=f,this.finishY=a},E.exports=e},function(E,A,o){"use strict";function i(e,t){this.width=0,this.height=0,e!==null&&t!==null&&(this.height=t,this.width=e)}i.prototype.getWidth=function(){return this.width},i.prototype.setWidth=function(e){this.width=e},i.prototype.getHeight=function(){return this.height},i.prototype.setHeight=function(e){this.height=e},E.exports=i},function(E,A,o){"use strict";var i=o(14);function e(){this.map={},this.keys=[]}e.prototype.put=function(t,r){var l=i.createID(t);this.contains(l)||(this.map[l]=r,this.keys.push(t))},e.prototype.contains=function(t){var r=i.createID(t);return this.map[t]!=null},e.prototype.get=function(t){var r=i.createID(t);return this.map[r]},e.prototype.keySet=function(){return this.keys},E.exports=e},function(E,A,o){"use strict";var i=o(14);function e(){this.set={}}e.prototype.add=function(t){var r=i.createID(t);this.contains(r)||(this.set[r]=t)},e.prototype.remove=function(t){delete this.set[i.createID(t)]},e.prototype.clear=function(){this.set={}},e.prototype.contains=function(t){return this.set[i.createID(t)]==t},e.prototype.isEmpty=function(){return this.size()===0},e.prototype.size=function(){return Object.keys(this.set).length},e.prototype.addAllTo=function(t){for(var r=Object.keys(this.set),l=r.length,f=0;f<l;f++)t.push(this.set[r[f]])},e.prototype.size=function(){return Object.keys(this.set).length},e.prototype.addAll=function(t){for(var r=t.length,l=0;l<r;l++){var f=t[l];this.add(f)}},E.exports=e},function(E,A,o){"use strict";var i=function(){function l(f,a){for(var d=0;d<a.length;d++){var s=a[d];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(f,s.key,s)}}return function(f,a,d){return a&&l(f.prototype,a),d&&l(f,d),f}}();function e(l,f){if(!(l instanceof f))throw new TypeError("Cannot call a class as a function")}var t=o(11),r=function(){function l(f,a){e(this,l),(a!==null||a!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var d=void 0;f instanceof t?d=f.size():d=f.length,this._quicksort(f,0,d-1)}return i(l,[{key:"_quicksort",value:function(a,d,s){if(d<s){var c=this._partition(a,d,s);this._quicksort(a,d,c),this._quicksort(a,c+1,s)}}},{key:"_partition",value:function(a,d,s){for(var c=this._get(a,d),u=d,v=s;;){for(;this.compareFunction(c,this._get(a,v));)v--;for(;this.compareFunction(this._get(a,u),c);)u++;if(u<v)this._swap(a,u,v),u++,v--;else return v}}},{key:"_get",value:function(a,d){return a instanceof t?a.get_object_at(d):a[d]}},{key:"_set",value:function(a,d,s){a instanceof t?a.set_object_at(d,s):a[d]=s}},{key:"_swap",value:function(a,d,s){var c=this._get(a,d);this._set(a,d,this._get(a,s)),this._set(a,s,c)}},{key:"_defaultCompareFunction",value:function(a,d){return d>a}}]),l}();E.exports=r},function(E,A,o){"use strict";var i=function(){function r(l,f){for(var a=0;a<f.length;a++){var d=f[a];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(l,d.key,d)}}return function(l,f,a){return f&&r(l.prototype,f),a&&r(l,a),l}}();function e(r,l){if(!(r instanceof l))throw new TypeError("Cannot call a class as a function")}var t=function(){function r(l,f){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,d=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;e(this,r),this.sequence1=l,this.sequence2=f,this.match_score=a,this.mismatch_penalty=d,this.gap_penalty=s,this.iMax=l.length+1,this.jMax=f.length+1,this.grid=new Array(this.iMax);for(var c=0;c<this.iMax;c++){this.grid[c]=new Array(this.jMax);for(var u=0;u<this.jMax;u++)this.grid[c][u]=0}this.tracebackGrid=new Array(this.iMax);for(var v=0;v<this.iMax;v++){this.tracebackGrid[v]=new Array(this.jMax);for(var L=0;L<this.jMax;L++)this.tracebackGrid[v][L]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return i(r,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var f=1;f<this.jMax;f++)this.grid[0][f]=this.grid[0][f-1]+this.gap_penalty,this.tracebackGrid[0][f]=[!1,!1,!0];for(var a=1;a<this.iMax;a++)this.grid[a][0]=this.grid[a-1][0]+this.gap_penalty,this.tracebackGrid[a][0]=[!1,!0,!1];for(var d=1;d<this.iMax;d++)for(var s=1;s<this.jMax;s++){var c=void 0;this.sequence1[d-1]===this.sequence2[s-1]?c=this.grid[d-1][s-1]+this.match_score:c=this.grid[d-1][s-1]+this.mismatch_penalty;var u=this.grid[d-1][s]+this.gap_penalty,v=this.grid[d][s-1]+this.gap_penalty,L=[c,u,v],T=this.arrayAllMaxIndexes(L);this.grid[d][s]=L[T[0]],this.tracebackGrid[d][s]=[T.includes(0),T.includes(1),T.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var f=[];for(f.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});f[0];){var a=f[0],d=this.tracebackGrid[a.pos[0]][a.pos[1]];d[0]&&f.push({pos:[a.pos[0]-1,a.pos[1]-1],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),d[1]&&f.push({pos:[a.pos[0]-1,a.pos[1]],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:"-"+a.seq2}),d[2]&&f.push({pos:[a.pos[0],a.pos[1]-1],seq1:"-"+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),a.pos[0]===0&&a.pos[1]===0&&this.alignments.push({sequence1:a.seq1,sequence2:a.seq2}),f.shift()}return this.alignments}},{key:"getAllIndexes",value:function(f,a){for(var d=[],s=-1;(s=f.indexOf(a,s+1))!==-1;)d.push(s);return d}},{key:"arrayAllMaxIndexes",value:function(f){return this.getAllIndexes(f,Math.max.apply(null,f))}}]),r}();E.exports=t},function(E,A,o){"use strict";var i=function(){};i.FDLayout=o(18),i.FDLayoutConstants=o(7),i.FDLayoutEdge=o(19),i.FDLayoutNode=o(20),i.DimensionD=o(21),i.HashMap=o(22),i.HashSet=o(23),i.IGeometry=o(8),i.IMath=o(9),i.Integer=o(10),i.Point=o(12),i.PointD=o(4),i.RandomSeed=o(16),i.RectangleD=o(13),i.Transform=o(17),i.UniqueIDGeneretor=o(14),i.Quicksort=o(24),i.LinkedList=o(11),i.LGraphObject=o(2),i.LGraph=o(5),i.LEdge=o(1),i.LGraphManager=o(6),i.LNode=o(3),i.Layout=o(15),i.LayoutConstants=o(0),i.NeedlemanWunsch=o(25),E.exports=i},function(E,A,o){"use strict";function i(){this.listeners=[]}var e=i.prototype;e.addListener=function(t,r){this.listeners.push({event:t,callback:r})},e.removeListener=function(t,r){for(var l=this.listeners.length;l>=0;l--){var f=this.listeners[l];f.event===t&&f.callback===r&&this.listeners.splice(l,1)}},e.emit=function(t,r){for(var l=0;l<this.listeners.length;l++){var f=this.listeners[l];t===f.event&&f.callback(r)}},E.exports=i}])})});var pt=lt((rt,ft)=>{(function(A,o){typeof rt=="object"&&typeof ft=="object"?ft.exports=o(ut()):typeof define=="function"&&define.amd?define(["layout-base"],o):typeof rt=="object"?rt.coseBase=o(ut()):A.coseBase=o(A.layoutBase)})(rt,function(E){return function(A){var o={};function i(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return A[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}return i.m=A,i.c=o,i.i=function(e){return e},i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=7)}([function(A,o){A.exports=E},function(A,o,i){"use strict";var e=i(0).FDLayoutConstants;function t(){}for(var r in e)t[r]=e[r];t.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,t.DEFAULT_RADIAL_SEPARATION=e.DEFAULT_EDGE_LENGTH,t.DEFAULT_COMPONENT_SEPERATION=60,t.TILE=!0,t.TILING_PADDING_VERTICAL=10,t.TILING_PADDING_HORIZONTAL=10,t.TREE_REDUCTION_ON_INCREMENTAL=!1,A.exports=t},function(A,o,i){"use strict";var e=i(0).FDLayoutEdge;function t(l,f,a){e.call(this,l,f,a)}t.prototype=Object.create(e.prototype);for(var r in e)t[r]=e[r];A.exports=t},function(A,o,i){"use strict";var e=i(0).LGraph;function t(l,f,a){e.call(this,l,f,a)}t.prototype=Object.create(e.prototype);for(var r in e)t[r]=e[r];A.exports=t},function(A,o,i){"use strict";var e=i(0).LGraphManager;function t(l){e.call(this,l)}t.prototype=Object.create(e.prototype);for(var r in e)t[r]=e[r];A.exports=t},function(A,o,i){"use strict";var e=i(0).FDLayoutNode,t=i(0).IMath;function r(f,a,d,s){e.call(this,f,a,d,s)}r.prototype=Object.create(e.prototype);for(var l in e)r[l]=e[l];r.prototype.move=function(){var f=this.graphManager.getLayout();this.displacementX=f.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=f.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>f.coolingFactor*f.maxNodeDisplacement&&(this.displacementX=f.coolingFactor*f.maxNodeDisplacement*t.sign(this.displacementX)),Math.abs(this.displacementY)>f.coolingFactor*f.maxNodeDisplacement&&(this.displacementY=f.coolingFactor*f.maxNodeDisplacement*t.sign(this.displacementY)),this.child==null?this.moveBy(this.displacementX,this.displacementY):this.child.getNodes().length==0?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),f.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},r.prototype.propogateDisplacementToChildren=function(f,a){for(var d=this.getChild().getNodes(),s,c=0;c<d.length;c++)s=d[c],s.getChild()==null?(s.moveBy(f,a),s.displacementX+=f,s.displacementY+=a):s.propogateDisplacementToChildren(f,a)},r.prototype.setPred1=function(f){this.pred1=f},r.prototype.getPred1=function(){return pred1},r.prototype.getPred2=function(){return pred2},r.prototype.setNext=function(f){this.next=f},r.prototype.getNext=function(){return next},r.prototype.setProcessed=function(f){this.processed=f},r.prototype.isProcessed=function(){return processed},A.exports=r},function(A,o,i){"use strict";var e=i(0).FDLayout,t=i(4),r=i(3),l=i(5),f=i(2),a=i(1),d=i(0).FDLayoutConstants,s=i(0).LayoutConstants,c=i(0).Point,u=i(0).PointD,v=i(0).Layout,L=i(0).Integer,T=i(0).IGeometry,y=i(0).LGraph,O=i(0).Transform;function D(){e.call(this),this.toBeTiled={}}D.prototype=Object.create(e.prototype);for(var R in e)D[R]=e[R];D.prototype.newGraphManager=function(){var n=new t(this);return this.graphManager=n,n},D.prototype.newGraph=function(n){return new r(null,this.graphManager,n)},D.prototype.newNode=function(n){return new l(this.graphManager,n)},D.prototype.newEdge=function(n){return new f(null,null,n)},D.prototype.initParameters=function(){e.prototype.initParameters.call(this,arguments),this.isSubLayout||(a.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=a.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=a.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=d.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=d.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=d.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=d.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=d.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=d.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/d.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=d.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},D.prototype.layout=function(){var n=s.DEFAULT_CREATE_BENDS_AS_NEEDED;return n&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},D.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(a.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var h=new Set(this.getAllNodes()),g=this.nodesWithGravity.filter(function(m){return h.has(m)});this.graphManager.setAllNodesToApplyGravitation(g)}}else{var n=this.getFlatForest();if(n.length>0)this.positionNodesRadially(n);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var h=new Set(this.getAllNodes()),g=this.nodesWithGravity.filter(function(p){return h.has(p)});this.graphManager.setAllNodesToApplyGravitation(g),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},D.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%d.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var n=new Set(this.getAllNodes()),h=this.nodesWithGravity.filter(function(N){return n.has(N)});this.graphManager.setAllNodesToApplyGravitation(h),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var g=!this.isTreeGrowing&&!this.isGrowthFinished,p=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(g,p),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},D.prototype.getPositionsData=function(){for(var n=this.graphManager.getAllNodes(),h={},g=0;g<n.length;g++){var p=n[g].rect,N=n[g].id;h[N]={id:N,x:p.getCenterX(),y:p.getCenterY(),w:p.width,h:p.height}}return h},D.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var n=!1;if(d.ANIMATE==="during")this.emit("layoutstarted");else{for(;!n;)n=this.tick();this.graphManager.updateBounds()}},D.prototype.calculateNodesToApplyGravitationTo=function(){var n=[],h,g=this.graphManager.getGraphs(),p=g.length,N;for(N=0;N<p;N++)h=g[N],h.updateConnected(),h.isConnected||(n=n.concat(h.getNodes()));return n},D.prototype.createBendpoints=function(){var n=[];n=n.concat(this.graphManager.getAllEdges());var h=new Set,g;for(g=0;g<n.length;g++){var p=n[g];if(!h.has(p)){var N=p.getSource(),m=p.getTarget();if(N==m)p.getBendpoints().push(new u),p.getBendpoints().push(new u),this.createDummyNodesForBendpoints(p),h.add(p);else{var C=[];if(C=C.concat(N.getEdgeListToNode(m)),C=C.concat(m.getEdgeListToNode(N)),!h.has(C[0])){if(C.length>1){var I;for(I=0;I<C.length;I++){var x=C[I];x.getBendpoints().push(new u),this.createDummyNodesForBendpoints(x)}}C.forEach(function(U){h.add(U)})}}}if(h.size==n.length)break}},D.prototype.positionNodesRadially=function(n){for(var h=new c(0,0),g=Math.ceil(Math.sqrt(n.length)),p=0,N=0,m=0,C=new u(0,0),I=0;I<n.length;I++){I%g==0&&(m=0,N=p,I!=0&&(N+=a.DEFAULT_COMPONENT_SEPERATION),p=0);var x=n[I],U=v.findCenterOfTree(x);h.x=m,h.y=N,C=D.radialLayout(x,U,h),C.y>p&&(p=Math.floor(C.y)),m=Math.floor(C.x+a.DEFAULT_COMPONENT_SEPERATION)}this.transform(new u(s.WORLD_CENTER_X-C.x/2,s.WORLD_CENTER_Y-C.y/2))},D.radialLayout=function(n,h,g){var p=Math.max(this.maxDiagonalInTree(n),a.DEFAULT_RADIAL_SEPARATION);D.branchRadialLayout(h,null,0,359,0,p);var N=y.calculateBounds(n),m=new O;m.setDeviceOrgX(N.getMinX()),m.setDeviceOrgY(N.getMinY()),m.setWorldOrgX(g.x),m.setWorldOrgY(g.y);for(var C=0;C<n.length;C++){var I=n[C];I.transform(m)}var x=new u(N.getMaxX(),N.getMaxY());return m.inverseTransformPoint(x)},D.branchRadialLayout=function(n,h,g,p,N,m){var C=(p-g+1)/2;C<0&&(C+=180);var I=(C+g)%360,x=I*T.TWO_PI/360,U=Math.cos(x),P=N*Math.cos(x),_=N*Math.sin(x);n.setCenter(P,_);var M=[];M=M.concat(n.getEdges());var G=M.length;h!=null&&G--;for(var S=0,F=M.length,X,b=n.getEdgesBetween(h);b.length>1;){var H=b[0];b.splice(0,1);var z=M.indexOf(H);z>=0&&M.splice(z,1),F--,G--}h!=null?X=(M.indexOf(b[0])+1)%F:X=0;for(var B=Math.abs(p-g)/G,Y=X;S!=G;Y=++Y%F){var K=M[Y].getOtherEnd(n);if(K!=h){var Z=(g+S*B)%360,$=(Z+B)%360;D.branchRadialLayout(K,n,Z,$,N+m,m),S++}}},D.maxDiagonalInTree=function(n){for(var h=L.MIN_VALUE,g=0;g<n.length;g++){var p=n[g],N=p.getDiagonal();N>h&&(h=N)}return h},D.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},D.prototype.groupZeroDegreeMembers=function(){var n=this,h={};this.memberGroups={},this.idToDummyNode={};for(var g=[],p=this.graphManager.getAllNodes(),N=0;N<p.length;N++){var m=p[N],C=m.getParent();this.getNodeDegreeWithChildren(m)===0&&(C.id==null||!this.getToBeTiled(C))&&g.push(m)}for(var N=0;N<g.length;N++){var m=g[N],I=m.getParent().id;typeof h[I]>"u"&&(h[I]=[]),h[I]=h[I].concat(m)}Object.keys(h).forEach(function(x){if(h[x].length>1){var U="DummyCompound_"+x;n.memberGroups[U]=h[x];var P=h[x][0].getParent(),_=new l(n.graphManager);_.id=U,_.paddingLeft=P.paddingLeft||0,_.paddingRight=P.paddingRight||0,_.paddingBottom=P.paddingBottom||0,_.paddingTop=P.paddingTop||0,n.idToDummyNode[U]=_;var M=n.getGraphManager().add(n.newGraph(),_),G=P.getChild();G.add(_);for(var S=0;S<h[x].length;S++){var F=h[x][S];G.remove(F),M.add(F)}}})},D.prototype.clearCompounds=function(){var n={},h={};this.performDFSOnCompounds();for(var g=0;g<this.compoundOrder.length;g++)h[this.compoundOrder[g].id]=this.compoundOrder[g],n[this.compoundOrder[g].id]=[].concat(this.compoundOrder[g].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[g].getChild()),this.compoundOrder[g].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(n,h)},D.prototype.clearZeroDegreeMembers=function(){var n=this,h=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(g){var p=n.idToDummyNode[g];h[g]=n.tileNodes(n.memberGroups[g],p.paddingLeft+p.paddingRight),p.rect.width=h[g].width,p.rect.height=h[g].height})},D.prototype.repopulateCompounds=function(){for(var n=this.compoundOrder.length-1;n>=0;n--){var h=this.compoundOrder[n],g=h.id,p=h.paddingLeft,N=h.paddingTop;this.adjustLocations(this.tiledMemberPack[g],h.rect.x,h.rect.y,p,N)}},D.prototype.repopulateZeroDegreeMembers=function(){var n=this,h=this.tiledZeroDegreePack;Object.keys(h).forEach(function(g){var p=n.idToDummyNode[g],N=p.paddingLeft,m=p.paddingTop;n.adjustLocations(h[g],p.rect.x,p.rect.y,N,m)})},D.prototype.getToBeTiled=function(n){var h=n.id;if(this.toBeTiled[h]!=null)return this.toBeTiled[h];var g=n.getChild();if(g==null)return this.toBeTiled[h]=!1,!1;for(var p=g.getNodes(),N=0;N<p.length;N++){var m=p[N];if(this.getNodeDegree(m)>0)return this.toBeTiled[h]=!1,!1;if(m.getChild()==null){this.toBeTiled[m.id]=!1;continue}if(!this.getToBeTiled(m))return this.toBeTiled[h]=!1,!1}return this.toBeTiled[h]=!0,!0},D.prototype.getNodeDegree=function(n){for(var h=n.id,g=n.getEdges(),p=0,N=0;N<g.length;N++){var m=g[N];m.getSource().id!==m.getTarget().id&&(p=p+1)}return p},D.prototype.getNodeDegreeWithChildren=function(n){var h=this.getNodeDegree(n);if(n.getChild()==null)return h;for(var g=n.getChild().getNodes(),p=0;p<g.length;p++){var N=g[p];h+=this.getNodeDegreeWithChildren(N)}return h},D.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},D.prototype.fillCompexOrderByDFS=function(n){for(var h=0;h<n.length;h++){var g=n[h];g.getChild()!=null&&this.fillCompexOrderByDFS(g.getChild().getNodes()),this.getToBeTiled(g)&&this.compoundOrder.push(g)}},D.prototype.adjustLocations=function(n,h,g,p,N){h+=p,g+=N;for(var m=h,C=0;C<n.rows.length;C++){var I=n.rows[C];h=m;for(var x=0,U=0;U<I.length;U++){var P=I[U];P.rect.x=h,P.rect.y=g,h+=P.rect.width+n.horizontalPadding,P.rect.height>x&&(x=P.rect.height)}g+=x+n.verticalPadding}},D.prototype.tileCompoundMembers=function(n,h){var g=this;this.tiledMemberPack=[],Object.keys(n).forEach(function(p){var N=h[p];g.tiledMemberPack[p]=g.tileNodes(n[p],N.paddingLeft+N.paddingRight),N.rect.width=g.tiledMemberPack[p].width,N.rect.height=g.tiledMemberPack[p].height})},D.prototype.tileNodes=function(n,h){var g=a.TILING_PADDING_VERTICAL,p=a.TILING_PADDING_HORIZONTAL,N={rows:[],rowWidth:[],rowHeight:[],width:0,height:h,verticalPadding:g,horizontalPadding:p};n.sort(function(I,x){return I.rect.width*I.rect.height>x.rect.width*x.rect.height?-1:I.rect.width*I.rect.height<x.rect.width*x.rect.height?1:0});for(var m=0;m<n.length;m++){var C=n[m];N.rows.length==0?this.insertNodeToRow(N,C,0,h):this.canAddHorizontal(N,C.rect.width,C.rect.height)?this.insertNodeToRow(N,C,this.getShortestRowIndex(N),h):this.insertNodeToRow(N,C,N.rows.length,h),this.shiftToLastRow(N)}return N},D.prototype.insertNodeToRow=function(n,h,g,p){var N=p;if(g==n.rows.length){var m=[];n.rows.push(m),n.rowWidth.push(N),n.rowHeight.push(0)}var C=n.rowWidth[g]+h.rect.width;n.rows[g].length>0&&(C+=n.horizontalPadding),n.rowWidth[g]=C,n.width<C&&(n.width=C);var I=h.rect.height;g>0&&(I+=n.verticalPadding);var x=0;I>n.rowHeight[g]&&(x=n.rowHeight[g],n.rowHeight[g]=I,x=n.rowHeight[g]-x),n.height+=x,n.rows[g].push(h)},D.prototype.getShortestRowIndex=function(n){for(var h=-1,g=Number.MAX_VALUE,p=0;p<n.rows.length;p++)n.rowWidth[p]<g&&(h=p,g=n.rowWidth[p]);return h},D.prototype.getLongestRowIndex=function(n){for(var h=-1,g=Number.MIN_VALUE,p=0;p<n.rows.length;p++)n.rowWidth[p]>g&&(h=p,g=n.rowWidth[p]);return h},D.prototype.canAddHorizontal=function(n,h,g){var p=this.getShortestRowIndex(n);if(p<0)return!0;var N=n.rowWidth[p];if(N+n.horizontalPadding+h<=n.width)return!0;var m=0;n.rowHeight[p]<g&&p>0&&(m=g+n.verticalPadding-n.rowHeight[p]);var C;n.width-N>=h+n.horizontalPadding?C=(n.height+m)/(N+h+n.horizontalPadding):C=(n.height+m)/n.width,m=g+n.verticalPadding;var I;return n.width<h?I=(n.height+m)/h:I=(n.height+m)/n.width,I<1&&(I=1/I),C<1&&(C=1/C),C<I},D.prototype.shiftToLastRow=function(n){var h=this.getLongestRowIndex(n),g=n.rowWidth.length-1,p=n.rows[h],N=p[p.length-1],m=N.width+n.horizontalPadding;if(n.width-n.rowWidth[g]>m&&h!=g){p.splice(-1,1),n.rows[g].push(N),n.rowWidth[h]=n.rowWidth[h]-m,n.rowWidth[g]=n.rowWidth[g]+m,n.width=n.rowWidth[instance.getLongestRowIndex(n)];for(var C=Number.MIN_VALUE,I=0;I<p.length;I++)p[I].height>C&&(C=p[I].height);h>0&&(C+=n.verticalPadding);var x=n.rowHeight[h]+n.rowHeight[g];n.rowHeight[h]=C,n.rowHeight[g]<N.height+n.verticalPadding&&(n.rowHeight[g]=N.height+n.verticalPadding);var U=n.rowHeight[h]+n.rowHeight[g];n.height+=U-x,this.shiftToLastRow(n)}},D.prototype.tilingPreLayout=function(){a.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},D.prototype.tilingPostLayout=function(){a.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},D.prototype.reduceTrees=function(){for(var n=[],h=!0,g;h;){var p=this.graphManager.getAllNodes(),N=[];h=!1;for(var m=0;m<p.length;m++)g=p[m],g.getEdges().length==1&&!g.getEdges()[0].isInterGraph&&g.getChild()==null&&(N.push([g,g.getEdges()[0],g.getOwner()]),h=!0);if(h==!0){for(var C=[],I=0;I<N.length;I++)N[I][0].getEdges().length==1&&(C.push(N[I]),N[I][0].getOwner().remove(N[I][0]));n.push(C),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=n},D.prototype.growTree=function(n){for(var h=n.length,g=n[h-1],p,N=0;N<g.length;N++)p=g[N],this.findPlaceforPrunedNode(p),p[2].add(p[0]),p[2].add(p[1],p[1].source,p[1].target);n.splice(n.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},D.prototype.findPlaceforPrunedNode=function(n){var h,g,p=n[0];p==n[1].source?g=n[1].target:g=n[1].source;var N=g.startX,m=g.finishX,C=g.startY,I=g.finishY,x=0,U=0,P=0,_=0,M=[x,P,U,_];if(C>0)for(var G=N;G<=m;G++)M[0]+=this.grid[G][C-1].length+this.grid[G][C].length-1;if(m<this.grid.length-1)for(var G=C;G<=I;G++)M[1]+=this.grid[m+1][G].length+this.grid[m][G].length-1;if(I<this.grid[0].length-1)for(var G=N;G<=m;G++)M[2]+=this.grid[G][I+1].length+this.grid[G][I].length-1;if(N>0)for(var G=C;G<=I;G++)M[3]+=this.grid[N-1][G].length+this.grid[N][G].length-1;for(var S=L.MAX_VALUE,F,X,b=0;b<M.length;b++)M[b]<S?(S=M[b],F=1,X=b):M[b]==S&&F++;if(F==3&&S==0)M[0]==0&&M[1]==0&&M[2]==0?h=1:M[0]==0&&M[1]==0&&M[3]==0?h=0:M[0]==0&&M[2]==0&&M[3]==0?h=3:M[1]==0&&M[2]==0&&M[3]==0&&(h=2);else if(F==2&&S==0){var H=Math.floor(Math.random()*2);M[0]==0&&M[1]==0?H==0?h=0:h=1:M[0]==0&&M[2]==0?H==0?h=0:h=2:M[0]==0&&M[3]==0?H==0?h=0:h=3:M[1]==0&&M[2]==0?H==0?h=1:h=2:M[1]==0&&M[3]==0?H==0?h=1:h=3:H==0?h=2:h=3}else if(F==4&&S==0){var H=Math.floor(Math.random()*4);h=H}else h=X;h==0?p.setCenter(g.getCenterX(),g.getCenterY()-g.getHeight()/2-d.DEFAULT_EDGE_LENGTH-p.getHeight()/2):h==1?p.setCenter(g.getCenterX()+g.getWidth()/2+d.DEFAULT_EDGE_LENGTH+p.getWidth()/2,g.getCenterY()):h==2?p.setCenter(g.getCenterX(),g.getCenterY()+g.getHeight()/2+d.DEFAULT_EDGE_LENGTH+p.getHeight()/2):p.setCenter(g.getCenterX()-g.getWidth()/2-d.DEFAULT_EDGE_LENGTH-p.getWidth()/2,g.getCenterY())},A.exports=D},function(A,o,i){"use strict";var e={};e.layoutBase=i(0),e.CoSEConstants=i(1),e.CoSEEdge=i(2),e.CoSEGraph=i(3),e.CoSEGraphManager=i(4),e.CoSELayout=i(6),e.CoSENode=i(5),A.exports=e}])})});var Mt=lt((it,dt)=>{(function(A,o){typeof it=="object"&&typeof dt=="object"?dt.exports=o(pt()):typeof define=="function"&&define.amd?define(["cose-base"],o):typeof it=="object"?it.cytoscapeCoseBilkent=o(pt()):A.cytoscapeCoseBilkent=o(A.coseBase)})(it,function(E){return function(A){var o={};function i(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return A[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}return i.m=A,i.c=o,i.i=function(e){return e},i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=1)}([function(A,o){A.exports=E},function(A,o,i){"use strict";var e=i(0).layoutBase.LayoutConstants,t=i(0).layoutBase.FDLayoutConstants,r=i(0).CoSEConstants,l=i(0).CoSELayout,f=i(0).CoSENode,a=i(0).layoutBase.PointD,d=i(0).layoutBase.DimensionD,s={ready:function(){},stop:function(){},quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function c(T,y){var O={};for(var D in T)O[D]=T[D];for(var D in y)O[D]=y[D];return O}function u(T){this.options=c(s,T),v(this.options)}var v=function(y){y.nodeRepulsion!=null&&(r.DEFAULT_REPULSION_STRENGTH=t.DEFAULT_REPULSION_STRENGTH=y.nodeRepulsion),y.idealEdgeLength!=null&&(r.DEFAULT_EDGE_LENGTH=t.DEFAULT_EDGE_LENGTH=y.idealEdgeLength),y.edgeElasticity!=null&&(r.DEFAULT_SPRING_STRENGTH=t.DEFAULT_SPRING_STRENGTH=y.edgeElasticity),y.nestingFactor!=null&&(r.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=y.nestingFactor),y.gravity!=null&&(r.DEFAULT_GRAVITY_STRENGTH=t.DEFAULT_GRAVITY_STRENGTH=y.gravity),y.numIter!=null&&(r.MAX_ITERATIONS=t.MAX_ITERATIONS=y.numIter),y.gravityRange!=null&&(r.DEFAULT_GRAVITY_RANGE_FACTOR=t.DEFAULT_GRAVITY_RANGE_FACTOR=y.gravityRange),y.gravityCompound!=null&&(r.DEFAULT_COMPOUND_GRAVITY_STRENGTH=t.DEFAULT_COMPOUND_GRAVITY_STRENGTH=y.gravityCompound),y.gravityRangeCompound!=null&&(r.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=y.gravityRangeCompound),y.initialEnergyOnIncremental!=null&&(r.DEFAULT_COOLING_FACTOR_INCREMENTAL=t.DEFAULT_COOLING_FACTOR_INCREMENTAL=y.initialEnergyOnIncremental),y.quality=="draft"?e.QUALITY=0:y.quality=="proof"?e.QUALITY=2:e.QUALITY=1,r.NODE_DIMENSIONS_INCLUDE_LABELS=t.NODE_DIMENSIONS_INCLUDE_LABELS=e.NODE_DIMENSIONS_INCLUDE_LABELS=y.nodeDimensionsIncludeLabels,r.DEFAULT_INCREMENTAL=t.DEFAULT_INCREMENTAL=e.DEFAULT_INCREMENTAL=!y.randomize,r.ANIMATE=t.ANIMATE=e.ANIMATE=y.animate,r.TILE=y.tile,r.TILING_PADDING_VERTICAL=typeof y.tilingPaddingVertical=="function"?y.tilingPaddingVertical.call():y.tilingPaddingVertical,r.TILING_PADDING_HORIZONTAL=typeof y.tilingPaddingHorizontal=="function"?y.tilingPaddingHorizontal.call():y.tilingPaddingHorizontal};u.prototype.run=function(){var T,y,O=this.options,D=this.idToLNode={},R=this.layout=new l,n=this;n.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var h=R.newGraphManager();this.gm=h;var g=this.options.eles.nodes(),p=this.options.eles.edges();this.root=h.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(g),R);for(var N=0;N<p.length;N++){var m=p[N],C=this.idToLNode[m.data("source")],I=this.idToLNode[m.data("target")];if(C!==I&&C.getEdgesBetween(I).length==0){var x=h.add(R.newEdge(),C,I);x.id=m.id()}}var U=function(M,G){typeof M=="number"&&(M=G);var S=M.data("id"),F=n.idToLNode[S];return{x:F.getRect().getCenterX(),y:F.getRect().getCenterY()}},P=function _(){for(var M=function(){O.fit&&O.cy.fit(O.eles,O.padding),T||(T=!0,n.cy.one("layoutready",O.ready),n.cy.trigger({type:"layoutready",layout:n}))},G=n.options.refresh,S,F=0;F<G&&!S;F++)S=n.stopped||n.layout.tick();if(S){R.checkLayoutSuccess()&&!R.isSubLayout&&R.doPostLayout(),R.tilingPostLayout&&R.tilingPostLayout(),R.isLayoutFinished=!0,n.options.eles.nodes().positions(U),M(),n.cy.one("layoutstop",n.options.stop),n.cy.trigger({type:"layoutstop",layout:n}),y&&cancelAnimationFrame(y),T=!1;return}var X=n.layout.getPositionsData();O.eles.nodes().positions(function(b,H){if(typeof b=="number"&&(b=H),!b.isParent()){for(var z=b.id(),B=X[z],Y=b;B==null&&(B=X[Y.data("parent")]||X["DummyCompound_"+Y.data("parent")],X[z]=B,Y=Y.parent()[0],Y!=null););return B!=null?{x:B.x,y:B.y}:{x:b.position("x"),y:b.position("y")}}}),M(),y=requestAnimationFrame(_)};return R.addListener("layoutstarted",function(){n.options.animate==="during"&&(y=requestAnimationFrame(P))}),R.runLayout(),this.options.animate!=="during"&&(n.options.eles.nodes().not(":parent").layoutPositions(n,n.options,U),T=!1),this},u.prototype.getTopMostNodes=function(T){for(var y={},O=0;O<T.length;O++)y[T[O].id()]=!0;var D=T.filter(function(R,n){typeof R=="number"&&(R=n);for(var h=R.parent()[0];h!=null;){if(y[h.id()])return!1;h=h.parent()[0]}return!0});return D},u.prototype.processChildrenList=function(T,y,O){for(var D=y.length,R=0;R<D;R++){var n=y[R],h=n.children(),g,p=n.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if(n.outerWidth()!=null&&n.outerHeight()!=null?g=T.add(new f(O.graphManager,new a(n.position("x")-p.w/2,n.position("y")-p.h/2),new d(parseFloat(p.w),parseFloat(p.h)))):g=T.add(new f(this.graphManager)),g.id=n.data("id"),g.paddingLeft=parseInt(n.css("padding")),g.paddingTop=parseInt(n.css("padding")),g.paddingRight=parseInt(n.css("padding")),g.paddingBottom=parseInt(n.css("padding")),this.options.nodeDimensionsIncludeLabels&&n.isParent()){var N=n.boundingBox({includeLabels:!0,includeNodes:!1}).w,m=n.boundingBox({includeLabels:!0,includeNodes:!1}).h,C=n.css("text-halign");g.labelWidth=N,g.labelHeight=m,g.labelPos=C}if(this.idToLNode[n.data("id")]=g,isNaN(g.rect.x)&&(g.rect.x=0),isNaN(g.rect.y)&&(g.rect.y=0),h!=null&&h.length>0){var I;I=O.getGraphManager().add(O.newGraph(),g),this.processChildrenList(I,h,O)}}},u.prototype.stop=function(){return this.stopped=!0,this};var L=function(y){y("layout","cose-bilkent",u)};typeof cytoscape<"u"&&L(cytoscape),A.exports=L}])})});var xt=bt(Mt(),1);var vt=function(){var E=w(function(R,n,h,g){for(h=h||{},g=R.length;g--;h[R[g]]=n);return h},"o"),A=[1,4],o=[1,13],i=[1,12],e=[1,15],t=[1,16],r=[1,20],l=[1,19],f=[6,7,8],a=[1,26],d=[1,24],s=[1,25],c=[6,7,11],u=[1,6,13,15,16,19,22],v=[1,33],L=[1,34],T=[1,6,7,11,13,15,16,19,22],y={trace:w(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:w(function(n,h,g,p,N,m,C){var I=m.length-1;switch(N){case 6:case 7:return p;case 8:p.getLogger().trace("Stop NL ");break;case 9:p.getLogger().trace("Stop EOF ");break;case 11:p.getLogger().trace("Stop NL2 ");break;case 12:p.getLogger().trace("Stop EOF2 ");break;case 15:p.getLogger().info("Node: ",m[I].id),p.addNode(m[I-1].length,m[I].id,m[I].descr,m[I].type);break;case 16:p.getLogger().trace("Icon: ",m[I]),p.decorateNode({icon:m[I]});break;case 17:case 21:p.decorateNode({class:m[I]});break;case 18:p.getLogger().trace("SPACELIST");break;case 19:p.getLogger().trace("Node: ",m[I].id),p.addNode(0,m[I].id,m[I].descr,m[I].type);break;case 20:p.decorateNode({icon:m[I]});break;case 25:p.getLogger().trace("node found ..",m[I-2]),this.$={id:m[I-1],descr:m[I-1],type:p.getType(m[I-2],m[I])};break;case 26:this.$={id:m[I],descr:m[I],type:p.nodeType.DEFAULT};break;case 27:p.getLogger().trace("node found ..",m[I-3]),this.$={id:m[I-3],descr:m[I-1],type:p.getType(m[I-2],m[I])};break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:A},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:A},{6:o,7:[1,10],9:9,12:11,13:i,14:14,15:e,16:t,17:17,18:18,19:r,22:l},E(f,[2,3]),{1:[2,2]},E(f,[2,4]),E(f,[2,5]),{1:[2,6],6:o,12:21,13:i,14:14,15:e,16:t,17:17,18:18,19:r,22:l},{6:o,9:22,12:11,13:i,14:14,15:e,16:t,17:17,18:18,19:r,22:l},{6:a,7:d,10:23,11:s},E(c,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:r,22:l}),E(c,[2,18]),E(c,[2,19]),E(c,[2,20]),E(c,[2,21]),E(c,[2,23]),E(c,[2,24]),E(c,[2,26],{19:[1,30]}),{20:[1,31]},{6:a,7:d,10:32,11:s},{1:[2,7],6:o,12:21,13:i,14:14,15:e,16:t,17:17,18:18,19:r,22:l},E(u,[2,14],{7:v,11:L}),E(T,[2,8]),E(T,[2,9]),E(T,[2,10]),E(c,[2,15]),E(c,[2,16]),E(c,[2,17]),{20:[1,35]},{21:[1,36]},E(u,[2,13],{7:v,11:L}),E(T,[2,11]),E(T,[2,12]),{21:[1,37]},E(c,[2,25]),E(c,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:w(function(n,h){if(h.recoverable)this.trace(n);else{var g=new Error(n);throw g.hash=h,g}},"parseError"),parse:w(function(n){var h=this,g=[0],p=[],N=[null],m=[],C=this.table,I="",x=0,U=0,P=0,_=2,M=1,G=m.slice.call(arguments,1),S=Object.create(this.lexer),F={yy:{}};for(var X in this.yy)Object.prototype.hasOwnProperty.call(this.yy,X)&&(F.yy[X]=this.yy[X]);S.setInput(n,F.yy),F.yy.lexer=S,F.yy.parser=this,typeof S.yylloc>"u"&&(S.yylloc={});var b=S.yylloc;m.push(b);var H=S.options&&S.options.ranges;typeof F.yy.parseError=="function"?this.parseError=F.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function z(W){g.length=g.length-2*W,N.length=N.length-W,m.length=m.length-W}w(z,"popStack");function B(){var W;return W=p.pop()||S.lex()||M,typeof W!="number"&&(W instanceof Array&&(p=W,W=p.pop()),W=h.symbols_[W]||W),W}w(B,"lex");for(var Y,K,Z,$,ge,ot,J={},nt,j,Tt,st;;){if(Z=g[g.length-1],this.defaultActions[Z]?$=this.defaultActions[Z]:((Y===null||typeof Y>"u")&&(Y=B()),$=C[Z]&&C[Z][Y]),typeof $>"u"||!$.length||!$[0]){var ht="";st=[];for(nt in C[Z])this.terminals_[nt]&&nt>_&&st.push("'"+this.terminals_[nt]+"'");S.showPosition?ht="Parse error on line "+(x+1)+`:
`+S.showPosition()+`
Expecting `+st.join(", ")+", got '"+(this.terminals_[Y]||Y)+"'":ht="Parse error on line "+(x+1)+": Unexpected "+(Y==M?"end of input":"'"+(this.terminals_[Y]||Y)+"'"),this.parseError(ht,{text:S.match,token:this.terminals_[Y]||Y,line:S.yylineno,loc:b,expected:st})}if($[0]instanceof Array&&$.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Z+", token: "+Y);switch($[0]){case 1:g.push(Y),N.push(S.yytext),m.push(S.yylloc),g.push($[1]),Y=null,K?(Y=K,K=null):(U=S.yyleng,I=S.yytext,x=S.yylineno,b=S.yylloc,P>0&&P--);break;case 2:if(j=this.productions_[$[1]][1],J.$=N[N.length-j],J._$={first_line:m[m.length-(j||1)].first_line,last_line:m[m.length-1].last_line,first_column:m[m.length-(j||1)].first_column,last_column:m[m.length-1].last_column},H&&(J._$.range=[m[m.length-(j||1)].range[0],m[m.length-1].range[1]]),ot=this.performAction.apply(J,[I,U,x,F.yy,$[1],N,m].concat(G)),typeof ot<"u")return ot;j&&(g=g.slice(0,-1*j*2),N=N.slice(0,-1*j),m=m.slice(0,-1*j)),g.push(this.productions_[$[1]][0]),N.push(J.$),m.push(J._$),Tt=C[g[g.length-2]][g[g.length-1]],g.push(Tt);break;case 3:return!0}}return!0},"parse")},O=function(){var R={EOF:1,parseError:w(function(h,g){if(this.yy.parser)this.yy.parser.parseError(h,g);else throw new Error(h)},"parseError"),setInput:w(function(n,h){return this.yy=h||this.yy||{},this._input=n,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:w(function(){var n=this._input[0];this.yytext+=n,this.yyleng++,this.offset++,this.match+=n,this.matched+=n;var h=n.match(/(?:\r\n?|\n).*/g);return h?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),n},"input"),unput:w(function(n){var h=n.length,g=n.split(/(?:\r\n?|\n)/g);this._input=n+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-h),this.offset-=h;var p=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),g.length-1&&(this.yylineno-=g.length-1);var N=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:g?(g.length===p.length?this.yylloc.first_column:0)+p[p.length-g.length].length-g[0].length:this.yylloc.first_column-h},this.options.ranges&&(this.yylloc.range=[N[0],N[0]+this.yyleng-h]),this.yyleng=this.yytext.length,this},"unput"),more:w(function(){return this._more=!0,this},"more"),reject:w(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:w(function(n){this.unput(this.match.slice(n))},"less"),pastInput:w(function(){var n=this.matched.substr(0,this.matched.length-this.match.length);return(n.length>20?"...":"")+n.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:w(function(){var n=this.match;return n.length<20&&(n+=this._input.substr(0,20-n.length)),(n.substr(0,20)+(n.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:w(function(){var n=this.pastInput(),h=new Array(n.length+1).join("-");return n+this.upcomingInput()+`
`+h+"^"},"showPosition"),test_match:w(function(n,h){var g,p,N;if(this.options.backtrack_lexer&&(N={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(N.yylloc.range=this.yylloc.range.slice(0))),p=n[0].match(/(?:\r\n?|\n).*/g),p&&(this.yylineno+=p.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:p?p[p.length-1].length-p[p.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+n[0].length},this.yytext+=n[0],this.match+=n[0],this.matches=n,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(n[0].length),this.matched+=n[0],g=this.performAction.call(this,this.yy,this,h,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),g)return g;if(this._backtrack){for(var m in N)this[m]=N[m];return!1}return!1},"test_match"),next:w(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var n,h,g,p;this._more||(this.yytext="",this.match="");for(var N=this._currentRules(),m=0;m<N.length;m++)if(g=this._input.match(this.rules[N[m]]),g&&(!h||g[0].length>h[0].length)){if(h=g,p=m,this.options.backtrack_lexer){if(n=this.test_match(g,N[m]),n!==!1)return n;if(this._backtrack){h=!1;continue}else return!1}else if(!this.options.flex)break}return h?(n=this.test_match(h,N[p]),n!==!1?n:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:w(function(){var h=this.next();return h||this.lex()},"lex"),begin:w(function(h){this.conditionStack.push(h)},"begin"),popState:w(function(){var h=this.conditionStack.length-1;return h>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:w(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:w(function(h){return h=this.conditionStack.length-1-Math.abs(h||0),h>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:w(function(h){this.begin(h)},"pushState"),stateStackSize:w(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:w(function(h,g,p,N){var m=N;switch(p){case 0:return h.getLogger().trace("Found comment",g.yytext),6;break;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;break;case 4:this.popState();break;case 5:h.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return h.getLogger().trace("SPACELINE"),6;break;case 7:return 7;case 8:return 15;case 9:h.getLogger().trace("end icon"),this.popState();break;case 10:return h.getLogger().trace("Exploding node"),this.begin("NODE"),19;break;case 11:return h.getLogger().trace("Cloud"),this.begin("NODE"),19;break;case 12:return h.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;break;case 13:return h.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;break;case 14:return this.begin("NODE"),19;break;case 15:return this.begin("NODE"),19;break;case 16:return this.begin("NODE"),19;break;case 17:return this.begin("NODE"),19;break;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 23:this.popState();break;case 24:h.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return h.getLogger().trace("description:",g.yytext),"NODE_DESCR";break;case 26:this.popState();break;case 27:return this.popState(),h.getLogger().trace("node end ))"),"NODE_DEND";break;case 28:return this.popState(),h.getLogger().trace("node end )"),"NODE_DEND";break;case 29:return this.popState(),h.getLogger().trace("node end ...",g.yytext),"NODE_DEND";break;case 30:return this.popState(),h.getLogger().trace("node end (("),"NODE_DEND";break;case 31:return this.popState(),h.getLogger().trace("node end (-"),"NODE_DEND";break;case 32:return this.popState(),h.getLogger().trace("node end (-"),"NODE_DEND";break;case 33:return this.popState(),h.getLogger().trace("node end (("),"NODE_DEND";break;case 34:return this.popState(),h.getLogger().trace("node end (("),"NODE_DEND";break;case 35:return h.getLogger().trace("Long description:",g.yytext),20;break;case 36:return h.getLogger().trace("Long description:",g.yytext),20;break}},"anonymous"),rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}};return R}();y.lexer=O;function D(){this.yy={}}return w(D,"Parser"),D.prototype=y,y.Parser=D,new D}();vt.parser=vt;var Ut=vt,V=[],wt=0,yt={},Pt=w(()=>{V=[],wt=0,yt={}},"clear"),Yt=w(function(E){for(let A=V.length-1;A>=0;A--)if(V[A].level<E)return V[A];return null},"getParent"),Xt=w(()=>V.length>0?V[0]:null,"getMindmap"),kt=w((E,A,o,i)=>{Q.info("addNode",E,A,o,i);let e=at(),t=e.mindmap?.padding??q.mindmap.padding;switch(i){case k.ROUNDED_RECT:case k.RECT:case k.HEXAGON:t*=2}let r={id:wt++,nodeId:tt(A,e),level:E,descr:tt(o,e),type:i,children:[],width:e.mindmap?.maxNodeWidth??q.mindmap.maxNodeWidth,padding:t},l=Yt(E);if(l)l.children.push(r),V.push(r);else if(V.length===0)V.push(r);else throw new Error('There can be only one root. No parent could be found for ("'+r.descr+'")')},"addNode"),k={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},Ht=w((E,A)=>{switch(Q.debug("In get type",E,A),E){case"[":return k.RECT;case"(":return A===")"?k.ROUNDED_RECT:k.CLOUD;case"((":return k.CIRCLE;case")":return k.CLOUD;case"))":return k.BANG;case"{{":return k.HEXAGON;default:return k.DEFAULT}},"getType"),$t=w((E,A)=>{yt[E]=A},"setElementForId"),Wt=w(E=>{if(!E)return;let A=at(),o=V[V.length-1];E.icon&&(o.icon=tt(E.icon,A)),E.class&&(o.class=tt(E.class,A))},"decorateNode"),Bt=w(E=>{switch(E){case k.DEFAULT:return"no-border";case k.RECT:return"rect";case k.ROUNDED_RECT:return"rounded-rect";case k.CIRCLE:return"circle";case k.CLOUD:return"cloud";case k.BANG:return"bang";case k.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),Vt=w(()=>Q,"getLogger"),Zt=w(E=>yt[E],"getElementById"),jt={clear:Pt,addNode:kt,getMindmap:Xt,nodeType:k,getType:Ht,setElementForId:$t,decorateNode:Wt,type2Str:Bt,getLogger:Vt,getElementById:Zt},Qt=jt,zt=12,Kt=w(function(E,A,o,i){A.append("path").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("d",`M0 ${o.height-5} v${-o.height+2*5} q0,-5 5,-5 h${o.width-2*5} q5,0 5,5 v${o.height-5} H0 Z`),A.append("line").attr("class","node-line-"+i).attr("x1",0).attr("y1",o.height).attr("x2",o.width).attr("y2",o.height)},"defaultBkg"),Jt=w(function(E,A,o){A.append("rect").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("height",o.height).attr("width",o.width)},"rectBkg"),qt=w(function(E,A,o){let i=o.width,e=o.height,t=.15*i,r=.25*i,l=.35*i,f=.2*i;A.append("path").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("d",`M0 0 a${t},${t} 0 0,1 ${i*.25},${-1*i*.1}
      a${l},${l} 1 0,1 ${i*.4},${-1*i*.1}
      a${r},${r} 1 0,1 ${i*.35},${1*i*.2}

      a${t},${t} 1 0,1 ${i*.15},${1*e*.35}
      a${f},${f} 1 0,1 ${-1*i*.15},${1*e*.65}

      a${r},${t} 1 0,1 ${-1*i*.25},${i*.15}
      a${l},${l} 1 0,1 ${-1*i*.5},0
      a${t},${t} 1 0,1 ${-1*i*.25},${-1*i*.15}

      a${t},${t} 1 0,1 ${-1*i*.1},${-1*e*.35}
      a${f},${f} 1 0,1 ${i*.1},${-1*e*.65}

    H0 V0 Z`)},"cloudBkg"),te=w(function(E,A,o){let i=o.width,e=o.height,t=.15*i;A.append("path").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("d",`M0 0 a${t},${t} 1 0,0 ${i*.25},${-1*e*.1}
      a${t},${t} 1 0,0 ${i*.25},0
      a${t},${t} 1 0,0 ${i*.25},0
      a${t},${t} 1 0,0 ${i*.25},${1*e*.1}

      a${t},${t} 1 0,0 ${i*.15},${1*e*.33}
      a${t*.8},${t*.8} 1 0,0 0,${1*e*.34}
      a${t},${t} 1 0,0 ${-1*i*.15},${1*e*.33}

      a${t},${t} 1 0,0 ${-1*i*.25},${e*.15}
      a${t},${t} 1 0,0 ${-1*i*.25},0
      a${t},${t} 1 0,0 ${-1*i*.25},0
      a${t},${t} 1 0,0 ${-1*i*.25},${-1*e*.15}

      a${t},${t} 1 0,0 ${-1*i*.1},${-1*e*.33}
      a${t*.8},${t*.8} 1 0,0 0,${-1*e*.34}
      a${t},${t} 1 0,0 ${i*.1},${-1*e*.33}

    H0 V0 Z`)},"bangBkg"),ee=w(function(E,A,o){A.append("circle").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("r",o.width/2)},"circleBkg");function St(E,A,o,i,e){return E.insert("polygon",":first-child").attr("points",i.map(function(t){return t.x+","+t.y}).join(" ")).attr("transform","translate("+(e.width-A)/2+", "+o+")")}w(St,"insertPolygonShape");var re=w(function(E,A,o){let i=o.height,t=i/4,r=o.width-o.padding+2*t,l=[{x:t,y:0},{x:r-t,y:0},{x:r,y:-i/2},{x:r-t,y:-i},{x:t,y:-i},{x:0,y:-i/2}];St(A,r,i,l,o)},"hexagonBkg"),ie=w(function(E,A,o){A.append("rect").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("height",o.height).attr("rx",o.padding).attr("ry",o.padding).attr("width",o.width)},"roundedRectBkg"),ne=w(async function(E,A,o,i,e){let t=e.htmlLabels,r=i%(zt-1),l=A.append("g");o.section=r;let f="section-"+r;r<0&&(f+=" section-root"),l.attr("class",(o.class?o.class+" ":"")+"mindmap-node "+f);let a=l.append("g"),d=l.append("g"),s=o.descr.replace(/(<br\/*>)/g,`
`);await Ct(d,s,{useHtmlLabels:t,width:o.width,classes:"mindmap-node-label"},e),t||d.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");let c=d.node().getBBox(),[u]=It(e.fontSize);if(o.height=c.height+u*1.1*.5+o.padding,o.width=c.width+2*o.padding,o.icon)if(o.type===E.nodeType.CIRCLE)o.height+=50,o.width+=50,l.append("foreignObject").attr("height","50px").attr("width",o.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+r+" "+o.icon),d.attr("transform","translate("+o.width/2+", "+(o.height/2-1.5*o.padding)+")");else{o.width+=50;let v=o.height;o.height=Math.max(v,60);let L=Math.abs(o.height-v);l.append("foreignObject").attr("width","60px").attr("height",o.height).attr("style","text-align: center;margin-top:"+L/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+r+" "+o.icon),d.attr("transform","translate("+(25+o.width/2)+", "+(L/2+o.padding/2)+")")}else if(t){let v=(o.width-c.width)/2,L=(o.height-c.height)/2;d.attr("transform","translate("+v+", "+L+")")}else{let v=o.width/2,L=o.padding/2;d.attr("transform","translate("+v+", "+L+")")}switch(o.type){case E.nodeType.DEFAULT:Kt(E,a,o,r);break;case E.nodeType.ROUNDED_RECT:ie(E,a,o,r);break;case E.nodeType.RECT:Jt(E,a,o,r);break;case E.nodeType.CIRCLE:a.attr("transform","translate("+o.width/2+", "+ +o.height/2+")"),ee(E,a,o,r);break;case E.nodeType.CLOUD:qt(E,a,o,r);break;case E.nodeType.BANG:te(E,a,o,r);break;case E.nodeType.HEXAGON:re(E,a,o,r);break}return E.setElementForId(o.id,l),o.height},"drawNode"),se=w(function(E,A){let o=E.getElementById(A.id),i=A.x||0,e=A.y||0;o.attr("transform","translate("+i+","+e+")")},"positionNode");ct.use(xt.default);async function Et(E,A,o,i,e){await ne(E,A,o,i,e),o.children&&await Promise.all(o.children.map((t,r)=>Et(E,A,t,i<0?r:i,e)))}w(Et,"drawNodes");function Gt(E,A){A.edges().map((o,i)=>{let e=o.data();if(o[0]._private.bodyBounds){let t=o[0]._private.rscratch;Q.trace("Edge: ",i,e),E.insert("path").attr("d",`M ${t.startX},${t.startY} L ${t.midX},${t.midY} L${t.endX},${t.endY} `).attr("class","edge section-edge-"+e.section+" edge-depth-"+e.depth)}})}w(Gt,"drawEdges");function Lt(E,A,o,i){A.add({group:"nodes",data:{id:E.id.toString(),labelText:E.descr,height:E.height,width:E.width,level:i,nodeId:E.id,padding:E.padding,type:E.type},position:{x:E.x,y:E.y}}),E.children&&E.children.forEach(e=>{Lt(e,A,o,i+1),A.add({group:"edges",data:{id:`${E.id}_${e.id}`,source:E.id,target:e.id,depth:i,section:e.section}})})}w(Lt,"addNodes");function _t(E,A){return new Promise(o=>{let i=At("body").append("div").attr("id","cy").attr("style","display:none"),e=ct({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});i.remove(),Lt(E,e,A,0),e.nodes().forEach(function(t){t.layoutDimensions=()=>{let r=t.data();return{w:r.width,h:r.height}}}),e.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),e.ready(t=>{Q.info("Ready",t),o(e)})})}w(_t,"layoutMindmap");function Ft(E,A){A.nodes().map((o,i)=>{let e=o.data();e.x=o.position().x,e.y=o.position().y,se(E,e);let t=E.getElementById(e.nodeId);Q.info("Id:",i,"Position: (",o.position().x,", ",o.position().y,")",e),t.attr("transform",`translate(${o.position().x-e.width/2}, ${o.position().y-e.height/2})`),t.attr("attr",`apa-${i})`)})}w(Ft,"positionNodes");var ae=w(async(E,A,o,i)=>{Q.debug(`Rendering mindmap diagram
`+E);let e=i.db,t=e.getMindmap();if(!t)return;let r=at();r.htmlLabels=!1;let l=Rt(A),f=l.append("g");f.attr("class","mindmap-edges");let a=l.append("g");a.attr("class","mindmap-nodes"),await Et(e,a,t,-1,r);let d=await _t(t,r);Gt(f,d),Ft(e,d),Ot(void 0,l,r.mindmap?.padding??q.mindmap.padding,r.mindmap?.useMaxWidth??q.mindmap.useMaxWidth)},"draw"),oe={draw:ae},he=w(E=>{let A="";for(let o=0;o<E.THEME_COLOR_LIMIT;o++)E["lineColor"+o]=E["lineColor"+o]||E["cScaleInv"+o],Nt(E["lineColor"+o])?E["lineColor"+o]=mt(E["lineColor"+o],20):E["lineColor"+o]=Dt(E["lineColor"+o],20);for(let o=0;o<E.THEME_COLOR_LIMIT;o++){let i=""+(17-3*o);A+=`
    .section-${o-1} rect, .section-${o-1} path, .section-${o-1} circle, .section-${o-1} polygon, .section-${o-1} path  {
      fill: ${E["cScale"+o]};
    }
    .section-${o-1} text {
     fill: ${E["cScaleLabel"+o]};
    }
    .node-icon-${o-1} {
      font-size: 40px;
      color: ${E["cScaleLabel"+o]};
    }
    .section-edge-${o-1}{
      stroke: ${E["cScale"+o]};
    }
    .edge-depth-${o-1}{
      stroke-width: ${i};
    }
    .section-${o-1} line {
      stroke: ${E["cScaleInv"+o]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return A},"genSections"),le=w(E=>`
  .edge {
    stroke-width: 3;
  }
  ${he(E)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${E.git0};
  }
  .section-root text {
    fill: ${E.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .mindmap-node-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles"),ce=le,Le={db:Qt,renderer:oe,parser:Ut,styles:ce};export{Le as diagram};
//# sourceMappingURL=mindmap-definition-GWI6TPTV-XCX7U2FR.min.js.map
