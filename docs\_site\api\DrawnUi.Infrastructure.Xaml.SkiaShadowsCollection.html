<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaShadowsCollection | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaShadowsCollection | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Infrastructure_Xaml_SkiaShadowsCollection.md&amp;value=---%0Auid%3A%20DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection">



  <h1 id="DrawnUi_Infrastructure_Xaml_SkiaShadowsCollection" data-uid="DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection" class="text-break">
Class SkiaShadowsCollection  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Xaml/SkiaShadowsCollection.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Infrastructure.html">Infrastructure</a>.<a class="xref" href="DrawnUi.Infrastructure.Xaml.html">Xaml</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SkiaShadowsCollection : ObservableCollection&lt;SkiaShadow&gt;, IList&lt;SkiaShadow&gt;, ICollection&lt;SkiaShadow&gt;, IReadOnlyList&lt;SkiaShadow&gt;, IReadOnlyCollection&lt;SkiaShadow&gt;, IEnumerable&lt;SkiaShadow&gt;, INotifyCollectionChanged, INotifyPropertyChanged, IList, ICollection, IEnumerable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1">Collection</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1">ObservableCollection</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a>&gt;</div>
      <div><span class="xref">SkiaShadowsCollection</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1">ICollection</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1">IReadOnlyCollection</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.specialized.inotifycollectionchanged">INotifyCollectionChanged</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.icollection">ICollection</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ienumerable">IEnumerable</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy">ObservableCollection&lt;SkiaShadow&gt;.BlockReentrancy()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy">ObservableCollection&lt;SkiaShadow&gt;.CheckReentrancy()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems">ObservableCollection&lt;SkiaShadow&gt;.ClearItems()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem">ObservableCollection&lt;SkiaShadow&gt;.InsertItem(int, SkiaShadow)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move">ObservableCollection&lt;SkiaShadow&gt;.Move(int, int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem">ObservableCollection&lt;SkiaShadow&gt;.MoveItem(int, int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged">ObservableCollection&lt;SkiaShadow&gt;.OnCollectionChanged(NotifyCollectionChangedEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged">ObservableCollection&lt;SkiaShadow&gt;.OnPropertyChanged(PropertyChangedEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem">ObservableCollection&lt;SkiaShadow&gt;.RemoveItem(int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem">ObservableCollection&lt;SkiaShadow&gt;.SetItem(int, SkiaShadow)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.collectionchanged">ObservableCollection&lt;SkiaShadow&gt;.CollectionChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.propertychanged">ObservableCollection&lt;SkiaShadow&gt;.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add">Collection&lt;SkiaShadow&gt;.Add(SkiaShadow)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear">Collection&lt;SkiaShadow&gt;.Clear()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains">Collection&lt;SkiaShadow&gt;.Contains(SkiaShadow)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto">Collection&lt;SkiaShadow&gt;.CopyTo(SkiaShadow[], int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator">Collection&lt;SkiaShadow&gt;.GetEnumerator()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof">Collection&lt;SkiaShadow&gt;.IndexOf(SkiaShadow)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert">Collection&lt;SkiaShadow&gt;.Insert(int, SkiaShadow)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove">Collection&lt;SkiaShadow&gt;.Remove(SkiaShadow)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat">Collection&lt;SkiaShadow&gt;.RemoveAt(int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.count">Collection&lt;SkiaShadow&gt;.Count</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">Collection&lt;SkiaShadow&gt;.this[int]</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.items">Collection&lt;SkiaShadow&gt;.Items</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Infrastructure_Xaml_SkiaShadowsCollection_Add_" data-uid="DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add*"></a>

  <h3 id="DrawnUi_Infrastructure_Xaml_SkiaShadowsCollection_Add_System_Object_" data-uid="DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add(System.Object)">
  Add(object)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Xaml/SkiaShadowsCollection.cs/#L10"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds an item to the <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a>.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Add(object value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd><p>The object to add to the <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a>.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The position into which the new element was inserted, or -1 to indicate that the item was not inserted into the collection.</p>
</dd>
  </dl>








  <h4 class="section">Exceptions</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.notsupportedexception">NotSupportedException</a></dt>
    <dd><p>The <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a> is read-only.</p>
<p>-or-</p>
<p>The <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a> has a fixed size.</p>
</dd>
  </dl>




</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Xaml/SkiaShadowsCollection.cs/#L7" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
