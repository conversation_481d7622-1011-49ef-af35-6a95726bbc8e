<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Interface ISkiaControl | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Interface ISkiaControl | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaControl.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaControl%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.ISkiaControl">



  <h1 id="DrawnUi_Draw_ISkiaControl" data-uid="DrawnUi.Draw.ISkiaControl" class="text-break">
Interface ISkiaControl  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L3"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface ISkiaControl : IDrawnBase, IDisposable, ICanBeUpdatedWithContext, ICanBeUpdated</code></pre>
  </div>







  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DrawingRect">IDrawnBase.DrawingRect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Tag">IDrawnBase.Tag</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisible">IDrawnBase.IsVisible</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsDisposed">IDrawnBase.IsDisposed</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsDisposing">IDrawnBase.IsDisposing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisibleInViewTree">IDrawnBase.IsVisibleInViewTree()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_">IDrawnBase.GetOnScreenVisibleArea(DrawingContext, Vector2)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Invalidate">IDrawnBase.Invalidate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateParents">IDrawnBase.InvalidateParents()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_">IDrawnBase.ClipSmart(SKCanvas, SKPath, SKClipOperation)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_">IDrawnBase.CreateClip(object, bool, SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_">IDrawnBase.RegisterAnimator(ISkiaAnimator)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAnimator_System_Guid_">IDrawnBase.UnregisterAnimator(Guid)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAllAnimatorsByType_System_Type_">IDrawnBase.UnregisterAllAnimatorsByType(Type)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">IDrawnBase.RegisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">IDrawnBase.UnregisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_PostAnimators">IDrawnBase.PostAnimators</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Views">IDrawnBase.Views</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_AddSubView_DrawnUi_Draw_SkiaControl_">IDrawnBase.AddSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RemoveSubView_DrawnUi_Draw_SkiaControl_">IDrawnBase.RemoveSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_MeasuredSize">IDrawnBase.MeasuredSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Destination">IDrawnBase.Destination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_HeightRequest">IDrawnBase.HeightRequest</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_WidthRequest">IDrawnBase.WidthRequest</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Height">IDrawnBase.Height</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Width">IDrawnBase.Width</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_TranslationX">IDrawnBase.TranslationX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_TranslationY">IDrawnBase.TranslationY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InputTransparent">IDrawnBase.InputTransparent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsClippedToBounds">IDrawnBase.IsClippedToBounds</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipEffects">IDrawnBase.ClipEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateLocks">IDrawnBase.UpdateLocks</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateByChild_DrawnUi_Draw_SkiaControl_">IDrawnBase.InvalidateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateByChild_DrawnUi_Draw_SkiaControl_">IDrawnBase.UpdateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ShouldInvalidateByChildren">IDrawnBase.ShouldInvalidateByChildren</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RenderingScale">IDrawnBase.RenderingScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_X">IDrawnBase.X</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Y">IDrawnBase.Y</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewport">IDrawnBase.InvalidateViewport()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Repaint">IDrawnBase.Repaint()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewsList">IDrawnBase.InvalidateViewsList()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DisposeObject_System_IDisposable_">IDrawnBase.DisposeObject(IDisposable)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable.dispose">IDisposable.Dispose()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html#DrawnUi_Draw_ICanBeUpdatedWithContext_BindingContext">ICanBeUpdatedWithContext.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html#DrawnUi_Draw_ICanBeUpdated_Update">ICanBeUpdated.Update()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_">DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_ISkiaControl_CanDraw_" data-uid="DrawnUi.Draw.ISkiaControl.CanDraw*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_CanDraw" data-uid="DrawnUi.Draw.ISkiaControl.CanDraw">
  CanDraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L49"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool CanDraw { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_Clipping_" data-uid="DrawnUi.Draw.ISkiaControl.Clipping*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_Clipping" data-uid="DrawnUi.Draw.ISkiaControl.Clipping">
  Clipping
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L20"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Action&lt;SKPath, SKRect&gt; Clipping { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpath">SKPath</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_HorizontalOptions_" data-uid="DrawnUi.Draw.ISkiaControl.HorizontalOptions*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_HorizontalOptions" data-uid="DrawnUi.Draw.ISkiaControl.HorizontalOptions">
  HorizontalOptions
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L45"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">LayoutOptions HorizontalOptions { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.layoutoptions">LayoutOptions</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_IsGhost_" data-uid="DrawnUi.Draw.ISkiaControl.IsGhost*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_IsGhost" data-uid="DrawnUi.Draw.ISkiaControl.IsGhost">
  IsGhost
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L18"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Takes place in layout, acts like is visible, but just not rendering</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsGhost { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_Margin_" data-uid="DrawnUi.Draw.ISkiaControl.Margin*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_Margin" data-uid="DrawnUi.Draw.ISkiaControl.Margin">
  Margin
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L9"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Thickness Margin { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_Padding_" data-uid="DrawnUi.Draw.ISkiaControl.Padding*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_Padding" data-uid="DrawnUi.Draw.ISkiaControl.Padding">
  Padding
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Thickness Padding { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_Parent_" data-uid="DrawnUi.Draw.ISkiaControl.Parent*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_Parent" data-uid="DrawnUi.Draw.ISkiaControl.Parent">
  Parent
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IDrawnBase Parent { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_RenderedAtDestination_" data-uid="DrawnUi.Draw.ISkiaControl.RenderedAtDestination*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_RenderedAtDestination" data-uid="DrawnUi.Draw.ISkiaControl.RenderedAtDestination">
  RenderedAtDestination
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L32"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">SKRect RenderedAtDestination { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_VerticalOptions_" data-uid="DrawnUi.Draw.ISkiaControl.VerticalOptions*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_VerticalOptions" data-uid="DrawnUi.Draw.ISkiaControl.VerticalOptions">
  VerticalOptions
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L47"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">LayoutOptions VerticalOptions { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.layoutoptions">LayoutOptions</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_VisualLayer_" data-uid="DrawnUi.Draw.ISkiaControl.VisualLayer*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_VisualLayer" data-uid="DrawnUi.Draw.ISkiaControl.VisualLayer">
  VisualLayer
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">VisualLayer? VisualLayer { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaControl_ZIndex_" data-uid="DrawnUi.Draw.ISkiaControl.ZIndex*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_ZIndex" data-uid="DrawnUi.Draw.ISkiaControl.ZIndex">
  ZIndex
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L22"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">int ZIndex { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_ISkiaControl_Arrange_" data-uid="DrawnUi.Draw.ISkiaControl.Arrange*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_Arrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.ISkiaControl.Arrange(SkiaSharp.SKRect,System.Single,System.Single,System.Single)">
  Arrange(SKRect, float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L28"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void Arrange(SKRect destination, float widthRequest, float heightRequest, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>destination</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>widthRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>heightRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ISkiaControl_Measure_" data-uid="DrawnUi.Draw.ISkiaControl.Measure*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_Measure_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.ISkiaControl.Measure(System.Single,System.Single,System.Single)">
  Measure(float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L43"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Expecting PIXELS as input
sets NeedMeasure to false</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">ScaledSize Measure(float widthConstraint, float heightConstraint, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>widthConstraint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>heightConstraint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_ISkiaControl_OnBeforeMeasure_" data-uid="DrawnUi.Draw.ISkiaControl.OnBeforeMeasure*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_OnBeforeMeasure" data-uid="DrawnUi.Draw.ISkiaControl.OnBeforeMeasure">
  OnBeforeMeasure()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L26"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void OnBeforeMeasure()</code></pre>
  </div>













  <a id="DrawnUi_Draw_ISkiaControl_OptionalOnBeforeDrawing_" data-uid="DrawnUi.Draw.ISkiaControl.OptionalOnBeforeDrawing*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_OptionalOnBeforeDrawing" data-uid="DrawnUi.Draw.ISkiaControl.OptionalOnBeforeDrawing">
  OptionalOnBeforeDrawing()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L24"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void OptionalOnBeforeDrawing()</code></pre>
  </div>













  <a id="DrawnUi_Draw_ISkiaControl_Render_" data-uid="DrawnUi.Draw.ISkiaControl.Render*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_Render_DrawnUi_Draw_DrawingContext_" data-uid="DrawnUi.Draw.ISkiaControl.Render(DrawnUi.Draw.DrawingContext)">
  Render(DrawingContext)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L30"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void Render(DrawingContext context)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ISkiaControl_SetChildren_" data-uid="DrawnUi.Draw.ISkiaControl.SetChildren*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__" data-uid="DrawnUi.Draw.ISkiaControl.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})">
  SetChildren(IEnumerable&lt;SkiaControl&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L34"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetChildren(IEnumerable&lt;SkiaControl&gt; views)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>views</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ISkiaControl_SetParent_" data-uid="DrawnUi.Draw.ISkiaControl.SetParent*"></a>

  <h3 id="DrawnUi_Draw_ISkiaControl_SetParent_DrawnUi_Draw_IDrawnBase_" data-uid="DrawnUi.Draw.ISkiaControl.SetParent(DrawnUi.Draw.IDrawnBase)">
  SetParent(IDrawnBase)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L13"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetParent(IDrawnBase parent)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></dt>
    <dd></dd>
  </dl>













</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaControl.cs/#L3" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
