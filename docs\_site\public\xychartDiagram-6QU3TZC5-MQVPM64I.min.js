import{a as Ht}from"./chunk-5IIW54K6.min.js";import{b as Yt}from"./chunk-AUO2PXKS.min.js";import{l as gt}from"./chunk-PYPO7LRM.min.js";import"./chunk-CM5D5KZN.min.js";import{D as K,Fa as ut,H as Bt,O as Mt,S as Wt,T as Ot,U as zt,V as Ft,W as Xt,X as Nt,Y as ht,h as a,j as ot,la as lt,ma as ct,t as It,v as Vt}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import"./chunk-OSRY5VT3.min.js";var xt=function(){var t=a(function(M,o,l,c){for(l=l||{},c=M.length;c--;l[M[c]]=o);return l},"o"),i=[1,10,12,14,16,18,19,21,23],e=[2,6],s=[1,3],r=[1,5],p=[1,6],g=[1,7],y=[1,5,10,12,14,16,18,19,21,23,34,35,36],T=[1,25],E=[1,26],w=[1,28],R=[1,29],I=[1,30],V=[1,31],S=[1,32],L=[1,33],f=[1,34],k=[1,35],h=[1,36],D=[1,37],X=[1,43],_t=[1,42],Tt=[1,47],$=[1,50],b=[1,10,12,14,16,18,19,21,23,34,35,36],tt=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36],v=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36,41,42,43,44,45,46,47,48,49,50],Rt=[1,64],it={trace:a(function(){},"trace"),yy:{},symbols_:{error:2,start:3,eol:4,XYCHART:5,chartConfig:6,document:7,CHART_ORIENTATION:8,statement:9,title:10,text:11,X_AXIS:12,parseXAxis:13,Y_AXIS:14,parseYAxis:15,LINE:16,plotData:17,BAR:18,acc_title:19,acc_title_value:20,acc_descr:21,acc_descr_value:22,acc_descr_multiline_value:23,SQUARE_BRACES_START:24,commaSeparatedNumbers:25,SQUARE_BRACES_END:26,NUMBER_WITH_DECIMAL:27,COMMA:28,xAxisData:29,bandData:30,ARROW_DELIMITER:31,commaSeparatedTexts:32,yAxisData:33,NEWLINE:34,SEMI:35,EOF:36,alphaNum:37,STR:38,MD_STR:39,alphaNumToken:40,AMP:41,NUM:42,ALPHA:43,PLUS:44,EQUALS:45,MULT:46,DOT:47,BRKT:48,MINUS:49,UNDERSCORE:50,$accept:0,$end:1},terminals_:{2:"error",5:"XYCHART",8:"CHART_ORIENTATION",10:"title",12:"X_AXIS",14:"Y_AXIS",16:"LINE",18:"BAR",19:"acc_title",20:"acc_title_value",21:"acc_descr",22:"acc_descr_value",23:"acc_descr_multiline_value",24:"SQUARE_BRACES_START",26:"SQUARE_BRACES_END",27:"NUMBER_WITH_DECIMAL",28:"COMMA",31:"ARROW_DELIMITER",34:"NEWLINE",35:"SEMI",36:"EOF",38:"STR",39:"MD_STR",41:"AMP",42:"NUM",43:"ALPHA",44:"PLUS",45:"EQUALS",46:"MULT",47:"DOT",48:"BRKT",49:"MINUS",50:"UNDERSCORE"},productions_:[0,[3,2],[3,3],[3,2],[3,1],[6,1],[7,0],[7,2],[9,2],[9,2],[9,2],[9,2],[9,2],[9,3],[9,2],[9,3],[9,2],[9,2],[9,1],[17,3],[25,3],[25,1],[13,1],[13,2],[13,1],[29,1],[29,3],[30,3],[32,3],[32,1],[15,1],[15,2],[15,1],[33,3],[4,1],[4,1],[4,1],[11,1],[11,1],[11,1],[37,1],[37,2],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1]],performAction:a(function(o,l,c,u,m,n,N){var x=n.length-1;switch(m){case 5:u.setOrientation(n[x]);break;case 9:u.setDiagramTitle(n[x].text.trim());break;case 12:u.setLineData({text:"",type:"text"},n[x]);break;case 13:u.setLineData(n[x-1],n[x]);break;case 14:u.setBarData({text:"",type:"text"},n[x]);break;case 15:u.setBarData(n[x-1],n[x]);break;case 16:this.$=n[x].trim(),u.setAccTitle(this.$);break;case 17:case 18:this.$=n[x].trim(),u.setAccDescription(this.$);break;case 19:this.$=n[x-1];break;case 20:this.$=[Number(n[x-2]),...n[x]];break;case 21:this.$=[Number(n[x])];break;case 22:u.setXAxisTitle(n[x]);break;case 23:u.setXAxisTitle(n[x-1]);break;case 24:u.setXAxisTitle({type:"text",text:""});break;case 25:u.setXAxisBand(n[x]);break;case 26:u.setXAxisRangeData(Number(n[x-2]),Number(n[x]));break;case 27:this.$=n[x-1];break;case 28:this.$=[n[x-2],...n[x]];break;case 29:this.$=[n[x]];break;case 30:u.setYAxisTitle(n[x]);break;case 31:u.setYAxisTitle(n[x-1]);break;case 32:u.setYAxisTitle({type:"text",text:""});break;case 33:u.setYAxisRangeData(Number(n[x-2]),Number(n[x]));break;case 37:this.$={text:n[x],type:"text"};break;case 38:this.$={text:n[x],type:"text"};break;case 39:this.$={text:n[x],type:"markdown"};break;case 40:this.$=n[x];break;case 41:this.$=n[x-1]+""+n[x];break}},"anonymous"),table:[t(i,e,{3:1,4:2,7:4,5:s,34:r,35:p,36:g}),{1:[3]},t(i,e,{4:2,7:4,3:8,5:s,34:r,35:p,36:g}),t(i,e,{4:2,7:4,6:9,3:10,5:s,8:[1,11],34:r,35:p,36:g}),{1:[2,4],9:12,10:[1,13],12:[1,14],14:[1,15],16:[1,16],18:[1,17],19:[1,18],21:[1,19],23:[1,20]},t(y,[2,34]),t(y,[2,35]),t(y,[2,36]),{1:[2,1]},t(i,e,{4:2,7:4,3:21,5:s,34:r,35:p,36:g}),{1:[2,3]},t(y,[2,5]),t(i,[2,7],{4:22,34:r,35:p,36:g}),{11:23,37:24,38:T,39:E,40:27,41:w,42:R,43:I,44:V,45:S,46:L,47:f,48:k,49:h,50:D},{11:39,13:38,24:X,27:_t,29:40,30:41,37:24,38:T,39:E,40:27,41:w,42:R,43:I,44:V,45:S,46:L,47:f,48:k,49:h,50:D},{11:45,15:44,27:Tt,33:46,37:24,38:T,39:E,40:27,41:w,42:R,43:I,44:V,45:S,46:L,47:f,48:k,49:h,50:D},{11:49,17:48,24:$,37:24,38:T,39:E,40:27,41:w,42:R,43:I,44:V,45:S,46:L,47:f,48:k,49:h,50:D},{11:52,17:51,24:$,37:24,38:T,39:E,40:27,41:w,42:R,43:I,44:V,45:S,46:L,47:f,48:k,49:h,50:D},{20:[1,53]},{22:[1,54]},t(b,[2,18]),{1:[2,2]},t(b,[2,8]),t(b,[2,9]),t(tt,[2,37],{40:55,41:w,42:R,43:I,44:V,45:S,46:L,47:f,48:k,49:h,50:D}),t(tt,[2,38]),t(tt,[2,39]),t(v,[2,40]),t(v,[2,42]),t(v,[2,43]),t(v,[2,44]),t(v,[2,45]),t(v,[2,46]),t(v,[2,47]),t(v,[2,48]),t(v,[2,49]),t(v,[2,50]),t(v,[2,51]),t(b,[2,10]),t(b,[2,22],{30:41,29:56,24:X,27:_t}),t(b,[2,24]),t(b,[2,25]),{31:[1,57]},{11:59,32:58,37:24,38:T,39:E,40:27,41:w,42:R,43:I,44:V,45:S,46:L,47:f,48:k,49:h,50:D},t(b,[2,11]),t(b,[2,30],{33:60,27:Tt}),t(b,[2,32]),{31:[1,61]},t(b,[2,12]),{17:62,24:$},{25:63,27:Rt},t(b,[2,14]),{17:65,24:$},t(b,[2,16]),t(b,[2,17]),t(v,[2,41]),t(b,[2,23]),{27:[1,66]},{26:[1,67]},{26:[2,29],28:[1,68]},t(b,[2,31]),{27:[1,69]},t(b,[2,13]),{26:[1,70]},{26:[2,21],28:[1,71]},t(b,[2,15]),t(b,[2,26]),t(b,[2,27]),{11:59,32:72,37:24,38:T,39:E,40:27,41:w,42:R,43:I,44:V,45:S,46:L,47:f,48:k,49:h,50:D},t(b,[2,33]),t(b,[2,19]),{25:73,27:Rt},{26:[2,28]},{26:[2,20]}],defaultActions:{8:[2,1],10:[2,3],21:[2,2],72:[2,28],73:[2,20]},parseError:a(function(o,l){if(l.recoverable)this.trace(o);else{var c=new Error(o);throw c.hash=l,c}},"parseError"),parse:a(function(o){var l=this,c=[0],u=[],m=[null],n=[],N=this.table,x="",G=0,Dt=0,vt=0,ui=2,Pt=1,gi=n.slice.call(arguments,1),A=Object.create(this.lexer),W={yy:{}};for(var et in this.yy)Object.prototype.hasOwnProperty.call(this.yy,et)&&(W.yy[et]=this.yy[et]);A.setInput(o,W.yy),W.yy.lexer=A,W.yy.parser=this,typeof A.yylloc>"u"&&(A.yylloc={});var st=A.yylloc;n.push(st);var xi=A.options&&A.options.ranges;typeof W.yy.parseError=="function"?this.parseError=W.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function pi(_){c.length=c.length-2*_,m.length=m.length-_,n.length=n.length-_}a(pi,"popStack");function Lt(){var _;return _=u.pop()||A.lex()||Pt,typeof _!="number"&&(_ instanceof Array&&(u=_,_=u.pop()),_=l.symbols_[_]||_),_}a(Lt,"lex");for(var C,at,O,P,Di,nt,z={},j,B,Et,Q;;){if(O=c[c.length-1],this.defaultActions[O]?P=this.defaultActions[O]:((C===null||typeof C>"u")&&(C=Lt()),P=N[O]&&N[O][C]),typeof P>"u"||!P.length||!P[0]){var rt="";Q=[];for(j in N[O])this.terminals_[j]&&j>ui&&Q.push("'"+this.terminals_[j]+"'");A.showPosition?rt="Parse error on line "+(G+1)+`:
`+A.showPosition()+`
Expecting `+Q.join(", ")+", got '"+(this.terminals_[C]||C)+"'":rt="Parse error on line "+(G+1)+": Unexpected "+(C==Pt?"end of input":"'"+(this.terminals_[C]||C)+"'"),this.parseError(rt,{text:A.match,token:this.terminals_[C]||C,line:A.yylineno,loc:st,expected:Q})}if(P[0]instanceof Array&&P.length>1)throw new Error("Parse Error: multiple actions possible at state: "+O+", token: "+C);switch(P[0]){case 1:c.push(C),m.push(A.yytext),n.push(A.yylloc),c.push(P[1]),C=null,at?(C=at,at=null):(Dt=A.yyleng,x=A.yytext,G=A.yylineno,st=A.yylloc,vt>0&&vt--);break;case 2:if(B=this.productions_[P[1]][1],z.$=m[m.length-B],z._$={first_line:n[n.length-(B||1)].first_line,last_line:n[n.length-1].last_line,first_column:n[n.length-(B||1)].first_column,last_column:n[n.length-1].last_column},xi&&(z._$.range=[n[n.length-(B||1)].range[0],n[n.length-1].range[1]]),nt=this.performAction.apply(z,[x,Dt,G,W.yy,P[1],m,n].concat(gi)),typeof nt<"u")return nt;B&&(c=c.slice(0,-1*B*2),m=m.slice(0,-1*B),n=n.slice(0,-1*B)),c.push(this.productions_[P[1]][0]),m.push(z.$),n.push(z._$),Et=N[c[c.length-2]][c[c.length-1]],c.push(Et);break;case 3:return!0}}return!0},"parse")},ci=function(){var M={EOF:1,parseError:a(function(l,c){if(this.yy.parser)this.yy.parser.parseError(l,c);else throw new Error(l)},"parseError"),setInput:a(function(o,l){return this.yy=l||this.yy||{},this._input=o,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:a(function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var l=o.match(/(?:\r\n?|\n).*/g);return l?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},"input"),unput:a(function(o){var l=o.length,c=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-l),this.offset-=l;var u=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var m=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===u.length?this.yylloc.first_column:0)+u[u.length-c.length].length-c[0].length:this.yylloc.first_column-l},this.options.ranges&&(this.yylloc.range=[m[0],m[0]+this.yyleng-l]),this.yyleng=this.yytext.length,this},"unput"),more:a(function(){return this._more=!0,this},"more"),reject:a(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:a(function(o){this.unput(this.match.slice(o))},"less"),pastInput:a(function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:a(function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:a(function(){var o=this.pastInput(),l=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+l+"^"},"showPosition"),test_match:a(function(o,l){var c,u,m;if(this.options.backtrack_lexer&&(m={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(m.yylloc.range=this.yylloc.range.slice(0))),u=o[0].match(/(?:\r\n?|\n).*/g),u&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+o[0].length},this.yytext+=o[0],this.match+=o[0],this.matches=o,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(o[0].length),this.matched+=o[0],c=this.performAction.call(this,this.yy,this,l,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var n in m)this[n]=m[n];return!1}return!1},"test_match"),next:a(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,l,c,u;this._more||(this.yytext="",this.match="");for(var m=this._currentRules(),n=0;n<m.length;n++)if(c=this._input.match(this.rules[m[n]]),c&&(!l||c[0].length>l[0].length)){if(l=c,u=n,this.options.backtrack_lexer){if(o=this.test_match(c,m[n]),o!==!1)return o;if(this._backtrack){l=!1;continue}else return!1}else if(!this.options.flex)break}return l?(o=this.test_match(l,m[u]),o!==!1?o:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:a(function(){var l=this.next();return l||this.lex()},"lex"),begin:a(function(l){this.conditionStack.push(l)},"begin"),popState:a(function(){var l=this.conditionStack.length-1;return l>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:a(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:a(function(l){return l=this.conditionStack.length-1-Math.abs(l||0),l>=0?this.conditionStack[l]:"INITIAL"},"topState"),pushState:a(function(l){this.begin(l)},"pushState"),stateStackSize:a(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:a(function(l,c,u,m){var n=m;switch(u){case 0:break;case 1:break;case 2:return this.popState(),34;break;case 3:return this.popState(),34;break;case 4:return 34;case 5:break;case 6:return 10;case 7:return this.pushState("acc_title"),19;break;case 8:return this.popState(),"acc_title_value";break;case 9:return this.pushState("acc_descr"),21;break;case 10:return this.popState(),"acc_descr_value";break;case 11:this.pushState("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 5;case 15:return 8;case 16:return this.pushState("axis_data"),"X_AXIS";break;case 17:return this.pushState("axis_data"),"Y_AXIS";break;case 18:return this.pushState("axis_band_data"),24;break;case 19:return 31;case 20:return this.pushState("data"),16;break;case 21:return this.pushState("data"),18;break;case 22:return this.pushState("data_inner"),24;break;case 23:return 27;case 24:return this.popState(),26;break;case 25:this.popState();break;case 26:this.pushState("string");break;case 27:this.popState();break;case 28:return"STR";case 29:return 24;case 30:return 26;case 31:return 43;case 32:return"COLON";case 33:return 44;case 34:return 28;case 35:return 45;case 36:return 46;case 37:return 48;case 38:return 50;case 39:return 47;case 40:return 41;case 41:return 49;case 42:return 42;case 43:break;case 44:return 35;case 45:return 36}},"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:(\r?\n))/i,/^(?:(\r?\n))/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:\{)/i,/^(?:[^\}]*)/i,/^(?:xychart-beta\b)/i,/^(?:(?:vertical|horizontal))/i,/^(?:x-axis\b)/i,/^(?:y-axis\b)/i,/^(?:\[)/i,/^(?:-->)/i,/^(?:line\b)/i,/^(?:bar\b)/i,/^(?:\[)/i,/^(?:[+-]?(?:\d+(?:\.\d+)?|\.\d+))/i,/^(?:\])/i,/^(?:(?:`\)                                    \{ this\.pushState\(md_string\); \}\n<md_string>\(\?:\(\?!`"\)\.\)\+                  \{ return MD_STR; \}\n<md_string>\(\?:`))/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s+)/i,/^(?:;)/i,/^(?:$)/i],conditions:{data_inner:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,23,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},data:{rules:[0,1,3,4,5,6,7,9,11,14,15,16,17,20,21,22,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},axis_band_data:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},axis_data:{rules:[0,1,2,4,5,6,7,9,11,14,15,16,17,18,19,20,21,23,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},title:{rules:[],inclusive:!1},md_string:{rules:[],inclusive:!1},string:{rules:[27,28],inclusive:!1},INITIAL:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0}}};return M}();it.lexer=ci;function q(){this.yy={}}return a(q,"Parser"),q.prototype=it,it.Parser=q,new q}();xt.parser=xt;var di=xt;function pt(t){return t.type==="bar"}a(pt,"isBarPlot");function mt(t){return t.type==="band"}a(mt,"isBandAxisData");function F(t){return t.type==="linear"}a(F,"isLinearAxisData");var qt=class{constructor(t){this.parentGroup=t}static{a(this,"TextDimensionCalculatorWithFont")}getMaxDimension(t,i){if(!this.parentGroup)return{width:t.reduce((r,p)=>Math.max(p.length,r),0)*i,height:i};let e={width:0,height:0},s=this.parentGroup.append("g").attr("visibility","hidden").attr("font-size",i);for(let r of t){let p=Yt(s,1,r),g=p?p.width:r.length*i,y=p?p.height:i;e.width=Math.max(e.width,g),e.height=Math.max(e.height,y)}return s.remove(),e}},Ut=.7,$t=.2,Gt=class{constructor(t,i,e,s){this.axisConfig=t,this.title=i,this.textDimensionCalculator=e,this.axisThemeConfig=s,this.boundingRect={x:0,y:0,width:0,height:0},this.axisPosition="left",this.showTitle=!1,this.showLabel=!1,this.showTick=!1,this.showAxisLine=!1,this.outerPadding=0,this.titleTextHeight=0,this.labelTextHeight=0,this.range=[0,10],this.boundingRect={x:0,y:0,width:0,height:0},this.axisPosition="left"}static{a(this,"BaseAxis")}setRange(t){this.range=t,this.axisPosition==="left"||this.axisPosition==="right"?this.boundingRect.height=t[1]-t[0]:this.boundingRect.width=t[1]-t[0],this.recalculateScale()}getRange(){return[this.range[0]+this.outerPadding,this.range[1]-this.outerPadding]}setAxisPosition(t){this.axisPosition=t,this.setRange(this.range)}getTickDistance(){let t=this.getRange();return Math.abs(t[0]-t[1])/this.getTickValues().length}getAxisOuterPadding(){return this.outerPadding}getLabelDimension(){return this.textDimensionCalculator.getMaxDimension(this.getTickValues().map(t=>t.toString()),this.axisConfig.labelFontSize)}recalculateOuterPaddingToDrawBar(){Ut*this.getTickDistance()>this.outerPadding*2&&(this.outerPadding=Math.floor(Ut*this.getTickDistance()/2)),this.recalculateScale()}calculateSpaceIfDrawnHorizontally(t){let i=t.height;if(this.axisConfig.showAxisLine&&i>this.axisConfig.axisLineWidth&&(i-=this.axisConfig.axisLineWidth,this.showAxisLine=!0),this.axisConfig.showLabel){let e=this.getLabelDimension(),s=$t*t.width;this.outerPadding=Math.min(e.width/2,s);let r=e.height+this.axisConfig.labelPadding*2;this.labelTextHeight=e.height,r<=i&&(i-=r,this.showLabel=!0)}if(this.axisConfig.showTick&&i>=this.axisConfig.tickLength&&(this.showTick=!0,i-=this.axisConfig.tickLength),this.axisConfig.showTitle&&this.title){let e=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize),s=e.height+this.axisConfig.titlePadding*2;this.titleTextHeight=e.height,s<=i&&(i-=s,this.showTitle=!0)}this.boundingRect.width=t.width,this.boundingRect.height=t.height-i}calculateSpaceIfDrawnVertical(t){let i=t.width;if(this.axisConfig.showAxisLine&&i>this.axisConfig.axisLineWidth&&(i-=this.axisConfig.axisLineWidth,this.showAxisLine=!0),this.axisConfig.showLabel){let e=this.getLabelDimension(),s=$t*t.height;this.outerPadding=Math.min(e.height/2,s);let r=e.width+this.axisConfig.labelPadding*2;r<=i&&(i-=r,this.showLabel=!0)}if(this.axisConfig.showTick&&i>=this.axisConfig.tickLength&&(this.showTick=!0,i-=this.axisConfig.tickLength),this.axisConfig.showTitle&&this.title){let e=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize),s=e.height+this.axisConfig.titlePadding*2;this.titleTextHeight=e.height,s<=i&&(i-=s,this.showTitle=!0)}this.boundingRect.width=t.width-i,this.boundingRect.height=t.height}calculateSpace(t){return this.axisPosition==="left"||this.axisPosition==="right"?this.calculateSpaceIfDrawnVertical(t):this.calculateSpaceIfDrawnHorizontally(t),this.recalculateScale(),{width:this.boundingRect.width,height:this.boundingRect.height}}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}getDrawableElementsForLeftAxis(){let t=[];if(this.showAxisLine){let i=this.boundingRect.x+this.boundingRect.width-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["left-axis","axisl-line"],data:[{path:`M ${i},${this.boundingRect.y} L ${i},${this.boundingRect.y+this.boundingRect.height} `,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["left-axis","label"],data:this.getTickValues().map(i=>({text:i.toString(),x:this.boundingRect.x+this.boundingRect.width-(this.showLabel?this.axisConfig.labelPadding:0)-(this.showTick?this.axisConfig.tickLength:0)-(this.showAxisLine?this.axisConfig.axisLineWidth:0),y:this.getScaleValue(i),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"middle",horizontalPos:"right"}))}),this.showTick){let i=this.boundingRect.x+this.boundingRect.width-(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["left-axis","ticks"],data:this.getTickValues().map(e=>({path:`M ${i},${this.getScaleValue(e)} L ${i-this.axisConfig.tickLength},${this.getScaleValue(e)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["left-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.axisConfig.titlePadding,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:270,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElementsForBottomAxis(){let t=[];if(this.showAxisLine){let i=this.boundingRect.y+this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["bottom-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${i} L ${this.boundingRect.x+this.boundingRect.width},${i}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["bottom-axis","label"],data:this.getTickValues().map(i=>({text:i.toString(),x:this.getScaleValue(i),y:this.boundingRect.y+this.axisConfig.labelPadding+(this.showTick?this.axisConfig.tickLength:0)+(this.showAxisLine?this.axisConfig.axisLineWidth:0),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}))}),this.showTick){let i=this.boundingRect.y+(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["bottom-axis","ticks"],data:this.getTickValues().map(e=>({path:`M ${this.getScaleValue(e)},${i} L ${this.getScaleValue(e)},${i+this.axisConfig.tickLength}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["bottom-axis","title"],data:[{text:this.title,x:this.range[0]+(this.range[1]-this.range[0])/2,y:this.boundingRect.y+this.boundingRect.height-this.axisConfig.titlePadding-this.titleTextHeight,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElementsForTopAxis(){let t=[];if(this.showAxisLine){let i=this.boundingRect.y+this.boundingRect.height-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["top-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${i} L ${this.boundingRect.x+this.boundingRect.width},${i}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["top-axis","label"],data:this.getTickValues().map(i=>({text:i.toString(),x:this.getScaleValue(i),y:this.boundingRect.y+(this.showTitle?this.titleTextHeight+this.axisConfig.titlePadding*2:0)+this.axisConfig.labelPadding,fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}))}),this.showTick){let i=this.boundingRect.y;t.push({type:"path",groupTexts:["top-axis","ticks"],data:this.getTickValues().map(e=>({path:`M ${this.getScaleValue(e)},${i+this.boundingRect.height-(this.showAxisLine?this.axisConfig.axisLineWidth:0)} L ${this.getScaleValue(e)},${i+this.boundingRect.height-this.axisConfig.tickLength-(this.showAxisLine?this.axisConfig.axisLineWidth:0)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["top-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.axisConfig.titlePadding,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElements(){if(this.axisPosition==="left")return this.getDrawableElementsForLeftAxis();if(this.axisPosition==="right")throw Error("Drawing of right axis is not implemented");return this.axisPosition==="bottom"?this.getDrawableElementsForBottomAxis():this.axisPosition==="top"?this.getDrawableElementsForTopAxis():[]}},fi=class extends Gt{static{a(this,"BandAxis")}constructor(t,i,e,s,r){super(t,s,r,i),this.categories=e,this.scale=lt().domain(this.categories).range(this.getRange())}setRange(t){super.setRange(t)}recalculateScale(){this.scale=lt().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(.5),ot.trace("BandAxis axis final categories, range: ",this.categories,this.getRange())}getTickValues(){return this.categories}getScaleValue(t){return this.scale(t)??this.getRange()[0]}},mi=class extends Gt{static{a(this,"LinearAxis")}constructor(t,i,e,s,r){super(t,s,r,i),this.domain=e,this.scale=ct().domain(this.domain).range(this.getRange())}getTickValues(){return this.scale.ticks()}recalculateScale(){let t=[...this.domain];this.axisPosition==="left"&&t.reverse(),this.scale=ct().domain(t).range(this.getRange())}getScaleValue(t){return this.scale(t)}};function dt(t,i,e,s){let r=new qt(s);return mt(t)?new fi(i,e,t.categories,t.title,r):new mi(i,e,[t.min,t.max],t.title,r)}a(dt,"getAxis");var yi=class{constructor(t,i,e,s){this.textDimensionCalculator=t,this.chartConfig=i,this.chartData=e,this.chartThemeConfig=s,this.boundingRect={x:0,y:0,width:0,height:0},this.showChartTitle=!1}static{a(this,"ChartTitle")}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}calculateSpace(t){let i=this.textDimensionCalculator.getMaxDimension([this.chartData.title],this.chartConfig.titleFontSize),e=Math.max(i.width,t.width),s=i.height+2*this.chartConfig.titlePadding;return i.width<=e&&i.height<=s&&this.chartConfig.showTitle&&this.chartData.title&&(this.boundingRect.width=e,this.boundingRect.height=s,this.showChartTitle=!0),{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){let t=[];return this.showChartTitle&&t.push({groupTexts:["chart-title"],type:"text",data:[{fontSize:this.chartConfig.titleFontSize,text:this.chartData.title,verticalPos:"middle",horizontalPos:"center",x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.chartThemeConfig.titleColor,rotation:0}]}),t}};function jt(t,i,e,s){let r=new qt(s);return new yi(r,t,i,e)}a(jt,"getChartTitleComponent");var bi=class{constructor(t,i,e,s,r){this.plotData=t,this.xAxis=i,this.yAxis=e,this.orientation=s,this.plotIndex=r}static{a(this,"LinePlot")}getDrawableElement(){let t=this.plotData.data.map(e=>[this.xAxis.getScaleValue(e[0]),this.yAxis.getScaleValue(e[1])]),i;return this.orientation==="horizontal"?i=ut().y(e=>e[0]).x(e=>e[1])(t):i=ut().x(e=>e[0]).y(e=>e[1])(t),i?[{groupTexts:["plot",`line-plot-${this.plotIndex}`],type:"path",data:[{path:i,strokeFill:this.plotData.strokeFill,strokeWidth:this.plotData.strokeWidth}]}]:[]}},Ai=class{constructor(t,i,e,s,r,p){this.barData=t,this.boundingRect=i,this.xAxis=e,this.yAxis=s,this.orientation=r,this.plotIndex=p}static{a(this,"BarPlot")}getDrawableElement(){let t=this.barData.data.map(r=>[this.xAxis.getScaleValue(r[0]),this.yAxis.getScaleValue(r[1])]),e=Math.min(this.xAxis.getAxisOuterPadding()*2,this.xAxis.getTickDistance())*(1-.05),s=e/2;return this.orientation==="horizontal"?[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map(r=>({x:this.boundingRect.x,y:r[0]-s,height:e,width:r[1]-this.boundingRect.x,fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill}))}]:[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map(r=>({x:r[0]-s,y:r[1],width:e,height:this.boundingRect.y+this.boundingRect.height-r[1],fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill}))}]}},ki=class{constructor(t,i,e){this.chartConfig=t,this.chartData=i,this.chartThemeConfig=e,this.boundingRect={x:0,y:0,width:0,height:0}}static{a(this,"BasePlot")}setAxes(t,i){this.xAxis=t,this.yAxis=i}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}calculateSpace(t){return this.boundingRect.width=t.width,this.boundingRect.height=t.height,{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){if(!(this.xAxis&&this.yAxis))throw Error("Axes must be passed to render Plots");let t=[];for(let[i,e]of this.chartData.plots.entries())switch(e.type){case"line":{let s=new bi(e,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,i);t.push(...s.getDrawableElement())}break;case"bar":{let s=new Ai(e,this.boundingRect,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,i);t.push(...s.getDrawableElement())}break}return t}};function Qt(t,i,e){return new ki(t,i,e)}a(Qt,"getPlotComponent");var Ci=class{constructor(t,i,e,s){this.chartConfig=t,this.chartData=i,this.componentStore={title:jt(t,i,e,s),plot:Qt(t,i,e),xAxis:dt(i.xAxis,t.xAxis,{titleColor:e.xAxisTitleColor,labelColor:e.xAxisLabelColor,tickColor:e.xAxisTickColor,axisLineColor:e.xAxisLineColor},s),yAxis:dt(i.yAxis,t.yAxis,{titleColor:e.yAxisTitleColor,labelColor:e.yAxisLabelColor,tickColor:e.yAxisTickColor,axisLineColor:e.yAxisLineColor},s)}}static{a(this,"Orchestrator")}calculateVerticalSpace(){let t=this.chartConfig.width,i=this.chartConfig.height,e=0,s=0,r=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100),p=Math.floor(i*this.chartConfig.plotReservedSpacePercent/100),g=this.componentStore.plot.calculateSpace({width:r,height:p});t-=g.width,i-=g.height,g=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:i}),s=g.height,i-=g.height,this.componentStore.xAxis.setAxisPosition("bottom"),g=this.componentStore.xAxis.calculateSpace({width:t,height:i}),i-=g.height,this.componentStore.yAxis.setAxisPosition("left"),g=this.componentStore.yAxis.calculateSpace({width:t,height:i}),e=g.width,t-=g.width,t>0&&(r+=t,t=0),i>0&&(p+=i,i=0),this.componentStore.plot.calculateSpace({width:r,height:p}),this.componentStore.plot.setBoundingBoxXY({x:e,y:s}),this.componentStore.xAxis.setRange([e,e+r]),this.componentStore.xAxis.setBoundingBoxXY({x:e,y:s+p}),this.componentStore.yAxis.setRange([s,s+p]),this.componentStore.yAxis.setBoundingBoxXY({x:0,y:s}),this.chartData.plots.some(y=>pt(y))&&this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}calculateHorizontalSpace(){let t=this.chartConfig.width,i=this.chartConfig.height,e=0,s=0,r=0,p=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100),g=Math.floor(i*this.chartConfig.plotReservedSpacePercent/100),y=this.componentStore.plot.calculateSpace({width:p,height:g});t-=y.width,i-=y.height,y=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:i}),e=y.height,i-=y.height,this.componentStore.xAxis.setAxisPosition("left"),y=this.componentStore.xAxis.calculateSpace({width:t,height:i}),t-=y.width,s=y.width,this.componentStore.yAxis.setAxisPosition("top"),y=this.componentStore.yAxis.calculateSpace({width:t,height:i}),i-=y.height,r=e+y.height,t>0&&(p+=t,t=0),i>0&&(g+=i,i=0),this.componentStore.plot.calculateSpace({width:p,height:g}),this.componentStore.plot.setBoundingBoxXY({x:s,y:r}),this.componentStore.yAxis.setRange([s,s+p]),this.componentStore.yAxis.setBoundingBoxXY({x:s,y:e}),this.componentStore.xAxis.setRange([r,r+g]),this.componentStore.xAxis.setBoundingBoxXY({x:0,y:r}),this.chartData.plots.some(T=>pt(T))&&this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}calculateSpace(){this.chartConfig.chartOrientation==="horizontal"?this.calculateHorizontalSpace():this.calculateVerticalSpace()}getDrawableElement(){this.calculateSpace();let t=[];this.componentStore.plot.setAxes(this.componentStore.xAxis,this.componentStore.yAxis);for(let i of Object.values(this.componentStore))t.push(...i.getDrawableElements());return t}},wi=class{static{a(this,"XYChartBuilder")}static build(t,i,e,s){return new Ci(t,i,e,s).getDrawableElement()}},Y=0,Kt,H=At(),U=bt(),d=kt(),ft=U.plotColorPalette.split(",").map(t=>t.trim()),Z=!1,yt=!1;function bt(){let t=It(),i=K();return gt(t.xyChart,i.themeVariables.xyChart)}a(bt,"getChartDefaultThemeConfig");function At(){let t=K();return gt(Vt.xyChart,t.xyChart)}a(At,"getChartDefaultConfig");function kt(){return{yAxis:{type:"linear",title:"",min:1/0,max:-1/0},xAxis:{type:"band",title:"",categories:[]},title:"",plots:[]}}a(kt,"getChartDefaultData");function J(t){let i=K();return Bt(t.trim(),i)}a(J,"textSanitizer");function Zt(t){Kt=t}a(Zt,"setTmpSVGG");function Jt(t){t==="horizontal"?H.chartOrientation="horizontal":H.chartOrientation="vertical"}a(Jt,"setOrientation");function ti(t){d.xAxis.title=J(t.text)}a(ti,"setXAxisTitle");function Ct(t,i){d.xAxis={type:"linear",title:d.xAxis.title,min:t,max:i},Z=!0}a(Ct,"setXAxisRangeData");function ii(t){d.xAxis={type:"band",title:d.xAxis.title,categories:t.map(i=>J(i.text))},Z=!0}a(ii,"setXAxisBand");function ei(t){d.yAxis.title=J(t.text)}a(ei,"setYAxisTitle");function si(t,i){d.yAxis={type:"linear",title:d.yAxis.title,min:t,max:i},yt=!0}a(si,"setYAxisRangeData");function ai(t){let i=Math.min(...t),e=Math.max(...t),s=F(d.yAxis)?d.yAxis.min:1/0,r=F(d.yAxis)?d.yAxis.max:-1/0;d.yAxis={type:"linear",title:d.yAxis.title,min:Math.min(s,i),max:Math.max(r,e)}}a(ai,"setYAxisRangeFromPlotData");function wt(t){let i=[];if(t.length===0)return i;if(!Z){let e=F(d.xAxis)?d.xAxis.min:1/0,s=F(d.xAxis)?d.xAxis.max:-1/0;Ct(Math.min(e,1),Math.max(s,t.length))}if(yt||ai(t),mt(d.xAxis)&&(i=d.xAxis.categories.map((e,s)=>[e,t[s]])),F(d.xAxis)){let e=d.xAxis.min,s=d.xAxis.max,r=(s-e)/(t.length-1),p=[];for(let g=e;g<=s;g+=r)p.push(`${g}`);i=p.map((g,y)=>[g,t[y]])}return i}a(wt,"transformDataWithoutCategory");function St(t){return ft[t===0?0:t%ft.length]}a(St,"getPlotColorFromPalette");function ni(t,i){let e=wt(i);d.plots.push({type:"line",strokeFill:St(Y),strokeWidth:2,data:e}),Y++}a(ni,"setLineData");function ri(t,i){let e=wt(i);d.plots.push({type:"bar",fill:St(Y),data:e}),Y++}a(ri,"setBarData");function oi(){if(d.plots.length===0)throw Error("No Plot to render, please provide a plot with some data");return d.title=ht(),wi.build(H,d,U,Kt)}a(oi,"getDrawableElem");function hi(){return U}a(hi,"getChartThemeConfig");function li(){return H}a(li,"getChartConfig");var Si=a(function(){Wt(),Y=0,H=At(),d=kt(),U=bt(),ft=U.plotColorPalette.split(",").map(t=>t.trim()),Z=!1,yt=!1},"clear"),_i={getDrawableElem:oi,clear:Si,setAccTitle:Ot,getAccTitle:zt,setDiagramTitle:Nt,getDiagramTitle:ht,getAccDescription:Xt,setAccDescription:Ft,setOrientation:Jt,setXAxisTitle:ti,setXAxisRangeData:Ct,setXAxisBand:ii,setYAxisTitle:ei,setYAxisRangeData:si,setLineData:ni,setBarData:ri,setTmpSVGG:Zt,getChartThemeConfig:hi,getChartConfig:li},Ti=a((t,i,e,s)=>{let r=s.db,p=r.getChartThemeConfig(),g=r.getChartConfig();function y(f){return f==="top"?"text-before-edge":"middle"}a(y,"getDominantBaseLine");function T(f){return f==="left"?"start":f==="right"?"end":"middle"}a(T,"getTextAnchor");function E(f){return`translate(${f.x}, ${f.y}) rotate(${f.rotation||0})`}a(E,"getTextTransformation"),ot.debug(`Rendering xychart chart
`+t);let w=Ht(i),R=w.append("g").attr("class","main"),I=R.append("rect").attr("width",g.width).attr("height",g.height).attr("class","background");Mt(w,g.height,g.width,!0),w.attr("viewBox",`0 0 ${g.width} ${g.height}`),I.attr("fill",p.backgroundColor),r.setTmpSVGG(w.append("g").attr("class","mermaid-tmp-group"));let V=r.getDrawableElem(),S={};function L(f){let k=R,h="";for(let[D]of f.entries()){let X=R;D>0&&S[h]&&(X=S[h]),h+=f[D],k=S[h],k||(k=S[h]=X.append("g").attr("class",f[D]))}return k}a(L,"getGroup");for(let f of V){if(f.data.length===0)continue;let k=L(f.groupTexts);switch(f.type){case"rect":k.selectAll("rect").data(f.data).enter().append("rect").attr("x",h=>h.x).attr("y",h=>h.y).attr("width",h=>h.width).attr("height",h=>h.height).attr("fill",h=>h.fill).attr("stroke",h=>h.strokeFill).attr("stroke-width",h=>h.strokeWidth);break;case"text":k.selectAll("text").data(f.data).enter().append("text").attr("x",0).attr("y",0).attr("fill",h=>h.fill).attr("font-size",h=>h.fontSize).attr("dominant-baseline",h=>y(h.verticalPos)).attr("text-anchor",h=>T(h.horizontalPos)).attr("transform",h=>E(h)).text(h=>h.text);break;case"path":k.selectAll("path").data(f.data).enter().append("path").attr("d",h=>h.path).attr("fill",h=>h.fill?h.fill:"none").attr("stroke",h=>h.strokeFill).attr("stroke-width",h=>h.strokeWidth);break}}},"draw"),Ri={draw:Ti},Mi={parser:di,db:_i,renderer:Ri};export{Mi as diagram};
//# sourceMappingURL=xychartDiagram-6QU3TZC5-MQVPM64I.min.js.map
