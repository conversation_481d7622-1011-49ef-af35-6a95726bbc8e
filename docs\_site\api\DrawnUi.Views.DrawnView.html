<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class DrawnView | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class DrawnView | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Views_DrawnView.md&amp;value=---%0Auid%3A%20DrawnUi.Views.DrawnView%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Views.DrawnView">



  <h1 id="DrawnUi_Views_DrawnView" data-uid="DrawnUi.Views.DrawnView" class="text-break">
Class DrawnView  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.Maui.cs/#L12"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Views.html">Views</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[ContentProperty(&quot;Children&quot;)]
public class DrawnView : ContentView, INotifyPropertyChanged, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IViewController, IVisualElementController, IElementController, IGestureController, IGestureRecognizers, IPropertyMapperView, IHotReloadableView, IReplaceableView, ILayout, ILayoutController, IContentView, IView, IElement, ITransform, IPadding, ICrossPlatformLayout, IDrawnBase, IDisposable, ICanBeUpdatedWithContext, ICanBeUpdated, IAnimatorsManager, IVisualTreeElement</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement">StyleableElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement">NavigableElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement">VisualElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view">View</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout">Layout</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview">TemplatedView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentview">ContentView</a></div>
      <div><span class="xref">DrawnView</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider">IEffectControlProvider</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement">IToolTipElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement">IContextFlyoutElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable">IAnimatable</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.iviewcontroller">IViewController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller">IVisualElementController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller">IElementController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals.igesturecontroller">IGestureController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.igesturerecognizers">IGestureRecognizers</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ipropertymapperview">IPropertyMapperView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ihotreloadableview">IHotReloadableView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ireplaceableview">IReplaceableView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ilayout">ILayout</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ilayoutcontroller">ILayoutController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontentview">IContentView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement">IElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform">ITransform</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ipadding">IPadding</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icrossplatformlayout">ICrossPlatformLayout</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html">ICanBeUpdatedWithContext</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html">ICanBeUpdated</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IAnimatorsManager.html">IAnimatorsManager</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a></div>
    </dd>
  </dl>

  <dl class="typelist derived">
    <dt>Derived</dt>
    <dd>
      <div><a class="xref" href="DrawnUi.Views.Canvas.html">Canvas</a></div>
    </dd>
  </dl>

  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentview.contentproperty">ContentView.ContentProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentview.content">ContentView.Content</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.controltemplateproperty">TemplatedView.ControlTemplateProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.layoutchildren">TemplatedView.LayoutChildren(double, double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onmeasure">TemplatedView.OnMeasure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onapplytemplate">TemplatedView.OnApplyTemplate()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onchildremoved">TemplatedView.OnChildRemoved(Element, int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.gettemplatechild">TemplatedView.GetTemplateChild(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.resolvecontroltemplate">TemplatedView.ResolveControlTemplate()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.measureoverride">TemplatedView.MeasureOverride(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.arrangeoverride">TemplatedView.ArrangeOverride(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.controltemplate">TemplatedView.ControlTemplate</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.isclippedtoboundsproperty">Layout.IsClippedToBoundsProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.cascadeinputtransparentproperty">Layout.CascadeInputTransparentProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.paddingproperty">Layout.PaddingProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.forcelayout">Layout.ForceLayout()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.measure">Layout.Measure(double, double, MeasureFlags)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.layoutchildintoboundingregion">Layout.LayoutChildIntoBoundingRegion(VisualElement, Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.lowerchild">Layout.LowerChild(View)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.raisechild">Layout.RaiseChild(View)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.invalidatelayout">Layout.InvalidateLayout()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onchildmeasureinvalidated#microsoft-maui-controls-compatibility-layout-onchildmeasureinvalidated(system-object-system-eventargs)">Layout.OnChildMeasureInvalidated(object, EventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onchildmeasureinvalidated#microsoft-maui-controls-compatibility-layout-onchildmeasureinvalidated">Layout.OnChildMeasureInvalidated()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onsizeallocated">Layout.OnSizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.shouldinvalidateonchildadded">Layout.ShouldInvalidateOnChildAdded(View)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.shouldinvalidateonchildremoved">Layout.ShouldInvalidateOnChildRemoved(View)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.updatechildrenlayout">Layout.UpdateChildrenLayout()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.crossplatformmeasure">Layout.CrossPlatformMeasure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.crossplatformarrange">Layout.CrossPlatformArrange(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.isclippedtobounds">Layout.IsClippedToBounds</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.padding">Layout.Padding</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.cascadeinputtransparent">Layout.CascadeInputTransparent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.layoutchanged">Layout.LayoutChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.verticaloptionsproperty">View.VerticalOptionsProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.horizontaloptionsproperty">View.HorizontalOptionsProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.marginproperty">View.MarginProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.propertymapper">View.propertyMapper</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate">View.ChangeVisualState()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements">View.GetChildElements(Point)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides">View.GetRendererOverrides&lt;T&gt;()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.gesturecontroller">View.GestureController</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.gesturerecognizers">View.GestureRecognizers</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.horizontaloptions">View.HorizontalOptions</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.margin">View.Margin</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.verticaloptions">View.VerticalOptions</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.navigationproperty">VisualElement.NavigationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.styleproperty">VisualElement.StyleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparentproperty">VisualElement.InputTransparentProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledproperty">VisualElement.IsEnabledProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.xproperty">VisualElement.XProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.yproperty">VisualElement.YProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorxproperty">VisualElement.AnchorXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchoryproperty">VisualElement.AnchorYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationxproperty">VisualElement.TranslationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationyproperty">VisualElement.TranslationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthproperty">VisualElement.WidthProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightproperty">VisualElement.HeightProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationproperty">VisualElement.RotationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationxproperty">VisualElement.RotationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationyproperty">VisualElement.RotationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleproperty">VisualElement.ScaleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalexproperty">VisualElement.ScaleXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleyproperty">VisualElement.ScaleYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clipproperty">VisualElement.ClipProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visualproperty">VisualElement.VisualProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisibleproperty">VisualElement.IsVisibleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacityproperty">VisualElement.OpacityProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolorproperty">VisualElement.BackgroundColorProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundproperty">VisualElement.BackgroundProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviorsproperty">VisualElement.BehaviorsProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggersproperty">VisualElement.TriggersProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequestproperty">VisualElement.WidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequestproperty">VisualElement.HeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequestproperty">VisualElement.MinimumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequestproperty">VisualElement.MinimumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequestproperty">VisualElement.MaximumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequestproperty">VisualElement.MaximumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocusedproperty">VisualElement.IsFocusedProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirectionproperty">VisualElement.FlowDirectionProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.windowproperty">VisualElement.WindowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadowproperty">VisualElement.ShadowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindexproperty">VisualElement.ZIndexProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin">VisualElement.BatchBegin()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit">VisualElement.BatchCommit()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus">VisualElement.Focus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)">VisualElement.Measure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus">VisualElement.Unfocus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure">VisualElement.InvalidateMeasure()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded">VisualElement.OnChildAdded(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered">VisualElement.OnChildrenReordered()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated">VisualElement.SizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty">VisualElement.RefreshIsEnabledProperty()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange">VisualElement.Arrange(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout">VisualElement.Layout(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride">VisualElement.InvalidateMeasureOverride()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor">VisualElement.MapBackgroundColor(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource">VisualElement.MapBackgroundImageSource(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visual">VisualElement.Visual</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirection">VisualElement.FlowDirection</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.window">VisualElement.Window</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorx">VisualElement.AnchorX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchory">VisualElement.AnchorY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolor">VisualElement.BackgroundColor</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.background">VisualElement.Background</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviors">VisualElement.Behaviors</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.bounds">VisualElement.Bounds</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.height">VisualElement.Height</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequest">VisualElement.HeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparent">VisualElement.InputTransparent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabled">VisualElement.IsEnabled</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledcore">VisualElement.IsEnabledCore</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocused">VisualElement.IsFocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisible">VisualElement.IsVisible</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequest">VisualElement.MinimumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequest">VisualElement.MinimumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequest">VisualElement.MaximumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequest">VisualElement.MaximumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacity">VisualElement.Opacity</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotation">VisualElement.Rotation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationx">VisualElement.RotationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationy">VisualElement.RotationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scale">VisualElement.Scale</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalex">VisualElement.ScaleX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaley">VisualElement.ScaleY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationx">VisualElement.TranslationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationy">VisualElement.TranslationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggers">VisualElement.Triggers</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.width">VisualElement.Width</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequest">VisualElement.WidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.x">VisualElement.X</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.y">VisualElement.Y</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clip">VisualElement.Clip</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.resources">VisualElement.Resources</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.frame">VisualElement.Frame</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.handler">VisualElement.Handler</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadow">VisualElement.Shadow</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindex">VisualElement.ZIndex</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.desiredsize">VisualElement.DesiredSize</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isloaded">VisualElement.IsLoaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.childrenreordered">VisualElement.ChildrenReordered</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focused">VisualElement.Focused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureinvalidated">VisualElement.MeasureInvalidated</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizechanged">VisualElement.SizeChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocused">VisualElement.Unfocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.loaded">VisualElement.Loaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unloaded">VisualElement.Unloaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.navigation">NavigableElement.Navigation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.style">StyleableElement.Style</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.styleclass">StyleableElement.StyleClass</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.class">StyleableElement.class</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty">Element.AutomationIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty">Element.ClassIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild">Element.InsertLogicalChild(int, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild">Element.AddLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild">Element.RemoveLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren">Element.ClearLogicalChildren()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname">Element.FindByName(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource">Element.RemoveDynamicResource(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource">Element.SetDynamicResource(BindableProperty, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging">Element.OnParentChanging(ParentChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged">Element.OnParentChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree">Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren">Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid">Element.AutomationId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid">Element.ClassId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects">Element.Effects</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id">Element.Id</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid">Element.StyleId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent">Element.Parent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded">Element.ChildAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved">Element.ChildRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded">Element.DescendantAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved">Element.DescendantRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging">Element.ParentChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged">Element.ParentChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">Element.HandlerChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">Element.HandlerChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty">BindableObject.BindingContextProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)">BindableObject.ClearValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.ClearValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue">BindableObject.GetValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset">BindableObject.IsSet(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding">BindableObject.RemoveBinding(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding">BindableObject.SetBinding(BindableProperty, BindingBase)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings">BindableObject.ApplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging">BindableObject.OnPropertyChanging(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings">BindableObject.UnapplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)">BindableObject.SetValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)">BindableObject.SetValue(BindablePropertyKey, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)">BindableObject.CoerceValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.CoerceValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher">BindableObject.Dispatcher</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext">BindableObject.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged">BindableObject.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging">BindableObject.PropertyChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged">BindableObject.BindingContextChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_">DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__">FluentExtensions.AssignNative&lt;T&gt;(T, out T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_">StaticResourcesExtensions.FindParent&lt;T&gt;(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_">InternalExtensions.FindMauiContext(Element, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_">InternalExtensions.GetParentsPath(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_">StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_">InternalExtensions.DisposeControlAndChildren(IView)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Views_DrawnView__ctor_" data-uid="DrawnUi.Views.DrawnView.#ctor*"></a>

  <h3 id="DrawnUi_Views_DrawnView__ctor" data-uid="DrawnUi.Views.DrawnView.#ctor">
  DrawnView()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1103"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DrawnView()</code></pre>
  </div>













  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Views_DrawnView_CallbackScreenshot" data-uid="DrawnUi.Views.DrawnView.CallbackScreenshot">
  CallbackScreenshot
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L216"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Action&lt;SKImage&gt; CallbackScreenshot</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-1">Action</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skimage">SKImage</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_CanRenderOffScreenProperty" data-uid="DrawnUi.Views.DrawnView.CanRenderOffScreenProperty">
  CanRenderOffScreenProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2044"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty CanRenderOffScreenProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_ChildrenProperty" data-uid="DrawnUi.Views.DrawnView.ChildrenProperty">
  ChildrenProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2233"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ChildrenProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_ClipEffectsProperty" data-uid="DrawnUi.Views.DrawnView.ClipEffectsProperty">
  ClipEffectsProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2356"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ClipEffectsProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_Diagnostics" data-uid="DrawnUi.Views.DrawnView.Diagnostics">
  Diagnostics
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L61"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DrawnView.DiagnosticData Diagnostics</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Views.DrawnView.html">DrawnView</a>.<a class="xref" href="DrawnUi.Views.DrawnView.DiagnosticData.html">DiagnosticData</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_DirtyChildrenTracker" data-uid="DrawnUi.Views.DrawnView.DirtyChildrenTracker">
  DirtyChildrenTracker
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1651"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected ConcurrentDictionary&lt;Guid, SkiaControl&gt; DirtyChildrenTracker</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.concurrent.concurrentdictionary-2">ConcurrentDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.guid">Guid</a>, <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_DisplayRotationProperty" data-uid="DrawnUi.Views.DrawnView.DisplayRotationProperty">
  DisplayRotationProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L624"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty DisplayRotationProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_FrameTimeInterpolator" data-uid="DrawnUi.Views.DrawnView.FrameTimeInterpolator">
  FrameTimeInterpolator
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L469"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected FrameTimeInterpolator FrameTimeInterpolator</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.FrameTimeInterpolator.html">FrameTimeInterpolator</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_InvalidationActionsA" data-uid="DrawnUi.Views.DrawnView.InvalidationActionsA">
  InvalidationActionsA
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1619"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected readonly Dictionary&lt;Action, SkiaControl&gt; InvalidationActionsA</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a>, <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_InvalidationActionsB" data-uid="DrawnUi.Views.DrawnView.InvalidationActionsB">
  InvalidationActionsB
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1620"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected readonly Dictionary&lt;Action, SkiaControl&gt; InvalidationActionsB</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a>, <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_LastDrawnRect" data-uid="DrawnUi.Views.DrawnView.LastDrawnRect">
  LastDrawnRect
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1387"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SKRect LastDrawnRect</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_LockDraw" data-uid="DrawnUi.Views.DrawnView.LockDraw">
  LockDraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1571"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected object LockDraw</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_LockIterateListeners" data-uid="DrawnUi.Views.DrawnView.LockIterateListeners">
  LockIterateListeners
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L262"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected object LockIterateListeners</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_LockStartOffscreenQueue" data-uid="DrawnUi.Views.DrawnView.LockStartOffscreenQueue">
  LockStartOffscreenQueue
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1677"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected object LockStartOffscreenQueue</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_RenderingModeProperty" data-uid="DrawnUi.Views.DrawnView.RenderingModeProperty">
  RenderingModeProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2031"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty RenderingModeProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_RenderingScaleProperty" data-uid="DrawnUi.Views.DrawnView.RenderingScaleProperty">
  RenderingScaleProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1975"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty RenderingScaleProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_RenderingSubscribers" data-uid="DrawnUi.Views.DrawnView.RenderingSubscribers">
  RenderingSubscribers
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L222"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>For native controls over Canvas to be notified after every of their position</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Dictionary&lt;SkiaControl, bool&gt; RenderingSubscribers</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_UpdateLocksProperty" data-uid="DrawnUi.Views.DrawnView.UpdateLocksProperty">
  UpdateLocksProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L979"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty UpdateLocksProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_UpdateModeProperty" data-uid="DrawnUi.Views.DrawnView.UpdateModeProperty">
  UpdateModeProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2335"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty UpdateModeProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_Value1Property" data-uid="DrawnUi.Views.DrawnView.Value1Property">
  Value1Property
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2368"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty Value1Property</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_Value2Property" data-uid="DrawnUi.Views.DrawnView.Value2Property">
  Value2Property
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2381"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty Value2Property</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_Value3Property" data-uid="DrawnUi.Views.DrawnView.Value3Property">
  Value3Property
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2394"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty Value3Property</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_Value4Property" data-uid="DrawnUi.Views.DrawnView.Value4Property">
  Value4Property
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2407"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty Value4Property</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView__fps" data-uid="DrawnUi.Views.DrawnView._fps">
  _fps
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1544"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected double _fps</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Views_DrawnView_semaphoreOffscreenProcess" data-uid="DrawnUi.Views.DrawnView.semaphoreOffscreenProcess">
  semaphoreOffscreenProcess
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1685"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SemaphoreSlim semaphoreOffscreenProcess</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.semaphoreslim">SemaphoreSlim</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Views_DrawnView_AnimatingControls_" data-uid="DrawnUi.Views.DrawnView.AnimatingControls*"></a>

  <h3 id="DrawnUi_Views_DrawnView_AnimatingControls" data-uid="DrawnUi.Views.DrawnView.AnimatingControls">
  AnimatingControls
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L467"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Tracking controls that what to be animated right now so we constantly refresh
canvas until there is none left</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ConcurrentDictionary&lt;Guid, ISkiaAnimator&gt; AnimatingControls { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.concurrent.concurrentdictionary-2">ConcurrentDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.guid">Guid</a>, <a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_AvailableDestination_" data-uid="DrawnUi.Views.DrawnView.AvailableDestination*"></a>

  <h3 id="DrawnUi_Views_DrawnView_AvailableDestination" data-uid="DrawnUi.Views.DrawnView.AvailableDestination">
  AvailableDestination
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1511"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKRect AvailableDestination { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_CanDraw_" data-uid="DrawnUi.Views.DrawnView.CanDraw*"></a>

  <h3 id="DrawnUi_Views_DrawnView_CanDraw" data-uid="DrawnUi.Views.DrawnView.CanDraw">
  CanDraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2063"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Indicates that it is allowed to be rendered by engine, internal use</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool CanDraw { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_CanRenderOffScreen_" data-uid="DrawnUi.Views.DrawnView.CanRenderOffScreen*"></a>

  <h3 id="DrawnUi_Views_DrawnView_CanRenderOffScreen" data-uid="DrawnUi.Views.DrawnView.CanRenderOffScreen">
  CanRenderOffScreen
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2053"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>If this is check you view will be refreshed even offScreen or hidden</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool CanRenderOffScreen { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_CanvasFps_" data-uid="DrawnUi.Views.DrawnView.CanvasFps*"></a>

  <h3 id="DrawnUi_Views_DrawnView_CanvasFps" data-uid="DrawnUi.Views.DrawnView.CanvasFps">
  CanvasFps
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1554"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Actual FPS</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double CanvasFps { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_CanvasView_" data-uid="DrawnUi.Views.DrawnView.CanvasView*"></a>

  <h3 id="DrawnUi_Views_DrawnView_CanvasView" data-uid="DrawnUi.Views.DrawnView.CanvasView">
  CanvasView
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L559"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ISkiaDrawable CanvasView { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ISkiaDrawable.html">ISkiaDrawable</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Children_" data-uid="DrawnUi.Views.DrawnView.Children*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Children" data-uid="DrawnUi.Views.DrawnView.Children">
  Children
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2246"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IList&lt;SkiaControl&gt; Children { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_ClipEffects_" data-uid="DrawnUi.Views.DrawnView.ClipEffects*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ClipEffects" data-uid="DrawnUi.Views.DrawnView.ClipEffects">
  ClipEffects
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2362"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ClipEffects { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Clipping_" data-uid="DrawnUi.Views.DrawnView.Clipping*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Clipping" data-uid="DrawnUi.Views.DrawnView.Clipping">
  Clipping
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1108"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Action&lt;SKPath, SKRect&gt; Clipping { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpath">SKPath</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Delayed_" data-uid="DrawnUi.Views.DrawnView.Delayed*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Delayed" data-uid="DrawnUi.Views.DrawnView.Delayed">
  Delayed
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L748"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Grid Delayed { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.grid">Grid</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Destination_" data-uid="DrawnUi.Views.DrawnView.Destination*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Destination" data-uid="DrawnUi.Views.DrawnView.Destination">
  Destination
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2114"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKRect Destination { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_DeviceRotation_" data-uid="DrawnUi.Views.DrawnView.DeviceRotation*"></a>

  <h3 id="DrawnUi_Views_DrawnView_DeviceRotation" data-uid="DrawnUi.Views.DrawnView.DeviceRotation">
  DeviceRotation
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L640"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int DeviceRotation { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_DisposeManager_" data-uid="DrawnUi.Views.DrawnView.DisposeManager*"></a>

  <h3 id="DrawnUi_Views_DrawnView_DisposeManager" data-uid="DrawnUi.Views.DrawnView.DisposeManager">
  DisposeManager
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1587"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected DisposableManager DisposeManager { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Views.DisposableManager.html">DisposableManager</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_DrawingRect_" data-uid="DrawnUi.Views.DrawnView.DrawingRect*"></a>

  <h3 id="DrawnUi_Views_DrawnView_DrawingRect" data-uid="DrawnUi.Views.DrawnView.DrawingRect">
  DrawingRect
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L271"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKRect DrawingRect { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_DrawingThreadId_" data-uid="DrawnUi.Views.DrawnView.DrawingThreadId*"></a>

  <h3 id="DrawnUi_Views_DrawnView_DrawingThreadId" data-uid="DrawnUi.Views.DrawnView.DrawingThreadId">
  DrawingThreadId
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1368"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Can use this to manage double buffering to detect if we are in the drawing thread or in background.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int DrawingThreadId { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_DrawingThreads_" data-uid="DrawnUi.Views.DrawnView.DrawingThreads*"></a>

  <h3 id="DrawnUi_Views_DrawnView_DrawingThreads" data-uid="DrawnUi.Views.DrawnView.DrawingThreads">
  DrawingThreads
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1649"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>For debugging purposes check if dont have concurrent threads</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int DrawingThreads { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_ExecuteAfterDraw_" data-uid="DrawnUi.Views.DrawnView.ExecuteAfterDraw*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ExecuteAfterDraw" data-uid="DrawnUi.Views.DrawnView.ExecuteAfterDraw">
  ExecuteAfterDraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L215"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Queue&lt;Action&gt; ExecuteAfterDraw { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.queue-1">Queue</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_ExecuteBeforeDraw_" data-uid="DrawnUi.Views.DrawnView.ExecuteBeforeDraw*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ExecuteBeforeDraw" data-uid="DrawnUi.Views.DrawnView.ExecuteBeforeDraw">
  ExecuteBeforeDraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L214"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Queue&lt;Action&gt; ExecuteBeforeDraw { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.queue-1">Queue</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_FPS_" data-uid="DrawnUi.Views.DrawnView.FPS*"></a>

  <h3 id="DrawnUi_Views_DrawnView_FPS" data-uid="DrawnUi.Views.DrawnView.FPS">
  FPS
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1567"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Average FPS</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double FPS { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_FocusLocked_" data-uid="DrawnUi.Views.DrawnView.FocusLocked*"></a>

  <h3 id="DrawnUi_Views_DrawnView_FocusLocked" data-uid="DrawnUi.Views.DrawnView.FocusLocked">
  FocusLocked
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2422"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool FocusLocked { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_FocusedChild_" data-uid="DrawnUi.Views.DrawnView.FocusedChild*"></a>

  <h3 id="DrawnUi_Views_DrawnView_FocusedChild" data-uid="DrawnUi.Views.DrawnView.FocusedChild">
  FocusedChild
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2543"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Is set upon the consumer of the DOWN gesture. Calls ReportFocus methos when set.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ISkiaGestureListener FocusedChild { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_FrameNumber_" data-uid="DrawnUi.Views.DrawnView.FrameNumber*"></a>

  <h3 id="DrawnUi_Views_DrawnView_FrameNumber" data-uid="DrawnUi.Views.DrawnView.FrameNumber">
  FrameNumber
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1573"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public long FrameNumber { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_FrameTime_" data-uid="DrawnUi.Views.DrawnView.FrameTime*"></a>

  <h3 id="DrawnUi_Views_DrawnView_FrameTime" data-uid="DrawnUi.Views.DrawnView.FrameTime">
  FrameTime
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1549"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Frame started rendering nanoseconds</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public long FrameTime { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_GestureListeners_" data-uid="DrawnUi.Views.DrawnView.GestureListeners*"></a>

  <h3 id="DrawnUi_Views_DrawnView_GestureListeners" data-uid="DrawnUi.Views.DrawnView.GestureListeners">
  GestureListeners
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L267"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Children we should check for touch hits</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SortedGestureListeners GestureListeners { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SortedGestureListeners.html">SortedGestureListeners</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_HasHandler_" data-uid="DrawnUi.Views.DrawnView.HasHandler*"></a>

  <h3 id="DrawnUi_Views_DrawnView_HasHandler" data-uid="DrawnUi.Views.DrawnView.HasHandler">
  HasHandler
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L650"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool HasHandler { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_InvalidatedCanvas_" data-uid="DrawnUi.Views.DrawnView.InvalidatedCanvas*"></a>

  <h3 id="DrawnUi_Views_DrawnView_InvalidatedCanvas" data-uid="DrawnUi.Views.DrawnView.InvalidatedCanvas">
  InvalidatedCanvas
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L745"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>A very important tracking prop to avoid saturating main thread with too many updates</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected long InvalidatedCanvas { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_IsDirty_" data-uid="DrawnUi.Views.DrawnView.IsDirty*"></a>

  <h3 id="DrawnUi_Views_DrawnView_IsDirty" data-uid="DrawnUi.Views.DrawnView.IsDirty">
  IsDirty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L88"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsDirty { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_IsDisposed_" data-uid="DrawnUi.Views.DrawnView.IsDisposed*"></a>

  <h3 id="DrawnUi_Views_DrawnView_IsDisposed" data-uid="DrawnUi.Views.DrawnView.IsDisposed">
  IsDisposed
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1350"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsDisposed { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_IsDisposing_" data-uid="DrawnUi.Views.DrawnView.IsDisposing*"></a>

  <h3 id="DrawnUi_Views_DrawnView_IsDisposing" data-uid="DrawnUi.Views.DrawnView.IsDisposing">
  IsDisposing
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L821"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsDisposing { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_IsGhost_" data-uid="DrawnUi.Views.DrawnView.IsGhost*"></a>

  <h3 id="DrawnUi_Views_DrawnView_IsGhost" data-uid="DrawnUi.Views.DrawnView.IsGhost">
  IsGhost
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1084"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsGhost { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_IsHiddenInViewTree_" data-uid="DrawnUi.Views.DrawnView.IsHiddenInViewTree*"></a>

  <h3 id="DrawnUi_Views_DrawnView_IsHiddenInViewTree" data-uid="DrawnUi.Views.DrawnView.IsHiddenInViewTree">
  IsHiddenInViewTree
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2076"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Indicates that view is either hidden or offscreen.
This disables rendering if you don't set CanRenderOffScreen to true</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsHiddenInViewTree { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_IsRendering_" data-uid="DrawnUi.Views.DrawnView.IsRendering*"></a>

  <h3 id="DrawnUi_Views_DrawnView_IsRendering" data-uid="DrawnUi.Views.DrawnView.IsRendering">
  IsRendering
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L747"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsRendering { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_IsUsingHardwareAcceleration_" data-uid="DrawnUi.Views.DrawnView.IsUsingHardwareAcceleration*"></a>

  <h3 id="DrawnUi_Views_DrawnView_IsUsingHardwareAcceleration" data-uid="DrawnUi.Views.DrawnView.IsUsingHardwareAcceleration">
  IsUsingHardwareAcceleration
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L75"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsUsingHardwareAcceleration { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_MeasuredSize_" data-uid="DrawnUi.Views.DrawnView.MeasuredSize*"></a>

  <h3 id="DrawnUi_Views_DrawnView_MeasuredSize" data-uid="DrawnUi.Views.DrawnView.MeasuredSize">
  MeasuredSize
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1268"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ScaledSize MeasuredSize { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_NeedAutoHeight_" data-uid="DrawnUi.Views.DrawnView.NeedAutoHeight*"></a>

  <h3 id="DrawnUi_Views_DrawnView_NeedAutoHeight" data-uid="DrawnUi.Views.DrawnView.NeedAutoHeight">
  NeedAutoHeight
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L964"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool NeedAutoHeight { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_NeedAutoSize_" data-uid="DrawnUi.Views.DrawnView.NeedAutoSize*"></a>

  <h3 id="DrawnUi_Views_DrawnView_NeedAutoSize" data-uid="DrawnUi.Views.DrawnView.NeedAutoSize">
  NeedAutoSize
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L959"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool NeedAutoSize { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_NeedAutoWidth_" data-uid="DrawnUi.Views.DrawnView.NeedAutoWidth*"></a>

  <h3 id="DrawnUi_Views_DrawnView_NeedAutoWidth" data-uid="DrawnUi.Views.DrawnView.NeedAutoWidth">
  NeedAutoWidth
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L969"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool NeedAutoWidth { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_NeedCheckParentVisibility_" data-uid="DrawnUi.Views.DrawnView.NeedCheckParentVisibility*"></a>

  <h3 id="DrawnUi_Views_DrawnView_NeedCheckParentVisibility" data-uid="DrawnUi.Views.DrawnView.NeedCheckParentVisibility">
  NeedCheckParentVisibility
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2091"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool NeedCheckParentVisibility { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_NeedGlobalRefreshCount_" data-uid="DrawnUi.Views.DrawnView.NeedGlobalRefreshCount*"></a>

  <h3 id="DrawnUi_Views_DrawnView_NeedGlobalRefreshCount" data-uid="DrawnUi.Views.DrawnView.NeedGlobalRefreshCount">
  NeedGlobalRefreshCount
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L658"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public long NeedGlobalRefreshCount { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_NeedMeasure_" data-uid="DrawnUi.Views.DrawnView.NeedMeasure*"></a>

  <h3 id="DrawnUi_Views_DrawnView_NeedMeasure" data-uid="DrawnUi.Views.DrawnView.NeedMeasure">
  NeedMeasure
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1298"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The virtual view needs native measurement</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual bool NeedMeasure { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_NeedMeasureDrawn_" data-uid="DrawnUi.Views.DrawnView.NeedMeasureDrawn*"></a>

  <h3 id="DrawnUi_Views_DrawnView_NeedMeasureDrawn" data-uid="DrawnUi.Views.DrawnView.NeedMeasureDrawn">
  NeedMeasureDrawn
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L677"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Underlying drawn views need measurement</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected bool NeedMeasureDrawn { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_NeedRedraw_" data-uid="DrawnUi.Views.DrawnView.NeedRedraw*"></a>

  <h3 id="DrawnUi_Views_DrawnView_NeedRedraw" data-uid="DrawnUi.Views.DrawnView.NeedRedraw">
  NeedRedraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L86"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool NeedRedraw { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_OrderedDraw_" data-uid="DrawnUi.Views.DrawnView.OrderedDraw*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OrderedDraw" data-uid="DrawnUi.Views.DrawnView.OrderedDraw">
  OrderedDraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L732"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool OrderedDraw { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_PaintSystem_" data-uid="DrawnUi.Views.DrawnView.PaintSystem*"></a>

  <h3 id="DrawnUi_Views_DrawnView_PaintSystem" data-uid="DrawnUi.Views.DrawnView.PaintSystem">
  PaintSystem
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2113"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SKPaint PaintSystem { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpaint">SKPaint</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_PostAnimators_" data-uid="DrawnUi.Views.DrawnView.PostAnimators*"></a>

  <h3 id="DrawnUi_Views_DrawnView_PostAnimators" data-uid="DrawnUi.Views.DrawnView.PostAnimators">
  PostAnimators
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L459"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Executed after the rendering</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public List&lt;IOverlayEffect&gt; PostAnimators { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.IOverlayEffect.html">IOverlayEffect</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_RenderingMode_" data-uid="DrawnUi.Views.DrawnView.RenderingMode*"></a>

  <h3 id="DrawnUi_Views_DrawnView_RenderingMode" data-uid="DrawnUi.Views.DrawnView.RenderingMode">
  RenderingMode
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2038"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public RenderingModeType RenderingMode { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.RenderingModeType.html">RenderingModeType</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_RenderingScale_" data-uid="DrawnUi.Views.DrawnView.RenderingScale*"></a>

  <h3 id="DrawnUi_Views_DrawnView_RenderingScale" data-uid="DrawnUi.Views.DrawnView.RenderingScale">
  RenderingScale
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1995"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RenderingScale { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_ShouldInvalidateByChildren_" data-uid="DrawnUi.Views.DrawnView.ShouldInvalidateByChildren*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ShouldInvalidateByChildren" data-uid="DrawnUi.Views.DrawnView.ShouldInvalidateByChildren">
  ShouldInvalidateByChildren
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L974"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual bool ShouldInvalidateByChildren { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_StopDrawingWhenUpdateIsLocked_" data-uid="DrawnUi.Views.DrawnView.StopDrawingWhenUpdateIsLocked*"></a>

  <h3 id="DrawnUi_Views_DrawnView_StopDrawingWhenUpdateIsLocked" data-uid="DrawnUi.Views.DrawnView.StopDrawingWhenUpdateIsLocked">
  StopDrawingWhenUpdateIsLocked
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L716"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Set this to true if you do not want the canvas to be redrawn as transparent and showing content below the canvas (splash?..) when UpdateLocks is True</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool StopDrawingWhenUpdateIsLocked { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Tag_" data-uid="DrawnUi.Views.DrawnView.Tag*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Tag" data-uid="DrawnUi.Views.DrawnView.Tag">
  Tag
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1126"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string Tag { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_TimeDrawingComplete_" data-uid="DrawnUi.Views.DrawnView.TimeDrawingComplete*"></a>

  <h3 id="DrawnUi_Views_DrawnView_TimeDrawingComplete" data-uid="DrawnUi.Views.DrawnView.TimeDrawingComplete">
  TimeDrawingComplete
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L719"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DateTime TimeDrawingComplete { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.datetime">DateTime</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_TimeDrawingStarted_" data-uid="DrawnUi.Views.DrawnView.TimeDrawingStarted*"></a>

  <h3 id="DrawnUi_Views_DrawnView_TimeDrawingStarted" data-uid="DrawnUi.Views.DrawnView.TimeDrawingStarted">
  TimeDrawingStarted
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L718"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DateTime TimeDrawingStarted { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.datetime">DateTime</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Uid_" data-uid="DrawnUi.Views.DrawnView.Uid*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Uid" data-uid="DrawnUi.Views.DrawnView.Uid">
  Uid
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1013"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Guid Uid { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.guid">Guid</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_UpdateLocks_" data-uid="DrawnUi.Views.DrawnView.UpdateLocks*"></a>

  <h3 id="DrawnUi_Views_DrawnView_UpdateLocks" data-uid="DrawnUi.Views.DrawnView.UpdateLocks">
  UpdateLocks
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L985"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int UpdateLocks { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_UpdateMode_" data-uid="DrawnUi.Views.DrawnView.UpdateMode*"></a>

  <h3 id="DrawnUi_Views_DrawnView_UpdateMode" data-uid="DrawnUi.Views.DrawnView.UpdateMode">
  UpdateMode
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2350"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public UpdateMode UpdateMode { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Infrastructure.Enums.UpdateMode.html">UpdateMode</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Value1_" data-uid="DrawnUi.Views.DrawnView.Value1*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Value1" data-uid="DrawnUi.Views.DrawnView.Value1">
  Value1
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2375"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Value1 { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Value2_" data-uid="DrawnUi.Views.DrawnView.Value2*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Value2" data-uid="DrawnUi.Views.DrawnView.Value2">
  Value2
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2388"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Value2 { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Value3_" data-uid="DrawnUi.Views.DrawnView.Value3*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Value3" data-uid="DrawnUi.Views.DrawnView.Value3">
  Value3
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2401"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Value3 { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Value4_" data-uid="DrawnUi.Views.DrawnView.Value4*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Value4" data-uid="DrawnUi.Views.DrawnView.Value4">
  Value4
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2414"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Value4 { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_Views_" data-uid="DrawnUi.Views.DrawnView.Views*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Views" data-uid="DrawnUi.Views.DrawnView.Views">
  Views
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2154"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>For code-behind access of children, XAML is using Children property</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public List&lt;SkiaControl&gt; Views { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_WasRendered_" data-uid="DrawnUi.Views.DrawnView.WasRendered*"></a>

  <h3 id="DrawnUi_Views_DrawnView_WasRendered" data-uid="DrawnUi.Views.DrawnView.WasRendered">
  WasRendered
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1370"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool WasRendered { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Views_DrawnView_mLastFrameTime_" data-uid="DrawnUi.Views.DrawnView.mLastFrameTime*"></a>

  <h3 id="DrawnUi_Views_DrawnView_mLastFrameTime" data-uid="DrawnUi.Views.DrawnView.mLastFrameTime">
  mLastFrameTime
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L470"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public long mLastFrameTime { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Views_DrawnView_AddAnimator_" data-uid="DrawnUi.Views.DrawnView.AddAnimator*"></a>

  <h3 id="DrawnUi_Views_DrawnView_AddAnimator_DrawnUi_Draw_ISkiaAnimator_" data-uid="DrawnUi.Views.DrawnView.AddAnimator(DrawnUi.Draw.ISkiaAnimator)">
  AddAnimator(ISkiaAnimator)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L276"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddAnimator(ISkiaAnimator animator)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>animator</code> <a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_AddOrRemoveView_" data-uid="DrawnUi.Views.DrawnView.AddOrRemoveView*"></a>

  <h3 id="DrawnUi_Views_DrawnView_AddOrRemoveView_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Views.DrawnView.AddOrRemoveView(DrawnUi.Draw.SkiaControl,System.Boolean)">
  AddOrRemoveView(SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2254"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void AddOrRemoveView(SkiaControl subView, bool add)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>subView</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>add</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_AddSubView_" data-uid="DrawnUi.Views.DrawnView.AddSubView*"></a>

  <h3 id="DrawnUi_Views_DrawnView_AddSubView_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.AddSubView(DrawnUi.Draw.SkiaControl)">
  AddSubView(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2177"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Directly adds a view to the control, without any layouting. Use this instead of Views.Add() to avoid memory leaks etc</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddSubView(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_Arrange_" data-uid="DrawnUi.Views.DrawnView.Arrange*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Arrange_SkiaSharp_SKRect_System_Double_System_Double_System_Double_" data-uid="DrawnUi.Views.DrawnView.Arrange(SkiaSharp.SKRect,System.Double,System.Double,System.Double)">
  Arrange(SKRect, double, double, double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1263"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>destination in PIXELS, requests in UNITS. resulting Destination prop will be filed in PIXELS.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void Arrange(SKRect destination, double widthRequest, double heightRequest, double scale = 1)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>destination</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd><p>PIXELS</p>
</dd>
    <dt><code>widthRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd><p>UNITS</p>
</dd>
    <dt><code>heightRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd><p>UNITS</p>
</dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_CalculateLayout_" data-uid="DrawnUi.Views.DrawnView.CalculateLayout*"></a>

  <h3 id="DrawnUi_Views_DrawnView_CalculateLayout_SkiaSharp_SKRect_System_Double_System_Double_System_Double_" data-uid="DrawnUi.Views.DrawnView.CalculateLayout(SkiaSharp.SKRect,System.Double,System.Double,System.Double)">
  CalculateLayout(SKRect, double, double, double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1146"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>destination in PIXELS, requests in UNITS. resulting Destination prop will be filed in PIXELS.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKRect CalculateLayout(SKRect destination, double widthRequest, double heightRequest, double scale = 1)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>destination</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd><p>PIXELS</p>
</dd>
    <dt><code>widthRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd><p>UNITS</p>
</dd>
    <dt><code>heightRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd><p>UNITS</p>
</dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_CheckElementVisibility_" data-uid="DrawnUi.Views.DrawnView.CheckElementVisibility*"></a>

  <h3 id="DrawnUi_Views_DrawnView_CheckElementVisibility_Microsoft_Maui_Controls_VisualElement_" data-uid="DrawnUi.Views.DrawnView.CheckElementVisibility(Microsoft.Maui.Controls.VisualElement)">
  CheckElementVisibility(VisualElement)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2691"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void CheckElementVisibility(VisualElement element)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>element</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement">VisualElement</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_ClearChildren_" data-uid="DrawnUi.Views.DrawnView.ClearChildren*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ClearChildren" data-uid="DrawnUi.Views.DrawnView.ClearChildren">
  ClearChildren()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2156"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ClearChildren()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_ClipSmart_" data-uid="DrawnUi.Views.DrawnView.ClipSmart*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_" data-uid="DrawnUi.Views.DrawnView.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)">
  ClipSmart(SKCanvas, SKPath, SKClipOperation)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2701"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Clip using internal custom settings of the control</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ClipSmart(SKCanvas canvas, SKPath path, SKClipOperation operation = SKClipOperation.Intersect)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>canvas</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas">SKCanvas</a></dt>
    <dd></dd>
    <dt><code>path</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpath">SKPath</a></dt>
    <dd></dd>
    <dt><code>operation</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skclipoperation">SKClipOperation</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_CommitInvalidations_" data-uid="DrawnUi.Views.DrawnView.CommitInvalidations*"></a>

  <h3 id="DrawnUi_Views_DrawnView_CommitInvalidations" data-uid="DrawnUi.Views.DrawnView.CommitInvalidations">
  CommitInvalidations()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1663"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void CommitInvalidations()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_ConnectedHandler_" data-uid="DrawnUi.Views.DrawnView.ConnectedHandler*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ConnectedHandler" data-uid="DrawnUi.Views.DrawnView.ConnectedHandler">
  ConnectedHandler()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L692"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ConnectedHandler()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_CreateClip_" data-uid="DrawnUi.Views.DrawnView.CreateClip*"></a>

  <h3 id="DrawnUi_Views_DrawnView_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_" data-uid="DrawnUi.Views.DrawnView.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)">
  CreateClip(object, bool, SKPath)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1110"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Creates a new disposable SKPath for clipping content according to the control shape and size.
Create this control clip for painting content.
Pass arguments if you want to use some time-frozen data for painting at any time from any thread..
If applyPosition is false will create clip without using drawing posiition, like if was drawing at 0,0.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual SKPath CreateClip(object arguments, bool usePosition, SKPath path = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
    <dt><code>usePosition</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>path</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpath">SKPath</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpath">SKPath</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_CreateSkiaView_" data-uid="DrawnUi.Views.DrawnView.CreateSkiaView*"></a>

  <h3 id="DrawnUi_Views_DrawnView_CreateSkiaView" data-uid="DrawnUi.Views.DrawnView.CreateSkiaView">
  CreateSkiaView()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L758"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Will safely destroy existing if any</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void CreateSkiaView()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_DestroySkiaView_" data-uid="DrawnUi.Views.DrawnView.DestroySkiaView*"></a>

  <h3 id="DrawnUi_Views_DrawnView_DestroySkiaView" data-uid="DrawnUi.Views.DrawnView.DestroySkiaView">
  DestroySkiaView()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L782"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void DestroySkiaView()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_DisconnectedHandler_" data-uid="DrawnUi.Views.DrawnView.DisconnectedHandler*"></a>

  <h3 id="DrawnUi_Views_DrawnView_DisconnectedHandler" data-uid="DrawnUi.Views.DrawnView.DisconnectedHandler">
  DisconnectedHandler()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L652"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void DisconnectedHandler()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_Dispose_" data-uid="DrawnUi.Views.DrawnView.Dispose*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Dispose" data-uid="DrawnUi.Views.DrawnView.Dispose">
  Dispose()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L830"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Dispose()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_DisposeObject_" data-uid="DrawnUi.Views.DrawnView.DisposeObject*"></a>

  <h3 id="DrawnUi_Views_DrawnView_DisposeObject_System_IDisposable_" data-uid="DrawnUi.Views.DrawnView.DisposeObject(System.IDisposable)">
  DisposeObject(IDisposable)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1577"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DisposeObject(IDisposable resource)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>resource</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_Draw_" data-uid="DrawnUi.Views.DrawnView.Draw*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Draw_DrawnUi_Draw_DrawingContext_" data-uid="DrawnUi.Views.DrawnView.Draw(DrawnUi.Draw.DrawingContext)">
  Draw(DrawingContext)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1782"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void Draw(DrawingContext context)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_DumpLayersTree_" data-uid="DrawnUi.Views.DrawnView.DumpLayersTree*"></a>

  <h3 id="DrawnUi_Views_DrawnView_DumpLayersTree_DrawnUi_Draw_VisualLayer_System_String_System_Boolean_System_Int32_" data-uid="DrawnUi.Views.DrawnView.DumpLayersTree(DrawnUi.Draw.VisualLayer,System.String,System.Boolean,System.Int32)">
  DumpLayersTree(VisualLayer, string, bool, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DumpLayersTree(VisualLayer node, string prefix = &quot;&quot;, bool isLast = true, int level = 0)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>node</code> <a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></dt>
    <dd></dd>
    <dt><code>prefix</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>isLast</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>level</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_ExecuteAnimators_" data-uid="DrawnUi.Views.DrawnView.ExecuteAnimators*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ExecuteAnimators_System_Int64_" data-uid="DrawnUi.Views.DrawnView.ExecuteAnimators(System.Int64)">
  ExecuteAnimators(long)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L472"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected int ExecuteAnimators(long frameTime)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>frameTime</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_ExecutePostAnimators_" data-uid="DrawnUi.Views.DrawnView.ExecutePostAnimators*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ExecutePostAnimators_DrawnUi_Draw_DrawingContext_" data-uid="DrawnUi.Views.DrawnView.ExecutePostAnimators(DrawnUi.Draw.DrawingContext)">
  ExecutePostAnimators(DrawingContext)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L419"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int ExecutePostAnimators(DrawingContext context)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_Finalize_" data-uid="DrawnUi.Views.DrawnView.Finalize*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Finalize" data-uid="DrawnUi.Views.DrawnView.Finalize">
  ~DrawnView()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L823"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected ~DrawnView()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_FixDensity_" data-uid="DrawnUi.Views.DrawnView.FixDensity*"></a>

  <h3 id="DrawnUi_Views_DrawnView_FixDensity" data-uid="DrawnUi.Views.DrawnView.FixDensity">
  FixDensity()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L699"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void FixDensity()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_GetBackInvalidations_" data-uid="DrawnUi.Views.DrawnView.GetBackInvalidations*"></a>

  <h3 id="DrawnUi_Views_DrawnView_GetBackInvalidations" data-uid="DrawnUi.Views.DrawnView.GetBackInvalidations">
  GetBackInvalidations()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1630"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Dictionary&lt;Action, SkiaControl&gt; GetBackInvalidations()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a>, <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_GetDensity_" data-uid="DrawnUi.Views.DrawnView.GetDensity*"></a>

  <h3 id="DrawnUi_Views_DrawnView_GetDensity" data-uid="DrawnUi.Views.DrawnView.GetDensity">
  GetDensity()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L750"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double GetDensity()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_GetFrontInvalidations_" data-uid="DrawnUi.Views.DrawnView.GetFrontInvalidations*"></a>

  <h3 id="DrawnUi_Views_DrawnView_GetFrontInvalidations" data-uid="DrawnUi.Views.DrawnView.GetFrontInvalidations">
  GetFrontInvalidations()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1622"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Dictionary&lt;Action, SkiaControl&gt; GetFrontInvalidations()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a>, <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_GetIsVisibleWithParent_" data-uid="DrawnUi.Views.DrawnView.GetIsVisibleWithParent*"></a>

  <h3 id="DrawnUi_Views_DrawnView_GetIsVisibleWithParent_Microsoft_Maui_Controls_VisualElement_" data-uid="DrawnUi.Views.DrawnView.GetIsVisibleWithParent(Microsoft.Maui.Controls.VisualElement)">
  GetIsVisibleWithParent(VisualElement)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2662"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool GetIsVisibleWithParent(VisualElement element)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>element</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement">VisualElement</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_GetOnScreenVisibleArea_" data-uid="DrawnUi.Views.DrawnView.GetOnScreenVisibleArea*"></a>

  <h3 id="DrawnUi_Views_DrawnView_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_" data-uid="DrawnUi.Views.DrawnView.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)">
  GetOnScreenVisibleArea(DrawingContext, Vector2)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L137"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>For virtualization. For this method to be conditional we introduced the <code>pixelsDestination</code>
parameter so that the Parent could return different visible areas upon context.
Normally pass your current destination you are drawing into as this parameter.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ScaledRect GetOnScreenVisibleArea(DrawingContext context, Vector2 inflateByPixels = default)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
    <dt><code>inflateByPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.numerics.vector2">Vector2</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledRect.html">ScaledRect</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_GetOrderedSubviews_" data-uid="DrawnUi.Views.DrawnView.GetOrderedSubviews*"></a>

  <h3 id="DrawnUi_Views_DrawnView_GetOrderedSubviews" data-uid="DrawnUi.Views.DrawnView.GetOrderedSubviews">
  GetOrderedSubviews()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1522"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>For non templated simple subviews</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IReadOnlyList&lt;SkiaControl&gt; GetOrderedSubviews()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_InitFramework_" data-uid="DrawnUi.Views.DrawnView.InitFramework*"></a>

  <h3 id="DrawnUi_Views_DrawnView_InitFramework_System_Boolean_" data-uid="DrawnUi.Views.DrawnView.InitFramework(System.Boolean)">
  InitFramework(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.Maui.cs/#L20"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void InitFramework(bool subscribe)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>subscribe</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_Invalidate_" data-uid="DrawnUi.Views.DrawnView.Invalidate*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Invalidate" data-uid="DrawnUi.Views.DrawnView.Invalidate">
  Invalidate()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L914"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Makes the control dirty, in need to be remeasured and rendered but this doesn't call Update, it's up yo you</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void Invalidate()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_InvalidateByChild_" data-uid="DrawnUi.Views.DrawnView.InvalidateByChild*"></a>

  <h3 id="DrawnUi_Views_DrawnView_InvalidateByChild_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.InvalidateByChild(DrawnUi.Draw.SkiaControl)">
  InvalidateByChild(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L119"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This is needed by layout to track which child changed to sometimes avoid recalculating other children</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void InvalidateByChild(SkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_InvalidateCanvas_" data-uid="DrawnUi.Views.DrawnView.InvalidateCanvas*"></a>

  <h3 id="DrawnUi_Views_DrawnView_InvalidateCanvas" data-uid="DrawnUi.Views.DrawnView.InvalidateCanvas">
  InvalidateCanvas()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2556"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Obsolete(&quot;Used by Update() when Super.UseLegacyLoop is True&quot;)]
protected void InvalidateCanvas()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_InvalidateChildren_" data-uid="DrawnUi.Views.DrawnView.InvalidateChildren*"></a>

  <h3 id="DrawnUi_Views_DrawnView_InvalidateChildren" data-uid="DrawnUi.Views.DrawnView.InvalidateChildren">
  InvalidateChildren()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L936"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>We need to invalidate children maui changed our storyboard size</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InvalidateChildren()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_InvalidateParents_" data-uid="DrawnUi.Views.DrawnView.InvalidateParents*"></a>

  <h3 id="DrawnUi_Views_DrawnView_InvalidateParents" data-uid="DrawnUi.Views.DrawnView.InvalidateParents">
  InvalidateParents()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L920"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>If need the re-measure all parents because child-auto-size has changed</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InvalidateParents()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_InvalidateViewport_" data-uid="DrawnUi.Views.DrawnView.InvalidateViewport*"></a>

  <h3 id="DrawnUi_Views_DrawnView_InvalidateViewport" data-uid="DrawnUi.Views.DrawnView.InvalidateViewport">
  InvalidateViewport()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L722"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void InvalidateViewport()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_InvalidateViewsList_" data-uid="DrawnUi.Views.DrawnView.InvalidateViewsList*"></a>

  <h3 id="DrawnUi_Views_DrawnView_InvalidateViewsList" data-uid="DrawnUi.Views.DrawnView.InvalidateViewsList">
  InvalidateViewsList()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1535"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>To make GetOrderedSubviews() regenerate next result instead of using cached</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InvalidateViewsList()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_IsRootView_" data-uid="DrawnUi.Views.DrawnView.IsRootView*"></a>

  <h3 id="DrawnUi_Views_DrawnView_IsRootView_System_Single_System_Single_SkiaSharp_SKRect_" data-uid="DrawnUi.Views.DrawnView.IsRootView(System.Single,System.Single,SkiaSharp.SKRect)">
  IsRootView(float, float, SKRect)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1128"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsRootView(float width, float height, SKRect destination)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>width</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>height</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>destination</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_IsVisibleInViewTree_" data-uid="DrawnUi.Views.DrawnView.IsVisibleInViewTree*"></a>

  <h3 id="DrawnUi_Views_DrawnView_IsVisibleInViewTree" data-uid="DrawnUi.Views.DrawnView.IsVisibleInViewTree">
  IsVisibleInViewTree()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L108"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual bool IsVisibleInViewTree()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_KickOffscreenCacheRendering_" data-uid="DrawnUi.Views.DrawnView.KickOffscreenCacheRendering*"></a>

  <h3 id="DrawnUi_Views_DrawnView_KickOffscreenCacheRendering" data-uid="DrawnUi.Views.DrawnView.KickOffscreenCacheRendering">
  KickOffscreenCacheRendering()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1692"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Ensures offscreen rendering queue is running</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void KickOffscreenCacheRendering()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_LinearGradientAngleToPoints_" data-uid="DrawnUi.Views.DrawnView.LinearGradientAngleToPoints*"></a>

  <h3 id="DrawnUi_Views_DrawnView_LinearGradientAngleToPoints_System_Double_" data-uid="DrawnUi.Views.DrawnView.LinearGradientAngleToPoints(System.Double)">
  LinearGradientAngleToPoints(double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1015"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (double X1, double Y1, double X2, double Y2) LinearGradientAngleToPoints(double direction)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>direction</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt>(<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-system.double,system.double,system.double,system.double-.x1">X1</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-system.double,system.double,system.double,system.double-.y1">Y1</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-system.double,system.double,system.double,system.double-.x2">X2</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-system.double,system.double,system.double,system.double-.y2">Y2</a>)</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_Measure_" data-uid="DrawnUi.Views.DrawnView.Measure*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Measure_System_Single_System_Single_" data-uid="DrawnUi.Views.DrawnView.Measure(System.Single,System.Single)">
  Measure(float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1270"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ScaledSize Measure(float widthConstraintPts, float heightConstraintPts)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>widthConstraintPts</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>heightConstraintPts</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_OnBindingContextChanged_" data-uid="DrawnUi.Views.DrawnView.OnBindingContextChanged*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnBindingContextChanged" data-uid="DrawnUi.Views.DrawnView.OnBindingContextChanged">
  OnBindingContextChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2142"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Invoked whenever the binding context of the <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view">View</a> changes.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnBindingContextChanged()</code></pre>
  </div>









  <h4 class="section" id="DrawnUi_Views_DrawnView_OnBindingContextChanged_remarks">Remarks</h4>
  <div class="markdown level1 remarks"><p>This method can be overridden to add class handling for this event. Overrides must call the base method.</p>
</div>




  <a id="DrawnUi_Views_DrawnView_OnCanRenderChanged_" data-uid="DrawnUi.Views.DrawnView.OnCanRenderChanged*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnCanRenderChanged_System_Boolean_" data-uid="DrawnUi.Views.DrawnView.OnCanRenderChanged(System.Boolean)">
  OnCanRenderChanged(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L683"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Invoked when IsHiddenInViewTree changes</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnCanRenderChanged(bool state)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>state</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_OnCanvasViewChanged_" data-uid="DrawnUi.Views.DrawnView.OnCanvasViewChanged*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnCanvasViewChanged" data-uid="DrawnUi.Views.DrawnView.OnCanvasViewChanged">
  OnCanvasViewChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L554"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnCanvasViewChanged()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_OnChildAdded_" data-uid="DrawnUi.Views.DrawnView.OnChildAdded*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnChildAdded_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.OnChildAdded(DrawnUi.Draw.SkiaControl)">
  OnChildAdded(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2217"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnChildAdded(SkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_OnChildRemoved_" data-uid="DrawnUi.Views.DrawnView.OnChildRemoved*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnChildRemoved_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.OnChildRemoved(DrawnUi.Draw.SkiaControl)">
  OnChildRemoved(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2222"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnChildRemoved(SkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_OnDensityChanged_" data-uid="DrawnUi.Views.DrawnView.OnDensityChanged*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnDensityChanged" data-uid="DrawnUi.Views.DrawnView.OnDensityChanged">
  OnDensityChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1989"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnDensityChanged()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_OnDispose_" data-uid="DrawnUi.Views.DrawnView.OnDispose*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnDispose" data-uid="DrawnUi.Views.DrawnView.OnDispose">
  OnDispose()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L863"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnDispose()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_OnDisposing_" data-uid="DrawnUi.Views.DrawnView.OnDisposing*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnDisposing" data-uid="DrawnUi.Views.DrawnView.OnDisposing">
  OnDisposing()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1491"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnDisposing()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_OnFinalizeRendering_" data-uid="DrawnUi.Views.DrawnView.OnFinalizeRendering*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnFinalizeRendering" data-uid="DrawnUi.Views.DrawnView.OnFinalizeRendering">
  OnFinalizeRendering()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1461"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnFinalizeRendering()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_OnHandlerChanged_" data-uid="DrawnUi.Views.DrawnView.OnHandlerChanged*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnHandlerChanged" data-uid="DrawnUi.Views.DrawnView.OnHandlerChanged">
  OnHandlerChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L160"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>When overridden in a derived class, should raise the <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">HandlerChanged</a> event.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnHandlerChanged()</code></pre>
  </div>









  <h4 class="section" id="DrawnUi_Views_DrawnView_OnHandlerChanged_remarks">Remarks</h4>
  <div class="markdown level1 remarks"><p>It is the implementor's responsibility to raise the <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">HandlerChanged</a> event.</p>
</div>




  <a id="DrawnUi_Views_DrawnView_OnHandlerChanging_" data-uid="DrawnUi.Views.DrawnView.OnHandlerChanging*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnHandlerChanging_Microsoft_Maui_Controls_HandlerChangingEventArgs_" data-uid="DrawnUi.Views.DrawnView.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)">
  OnHandlerChanging(HandlerChangingEventArgs)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L145"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>When overridden in a derived class, should raise the <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">HandlerChanging</a> event.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnHandlerChanging(HandlerChangingEventArgs args)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>args</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs">HandlerChangingEventArgs</a></dt>
    <dd><p>Provides data for the <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">HandlerChanging</a> event.</p>
</dd>
  </dl>








  <h4 class="section" id="DrawnUi_Views_DrawnView_OnHandlerChanging_Microsoft_Maui_Controls_HandlerChangingEventArgs__remarks">Remarks</h4>
  <div class="markdown level1 remarks"><p>It is the implementor's responsibility to raise the <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">HandlerChanging</a> event.</p>
</div>




  <a id="DrawnUi_Views_DrawnView_OnHotReload_" data-uid="DrawnUi.Views.DrawnView.OnHotReload*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnHotReload" data-uid="DrawnUi.Views.DrawnView.OnHotReload">
  OnHotReload()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.Maui.cs/#L15"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnHotReload()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_OnMeasured_" data-uid="DrawnUi.Views.DrawnView.OnMeasured*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnMeasured" data-uid="DrawnUi.Views.DrawnView.OnMeasured">
  OnMeasured()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1338"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnMeasured()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_OnParentSet_" data-uid="DrawnUi.Views.DrawnView.OnParentSet*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnParentSet" data-uid="DrawnUi.Views.DrawnView.OnParentSet">
  OnParentSet()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2642"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Raises the (internal) <code>ParentSet</code> event.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnParentSet()</code></pre>
  </div>









  <h4 class="section" id="DrawnUi_Views_DrawnView_OnParentSet_remarks">Remarks</h4>
  <div class="markdown level1 remarks"><p>Will set the <span class="xref">NavigationProxy&#39;s</span> inner navigation object to closest topmost element capable of handling navigation calls.</p>
</div>


  <dl class="typelist seealso">
    <dt>See Also</dt>
    <dd>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentset">OnParentSet</a>()</div>
    </dd>
  </dl>


  <a id="DrawnUi_Views_DrawnView_OnPropertyChanged_" data-uid="DrawnUi.Views.DrawnView.OnPropertyChanged*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnPropertyChanged_System_String_" data-uid="DrawnUi.Views.DrawnView.OnPropertyChanged(System.String)">
  OnPropertyChanged(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L991"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Method that is called when a bound property is changed.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnPropertyChanged(string propertyName = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>propertyName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The name of the bound property that changed.</p>
</dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_OnSizeChanged_" data-uid="DrawnUi.Views.DrawnView.OnSizeChanged*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnSizeChanged" data-uid="DrawnUi.Views.DrawnView.OnSizeChanged">
  OnSizeChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2696"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnSizeChanged()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_OnStartRendering_" data-uid="DrawnUi.Views.DrawnView.OnStartRendering*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnStartRendering_SkiaSharp_SKCanvas_" data-uid="DrawnUi.Views.DrawnView.OnStartRendering(SkiaSharp.SKCanvas)">
  OnStartRendering(SKCanvas)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1438"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual bool OnStartRendering(SKCanvas canvas)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>canvas</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas">SKCanvas</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_OnSuperviewShouldRenderChanged_" data-uid="DrawnUi.Views.DrawnView.OnSuperviewShouldRenderChanged*"></a>

  <h3 id="DrawnUi_Views_DrawnView_OnSuperviewShouldRenderChanged_System_Boolean_" data-uid="DrawnUi.Views.DrawnView.OnSuperviewShouldRenderChanged(System.Boolean)">
  OnSuperviewShouldRenderChanged(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L925"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnSuperviewShouldRenderChanged(bool state)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>state</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_PaintTintBackground_" data-uid="DrawnUi.Views.DrawnView.PaintTintBackground*"></a>

  <h3 id="DrawnUi_Views_DrawnView_PaintTintBackground_SkiaSharp_SKCanvas_" data-uid="DrawnUi.Views.DrawnView.PaintTintBackground(SkiaSharp.SKCanvas)">
  PaintTintBackground(SKCanvas)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2116"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PaintTintBackground(SKCanvas canvas)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>canvas</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas">SKCanvas</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_PostponeExecutionAfterDraw_" data-uid="DrawnUi.Views.DrawnView.PostponeExecutionAfterDraw*"></a>

  <h3 id="DrawnUi_Views_DrawnView_PostponeExecutionAfterDraw_System_Action_" data-uid="DrawnUi.Views.DrawnView.PostponeExecutionAfterDraw(System.Action)">
  PostponeExecutionAfterDraw(Action)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L209"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Postpone the action to be executed after the current frame is drawn. Exception-safe.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PostponeExecutionAfterDraw(Action action)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>action</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_PostponeExecutionBeforeDraw_" data-uid="DrawnUi.Views.DrawnView.PostponeExecutionBeforeDraw*"></a>

  <h3 id="DrawnUi_Views_DrawnView_PostponeExecutionBeforeDraw_System_Action_" data-uid="DrawnUi.Views.DrawnView.PostponeExecutionBeforeDraw(System.Action)">
  PostponeExecutionBeforeDraw(Action)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L200"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Postpone the action to be executed before the next frame being drawn. Exception-safe.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PostponeExecutionBeforeDraw(Action action)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>action</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_PostponeInvalidation_" data-uid="DrawnUi.Views.DrawnView.PostponeInvalidation*"></a>

  <h3 id="DrawnUi_Views_DrawnView_PostponeInvalidation_DrawnUi_Draw_SkiaControl_System_Action_" data-uid="DrawnUi.Views.DrawnView.PostponeInvalidation(DrawnUi.Draw.SkiaControl,System.Action)">
  PostponeInvalidation(SkiaControl, Action)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1608"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PostponeInvalidation(SkiaControl key, Action action)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>key</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>action</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_PrintDebug_" data-uid="DrawnUi.Views.DrawnView.PrintDebug*"></a>

  <h3 id="DrawnUi_Views_DrawnView_PrintDebug_System_String_" data-uid="DrawnUi.Views.DrawnView.PrintDebug(System.String)">
  PrintDebug(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L949"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PrintDebug(string indent = &quot;&quot;)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>indent</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_ProcessOffscreenCacheRenderingAsync_" data-uid="DrawnUi.Views.DrawnView.ProcessOffscreenCacheRenderingAsync*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ProcessOffscreenCacheRenderingAsync" data-uid="DrawnUi.Views.DrawnView.ProcessOffscreenCacheRenderingAsync">
  ProcessOffscreenCacheRenderingAsync()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1713"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task ProcessOffscreenCacheRenderingAsync()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_PushToOffscreenRendering_" data-uid="DrawnUi.Views.DrawnView.PushToOffscreenRendering*"></a>

  <h3 id="DrawnUi_Views_DrawnView_PushToOffscreenRendering_DrawnUi_Draw_SkiaControl_System_Threading_CancellationToken_" data-uid="DrawnUi.Views.DrawnView.PushToOffscreenRendering(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)">
  PushToOffscreenRendering(SkiaControl, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1707"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Push an offscreen rendering command without blocking the UI thread.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PushToOffscreenRendering(SkiaControl control, CancellationToken cancel = default)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>cancel</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_RedrawCanvas_" data-uid="DrawnUi.Views.DrawnView.RedrawCanvas*"></a>

  <h3 id="DrawnUi_Views_DrawnView_RedrawCanvas_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_" data-uid="DrawnUi.Views.DrawnView.RedrawCanvas(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)">
  RedrawCanvas(BindableObject, object, object)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2131"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected static void RedrawCanvas(BindableObject bindable, object oldvalue, object newvalue)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>oldvalue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
    <dt><code>newvalue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_RegisterAnimator_" data-uid="DrawnUi.Views.DrawnView.RegisterAnimator*"></a>

  <h3 id="DrawnUi_Views_DrawnView_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_" data-uid="DrawnUi.Views.DrawnView.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)">
  RegisterAnimator(ISkiaAnimator)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L314"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Called by a control that whats to be constantly animated or doesn't anymore,
so we know whether we should refresh canvas non-stop</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool RegisterAnimator(ISkiaAnimator animator)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>animator</code> <a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_RegisterGestureListener_" data-uid="DrawnUi.Views.DrawnView.RegisterGestureListener*"></a>

  <h3 id="DrawnUi_Views_DrawnView_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_" data-uid="DrawnUi.Views.DrawnView.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)">
  RegisterGestureListener(ISkiaGestureListener)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L244"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RegisterGestureListener(ISkiaGestureListener gestureListener)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>gestureListener</code> <a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_RemoveAnimator_" data-uid="DrawnUi.Views.DrawnView.RemoveAnimator*"></a>

  <h3 id="DrawnUi_Views_DrawnView_RemoveAnimator_System_Guid_" data-uid="DrawnUi.Views.DrawnView.RemoveAnimator(System.Guid)">
  RemoveAnimator(Guid)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L297"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveAnimator(Guid uid)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>uid</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.guid">Guid</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_RemoveSubView_" data-uid="DrawnUi.Views.DrawnView.RemoveSubView*"></a>

  <h3 id="DrawnUi_Views_DrawnView_RemoveSubView_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.RemoveSubView(DrawnUi.Draw.SkiaControl)">
  RemoveSubView(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2196"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Directly removes a view from the control, without any layouting.
Use this instead of Views.Remove() to avoid memory leaks etc</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveSubView(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_Repaint_" data-uid="DrawnUi.Views.DrawnView.Repaint*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Repaint" data-uid="DrawnUi.Views.DrawnView.Repaint">
  Repaint()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L727"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void Repaint()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_ReportFocus_" data-uid="DrawnUi.Views.DrawnView.ReportFocus*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ReportFocus_DrawnUi_Draw_ISkiaGestureListener_DrawnUi_Draw_ISkiaGestureListener_" data-uid="DrawnUi.Views.DrawnView.ReportFocus(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)">
  ReportFocus(ISkiaGestureListener, ISkiaGestureListener)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2453"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Internal call by control, after reporting will affect FocusedChild but will not get FocusedItemChanged as it was its own call</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ReportFocus(ISkiaGestureListener value, ISkiaGestureListener setter = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></dt>
    <dd></dd>
    <dt><code>setter</code> <a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_ReportHotreloadChildAdded_" data-uid="DrawnUi.Views.DrawnView.ReportHotreloadChildAdded*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ReportHotreloadChildAdded_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.ReportHotreloadChildAdded(DrawnUi.Draw.SkiaControl)">
  ReportHotreloadChildAdded(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2187"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ReportHotreloadChildAdded(SkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_ReportHotreloadChildRemoved_" data-uid="DrawnUi.Views.DrawnView.ReportHotreloadChildRemoved*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ReportHotreloadChildRemoved_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.ReportHotreloadChildRemoved(DrawnUi.Draw.SkiaControl)">
  ReportHotreloadChildRemoved(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2208"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ReportHotreloadChildRemoved(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_ResetFocusWithDelay_" data-uid="DrawnUi.Views.DrawnView.ResetFocusWithDelay*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ResetFocusWithDelay_System_Int32_" data-uid="DrawnUi.Views.DrawnView.ResetFocusWithDelay(System.Int32)">
  ResetFocusWithDelay(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2504"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ResetFocusWithDelay(int ms)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>ms</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_ResetUpdate_" data-uid="DrawnUi.Views.DrawnView.ResetUpdate*"></a>

  <h3 id="DrawnUi_Views_DrawnView_ResetUpdate" data-uid="DrawnUi.Views.DrawnView.ResetUpdate">
  ResetUpdate()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L735"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ResetUpdate()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_SetChildAsDirty_" data-uid="DrawnUi.Views.DrawnView.SetChildAsDirty*"></a>

  <h3 id="DrawnUi_Views_DrawnView_SetChildAsDirty_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.SetChildAsDirty(DrawnUi.Draw.SkiaControl)">
  SetChildAsDirty(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1653"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetChildAsDirty(SkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_SetChildren_" data-uid="DrawnUi.Views.DrawnView.SetChildren*"></a>

  <h3 id="DrawnUi_Views_DrawnView_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__" data-uid="DrawnUi.Views.DrawnView.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})">
  SetChildren(IEnumerable&lt;SkiaControl&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2168"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void SetChildren(IEnumerable&lt;SkiaControl&gt; views)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>views</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_SetDeviceOrientation_" data-uid="DrawnUi.Views.DrawnView.SetDeviceOrientation*"></a>

  <h3 id="DrawnUi_Views_DrawnView_SetDeviceOrientation_System_Int32_" data-uid="DrawnUi.Views.DrawnView.SetDeviceOrientation(System.Int32)">
  SetDeviceOrientation(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L617"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetDeviceOrientation(int rotation)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rotation</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_SetMeasured_" data-uid="DrawnUi.Views.DrawnView.SetMeasured*"></a>

  <h3 id="DrawnUi_Views_DrawnView_SetMeasured_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Views.DrawnView.SetMeasured(System.Single,System.Single,System.Single)">
  SetMeasured(float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1304"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected ScaledSize SetMeasured(float width, float height, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>width</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>height</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_SetPauseStateOfAllAnimatorsByParent_" data-uid="DrawnUi.Views.DrawnView.SetPauseStateOfAllAnimatorsByParent*"></a>

  <h3 id="DrawnUi_Views_DrawnView_SetPauseStateOfAllAnimatorsByParent_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Views.DrawnView.SetPauseStateOfAllAnimatorsByParent(DrawnUi.Draw.SkiaControl,System.Boolean)">
  SetPauseStateOfAllAnimatorsByParent(SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L395"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual IEnumerable&lt;ISkiaAnimator&gt; SetPauseStateOfAllAnimatorsByParent(SkiaControl parent, bool state)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>state</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_SetViewTreeVisibilityByParent_" data-uid="DrawnUi.Views.DrawnView.SetViewTreeVisibilityByParent*"></a>

  <h3 id="DrawnUi_Views_DrawnView_SetViewTreeVisibilityByParent_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Views.DrawnView.SetViewTreeVisibilityByParent(DrawnUi.Draw.SkiaControl,System.Boolean)">
  SetViewTreeVisibilityByParent(SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L374"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>TODO maybe use renderedNode tree</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual IEnumerable&lt;ISkiaAnimator&gt; SetViewTreeVisibilityByParent(SkiaControl parent, bool state)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>state</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_StreamFromString_" data-uid="DrawnUi.Views.DrawnView.StreamFromString*"></a>

  <h3 id="DrawnUi_Views_DrawnView_StreamFromString_System_String_" data-uid="DrawnUi.Views.DrawnView.StreamFromString(System.String)">
  StreamFromString(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2108"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MemoryStream StreamFromString(string value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.io.memorystream">MemoryStream</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_SubscribeToRenderingFinished_" data-uid="DrawnUi.Views.DrawnView.SubscribeToRenderingFinished*"></a>

  <h3 id="DrawnUi_Views_DrawnView_SubscribeToRenderingFinished_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.SubscribeToRenderingFinished(DrawnUi.Draw.SkiaControl)">
  SubscribeToRenderingFinished(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L228"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>SetVisualTransform will be called after every frame</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SubscribeToRenderingFinished(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_SwapInvalidations_" data-uid="DrawnUi.Views.DrawnView.SwapInvalidations*"></a>

  <h3 id="DrawnUi_Views_DrawnView_SwapInvalidations" data-uid="DrawnUi.Views.DrawnView.SwapInvalidations">
  SwapInvalidations()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1638"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void SwapInvalidations()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_TakeScreenShot_" data-uid="DrawnUi.Views.DrawnView.TakeScreenShot*"></a>

  <h3 id="DrawnUi_Views_DrawnView_TakeScreenShot_System_Action_SkiaSharp_SKImage__" data-uid="DrawnUi.Views.DrawnView.TakeScreenShot(System.Action{SkiaSharp.SKImage})">
  TakeScreenShot(Action&lt;SKImage&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L113"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void TakeScreenShot(Action&lt;SKImage&gt; callback)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>callback</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-1">Action</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skimage">SKImage</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_TakeScreenShotInternal_" data-uid="DrawnUi.Views.DrawnView.TakeScreenShotInternal*"></a>

  <h3 id="DrawnUi_Views_DrawnView_TakeScreenShotInternal_SkiaSharp_SKSurface_" data-uid="DrawnUi.Views.DrawnView.TakeScreenShotInternal(SkiaSharp.SKSurface)">
  TakeScreenShotInternal(SKSurface)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L183"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void TakeScreenShotInternal(SKSurface surface)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>surface</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksurface">SKSurface</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByParent_" data-uid="DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByParent*"></a>

  <h3 id="DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByParent_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByParent(DrawnUi.Draw.SkiaControl)">
  UnregisterAllAnimatorsByParent(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L347"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual IEnumerable&lt;ISkiaAnimator&gt; UnregisterAllAnimatorsByParent(SkiaControl parent)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByType_" data-uid="DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByType*"></a>

  <h3 id="DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByType_System_Type_" data-uid="DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByType(System.Type)">
  UnregisterAllAnimatorsByType(Type)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L326"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual IEnumerable&lt;ISkiaAnimator&gt; UnregisterAllAnimatorsByType(Type type)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>type</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.type">Type</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Views_DrawnView_UnregisterAnimator_" data-uid="DrawnUi.Views.DrawnView.UnregisterAnimator*"></a>

  <h3 id="DrawnUi_Views_DrawnView_UnregisterAnimator_System_Guid_" data-uid="DrawnUi.Views.DrawnView.UnregisterAnimator(System.Guid)">
  UnregisterAnimator(Guid)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L321"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UnregisterAnimator(Guid uid)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>uid</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.guid">Guid</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_UnregisterGestureListener_" data-uid="DrawnUi.Views.DrawnView.UnregisterGestureListener*"></a>

  <h3 id="DrawnUi_Views_DrawnView_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_" data-uid="DrawnUi.Views.DrawnView.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)">
  UnregisterGestureListener(ISkiaGestureListener)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L253"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UnregisterGestureListener(ISkiaGestureListener gestureListener)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>gestureListener</code> <a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_Update_" data-uid="DrawnUi.Views.DrawnView.Update*"></a>

  <h3 id="DrawnUi_Views_DrawnView_Update" data-uid="DrawnUi.Views.DrawnView.Update">
  Update()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L63"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Force redrawing, without invalidating the measured size</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void Update()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_UpdateByChild_" data-uid="DrawnUi.Views.DrawnView.UpdateByChild*"></a>

  <h3 id="DrawnUi_Views_DrawnView_UpdateByChild_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.UpdateByChild(DrawnUi.Draw.SkiaControl)">
  UpdateByChild(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L124"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>To track dirty area when Updating parent</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void UpdateByChild(SkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_UpdateGlobal_" data-uid="DrawnUi.Views.DrawnView.UpdateGlobal*"></a>

  <h3 id="DrawnUi_Views_DrawnView_UpdateGlobal" data-uid="DrawnUi.Views.DrawnView.UpdateGlobal">
  UpdateGlobal()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L665"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void UpdateGlobal()</code></pre>
  </div>













  <a id="DrawnUi_Views_DrawnView_UsubscribeFromRenderingFinished_" data-uid="DrawnUi.Views.DrawnView.UsubscribeFromRenderingFinished*"></a>

  <h3 id="DrawnUi_Views_DrawnView_UsubscribeFromRenderingFinished_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Views.DrawnView.UsubscribeFromRenderingFinished(DrawnUi.Draw.SkiaControl)">
  UsubscribeFromRenderingFinished(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L233"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UsubscribeFromRenderingFinished(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Views_DrawnView_WillDispose_" data-uid="DrawnUi.Views.DrawnView.WillDispose*"></a>

  <h3 id="DrawnUi_Views_DrawnView_WillDispose" data-uid="DrawnUi.Views.DrawnView.WillDispose">
  WillDispose()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L855"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void WillDispose()</code></pre>
  </div>













  <h2 class="section" id="events">Events
</h2>



  <h3 id="DrawnUi_Views_DrawnView_DeviceRotationChanged" data-uid="DrawnUi.Views.DrawnView.DeviceRotationChanged">
  DeviceRotationChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L622"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;int&gt; DeviceRotationChanged</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Views_DrawnView_FocusedItemChanged" data-uid="DrawnUi.Views.DrawnView.FocusedItemChanged">
  FocusedItemChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L2435"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;DrawnView.FocusedItemChangedArgs&gt; FocusedItemChanged</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="DrawnUi.Views.DrawnView.html">DrawnView</a>.<a class="xref" href="DrawnUi.Views.DrawnView.FocusedItemChangedArgs.html">FocusedItemChangedArgs</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Views_DrawnView_HandlerWasSet" data-uid="DrawnUi.Views.DrawnView.HandlerWasSet">
  HandlerWasSet
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L181"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;bool&gt; HandlerWasSet</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Views_DrawnView_Measured" data-uid="DrawnUi.Views.DrawnView.Measured">
  Measured
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1343"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;ScaledSize&gt; Measured</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Views_DrawnView_ViewDisposing" data-uid="DrawnUi.Views.DrawnView.ViewDisposing">
  ViewDisposing
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L853"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler ViewDisposing</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Views_DrawnView_WasDrawn" data-uid="DrawnUi.Views.DrawnView.WasDrawn">
  WasDrawn
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1375"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>OnDrawSurface will call that</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;SkiaDrawingContext?&gt; WasDrawn</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaDrawingContext.html">SkiaDrawingContext</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Views_DrawnView_WillDraw" data-uid="DrawnUi.Views.DrawnView.WillDraw">
  WillDraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1380"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>OnDrawSurface will call that</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;SkiaDrawingContext?&gt; WillDraw</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaDrawingContext.html">SkiaDrawingContext</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Views_DrawnView_WillFirstTimeDraw" data-uid="DrawnUi.Views.DrawnView.WillFirstTimeDraw">
  WillFirstTimeDraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.cs/#L1385"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>OnDrawSurface will call that if never been drawn yet</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;SkiaDrawingContext?&gt; WillFirstTimeDraw</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaDrawingContext.html">SkiaDrawingContext</a>&gt;</dt>
    <dd></dd>
  </dl>








</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Views/DrawnView.Maui.cs/#L12" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
