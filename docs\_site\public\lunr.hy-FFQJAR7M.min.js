import{a as o}from"./chunk-OSRY5VT3.min.js";var s=o((i,r)=>{(function(e,t){typeof define=="function"&&define.amd?define(t):typeof i=="object"?r.exports=t():t()(e.lunr)})(i,function(){return function(e){if(typeof e>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof e.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");e.hy=function(){this.pipeline.reset(),this.pipeline.add(e.hy.trimmer,e.hy.stopWordFilter)},e.hy.wordCharacters="[A-Za-z\u0530-\u058F\uFB00-\uFB4F]",e.hy.trimmer=e.trimmerSupport.generateTrimmer(e.hy.wordCharacters),e.Pipeline.registerFunction(e.hy.trimmer,"trimmer-hy"),e.hy.stopWordFilter=e.generateStopWordFilter("\u0564\u0578\u0582 \u0587 \u0565\u0584 \u0567\u056B\u0580 \u0567\u056B\u0584 \u0570\u0565\u057F\u0578 \u0576\u0561\u0587 \u0576\u0580\u0561\u0576\u0584 \u0578\u0580\u0568 \u057E\u0580\u0561 \u0567 \u0578\u0580 \u057A\u056B\u057F\u056B \u0565\u0576 \u0561\u0575\u057D \u0574\u0565\u057B \u0576 \u056B\u0580 \u0578\u0582 \u056B \u0561\u0575\u0564 \u0578\u0580\u0578\u0576\u0584 \u0561\u0575\u0576 \u056F\u0561\u0574 \u0567\u0580 \u0574\u056B \u0565\u057D \u0570\u0561\u0574\u0561\u0580 \u0561\u0575\u056C \u056B\u057D\u056F \u0567\u056B\u0576 \u0565\u0576\u0584 \u0570\u0565\u057F \u056B\u0576 \u0569 \u0567\u056B\u0576\u0584 \u0574\u0565\u0576\u0584 \u0576\u0580\u0561 \u0576\u0561 \u0564\u0578\u0582\u0584 \u0565\u0574 \u0567\u056B \u0568\u057D\u057F \u0578\u0580\u057A\u0565\u057D \u0578\u0582\u0574".split(" ")),e.Pipeline.registerFunction(e.hy.stopWordFilter,"stopWordFilter-hy"),e.hy.stemmer=function(){return function(t){return typeof t.update=="function"?t.update(function(n){return n}):t}}(),e.Pipeline.registerFunction(e.hy.stemmer,"stemmer-hy")}})});export default s();
/*! Bundled license information:

lunr-languages/lunr.hy.js:
  (*!
   * Lunr languages, `Armenian` language
   * https://github.com/turbobit/lunr-languages
   *
   * Copyright 2021, Manikandan Venkatasubban
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.hy-FFQJAR7M.min.js.map
