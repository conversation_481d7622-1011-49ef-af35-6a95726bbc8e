<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaLabel.ObjectPools | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaLabel.ObjectPools | DrawnUi Documentation ">
      
      <meta name="description" content="Thread-safe object pools for reducing GC allocations in text measurement">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_ObjectPools.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.ObjectPools%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools">



  <h1 id="DrawnUi_Draw_SkiaLabel_ObjectPools" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools" class="text-break">
Class SkiaLabel.ObjectPools  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>Thread-safe object pools for reducing GC allocations in text measurement</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class SkiaLabel.ObjectPools</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">SkiaLabel.ObjectPools</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_SkiaLabel_ObjectPools_GetLineGlyphList_" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_ObjectPools_GetLineGlyphList" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.GetLineGlyphList">
  GetLineGlyphList()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs/#L29"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static List&lt;LineGlyph&gt; GetLineGlyphList()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.LineGlyph.html">LineGlyph</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLabel_ObjectPools_GetPoolSizes_" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_ObjectPools_GetPoolSizes" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.GetPoolSizes">
  GetPoolSizes()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs/#L125"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (int LineGlyphLists, int TextLineLists, int StringBuilders) GetPoolSizes()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt>(<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-system.int32,system.int32,system.int32-.lineglyphlists">LineGlyphLists</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-system.int32,system.int32,system.int32-.textlinelists">TextLineLists</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-system.int32,system.int32,system.int32-.stringbuilders">StringBuilders</a>)</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLabel_ObjectPools_GetStringBuilder_" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_ObjectPools_GetStringBuilder" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.GetStringBuilder">
  GetStringBuilder()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs/#L93"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static StringBuilder GetStringBuilder()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.text.stringbuilder">StringBuilder</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLabel_ObjectPools_GetTextLineList_" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_ObjectPools_GetTextLineList" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.GetTextLineList">
  GetTextLineList()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs/#L61"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static List&lt;TextLine&gt; GetTextLineList()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.TextLine.html">TextLine</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLabel_ObjectPools_ReturnLineGlyphList_" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_ObjectPools_ReturnLineGlyphList_System_Collections_Generic_List_DrawnUi_Draw_LineGlyph__" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnLineGlyphList(System.Collections.Generic.List{DrawnUi.Draw.LineGlyph})">
  ReturnLineGlyphList(List&lt;LineGlyph&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs/#L40"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ReturnLineGlyphList(List&lt;LineGlyph&gt; list)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>list</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.LineGlyph.html">LineGlyph</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLabel_ObjectPools_ReturnStringBuilder_" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_ObjectPools_ReturnStringBuilder_System_Text_StringBuilder_" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnStringBuilder(System.Text.StringBuilder)">
  ReturnStringBuilder(StringBuilder)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs/#L104"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ReturnStringBuilder(StringBuilder sb)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>sb</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.text.stringbuilder">StringBuilder</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLabel_ObjectPools_ReturnTextLineList_" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_ObjectPools_ReturnTextLineList_System_Collections_Generic_List_DrawnUi_Draw_TextLine__" data-uid="DrawnUi.Draw.SkiaLabel.ObjectPools.ReturnTextLineList(System.Collections.Generic.List{DrawnUi.Draw.TextLine})">
  ReturnTextLineList(List&lt;TextLine&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs/#L72"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ReturnTextLineList(List&lt;TextLine&gt; list)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>list</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.TextLine.html">TextLine</a>&gt;</dt>
    <dd></dd>
  </dl>













</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.ObjectPools.cs/#L11" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
