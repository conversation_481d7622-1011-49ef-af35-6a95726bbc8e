<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaFontManager | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaFontManager | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaFontManager.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaFontManager%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaFontManager">



  <h1 id="DrawnUi_Draw_SkiaFontManager" data-uid="DrawnUi.Draw.SkiaFontManager" class="text-break">
Class SkiaFontManager  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L6"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SkiaFontManager</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">SkiaFontManager</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_SkiaFontManager_ThrowIfFailedToCreateFont" data-uid="DrawnUi.Draw.SkiaFontManager.ThrowIfFailedToCreateFont">
  ThrowIfFailedToCreateFont
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L72"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool ThrowIfFailedToCreateFont</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_SkiaFontManager_DefaultTypeface_" data-uid="DrawnUi.Draw.SkiaFontManager.DefaultTypeface*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_DefaultTypeface" data-uid="DrawnUi.Draw.SkiaFontManager.DefaultTypeface">
  DefaultTypeface
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L14"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKTypeface DefaultTypeface { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface">SKTypeface</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaFontManager_FontRegistrar_" data-uid="DrawnUi.Draw.SkiaFontManager.FontRegistrar*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_FontRegistrar" data-uid="DrawnUi.Draw.SkiaFontManager.FontRegistrar">
  FontRegistrar
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L299"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IFontRegistrar FontRegistrar { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ifontregistrar">IFontRegistrar</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaFontManager_Fonts_" data-uid="DrawnUi.Draw.SkiaFontManager.Fonts*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_Fonts" data-uid="DrawnUi.Draw.SkiaFontManager.Fonts">
  Fonts
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L296"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Dictionary&lt;string, SKTypeface&gt; Fonts { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface">SKTypeface</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaFontManager_Initialized_" data-uid="DrawnUi.Draw.SkiaFontManager.Initialized*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_Initialized" data-uid="DrawnUi.Draw.SkiaFontManager.Initialized">
  Initialized
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L12"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Initialized { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaFontManager_Instance_" data-uid="DrawnUi.Draw.SkiaFontManager.Instance*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_Instance" data-uid="DrawnUi.Draw.SkiaFontManager.Instance">
  Instance
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L283"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaFontManager Instance { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaFontManager.html">SkiaFontManager</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaFontManager_Manager_" data-uid="DrawnUi.Draw.SkiaFontManager.Manager*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_Manager" data-uid="DrawnUi.Draw.SkiaFontManager.Manager">
  Manager
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L75"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKFontManager Manager { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skfontmanager">SKFontManager</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_SkiaFontManager_CanRender_" data-uid="DrawnUi.Draw.SkiaFontManager.CanRender*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_CanRender_SkiaSharp_SKTypeface_System_Int32_" data-uid="DrawnUi.Draw.SkiaFontManager.CanRender(SkiaSharp.SKTypeface,System.Int32)">
  CanRender(SKTypeface, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L116"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool CanRender(SKTypeface typeface, int character)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>typeface</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface">SKTypeface</a></dt>
    <dd></dd>
    <dt><code>character</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_FindBestTypefaceForString_" data-uid="DrawnUi.Draw.SkiaFontManager.FindBestTypefaceForString*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_FindBestTypefaceForString_System_String_" data-uid="DrawnUi.Draw.SkiaFontManager.FindBestTypefaceForString(System.String)">
  FindBestTypefaceForString(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L91"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (SKTypeface, int) FindBestTypefaceForString(string text)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>text</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt>(<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface">SKTypeface</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a>)</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetAlias_" data-uid="DrawnUi.Draw.SkiaFontManager.GetAlias*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetAlias_System_String_DrawnUi_Draw_FontWeight_" data-uid="DrawnUi.Draw.SkiaFontManager.GetAlias(System.String,DrawnUi.Draw.FontWeight)">
  GetAlias(string, FontWeight)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L216"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetAlias(string alias, FontWeight weight)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>alias</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>weight</code> <a class="xref" href="DrawnUi.Draw.FontWeight.html">FontWeight</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetAlias_" data-uid="DrawnUi.Draw.SkiaFontManager.GetAlias*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetAlias_System_String_System_Int32_" data-uid="DrawnUi.Draw.SkiaFontManager.GetAlias(System.String,System.Int32)">
  GetAlias(string, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L224"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetAlias(string alias, int weight)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>alias</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>weight</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetEmbeddedResourceNames_" data-uid="DrawnUi.Draw.SkiaFontManager.GetEmbeddedResourceNames*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetEmbeddedResourceNames" data-uid="DrawnUi.Draw.SkiaFontManager.GetEmbeddedResourceNames">
  GetEmbeddedResourceNames()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L276"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Get the list of all emdedded resources in the assembly.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string[] GetEmbeddedResourceNames()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>[]</dt>
    <dd><p>An array of fully qualified resource names</p>
</dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetEmbeddedResourceStream_" data-uid="DrawnUi.Draw.SkiaFontManager.GetEmbeddedResourceStream*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetEmbeddedResourceStream_System_Reflection_Assembly_System_String_" data-uid="DrawnUi.Draw.SkiaFontManager.GetEmbeddedResourceStream(System.Reflection.Assembly,System.String)">
  GetEmbeddedResourceStream(Assembly, string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L244"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Stream GetEmbeddedResourceStream(Assembly assembly, string resourceFileName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>assembly</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.reflection.assembly">Assembly</a></dt>
    <dd></dd>
    <dt><code>resourceFileName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.io.stream">Stream</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetEmbeddedResourceStream_" data-uid="DrawnUi.Draw.SkiaFontManager.GetEmbeddedResourceStream*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetEmbeddedResourceStream_System_String_" data-uid="DrawnUi.Draw.SkiaFontManager.GetEmbeddedResourceStream(System.String)">
  GetEmbeddedResourceStream(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L239"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Takes the full name of a resource and loads it in to a stream.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Stream GetEmbeddedResourceStream(string resourceName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>resourceName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>Assuming an embedded resource is a file
called info.png and is located in a folder called Resources, it
will be compiled in to the assembly with this fully qualified
name: Full.Assembly.Name.Resources.info.png. That is the string
that you should pass to this method.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.io.stream">Stream</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetEmbeededFont_" data-uid="DrawnUi.Draw.SkiaFontManager.GetEmbeededFont*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetEmbeededFont_System_String_System_Reflection_Assembly_System_String_" data-uid="DrawnUi.Draw.SkiaFontManager.GetEmbeededFont(System.String,System.Reflection.Assembly,System.String)">
  GetEmbeededFont(string, Assembly, string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L312"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKTypeface GetEmbeededFont(string filename, Assembly assembly, string alias = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>filename</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>assembly</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.reflection.assembly">Assembly</a></dt>
    <dd></dd>
    <dt><code>alias</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface">SKTypeface</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetFont_" data-uid="DrawnUi.Draw.SkiaFontManager.GetFont*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetFont_System_String_" data-uid="DrawnUi.Draw.SkiaFontManager.GetFont(System.String)">
  GetFont(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L141"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKTypeface GetFont(string alias)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>alias</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface">SKTypeface</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetFont_" data-uid="DrawnUi.Draw.SkiaFontManager.GetFont*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetFont_System_String_System_Int32_" data-uid="DrawnUi.Draw.SkiaFontManager.GetFont(System.String,System.Int32)">
  GetFont(string, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L182"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKTypeface GetFont(string fontFamily, int fontWeight)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>fontFamily</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>fontWeight</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface">SKTypeface</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetRegisteredAlias_" data-uid="DrawnUi.Draw.SkiaFontManager.GetRegisteredAlias*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetRegisteredAlias_System_String_System_Int32_" data-uid="DrawnUi.Draw.SkiaFontManager.GetRegisteredAlias(System.String,System.Int32)">
  GetRegisteredAlias(string, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L164"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetRegisteredAlias(string alias, int weight)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>alias</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>weight</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_GetWeightEnum_" data-uid="DrawnUi.Draw.SkiaFontManager.GetWeightEnum*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_GetWeightEnum_System_Int32_" data-uid="DrawnUi.Draw.SkiaFontManager.GetWeightEnum(System.Int32)">
  GetWeightEnum(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L206"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the closest enum value to the given weight. Like 590 would return Semibold.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static FontWeight GetWeightEnum(int weight)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>weight</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.FontWeight.html">FontWeight</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaFontManager_Initialize_" data-uid="DrawnUi.Draw.SkiaFontManager.Initialize*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_Initialize" data-uid="DrawnUi.Draw.SkiaFontManager.Initialize">
  Initialize()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L30"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Initialize()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaFontManager_RegisterWeight_" data-uid="DrawnUi.Draw.SkiaFontManager.RegisterWeight*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_RegisterWeight_System_String_DrawnUi_Draw_FontWeight_" data-uid="DrawnUi.Draw.SkiaFontManager.RegisterWeight(System.String,DrawnUi.Draw.FontWeight)">
  RegisterWeight(string, FontWeight)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L151"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RegisterWeight(string alias, FontWeight weight)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>alias</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>weight</code> <a class="xref" href="DrawnUi.Draw.FontWeight.html">FontWeight</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaFontManager_StringToUnicodeValues_" data-uid="DrawnUi.Draw.SkiaFontManager.StringToUnicodeValues*"></a>

  <h3 id="DrawnUi_Draw_SkiaFontManager_StringToUnicodeValues_System_String_" data-uid="DrawnUi.Draw.SkiaFontManager.StringToUnicodeValues(System.String)">
  StringToUnicodeValues(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L121"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static List&lt;int&gt; StringToUnicodeValues(string text)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>text</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a>&gt;</dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Fonts/SkiaFontManager.cs/#L6" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
