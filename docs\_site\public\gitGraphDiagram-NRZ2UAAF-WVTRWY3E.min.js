import{a as ee}from"./chunk-EKP7MBOP.min.js";import{a as re}from"./chunk-WXIN66R4.min.js";import{a as te}from"./chunk-I4ZXTPQC.min.js";import"./chunk-33FU46FA.min.js";import"./chunk-OZ2RCKQJ.min.js";import"./chunk-PDS7545E.min.js";import"./chunk-IJ4BRSPX.min.js";import"./chunk-UEFJDIUO.min.js";import"./chunk-BIJFJY5F.min.js";import"./chunk-U4DUTLYF.min.js";import"./chunk-IQQ46AC6.min.js";import{f as J,l as X,m as Q}from"./chunk-PYPO7LRM.min.js";import"./chunk-CM5D5KZN.min.js";import{D as W,N as C,S as N,T as F,U as S,V as z,W as j,X as K,Y,Z as U,ba as Z,h,ia as V,j as u,v as H}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import"./chunk-OSRY5VT3.min.js";var x={NORMAL:0,REVERSE:1,HIGHLIGHT:2,MERGE:3,CHERRY_PICK:4},he=H.gitGraph,P=h(()=>X({...he,...W().gitGraph}),"getConfig"),c=new te(()=>{let r=P(),e=r.mainBranchName,s=r.mainBranchOrder;return{mainBranchName:e,commits:new Map,head:null,branchConfig:new Map([[e,{name:e,order:s}]]),branches:new Map([[e,null]]),currBranch:e,direction:"LR",seq:0,options:{}}});function q(){return J({length:7})}h(q,"getID");function se(r,e){let s=Object.create(null);return r.reduce((o,t)=>{let a=e(t);return s[a]||(s[a]=!0,o.push(t)),o},[])}h(se,"uniqBy");var le=h(function(r){c.records.direction=r},"setDirection"),me=h(function(r){u.debug("options str",r),r=r?.trim(),r=r||"{}";try{c.records.options=JSON.parse(r)}catch(e){u.error("error while parsing gitGraph options",e.message)}},"setOptions"),pe=h(function(){return c.records.options},"getOptions"),ge=h(function(r){let e=r.msg,s=r.id,o=r.type,t=r.tags;u.info("commit",e,s,o,t),u.debug("Entering commit:",e,s,o,t);let a=P();s=C.sanitizeText(s,a),e=C.sanitizeText(e,a),t=t?.map(n=>C.sanitizeText(n,a));let d={id:s||c.records.seq+"-"+q(),message:e,seq:c.records.seq++,type:o??x.NORMAL,tags:t??[],parents:c.records.head==null?[]:[c.records.head.id],branch:c.records.currBranch};c.records.head=d,u.info("main branch",a.mainBranchName),c.records.commits.set(d.id,d),c.records.branches.set(c.records.currBranch,d.id),u.debug("in pushCommit "+d.id)},"commit"),fe=h(function(r){let e=r.name,s=r.order;if(e=C.sanitizeText(e,P()),c.records.branches.has(e))throw new Error(`Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using "checkout ${e}")`);c.records.branches.set(e,c.records.head!=null?c.records.head.id:null),c.records.branchConfig.set(e,{name:e,order:s}),oe(e),u.debug("in createBranch")},"branch"),ye=h(r=>{let e=r.branch,s=r.id,o=r.type,t=r.tags,a=P();e=C.sanitizeText(e,a),s&&(s=C.sanitizeText(s,a));let d=c.records.branches.get(c.records.currBranch),n=c.records.branches.get(e),p=d?c.records.commits.get(d):void 0,l=n?c.records.commits.get(n):void 0;if(p&&l&&p.branch===e)throw new Error(`Cannot merge branch '${e}' into itself.`);if(c.records.currBranch===e){let i=new Error('Incorrect usage of "merge". Cannot merge a branch to itself');throw i.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["branch abc"]},i}if(p===void 0||!p){let i=new Error(`Incorrect usage of "merge". Current branch (${c.records.currBranch})has no commits`);throw i.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["commit"]},i}if(!c.records.branches.has(e)){let i=new Error('Incorrect usage of "merge". Branch to be merged ('+e+") does not exist");throw i.hash={text:`merge ${e}`,token:`merge ${e}`,expected:[`branch ${e}`]},i}if(l===void 0||!l){let i=new Error('Incorrect usage of "merge". Branch to be merged ('+e+") has no commits");throw i.hash={text:`merge ${e}`,token:`merge ${e}`,expected:['"commit"']},i}if(p===l){let i=new Error('Incorrect usage of "merge". Both branches have same head');throw i.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["branch abc"]},i}if(s&&c.records.commits.has(s)){let i=new Error('Incorrect usage of "merge". Commit with id:'+s+" already exists, use different custom Id");throw i.hash={text:`merge ${e} ${s} ${o} ${t?.join(" ")}`,token:`merge ${e} ${s} ${o} ${t?.join(" ")}`,expected:[`merge ${e} ${s}_UNIQUE ${o} ${t?.join(" ")}`]},i}let m=n||"",g={id:s||`${c.records.seq}-${q()}`,message:`merged branch ${e} into ${c.records.currBranch}`,seq:c.records.seq++,parents:c.records.head==null?[]:[c.records.head.id,m],branch:c.records.currBranch,type:x.MERGE,customType:o,customId:!!s,tags:t??[]};c.records.head=g,c.records.commits.set(g.id,g),c.records.branches.set(c.records.currBranch,g.id),u.debug(c.records.branches),u.debug("in mergeBranch")},"merge"),$e=h(function(r){let e=r.id,s=r.targetId,o=r.tags,t=r.parent;u.debug("Entering cherryPick:",e,s,o);let a=P();if(e=C.sanitizeText(e,a),s=C.sanitizeText(s,a),o=o?.map(p=>C.sanitizeText(p,a)),t=C.sanitizeText(t,a),!e||!c.records.commits.has(e)){let p=new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');throw p.hash={text:`cherryPick ${e} ${s}`,token:`cherryPick ${e} ${s}`,expected:["cherry-pick abc"]},p}let d=c.records.commits.get(e);if(d===void 0||!d)throw new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');if(t&&!(Array.isArray(d.parents)&&d.parents.includes(t)))throw new Error("Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.");let n=d.branch;if(d.type===x.MERGE&&!t)throw new Error("Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.");if(!s||!c.records.commits.has(s)){if(n===c.records.currBranch){let g=new Error('Incorrect usage of "cherryPick". Source commit is already on current branch');throw g.hash={text:`cherryPick ${e} ${s}`,token:`cherryPick ${e} ${s}`,expected:["cherry-pick abc"]},g}let p=c.records.branches.get(c.records.currBranch);if(p===void 0||!p){let g=new Error(`Incorrect usage of "cherry-pick". Current branch (${c.records.currBranch})has no commits`);throw g.hash={text:`cherryPick ${e} ${s}`,token:`cherryPick ${e} ${s}`,expected:["cherry-pick abc"]},g}let l=c.records.commits.get(p);if(l===void 0||!l){let g=new Error(`Incorrect usage of "cherry-pick". Current branch (${c.records.currBranch})has no commits`);throw g.hash={text:`cherryPick ${e} ${s}`,token:`cherryPick ${e} ${s}`,expected:["cherry-pick abc"]},g}let m={id:c.records.seq+"-"+q(),message:`cherry-picked ${d?.message} into ${c.records.currBranch}`,seq:c.records.seq++,parents:c.records.head==null?[]:[c.records.head.id,d.id],branch:c.records.currBranch,type:x.CHERRY_PICK,tags:o?o.filter(Boolean):[`cherry-pick:${d.id}${d.type===x.MERGE?`|parent:${t}`:""}`]};c.records.head=m,c.records.commits.set(m.id,m),c.records.branches.set(c.records.currBranch,m.id),u.debug(c.records.branches),u.debug("in cherryPick")}},"cherryPick"),oe=h(function(r){if(r=C.sanitizeText(r,P()),c.records.branches.has(r)){c.records.currBranch=r;let e=c.records.branches.get(c.records.currBranch);e===void 0||!e?c.records.head=null:c.records.head=c.records.commits.get(e)??null}else{let e=new Error(`Trying to checkout branch which is not yet created. (Help try using "branch ${r}")`);throw e.hash={text:`checkout ${r}`,token:`checkout ${r}`,expected:[`branch ${r}`]},e}},"checkout");function D(r,e,s){let o=r.indexOf(e);o===-1?r.push(s):r.splice(o,1,s)}h(D,"upsert");function _(r){let e=r.reduce((t,a)=>t.seq>a.seq?t:a,r[0]),s="";r.forEach(function(t){t===e?s+="	*":s+="	|"});let o=[s,e.id,e.seq];for(let t in c.records.branches)c.records.branches.get(t)===e.id&&o.push(t);if(u.debug(o.join(" ")),e.parents&&e.parents.length==2&&e.parents[0]&&e.parents[1]){let t=c.records.commits.get(e.parents[0]);D(r,e,t),e.parents[1]&&r.push(c.records.commits.get(e.parents[1]))}else{if(e.parents.length==0)return;if(e.parents[0]){let t=c.records.commits.get(e.parents[0]);D(r,e,t)}}r=se(r,t=>t.id),_(r)}h(_,"prettyPrintCommitHistory");var xe=h(function(){u.debug(c.records.commits);let r=ne()[0];_([r])},"prettyPrint"),ue=h(function(){c.reset(),N()},"clear"),be=h(function(){return[...c.records.branchConfig.values()].map((e,s)=>e.order!==null&&e.order!==void 0?e:{...e,order:parseFloat(`0.${s}`)}).sort((e,s)=>(e.order??0)-(s.order??0)).map(({name:e})=>({name:e}))},"getBranchesAsObjArray"),we=h(function(){return c.records.branches},"getBranches"),Ce=h(function(){return c.records.commits},"getCommits"),ne=h(function(){let r=[...c.records.commits.values()];return r.forEach(function(e){u.debug(e.id)}),r.sort((e,s)=>e.seq-s.seq),r},"getCommitsArray"),Be=h(function(){return c.records.currBranch},"getCurrentBranch"),ve=h(function(){return c.records.direction},"getDirection"),ke=h(function(){return c.records.head},"getHead"),ce={commitType:x,getConfig:P,setDirection:le,setOptions:me,getOptions:pe,commit:ge,branch:fe,merge:ye,cherryPick:$e,checkout:oe,prettyPrint:xe,clear:ue,getBranchesAsObjArray:be,getBranches:we,getCommits:Ce,getCommitsArray:ne,getCurrentBranch:Be,getDirection:ve,getHead:ke,setAccTitle:F,getAccTitle:S,getAccDescription:j,setAccDescription:z,setDiagramTitle:K,getDiagramTitle:Y},Te=h((r,e)=>{ee(r,e),r.dir&&e.setDirection(r.dir);for(let s of r.statements)Ee(s,e)},"populate"),Ee=h((r,e)=>{let o={Commit:h(t=>e.commit(Le(t)),"Commit"),Branch:h(t=>e.branch(Me(t)),"Branch"),Merge:h(t=>e.merge(Pe(t)),"Merge"),Checkout:h(t=>e.checkout(Re(t)),"Checkout"),CherryPicking:h(t=>e.cherryPick(Oe(t)),"CherryPicking")}[r.$type];o?o(r):u.error(`Unknown statement type: ${r.$type}`)},"parseStatement"),Le=h(r=>({id:r.id,msg:r.message??"",type:r.type!==void 0?x[r.type]:x.NORMAL,tags:r.tags??void 0}),"parseCommit"),Me=h(r=>({name:r.name,order:r.order??0}),"parseBranch"),Pe=h(r=>({branch:r.branch,id:r.id??"",type:r.type!==void 0?x[r.type]:void 0,tags:r.tags??void 0}),"parseMerge"),Re=h(r=>r.branch,"parseCheckout"),Oe=h(r=>({id:r.id,targetId:"",tags:r.tags?.length===0?void 0:r.tags,parent:r.parent}),"parseCherryPicking"),Ge={parse:h(async r=>{let e=await re("gitGraph",r);u.debug(e),Te(e,ce)},"parse")},Ie=U(),B=Ie?.gitGraph,E=10,L=40,v=4,k=2,M=8,b=new Map,w=new Map,G=30,R=new Map,I=[],T=0,y="LR",qe=h(()=>{b.clear(),w.clear(),R.clear(),T=0,I=[],y="LR"},"clear"),ie=h(r=>{let e=document.createElementNS("http://www.w3.org/2000/svg","text");return(typeof r=="string"?r.split(/\\n|\n|<br\s*\/?>/gi):r).forEach(o=>{let t=document.createElementNS("http://www.w3.org/2000/svg","tspan");t.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),t.setAttribute("dy","1em"),t.setAttribute("x","0"),t.setAttribute("class","row"),t.textContent=o.trim(),e.appendChild(t)}),e},"drawText"),de=h(r=>{let e,s,o;return y==="BT"?(s=h((t,a)=>t<=a,"comparisonFunc"),o=1/0):(s=h((t,a)=>t>=a,"comparisonFunc"),o=0),r.forEach(t=>{let a=y==="TB"||y=="BT"?w.get(t)?.y:w.get(t)?.x;a!==void 0&&s(a,o)&&(e=t,o=a)}),e},"findClosestParent"),Ae=h(r=>{let e="",s=1/0;return r.forEach(o=>{let t=w.get(o).y;t<=s&&(e=o,s=t)}),e||void 0},"findClosestParentBT"),De=h((r,e,s)=>{let o=s,t=s,a=[];r.forEach(d=>{let n=e.get(d);if(!n)throw new Error(`Commit not found for key ${d}`);n.parents.length?(o=He(n),t=Math.max(o,t)):a.push(n),We(n,o)}),o=t,a.forEach(d=>{Ne(d,o,s)}),r.forEach(d=>{let n=e.get(d);if(n?.parents.length){let p=Ae(n.parents);o=w.get(p).y-L,o<=t&&(t=o);let l=b.get(n.branch).pos,m=o-E;w.set(n.id,{x:l,y:m})}})},"setParallelBTPos"),_e=h(r=>{let e=de(r.parents.filter(o=>o!==null));if(!e)throw new Error(`Closest parent not found for commit ${r.id}`);let s=w.get(e)?.y;if(s===void 0)throw new Error(`Closest parent position not found for commit ${r.id}`);return s},"findClosestParentPos"),He=h(r=>_e(r)+L,"calculateCommitPosition"),We=h((r,e)=>{let s=b.get(r.branch);if(!s)throw new Error(`Branch not found for commit ${r.id}`);let o=s.pos,t=e+E;return w.set(r.id,{x:o,y:t}),{x:o,y:t}},"setCommitPosition"),Ne=h((r,e,s)=>{let o=b.get(r.branch);if(!o)throw new Error(`Branch not found for commit ${r.id}`);let t=e+s,a=o.pos;w.set(r.id,{x:a,y:t})},"setRootPosition"),Fe=h((r,e,s,o,t,a)=>{if(a===x.HIGHLIGHT)r.append("rect").attr("x",s.x-10).attr("y",s.y-10).attr("width",20).attr("height",20).attr("class",`commit ${e.id} commit-highlight${t%M} ${o}-outer`),r.append("rect").attr("x",s.x-6).attr("y",s.y-6).attr("width",12).attr("height",12).attr("class",`commit ${e.id} commit${t%M} ${o}-inner`);else if(a===x.CHERRY_PICK)r.append("circle").attr("cx",s.x).attr("cy",s.y).attr("r",10).attr("class",`commit ${e.id} ${o}`),r.append("circle").attr("cx",s.x-3).attr("cy",s.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${e.id} ${o}`),r.append("circle").attr("cx",s.x+3).attr("cy",s.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${e.id} ${o}`),r.append("line").attr("x1",s.x+3).attr("y1",s.y+1).attr("x2",s.x).attr("y2",s.y-5).attr("stroke","#fff").attr("class",`commit ${e.id} ${o}`),r.append("line").attr("x1",s.x-3).attr("y1",s.y+1).attr("x2",s.x).attr("y2",s.y-5).attr("stroke","#fff").attr("class",`commit ${e.id} ${o}`);else{let d=r.append("circle");if(d.attr("cx",s.x),d.attr("cy",s.y),d.attr("r",e.type===x.MERGE?9:10),d.attr("class",`commit ${e.id} commit${t%M}`),a===x.MERGE){let n=r.append("circle");n.attr("cx",s.x),n.attr("cy",s.y),n.attr("r",6),n.attr("class",`commit ${o} ${e.id} commit${t%M}`)}a===x.REVERSE&&r.append("path").attr("d",`M ${s.x-5},${s.y-5}L${s.x+5},${s.y+5}M${s.x-5},${s.y+5}L${s.x+5},${s.y-5}`).attr("class",`commit ${o} ${e.id} commit${t%M}`)}},"drawCommitBullet"),Se=h((r,e,s,o)=>{if(e.type!==x.CHERRY_PICK&&(e.customId&&e.type===x.MERGE||e.type!==x.MERGE)&&B?.showCommitLabel){let t=r.append("g"),a=t.insert("rect").attr("class","commit-label-bkg"),d=t.append("text").attr("x",o).attr("y",s.y+25).attr("class","commit-label").text(e.id),n=d.node()?.getBBox();if(n&&(a.attr("x",s.posWithOffset-n.width/2-k).attr("y",s.y+13.5).attr("width",n.width+2*k).attr("height",n.height+2*k),y==="TB"||y==="BT"?(a.attr("x",s.x-(n.width+4*v+5)).attr("y",s.y-12),d.attr("x",s.x-(n.width+4*v)).attr("y",s.y+n.height-12)):d.attr("x",s.posWithOffset-n.width/2),B.rotateCommitLabel))if(y==="TB"||y==="BT")d.attr("transform","rotate(-45, "+s.x+", "+s.y+")"),a.attr("transform","rotate(-45, "+s.x+", "+s.y+")");else{let p=-7.5-(n.width+10)/25*9.5,l=10+n.width/25*8.5;t.attr("transform","translate("+p+", "+l+") rotate(-45, "+o+", "+s.y+")")}}},"drawCommitLabel"),ze=h((r,e,s,o)=>{if(e.tags.length>0){let t=0,a=0,d=0,n=[];for(let p of e.tags.reverse()){let l=r.insert("polygon"),m=r.append("circle"),g=r.append("text").attr("y",s.y-16-t).attr("class","tag-label").text(p),i=g.node()?.getBBox();if(!i)throw new Error("Tag bbox not found");a=Math.max(a,i.width),d=Math.max(d,i.height),g.attr("x",s.posWithOffset-i.width/2),n.push({tag:g,hole:m,rect:l,yOffset:t}),t+=20}for(let{tag:p,hole:l,rect:m,yOffset:g}of n){let i=d/2,f=s.y-19.2-g;if(m.attr("class","tag-label-bkg").attr("points",`
      ${o-a/2-v/2},${f+k}  
      ${o-a/2-v/2},${f-k}
      ${s.posWithOffset-a/2-v},${f-i-k}
      ${s.posWithOffset+a/2+v},${f-i-k}
      ${s.posWithOffset+a/2+v},${f+i+k}
      ${s.posWithOffset-a/2-v},${f+i+k}`),l.attr("cy",f).attr("cx",o-a/2+v/2).attr("r",1.5).attr("class","tag-hole"),y==="TB"||y==="BT"){let $=o+g;m.attr("class","tag-label-bkg").attr("points",`
        ${s.x},${$+2}
        ${s.x},${$-2}
        ${s.x+E},${$-i-2}
        ${s.x+E+a+4},${$-i-2}
        ${s.x+E+a+4},${$+i+2}
        ${s.x+E},${$+i+2}`).attr("transform","translate(12,12) rotate(45, "+s.x+","+o+")"),l.attr("cx",s.x+v/2).attr("cy",$).attr("transform","translate(12,12) rotate(45, "+s.x+","+o+")"),p.attr("x",s.x+5).attr("y",$+3).attr("transform","translate(14,14) rotate(45, "+s.x+","+o+")")}}}},"drawCommitTags"),je=h(r=>{switch(r.customType??r.type){case x.NORMAL:return"commit-normal";case x.REVERSE:return"commit-reverse";case x.HIGHLIGHT:return"commit-highlight";case x.MERGE:return"commit-merge";case x.CHERRY_PICK:return"commit-cherry-pick";default:return"commit-normal"}},"getCommitClassType"),Ke=h((r,e,s,o)=>{let t={x:0,y:0};if(r.parents.length>0){let a=de(r.parents);if(a){let d=o.get(a)??t;return e==="TB"?d.y+L:e==="BT"?(o.get(r.id)??t).y-L:d.x+L}}else return e==="TB"?G:e==="BT"?(o.get(r.id)??t).y-L:0;return 0},"calculatePosition"),Ye=h((r,e,s)=>{let o=y==="BT"&&s?e:e+E,t=y==="TB"||y==="BT"?o:b.get(r.branch)?.pos,a=y==="TB"||y==="BT"?b.get(r.branch)?.pos:o;if(a===void 0||t===void 0)throw new Error(`Position were undefined for commit ${r.id}`);return{x:a,y:t,posWithOffset:o}},"getCommitPosition"),ae=h((r,e,s)=>{if(!B)throw new Error("GitGraph config not found");let o=r.append("g").attr("class","commit-bullets"),t=r.append("g").attr("class","commit-labels"),a=y==="TB"||y==="BT"?G:0,d=[...e.keys()],n=B?.parallelCommits??!1,p=h((m,g)=>{let i=e.get(m)?.seq,f=e.get(g)?.seq;return i!==void 0&&f!==void 0?i-f:0},"sortKeys"),l=d.sort(p);y==="BT"&&(n&&De(l,e,a),l=l.reverse()),l.forEach(m=>{let g=e.get(m);if(!g)throw new Error(`Commit not found for key ${m}`);n&&(a=Ke(g,y,a,w));let i=Ye(g,a,n);if(s){let f=je(g),$=g.customType??g.type,A=b.get(g.branch)?.index??0;Fe(o,g,i,f,A,$),Se(t,g,i,a),ze(t,g,i,a)}y==="TB"||y==="BT"?w.set(g.id,{x:i.x,y:i.posWithOffset}):w.set(g.id,{x:i.posWithOffset,y:i.y}),a=y==="BT"&&n?a+L:a+L+E,a>T&&(T=a)})},"drawCommits"),Ue=h((r,e,s,o,t)=>{let d=(y==="TB"||y==="BT"?s.x<o.x:s.y<o.y)?e.branch:r.branch,n=h(l=>l.branch===d,"isOnBranchToGetCurve"),p=h(l=>l.seq>r.seq&&l.seq<e.seq,"isBetweenCommits");return[...t.values()].some(l=>p(l)&&n(l))},"shouldRerouteArrow"),O=h((r,e,s=0)=>{let o=r+Math.abs(r-e)/2;if(s>5)return o;if(I.every(d=>Math.abs(d-o)>=10))return I.push(o),o;let a=Math.abs(r-e);return O(r,e-a/5,s+1)},"findLane"),Ze=h((r,e,s,o)=>{let t=w.get(e.id),a=w.get(s.id);if(t===void 0||a===void 0)throw new Error(`Commit positions not found for commits ${e.id} and ${s.id}`);let d=Ue(e,s,t,a,o),n="",p="",l=0,m=0,g=b.get(s.branch)?.index;s.type===x.MERGE&&e.id!==s.parents[0]&&(g=b.get(e.branch)?.index);let i;if(d){n="A 10 10, 0, 0, 0,",p="A 10 10, 0, 0, 1,",l=10,m=10;let f=t.y<a.y?O(t.y,a.y):O(a.y,t.y),$=t.x<a.x?O(t.x,a.x):O(a.x,t.x);y==="TB"?t.x<a.x?i=`M ${t.x} ${t.y} L ${$-l} ${t.y} ${p} ${$} ${t.y+m} L ${$} ${a.y-l} ${n} ${$+m} ${a.y} L ${a.x} ${a.y}`:(g=b.get(e.branch)?.index,i=`M ${t.x} ${t.y} L ${$+l} ${t.y} ${n} ${$} ${t.y+m} L ${$} ${a.y-l} ${p} ${$-m} ${a.y} L ${a.x} ${a.y}`):y==="BT"?t.x<a.x?i=`M ${t.x} ${t.y} L ${$-l} ${t.y} ${n} ${$} ${t.y-m} L ${$} ${a.y+l} ${p} ${$+m} ${a.y} L ${a.x} ${a.y}`:(g=b.get(e.branch)?.index,i=`M ${t.x} ${t.y} L ${$+l} ${t.y} ${p} ${$} ${t.y-m} L ${$} ${a.y+l} ${n} ${$-m} ${a.y} L ${a.x} ${a.y}`):t.y<a.y?i=`M ${t.x} ${t.y} L ${t.x} ${f-l} ${n} ${t.x+m} ${f} L ${a.x-l} ${f} ${p} ${a.x} ${f+m} L ${a.x} ${a.y}`:(g=b.get(e.branch)?.index,i=`M ${t.x} ${t.y} L ${t.x} ${f+l} ${p} ${t.x+m} ${f} L ${a.x-l} ${f} ${n} ${a.x} ${f-m} L ${a.x} ${a.y}`)}else n="A 20 20, 0, 0, 0,",p="A 20 20, 0, 0, 1,",l=20,m=20,y==="TB"?(t.x<a.x&&(s.type===x.MERGE&&e.id!==s.parents[0]?i=`M ${t.x} ${t.y} L ${t.x} ${a.y-l} ${n} ${t.x+m} ${a.y} L ${a.x} ${a.y}`:i=`M ${t.x} ${t.y} L ${a.x-l} ${t.y} ${p} ${a.x} ${t.y+m} L ${a.x} ${a.y}`),t.x>a.x&&(n="A 20 20, 0, 0, 0,",p="A 20 20, 0, 0, 1,",l=20,m=20,s.type===x.MERGE&&e.id!==s.parents[0]?i=`M ${t.x} ${t.y} L ${t.x} ${a.y-l} ${p} ${t.x-m} ${a.y} L ${a.x} ${a.y}`:i=`M ${t.x} ${t.y} L ${a.x+l} ${t.y} ${n} ${a.x} ${t.y+m} L ${a.x} ${a.y}`),t.x===a.x&&(i=`M ${t.x} ${t.y} L ${a.x} ${a.y}`)):y==="BT"?(t.x<a.x&&(s.type===x.MERGE&&e.id!==s.parents[0]?i=`M ${t.x} ${t.y} L ${t.x} ${a.y+l} ${p} ${t.x+m} ${a.y} L ${a.x} ${a.y}`:i=`M ${t.x} ${t.y} L ${a.x-l} ${t.y} ${n} ${a.x} ${t.y-m} L ${a.x} ${a.y}`),t.x>a.x&&(n="A 20 20, 0, 0, 0,",p="A 20 20, 0, 0, 1,",l=20,m=20,s.type===x.MERGE&&e.id!==s.parents[0]?i=`M ${t.x} ${t.y} L ${t.x} ${a.y+l} ${n} ${t.x-m} ${a.y} L ${a.x} ${a.y}`:i=`M ${t.x} ${t.y} L ${a.x-l} ${t.y} ${n} ${a.x} ${t.y-m} L ${a.x} ${a.y}`),t.x===a.x&&(i=`M ${t.x} ${t.y} L ${a.x} ${a.y}`)):(t.y<a.y&&(s.type===x.MERGE&&e.id!==s.parents[0]?i=`M ${t.x} ${t.y} L ${a.x-l} ${t.y} ${p} ${a.x} ${t.y+m} L ${a.x} ${a.y}`:i=`M ${t.x} ${t.y} L ${t.x} ${a.y-l} ${n} ${t.x+m} ${a.y} L ${a.x} ${a.y}`),t.y>a.y&&(s.type===x.MERGE&&e.id!==s.parents[0]?i=`M ${t.x} ${t.y} L ${a.x-l} ${t.y} ${n} ${a.x} ${t.y-m} L ${a.x} ${a.y}`:i=`M ${t.x} ${t.y} L ${t.x} ${a.y+l} ${p} ${t.x+m} ${a.y} L ${a.x} ${a.y}`),t.y===a.y&&(i=`M ${t.x} ${t.y} L ${a.x} ${a.y}`));if(i===void 0)throw new Error("Line definition not found");r.append("path").attr("d",i).attr("class","arrow arrow"+g%M)},"drawArrow"),Ve=h((r,e)=>{let s=r.append("g").attr("class","commit-arrows");[...e.keys()].forEach(o=>{let t=e.get(o);t.parents&&t.parents.length>0&&t.parents.forEach(a=>{Ze(s,e.get(a),t,e)})})},"drawArrows"),Je=h((r,e)=>{let s=r.append("g");e.forEach((o,t)=>{let a=t%M,d=b.get(o.name)?.pos;if(d===void 0)throw new Error(`Position not found for branch ${o.name}`);let n=s.append("line");n.attr("x1",0),n.attr("y1",d),n.attr("x2",T),n.attr("y2",d),n.attr("class","branch branch"+a),y==="TB"?(n.attr("y1",G),n.attr("x1",d),n.attr("y2",T),n.attr("x2",d)):y==="BT"&&(n.attr("y1",T),n.attr("x1",d),n.attr("y2",G),n.attr("x2",d)),I.push(d);let p=o.name,l=ie(p),m=s.insert("rect"),i=s.insert("g").attr("class","branchLabel").insert("g").attr("class","label branch-label"+a);i.node().appendChild(l);let f=l.getBBox();m.attr("class","branchLabelBkg label"+a).attr("rx",4).attr("ry",4).attr("x",-f.width-4-(B?.rotateCommitLabel===!0?30:0)).attr("y",-f.height/2+8).attr("width",f.width+18).attr("height",f.height+4),i.attr("transform","translate("+(-f.width-14-(B?.rotateCommitLabel===!0?30:0))+", "+(d-f.height/2-1)+")"),y==="TB"?(m.attr("x",d-f.width/2-10).attr("y",0),i.attr("transform","translate("+(d-f.width/2-5)+", 0)")):y==="BT"?(m.attr("x",d-f.width/2-10).attr("y",T),i.attr("transform","translate("+(d-f.width/2-5)+", "+T+")")):m.attr("transform","translate(-19, "+(d-f.height/2)+")")})},"drawBranches"),Xe=h(function(r,e,s,o,t){return b.set(r,{pos:e,index:s}),e+=50+(t?40:0)+(y==="TB"||y==="BT"?o.width/2:0),e},"setBranchPosition"),Qe=h(function(r,e,s,o){if(qe(),u.debug("in gitgraph renderer",r+`
`,"id:",e,s),!B)throw new Error("GitGraph config not found");let t=B.rotateCommitLabel??!1,a=o.db;R=a.getCommits();let d=a.getBranchesAsObjArray();y=a.getDirection();let n=V(`[id="${e}"]`),p=0;d.forEach((l,m)=>{let g=ie(l.name),i=n.append("g"),f=i.insert("g").attr("class","branchLabel"),$=f.insert("g").attr("class","label branch-label");$.node()?.appendChild(g);let A=g.getBBox();p=Xe(l.name,p,m,A,t),$.remove(),f.remove(),i.remove()}),ae(n,R,!1),B.showBranches&&Je(n,d),Ve(n,R),ae(n,R,!0),Q.insertTitle(n,"gitTitleText",B.titleTopMargin??0,a.getDiagramTitle()),Z(void 0,n,B.diagramPadding,B.useMaxWidth)},"draw"),et={draw:Qe},tt=h(r=>`
  .commit-id,
  .commit-msg,
  .branch-label {
    fill: lightgrey;
    color: lightgrey;
    font-family: 'trebuchet ms', verdana, arial, sans-serif;
    font-family: var(--mermaid-font-family);
  }
  ${[0,1,2,3,4,5,6,7].map(e=>`
        .branch-label${e} { fill: ${r["gitBranchLabel"+e]}; }
        .commit${e} { stroke: ${r["git"+e]}; fill: ${r["git"+e]}; }
        .commit-highlight${e} { stroke: ${r["gitInv"+e]}; fill: ${r["gitInv"+e]}; }
        .label${e}  { fill: ${r["git"+e]}; }
        .arrow${e} { stroke: ${r["git"+e]}; }
        `).join(`
`)}

  .branch {
    stroke-width: 1;
    stroke: ${r.lineColor};
    stroke-dasharray: 2;
  }
  .commit-label { font-size: ${r.commitLabelFontSize}; fill: ${r.commitLabelColor};}
  .commit-label-bkg { font-size: ${r.commitLabelFontSize}; fill: ${r.commitLabelBackground}; opacity: 0.5; }
  .tag-label { font-size: ${r.tagLabelFontSize}; fill: ${r.tagLabelColor};}
  .tag-label-bkg { fill: ${r.tagLabelBackground}; stroke: ${r.tagLabelBorder}; }
  .tag-hole { fill: ${r.textColor}; }

  .commit-merge {
    stroke: ${r.primaryColor};
    fill: ${r.primaryColor};
  }
  .commit-reverse {
    stroke: ${r.primaryColor};
    fill: ${r.primaryColor};
    stroke-width: 3;
  }
  .commit-highlight-outer {
  }
  .commit-highlight-inner {
    stroke: ${r.primaryColor};
    fill: ${r.primaryColor};
  }

  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}
  .gitTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${r.textColor};
  }
`,"getStyles"),rt=tt,dt={parser:Ge,db:ce,renderer:et,styles:rt};export{dt as diagram};
//# sourceMappingURL=gitGraphDiagram-NRZ2UAAF-WVTRWY3E.min.js.map
