<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaLayout | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaLayout | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLayout.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLayout%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaLayout">



  <h1 id="DrawnUi_Draw_SkiaLayout" data-uid="DrawnUi.Draw.SkiaLayout" class="text-break">
Class SkiaLayout  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Wrap.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SkiaLayout : SkiaControl, INotifyPropertyChanged, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IVisualElementController, IElementController, IView, ITransform, IElement, IVisualTreeElement, IContainer, IList&lt;IView&gt;, ICollection&lt;IView&gt;, IEnumerable&lt;IView&gt;, IEnumerable, IHasAfterEffects, ISkiaGestureListener, ISkiaGridLayout, ISkiaLayout, ISkiaControl, IDrawnBase, ICanBeUpdatedWithContext, ICanBeUpdated, ILayoutInsideViewport, IInsideViewport, IVisibilityAware, IDisposable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement">StyleableElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement">NavigableElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement">VisualElement</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></div>
      <div><span class="xref">SkiaLayout</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider">IEffectControlProvider</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement">IToolTipElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement">IContextFlyoutElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable">IAnimatable</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller">IVisualElementController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller">IElementController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform">ITransform</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement">IElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontainer">IContainer</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1">ICollection</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ienumerable">IEnumerable</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IHasAfterEffects.html">IHasAfterEffects</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaGridLayout.html">ISkiaGridLayout</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaLayout.html">ISkiaLayout</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html">ICanBeUpdatedWithContext</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html">ICanBeUpdated</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ILayoutInsideViewport.html">ILayoutInsideViewport</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IInsideViewport.html">IInsideViewport</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IVisibilityAware.html">IVisibilityAware</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    </dd>
  </dl>

  <dl class="typelist derived">
    <dt>Derived</dt>
    <dd>
      <div><a class="xref" href="DrawnUi.Controls.GridLayout.html">GridLayout</a></div>
      <div><a class="xref" href="DrawnUi.Controls.ScrollPickerLabelContainer.html">ScrollPickerLabelContainer</a></div>
      <div><a class="xref" href="DrawnUi.Controls.ScrollPickerWheel.html">ScrollPickerWheel</a></div>
      <div><a class="xref" href="DrawnUi.Controls.SkiaDecoratedGrid.html">SkiaDecoratedGrid</a></div>
      <div><a class="xref" href="DrawnUi.Controls.SkiaDrawnCell.html">SkiaDrawnCell</a></div>
      <div><a class="xref" href="DrawnUi.Controls.SkiaSpinner.html">SkiaSpinner</a></div>
      <div><a class="xref" href="DrawnUi.Controls.SkiaTabsSelector.html">SkiaTabsSelector</a></div>
      <div><a class="xref" href="DrawnUi.Controls.SkiaViewSwitcher.html">SkiaViewSwitcher</a></div>
      <div><a class="xref" href="DrawnUi.Controls.SkiaWheelPicker.html">SkiaWheelPicker</a></div>
      <div><a class="xref" href="DrawnUi.Controls.SkiaWheelPickerCell.html">SkiaWheelPickerCell</a></div>
      <div><a class="xref" href="DrawnUi.Controls.SkiaWheelStack.html">SkiaWheelStack</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ContentLayout.html">ContentLayout</a></div>
      <div><a class="xref" href="DrawnUi.Draw.RefreshIndicator.html">RefreshIndicator</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaButton.html">SkiaButton</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaEditor.html">SkiaEditor</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaGrid.html">SkiaGrid</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaLayer.html">SkiaLayer</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaRangeBase.html">SkiaRangeBase</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaRow.html">SkiaRow</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaShape.html">SkiaShape</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaSlider.html">SkiaSlider</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaStack.html">SkiaStack</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaToggle.html">SkiaToggle</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaWrap.html">SkiaWrap</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SliderThumb.html">SliderThumb</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SliderValueDesc.html">SliderValueDesc</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SnappingLayout.html">SnappingLayout</a></div>
    </dd>
  </dl>

  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetEnumerator">SkiaControl.GetEnumerator()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Add_Microsoft_Maui_IView_">SkiaControl.Add(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Contains_Microsoft_Maui_IView_">SkiaControl.Contains(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CopyTo_Microsoft_Maui_IView___System_Int32_">SkiaControl.CopyTo(IView[], int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Remove_Microsoft_Maui_IView_">SkiaControl.Remove(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Count">SkiaControl.Count</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsReadOnly">SkiaControl.IsReadOnly</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IndexOf_Microsoft_Maui_IView_">SkiaControl.IndexOf(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Insert_System_Int32_Microsoft_Maui_IView_">SkiaControl.Insert(int, IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RemoveAt_System_Int32_">SkiaControl.RemoveAt(int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Item_System_Int32_">SkiaControl.this[int]</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetVisualParent">SkiaControl.GetVisualParent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransparentColor">SkiaControl.TransparentColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WhiteColor">SkiaControl.WhiteColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BlackColor">SkiaControl.BlackColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RedColor">SkiaControl.RedColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UsingControlStyle">SkiaControl.UsingControlStyle</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearColorProperty">SkiaControl.ClearColorProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearColor">SkiaControl.ClearColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnPropertyChanged_System_String_">SkiaControl.OnPropertyChanged(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddSubView_DrawnUi_Draw_SkiaControl_">SkiaControl.AddSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RemoveSubView_DrawnUi_Draw_SkiaControl_">SkiaControl.RemoveSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnLayoutChanged">SkiaControl.OnLayoutChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetupGradient_SkiaSharp_SKPaint_DrawnUi_Draw_SkiaGradient_SkiaSharp_SKRect_">SkiaControl.SetupGradient(SKPaint, SkiaGradient, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateShadow_DrawnUi_Draw_SkiaShadow_System_Single_">SkiaControl.CreateShadow(SkiaShadow, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdatePlatformShadow">SkiaControl.UpdatePlatformShadow()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PlatformShadow">SkiaControl.PlatformShadow</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HasPlatformClip">SkiaControl.HasPlatformClip()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDensity">SkiaControl.GetDensity()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseCacheProperty">SkiaControl.UseCacheProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseCache">SkiaControl.UseCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AllowCachingProperty">SkiaControl.AllowCachingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AllowCaching">SkiaControl.AllowCaching</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderObject">SkiaControl.RenderObject</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnCacheCreated">SkiaControl.OnCacheCreated()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnCacheDestroyed">SkiaControl.OnCacheDestroyed()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreatedCache">SkiaControl.CreatedCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DestroyRenderingObject">SkiaControl.DestroyRenderingObject()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawRenderObject_DrawnUi_Draw_DrawingContext_System_Single_System_Single_DrawnUi_Draw_CachedObject_">SkiaControl.DrawRenderObject(DrawingContext, float, float, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsRenderObjectValid_SkiaSharp_SKSize_DrawnUi_Draw_CachedObject_">SkiaControl.IsRenderObjectValid(SKSize, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderedObject_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_System_Boolean_">SkiaControl.CreateRenderedObject(DrawingContext, SKRect, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RasterizeToCache_DrawnUi_Draw_DrawingContext_">SkiaControl.RasterizeToCache(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckCachedObjectValid_DrawnUi_Draw_CachedObject_SkiaSharp_SKRect_DrawnUi_Draw_SkiaDrawingContext_">SkiaControl.CheckCachedObjectValid(CachedObject, SKRect, SkiaDrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UsingCacheType">SkiaControl.UsingCacheType</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderingObject_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_DrawnUi_Draw_CachedObject_DrawnUi_Draw_SkiaCacheType_System_Action_DrawnUi_Draw_DrawingContext__">SkiaControl.CreateRenderingObject(DrawingContext, SKRect, CachedObject, SkiaCacheType, Action&lt;DrawingContext&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DelegateDrawCache">SkiaControl.DelegateDrawCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawRenderObjectInternal_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_">SkiaControl.DrawRenderObjectInternal(DrawingContext, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsCacheImage">SkiaControl.IsCacheImage</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsCacheOperations">SkiaControl.IsCacheOperations</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseRenderingObject_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_">SkiaControl.UseRenderingObject(DrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CacheValidity">SkiaControl.CacheValidity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOffscreenRenderingAction">SkiaControl.GetOffscreenRenderingAction()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PushToOffscreenRendering_System_Action_System_Threading_CancellationToken_">SkiaControl.PushToOffscreenRendering(Action, CancellationToken)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_semaphoreOffsecreenProcess">SkiaControl.semaphoreOffsecreenProcess</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ProcessOffscreenCacheRenderingAsync">SkiaControl.ProcessOffscreenCacheRenderingAsync()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedUpdateFrontCache">SkiaControl.NeedUpdateFrontCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateCache">SkiaControl.InvalidateCache()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateCacheWithPrevious">SkiaControl.InvalidateCacheWithPrevious()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetCacheRecordingArea_SkiaSharp_SKRect_">SkiaControl.GetCacheRecordingArea(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetCacheArea_SkiaSharp_SKRect_">SkiaControl.GetCacheArea(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawUsingRenderObject_DrawnUi_Draw_DrawingContext_System_Single_System_Single_">SkiaControl.DrawUsingRenderObject(DrawingContext, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PrepareNode_DrawnUi_Draw_DrawingContext_System_Single_System_Single_">SkiaControl.PrepareNode(DrawingContext, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawDirectInternal_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_">SkiaControl.DrawDirectInternal(DrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderingObjectAndPaint_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_System_Action_DrawnUi_Draw_DrawingContext__">SkiaControl.CreateRenderingObjectAndPaint(DrawingContext, SKRect, Action&lt;DrawingContext&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AttachEffects">SkiaControl.AttachEffects()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnVisualEffectsChanged">SkiaControl.OnVisualEffectsChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectsGestureProcessors">SkiaControl.EffectsGestureProcessors</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectsState">SkiaControl.EffectsState</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectRenderers">SkiaControl.EffectRenderers</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectImageFilter">SkiaControl.EffectImageFilter</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectColorFilter">SkiaControl.EffectColorFilter</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectPostRenderer">SkiaControl.EffectPostRenderer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisualEffectsProperty">SkiaControl.VisualEffectsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisualEffects">SkiaControl.VisualEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisableEffectsProperty">SkiaControl.DisableEffectsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisableEffects">SkiaControl.DisableEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateParents">SkiaControl.InvalidateParents()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidatedParent">SkiaControl.InvalidatedParent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateParent">SkiaControl.InvalidateParent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateViewport">SkiaControl.InvalidateViewport()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DirtyChildrenTracker">SkiaControl.DirtyChildrenTracker</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DirtyChildrenInternal">SkiaControl.DirtyChildrenInternal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateByChild_DrawnUi_Draw_SkiaControl_">SkiaControl.UpdateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisualLayer">SkiaControl.VisualLayer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CachedImage">SkiaControl.CachedImage</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Left">SkiaControl.Left</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Top">SkiaControl.Top</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ControlStyleProperty">SkiaControl.ControlStyleProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ControlStyle">SkiaControl.ControlStyle</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClippedEffectsWithProperty">SkiaControl.ClippedEffectsWithProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClippedEffectsWith">SkiaControl.ClippedEffectsWith</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipWithProperty">SkiaControl.ClipWithProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipWith">SkiaControl.ClipWith</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ChildrenProperty">SkiaControl.ChildrenProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Children">SkiaControl.Children</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DefaultBlendMode">SkiaControl.DefaultBlendMode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsVisibleInViewTree">SkiaControl.IsVisibleInViewTree()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOnCanvasInPoints">SkiaControl.GetPositionOnCanvasInPoints()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetFuturePositionOnCanvasInPoints_System_Boolean_">SkiaControl.GetFuturePositionOnCanvasInPoints(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOnCanvas">SkiaControl.GetPositionOnCanvas()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetFuturePositionOnCanvas">SkiaControl.GetFuturePositionOnCanvas()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetSelfDrawingPosition">SkiaControl.GetSelfDrawingPosition()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BuildSelfDrawingPosition_SkiaSharp_SKPoint_DrawnUi_Draw_SkiaControl_System_Boolean_">SkiaControl.BuildSelfDrawingPosition(SKPoint, SkiaControl, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BuildDrawingOffsetRecursive_SkiaSharp_SKPoint_DrawnUi_Draw_SkiaControl_System_Boolean_System_Boolean_">SkiaControl.BuildDrawingOffsetRecursive(SKPoint, SkiaControl, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BuildDrawnOffsetRecursive_SkiaSharp_SKPoint_DrawnUi_Draw_SkiaControl_System_Boolean_System_Boolean_">SkiaControl.BuildDrawnOffsetRecursive(SKPoint, SkiaControl, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CanDraw">SkiaControl.CanDraw</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkipRendering">SkiaControl.SkipRendering</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DefaultContentCreated">SkiaControl.DefaultContentCreated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateDefaultContent">SkiaControl.CreateDefaultContent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetDefaultMinimumContentSize_System_Double_System_Double_">SkiaControl.SetDefaultMinimumContentSize(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetDefaultContentSize_System_Double_System_Double_">SkiaControl.SetDefaultContentSize(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GenerateParentChain">SkiaControl.GenerateParentChain()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetVisualTransform_DrawnUi_Infrastructure_VisualTransform_">SkiaControl.SetVisualTransform(VisualTransform)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CommitInvalidations">SkiaControl.CommitInvalidations()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SuperViewChanged">SkiaControl.SuperViewChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PostponeInvalidation_System_String_System_Action_">SkiaControl.PostponeInvalidation(string, Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetRenderingScaleFor_System_Single_System_Single_">SkiaControl.GetRenderingScaleFor(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetRenderingScaleFor_System_Single_">SkiaControl.GetRenderingScaleFor(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimateAsync_System_Action_System_Double__System_Action_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.AnimateAsync(Action&lt;double&gt;, Action, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FadeToAsync_System_Double_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.FadeToAsync(double, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ScaleToAsync_System_Double_System_Double_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.ScaleToAsync(double, double, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateToAsync_System_Double_System_Double_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.TranslateToAsync(double, double, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RotateToAsync_System_Double_System_UInt32_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.RotateToAsync(double, uint, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PrintDebug_System_String_">SkiaControl.PrintDebug(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugRenderingProperty">SkiaControl.DebugRenderingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugRendering">SkiaControl.DebugRendering</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimateRangeAsync_System_Action_System_Double__System_Double_System_Double_System_Double_Microsoft_Maui_Easing_System_Threading_CancellationToken_System_Boolean_">SkiaControl.AnimateRangeAsync(Action&lt;double&gt;, double, double, double, Easing, CancellationToken, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NotValidPoint">SkiaControl.NotValidPoint()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PointIsValid_SkiaSharp_SKPoint_">SkiaControl.PointIsValid(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SizeRequest">SkiaControl.SizeRequest</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WidthRequestWithMargins">SkiaControl.WidthRequestWithMargins</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptWidthConstraintToRequest_System_Single_Microsoft_Maui_Thickness_System_Double_">SkiaControl.AdaptWidthConstraintToRequest(float, Thickness, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptHeightContraintToRequest_System_Single_Microsoft_Maui_Thickness_System_Double_">SkiaControl.AdaptHeightContraintToRequest(float, Thickness, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetMeasuringConstraints_DrawnUi_Draw_MeasureRequest_">SkiaControl.GetMeasuringConstraints(MeasureRequest)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedFillHorizontally">SkiaControl.NeedFillHorizontally</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedFillVertically">SkiaControl.NeedFillVertically</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptConstraintToContentRequest_System_Single_System_Double_System_Double_System_Boolean_System_Double_System_Double_System_Single_System_Boolean_">SkiaControl.AdaptConstraintToContentRequest(float, double, double, bool, double, double, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptWidthConstraintToContentRequest_DrawnUi_Infrastructure_MeasuringConstraints_System_Single_System_Boolean_">SkiaControl.AdaptWidthConstraintToContentRequest(MeasuringConstraints, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptHeightConstraintToContentRequest_DrawnUi_Infrastructure_MeasuringConstraints_System_Single_System_Boolean_">SkiaControl.AdaptHeightConstraintToContentRequest(MeasuringConstraints, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptWidthConstraintToContentRequest_System_Single_DrawnUi_Draw_ScaledSize_System_Double_">SkiaControl.AdaptWidthConstraintToContentRequest(float, ScaledSize, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptHeightConstraintToContentRequest_System_Single_DrawnUi_Draw_ScaledSize_System_Double_">SkiaControl.AdaptHeightConstraintToContentRequest(float, ScaledSize, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptToContraints_SkiaSharp_SKRect_System_Double_System_Double_System_Double_System_Double_">SkiaControl.AdaptToContraints(SKRect, double, double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptSizeRequestToContent_System_Double_System_Double_">SkiaControl.AdaptSizeRequestToContent(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetTopParentView">SkiaControl.GetTopParentView()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetParentElement_DrawnUi_Draw_IDrawnBase_">SkiaControl.GetParentElement(IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureIsInside_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.GestureIsInside(TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureStartedInside_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.GestureStartedInside(TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPointInside_System_Single_System_Single_System_Single_">SkiaControl.IsPointInside(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPointInside_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.IsPointInside(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPixelInside_SkiaSharp_SKRect_System_Single_System_Single_">SkiaControl.IsPixelInside(SKRect, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPixelInside_System_Single_System_Single_">SkiaControl.IsPixelInside(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckGestureIsInsideChild_DrawnUi_Draw_SkiaControl_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.CheckGestureIsInsideChild(SkiaControl, TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckGestureIsForChild_DrawnUi_Draw_SkiaControl_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.CheckGestureIsForChild(SkiaControl, TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockIterateListeners">SkiaControl.LockIterateListeners</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockChildrenGesturesProperty">SkiaControl.LockChildrenGesturesProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockChildrenGestures">SkiaControl.LockChildrenGestures</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckChildrenGesturesLocked_AppoMobi_Maui_Gestures_TouchActionResult_">SkiaControl.CheckChildrenGesturesLocked(TouchActionResult)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnSkiaGestureEvent_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaControl.OnSkiaGestureEvent(SkiaGesturesParameters, GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGestureForChild_DrawnUi_Draw_SkiaControl_DrawnUi_Draw_SkiaGesturesParameters_">SkiaControl.IsGestureForChild(SkiaControl, SkiaGesturesParameters)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CommandChildTappedProperty">SkiaControl.CommandChildTappedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CommandChildTapped">SkiaControl.CommandChildTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Tapped">SkiaControl.Tapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TouchEffectColorProperty">SkiaControl.TouchEffectColorProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TouchEffectColor">SkiaControl.TouchEffectColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimationTappedProperty">SkiaControl.AnimationTappedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimationTapped">SkiaControl.AnimationTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransformViewProperty">SkiaControl.TransformViewProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransformView">SkiaControl.TransformView</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SendTapped_System_Object_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_System_Boolean_">SkiaControl.SendTapped(object, SkiaGesturesParameters, GestureEventProcessingInfo, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ChildTapped">SkiaControl.ChildTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransformPointToLocalSpace_SkiaSharp_SKPoint_">SkiaControl.TransformPointToLocalSpace(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGestureInside_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaControl.IsGestureInside(GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnGestures">SkiaControl.OnGestures</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ProcessGestures_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaControl.ProcessGestures(SkiaGesturesParameters, GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BlockGesturesBelowProperty">SkiaControl.BlockGesturesBelowProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BlockGesturesBelow">SkiaControl.BlockGesturesBelow</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateLocks">SkiaControl.UpdateLocks</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnlockUpdate">SkiaControl.UnlockUpdate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockUpdate_System_Boolean_">SkiaControl.LockUpdate(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NativeParent">SkiaControl.NativeParent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ParentProperty">SkiaControl.ParentProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Parent">SkiaControl.Parent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentVerticalProperty">SkiaControl.AlignContentVerticalProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentVertical">SkiaControl.AlignContentVertical</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentHorizontalProperty">SkiaControl.AlignContentHorizontalProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentHorizontal">SkiaControl.AlignContentHorizontal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalOptionsProperty">SkiaControl.VerticalOptionsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalOptions">SkiaControl.VerticalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalOptionsProperty">SkiaControl.HorizontalOptionsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalOptions">SkiaControl.HorizontalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnParentVisibilityChanged_System_Boolean_">SkiaControl.OnParentVisibilityChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisibilityChanged">SkiaControl.VisibilityChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SendVisibilityChanged">SkiaControl.SendVisibilityChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnVisibilityChanged_System_Boolean_">SkiaControl.OnVisibilityChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_lockPausingAnimators">SkiaControl.lockPausingAnimators</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PauseAllAnimators">SkiaControl.PauseAllAnimators()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ResumePausedAnimators">SkiaControl.ResumePausedAnimators()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGhostProperty">SkiaControl.IsGhostProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGhost">SkiaControl.IsGhost</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IgnoreChildrenInvalidationsProperty">SkiaControl.IgnoreChildrenInvalidationsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IgnoreChildrenInvalidations">SkiaControl.IgnoreChildrenInvalidations</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillGradientProperty">SkiaControl.FillGradientProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillGradient">SkiaControl.FillGradient</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HasFillGradient">SkiaControl.HasFillGradient</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetSizeRequest_System_Single_System_Single_System_Boolean_">SkiaControl.GetSizeRequest(float, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SmartMax_System_Single_System_Single_">SkiaControl.SmartMax(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SmartMin_System_Single_System_Single_">SkiaControl.SmartMin(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportHeightLimitProperty">SkiaControl.ViewportHeightLimitProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportHeightLimit">SkiaControl.ViewportHeightLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportWidthLimitProperty">SkiaControl.ViewportWidthLimitProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportWidthLimit">SkiaControl.ViewportWidthLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Height">SkiaControl.Height</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Width">SkiaControl.Width</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Scale">SkiaControl.Scale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TagProperty">SkiaControl.TagProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Tag">SkiaControl.Tag</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockRatioProperty">SkiaControl.LockRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockRatio">SkiaControl.LockRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HeightRequestRatioProperty">SkiaControl.HeightRequestRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HeightRequestRatio">SkiaControl.HeightRequestRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WidthRequestRatioProperty">SkiaControl.WidthRequestRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WidthRequestRatio">SkiaControl.WidthRequestRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalFillRatioProperty">SkiaControl.HorizontalFillRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalFillRatio">SkiaControl.HorizontalFillRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalFillRatioProperty">SkiaControl.VerticalFillRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalFillRatio">SkiaControl.VerticalFillRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalPositionOffsetRatioProperty">SkiaControl.HorizontalPositionOffsetRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalPositionOffsetRatio">SkiaControl.HorizontalPositionOffsetRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalPositionOffsetRatioProperty">SkiaControl.VerticalPositionOffsetRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalPositionOffsetRatio">SkiaControl.VerticalPositionOffsetRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillBlendModeProperty">SkiaControl.FillBlendModeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillBlendMode">SkiaControl.FillBlendMode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewXProperty">SkiaControl.SkewXProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewX">SkiaControl.SkewX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewYProperty">SkiaControl.SkewYProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewY">SkiaControl.SkewY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslationZProperty">SkiaControl.TranslationZProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslationZ">SkiaControl.TranslationZ</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RotationZProperty">SkiaControl.RotationZProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RotationZ">SkiaControl.RotationZ</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective1Property">SkiaControl.Perspective1Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective1">SkiaControl.Perspective1</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective2Property">SkiaControl.Perspective2Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective2">SkiaControl.Perspective2</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ParentChanged">SkiaControl.ParentChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FastMeasurementProperty">SkiaControl.FastMeasurementProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FastMeasurement">SkiaControl.FastMeasurement</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdjustClippingProperty">SkiaControl.AdjustClippingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdjustClipping">SkiaControl.AdjustClipping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaddingProperty">SkiaControl.PaddingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Padding">SkiaControl.Padding</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MarginProperty">SkiaControl.MarginProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Margin">SkiaControl.Margin</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginTopProperty">SkiaControl.AddMarginTopProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginTop">SkiaControl.AddMarginTop</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginBottomProperty">SkiaControl.AddMarginBottomProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginBottom">SkiaControl.AddMarginBottom</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginLeftProperty">SkiaControl.AddMarginLeftProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginLeft">SkiaControl.AddMarginLeft</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginRightProperty">SkiaControl.AddMarginRightProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginRight">SkiaControl.AddMarginRight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Margins">SkiaControl.Margins</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SpacingProperty">SkiaControl.SpacingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Spacing">SkiaControl.Spacing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationYProperty">SkiaControl.AddTranslationYProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationY">SkiaControl.AddTranslationY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationXProperty">SkiaControl.AddTranslationXProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationX">SkiaControl.AddTranslationX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExpandDirtyRegionProperty">SkiaControl.ExpandDirtyRegionProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExpandDirtyRegion">SkiaControl.ExpandDirtyRegion</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockFocusProperty">SkiaControl.LockFocusProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockFocus">SkiaControl.LockFocus</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsClippedToBoundsProperty">SkiaControl.IsClippedToBoundsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsClippedToBounds">SkiaControl.IsClippedToBounds</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipEffectsProperty">SkiaControl.ClipEffectsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipEffects">SkiaControl.ClipEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BindableTriggerProperty">SkiaControl.BindableTriggerProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnTriggerChanged">SkiaControl.OnTriggerChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BindableTrigger">SkiaControl.BindableTrigger</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value1Property">SkiaControl.Value1Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value1">SkiaControl.Value1</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value2Property">SkiaControl.Value2Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value2">SkiaControl.Value2</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value3Property">SkiaControl.Value3Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value3">SkiaControl.Value3</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value4Property">SkiaControl.Value4Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value4">SkiaControl.Value4</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderingScaleProperty">SkiaControl.RenderingScaleProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderingScale">SkiaControl.RenderingScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderedAtDestination">SkiaControl.RenderedAtDestination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnScaleChanged">SkiaControl.OnScaleChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetPropertyValue_Microsoft_Maui_Controls_BindableProperty_System_Object_">SkiaControl.SetPropertyValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Uid">SkiaControl.Uid</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DegreesToRadians_System_Single_">SkiaControl.DegreesToRadians(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RadiansToDegrees_System_Single_">SkiaControl.RadiansToDegrees(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DegreesToRadians_System_Double_">SkiaControl.DegreesToRadians(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RadiansToDegrees_System_Double_">SkiaControl.RadiansToDegrees(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LinearGradientAngleToPoints_System_Double_">SkiaControl.LinearGradientAngleToPoints(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposalDelay">SkiaControl.DisposalDelay</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsAlive">SkiaControl.IsAlive</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposeObject">SkiaControl.DisposeObject()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposeObject_System_IDisposable_">SkiaControl.DisposeObject(IDisposable)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnSizeChanged">SkiaControl.OnSizeChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Clipping">SkiaControl.Clipping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Hero">SkiaControl.Hero</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContextIndex">SkiaControl.ContextIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsRootView">SkiaControl.IsRootView()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DefineAvailableSize_SkiaSharp_SKRect_System_Single_System_Single_System_Single_System_Boolean_">SkiaControl.DefineAvailableSize(SKRect, float, float, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsLayoutDirty">SkiaControl.IsLayoutDirty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapToPixel_System_Double_System_Double_">SkiaControl.SnapToPixel(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapSizeToPixel_SkiaSharp_SKSize_System_Double_">SkiaControl.SnapSizeToPixel(SKSize, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapPointToPixel_SkiaSharp_SKPoint_System_Double_">SkiaControl.SnapPointToPixel(SKPoint, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapRectCenter_SkiaSharp_SKRect_System_Single_">SkiaControl.SnapRectCenter(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapRectToPixels_SkiaSharp_SKRect_System_Single_">SkiaControl.SnapRectToPixels(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RoundCenterAlignment">SkiaControl.RoundCenterAlignment</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateLayout_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.CalculateLayout(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContentSize">SkiaControl.ContentSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WasMeasured">SkiaControl.WasMeasured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnDrawingSizeChanged">SkiaControl.OnDrawingSizeChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptCachedLayout_SkiaSharp_SKRect_System_Single_">SkiaControl.AdaptCachedLayout(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__lastMeasuredForWidth">SkiaControl._lastMeasuredForWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__lastMeasuredForHeight">SkiaControl._lastMeasuredForHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawingRect">SkiaControl.DrawingRect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DirtyRegion">SkiaControl.DirtyRegion</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HitIsInside_System_Single_System_Single_">SkiaControl.HitIsInside(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HitBoxAuto">SkiaControl.HitBoxAuto</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGestureForChild_DrawnUi_Draw_ISkiaGestureListener_SkiaSharp_SKPoint_">SkiaControl.IsGestureForChild(ISkiaGestureListener, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGestureForChild_DrawnUi_Draw_ISkiaGestureListener_System_Single_System_Single_">SkiaControl.IsGestureForChild(ISkiaGestureListener, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyTransforms_SkiaSharp_SKRect_">SkiaControl.ApplyTransforms(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderTransformMatrix">SkiaControl.RenderTransformMatrix</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InverseRenderTransformMatrix">SkiaControl.InverseRenderTransformMatrix</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyTransformationMatrix_DrawnUi_Draw_SkiaDrawingContext_">SkiaControl.ApplyTransformationMatrix(SkiaDrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateTransformationMatrix_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_">SkiaControl.CreateTransformationMatrix(SkiaDrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyTransforms_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_">SkiaControl.ApplyTransforms(SkiaDrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateInputDirectOffsetToPoints_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.TranslateInputDirectOffsetToPoints(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateInputOffsetToPixels_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.TranslateInputOffsetToPixels(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateInputCoords_SkiaSharp_SKPoint_System_Boolean_">SkiaControl.TranslateInputCoords(SKPoint, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculatePositionOffset_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.CalculatePositionOffset(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateFuturePositionOffset_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.CalculateFuturePositionOffset(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ArrangedDestination">SkiaControl.ArrangedDestination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LayoutIsReady">SkiaControl.LayoutIsReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Disposing">SkiaControl.Disposing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsLayoutReady">SkiaControl.IsLayoutReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LayoutReady">SkiaControl.LayoutReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckIsGhost">SkiaControl.CheckIsGhost()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Layout_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_">SkiaControl.Layout(DrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Layout_DrawnUi_Draw_DrawingContext_System_Single_System_Single_System_Single_System_Single_">SkiaControl.Layout(DrawingContext, float, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Layout_DrawnUi_Draw_DrawingContext_System_Int32_System_Int32_System_Int32_System_Int32_">SkiaControl.Layout(DrawingContext, int, int, int, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Arrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.Arrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PostArrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.PostArrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureChild_DrawnUi_Draw_SkiaControl_System_Double_System_Double_System_Single_">SkiaControl.MeasureChild(SkiaControl, double, double, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureContent_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__SkiaSharp_SKRect_System_Single_">SkiaControl.MeasureContent(IEnumerable&lt;SkiaControl&gt;, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetInheritedBindingContext_System_Object_">SkiaControl.SetInheritedBindingContext(object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyingBindingContext">SkiaControl.ApplyingBindingContext</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BindingContextWasSet">SkiaControl.BindingContextWasSet</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnBindingContextChanged">SkiaControl.OnBindingContextChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureInternal_DrawnUi_Draw_MeasureRequest_">SkiaControl.MeasureInternal(MeasureRequest)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Measure_System_Single_System_Single_System_Single_">SkiaControl.Measure(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InitializeMeasuring">SkiaControl.InitializeMeasuring()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InitializeDefaultContent_System_Boolean_">SkiaControl.InitializeDefaultContent(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetMeasuredAsEmpty_System_Single_">SkiaControl.SetMeasuredAsEmpty(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetContentSizeForAutosizeInPixels">SkiaControl.GetContentSizeForAutosizeInPixels()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetMeasuredAdaptToContentSize_DrawnUi_Infrastructure_MeasuringConstraints_System_Single_">SkiaControl.SetMeasuredAdaptToContentSize(MeasuringConstraints, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetSizeInPoints_SkiaSharp_SKSize_System_Single_">SkiaControl.GetSizeInPoints(SKSize, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateMeasureRequest_System_Single_System_Single_System_Single_">SkiaControl.CreateMeasureRequest(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetMeasuringRectForChildren_System_Single_System_Single_System_Double_">SkiaControl.GetMeasuringRectForChildren(float, float, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureAbsoluteBase_SkiaSharp_SKRect_System_Single_">SkiaControl.MeasureAbsoluteBase(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContractPixelsRect_SkiaSharp_SKRect_System_Single_">SkiaControl.ContractPixelsRect(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContractPixelsRect_SkiaSharp_SKRect_System_Single_Microsoft_Maui_Thickness_">SkiaControl.ContractPixelsRect(SKRect, float, Thickness)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExpandPixelsRect_SkiaSharp_SKRect_System_Single_Microsoft_Maui_Thickness_">SkiaControl.ExpandPixelsRect(SKRect, float, Thickness)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDrawingRectForChildren_SkiaSharp_SKRect_System_Double_">SkiaControl.GetDrawingRectForChildren(SKRect, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDrawingRectWithMargins_SkiaSharp_SKRect_System_Double_">SkiaControl.GetDrawingRectWithMargins(SKRect, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_lockMeasured">SkiaControl.lockMeasured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsMeasuring">SkiaControl.IsMeasuring</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockMeasure">SkiaControl.LockMeasure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SendOnMeasured">SkiaControl.SendOnMeasured()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Measured">SkiaControl.Measured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasuredSize">SkiaControl.MeasuredSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedAutoSize">SkiaControl.NeedAutoSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedAutoHeight">SkiaControl.NeedAutoHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedAutoWidth">SkiaControl.NeedAutoWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsDisposed">SkiaControl.IsDisposed</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsDisposing">SkiaControl.IsDisposing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedDispose">SkiaControl.NeedDispose</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindView__1_System_String_">SkiaControl.FindView&lt;TChild&gt;(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindView__1">SkiaControl.FindView&lt;TChild&gt;()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindViewByTag_System_String_">SkiaControl.FindViewByTag(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecuteUponDisposal">SkiaControl.ExecuteUponDisposal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecuteAfterCreated">SkiaControl.ExecuteAfterCreated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecuteOnPaint">SkiaControl.ExecuteOnPaint</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ThrowIfDisposed">SkiaControl.ThrowIfDisposed()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Dispose_System_Boolean_">SkiaControl.Dispose(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Dispose">SkiaControl.Dispose()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintErase">SkiaControl.PaintErase</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetNanoseconds">SkiaControl.GetNanoseconds()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnBeforeMeasure">SkiaControl.OnBeforeMeasure()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OptionalOnBeforeDrawing">SkiaControl.OptionalOnBeforeDrawing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsOverlay">SkiaControl.IsOverlay</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PostAnimators">SkiaControl.PostAnimators</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateWhenReturnedFromBackgroundProperty">SkiaControl.UpdateWhenReturnedFromBackgroundProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateWhenReturnedFromBackground">SkiaControl.UpdateWhenReturnedFromBackground</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnSuperviewShouldRenderChanged_System_Boolean_">SkiaControl.OnSuperviewShouldRenderChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureSelf_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.MeasureSelf(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsRendering">SkiaControl.IsRendering</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NodeAttached">SkiaControl.NodeAttached</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindRenderedNode_DrawnUi_Draw_SkiaControl_">SkiaControl.FindRenderedNode(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderedNode_SkiaSharp_SKRect_System_Single_">SkiaControl.CreateRenderedNode(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Render_DrawnUi_Draw_DrawingContext_">SkiaControl.Render(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Rendered">SkiaControl.Rendered</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockDraw">SkiaControl.LockDraw</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockRenderObject">SkiaControl.LockRenderObject</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddPaintArguments_DrawnUi_Draw_DrawingContext_">SkiaControl.AddPaintArguments(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnBeforeDrawing_DrawnUi_Draw_DrawingContext_">SkiaControl.OnBeforeDrawing(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnAfterDrawing_DrawnUi_Draw_DrawingContext_">SkiaControl.OnAfterDrawing(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_X">SkiaControl.X</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Y">SkiaControl.Y</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FinalizeDrawingWithRenderObject_DrawnUi_Draw_DrawingContext_">SkiaControl.FinalizeDrawingWithRenderObject(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOffsetInPoints">SkiaControl.GetPositionOffsetInPoints()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOffsetInPixels_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.GetPositionOffsetInPixels(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetFuturePositionOffsetInPixels_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.GetFuturePositionOffsetInPixels(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOffsetInsideControlInPoints_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.GetOffsetInsideControlInPoints(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOffsetInsideControlInPixels_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.GetOffsetInsideControlInPixels(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastDrawnAt">SkiaControl.LastDrawnAt</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecutePostAnimators_DrawnUi_Draw_DrawingContext_">SkiaControl.ExecutePostAnimators(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Repaint">SkiaControl.Repaint()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__paintWithEffects">SkiaControl._paintWithEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__paintWithOpacity">SkiaControl._paintWithOpacity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CustomizeLayerPaint">SkiaControl.CustomizeLayerPaint</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Helper3d">SkiaControl.Helper3d</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawWithClipAndTransforms_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_System_Boolean_System_Boolean_System_Action_DrawnUi_Draw_DrawingContext__">SkiaControl.DrawWithClipAndTransforms(DrawingContext, SKRect, bool, bool, Action&lt;DrawingContext&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsSimpleRectangle_SkiaSharp_SKPath_">SkiaControl.IsSimpleRectangle(SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_">SkiaControl.ClipSmart(SKCanvas, SKPath, SKClipOperation)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ShouldClipAntialiased">SkiaControl.ShouldClipAntialiased</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedMeasure">SkiaControl.NeedMeasure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SafeAction_System_Action_">SkiaControl.SafeAction(Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedRemeasuring">SkiaControl.NeedRemeasuring</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintWithShadows_DrawnUi_Draw_DrawingContext_System_Action_">SkiaControl.PaintWithShadows(DrawingContext, Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintWithEffects_DrawnUi_Draw_DrawingContext_">SkiaControl.PaintWithEffects(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WasFirstTimeDrawn">SkiaControl.WasFirstTimeDrawn</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_">SkiaControl.CreateClip(object, bool, SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugRenderingColor">SkiaControl.DebugRenderingColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseTranslationY">SkiaControl.UseTranslationY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseTranslationX">SkiaControl.UseTranslationX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LinkTransforms">SkiaControl.LinkTransforms</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderViewsList_DrawnUi_Draw_DrawingContext_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__">SkiaControl.RenderViewsList(DrawingContext, IEnumerable&lt;SkiaControl&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__measuredStamp">SkiaControl._measuredStamp</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__builtRenderTreeStamp">SkiaControl._builtRenderTreeStamp</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderTree">SkiaControl.RenderTree</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetRenderingTree_System_Collections_Generic_List_DrawnUi_Draw_SkiaControlWithRect__">SkiaControl.SetRenderingTree(List&lt;SkiaControlWithRect&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Invalidated">SkiaControl.Invalidated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedUpdate">SkiaControl.NeedUpdate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Superview">SkiaControl.Superview</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DelegateGetOnScreenVisibleArea">SkiaControl.DelegateGetOnScreenVisibleArea</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsParentIndependent">SkiaControl.IsParentIndependent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WillNotUpdateParent">SkiaControl.WillNotUpdateParent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateInternal">SkiaControl.UpdateInternal()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Update">SkiaControl.Update()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Updated">SkiaControl.Updated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_StreamFromString_System_String_">SkiaControl.StreamFromString(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DeviceUnitsToPixels_System_Double_">SkiaControl.DeviceUnitsToPixels(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PixelsToDeviceUnits_System_Double_">SkiaControl.PixelsToDeviceUnits(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintSystem">SkiaControl.PaintSystem</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetupBackgroundPaint_SkiaSharp_SKPaint_SkiaSharp_SKRect_">SkiaControl.SetupBackgroundPaint(SKPaint, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintTintBackground_SkiaSharp_SKCanvas_SkiaSharp_SKRect_">SkiaControl.PaintTintBackground(SKCanvas, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CombineClipping_SkiaSharp_SKPath_SkiaSharp_SKPath_">SkiaControl.CombineClipping(SKPath, SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ActionWithClipping_SkiaSharp_SKRect_SkiaSharp_SKCanvas_System_Action_">SkiaControl.ActionWithClipping(SKRect, SKCanvas, Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateMargins">SkiaControl.CalculateMargins()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetAllMarginsInPixels_System_Single_">SkiaControl.GetAllMarginsInPixels(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetMarginsInPixels_System_Single_">SkiaControl.GetMarginsInPixels(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateMeasureInternal">SkiaControl.InvalidateMeasureInternal()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateSizeRequest">SkiaControl.CalculateSizeRequest()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateChildrenTree_DrawnUi_Draw_SkiaControl_">SkiaControl.InvalidateChildrenTree(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateChildrenTree">SkiaControl.InvalidateChildrenTree()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OutputDebug">SkiaControl.OutputDebug</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WillInvalidateMeasure">SkiaControl.WillInvalidateMeasure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedInvalidateMeasure_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedInvalidateMeasure(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedDraw_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedDraw(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedRepaint_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedRepaint(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateMeasure">SkiaControl.InvalidateMeasure()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedInvalidateViewport_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedInvalidateViewport(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateContentFromTemplate">SkiaControl.CreateContentFromTemplate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateChanged_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.ItemTemplateChanged(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetAnimatorsManager">SkiaControl.GetAnimatorsManager()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_">SkiaControl.RegisterAnimator(ISkiaAnimator)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterAnimator_System_Guid_">SkiaControl.UnregisterAnimator(Guid)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterAllAnimatorsByType_System_Type_">SkiaControl.UnregisterAllAnimatorsByType(Type)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PlayRippleAnimation_Microsoft_Maui_Graphics_Color_System_Double_System_Double_System_Boolean_">SkiaControl.PlayRippleAnimation(Color, double, double, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PlayShimmerAnimation_Microsoft_Maui_Graphics_Color_System_Single_System_Single_System_Int32_System_Boolean_">SkiaControl.PlayShimmerAnimation(Color, float, float, int, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateGradient_SkiaSharp_SKRect_DrawnUi_Draw_SkiaGradient_">SkiaControl.CreateGradient(SKRect, SkiaGradient)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastGradient">SkiaControl.LastGradient</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastShadow">SkiaControl.LastShadow</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetupShadow_SkiaSharp_SKPaint_DrawnUi_Draw_SkiaShadow_System_Single_">SkiaControl.SetupShadow(SKPaint, SkiaShadow, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOrderedSubviews_System_Boolean_">SkiaControl.GetOrderedSubviews(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetUnorderedSubviews_System_Boolean_">SkiaControl.GetUnorderedSubviews(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Views">SkiaControl.Views</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposeChildren">SkiaControl.DisposeChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnWillDisposeWithChildren">SkiaControl.OnWillDisposeWithChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearChildren">SkiaControl.ClearChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildRemoved_Microsoft_Maui_Controls_Element_System_Int32_">SkiaControl.OnChildRemoved(Element, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildAdded_Microsoft_Maui_Controls_Element_">SkiaControl.OnChildAdded(Element)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildAdded_DrawnUi_Draw_SkiaControl_">SkiaControl.OnChildAdded(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildRemoved_DrawnUi_Draw_SkiaControl_">SkiaControl.OnChildRemoved(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnViewAttached">SkiaControl.OnViewAttached()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnViewDetached">SkiaControl.OnViewDetached()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureListenerRegistrationTime">SkiaControl.GestureListenerRegistrationTime</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">SkiaControl.RegisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">SkiaControl.UnregisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureListeners">SkiaControl.GestureListeners</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnParentChanged_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_IDrawnBase_">SkiaControl.OnParentChanged(IDrawnBase, IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearParent">SkiaControl.ClearParent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_StopAnimations">SkiaControl.StopAnimations()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetParent_DrawnUi_Draw_IDrawnBase_">SkiaControl.SetParent(IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RegisterGestureListenersTree_DrawnUi_Draw_SkiaControl_">SkiaControl.RegisterGestureListenersTree(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterGestureListenersTree_DrawnUi_Draw_SkiaControl_">SkiaControl.UnregisterGestureListenersTree(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateProperty">SkiaControl.ItemTemplateProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplate">SkiaControl.ItemTemplate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateTypeProperty">SkiaControl.ItemTemplateTypeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateType">SkiaControl.ItemTemplateType</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddOrRemoveView_DrawnUi_Draw_SkiaControl_System_Boolean_">SkiaControl.AddOrRemoveView(SkiaControl, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetChildrenAsParameters_DrawnUi_Draw_SkiaControl___">SkiaControl.SetChildrenAsParameters(params SkiaControl[])</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HasItemTemplate">SkiaControl.HasItemTemplate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GesturesEffect">SkiaControl.GesturesEffect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RescaleAspect_System_Single_System_Single_SkiaSharp_SKRect_DrawnUi_Draw_TransformAspect_">SkiaControl.RescaleAspect(float, float, SKRect, TransformAspect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Random">SkiaControl.Random</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastArrangedInside">SkiaControl.LastArrangedInside</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__arrangedViewportHeightLimit">SkiaControl._arrangedViewportHeightLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__arrangedViewportWidthLimit">SkiaControl._arrangedViewportWidthLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__lastMeasuredForScale">SkiaControl._lastMeasuredForScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetRandomColor">SkiaControl.GetRandomColor()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareFloats_System_Single_System_Single_System_Single_">SkiaControl.CompareFloats(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareDoubles_System_Double_System_Double_System_Double_">SkiaControl.CompareDoubles(double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareRects_SkiaSharp_SKRect_SkiaSharp_SKRect_System_Single_">SkiaControl.CompareRects(SKRect, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareRectsSize_SkiaSharp_SKRect_SkiaSharp_SKRect_System_Single_">SkiaControl.CompareRectsSize(SKRect, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareSize_SkiaSharp_SKSize_SkiaSharp_SKSize_System_Single_">SkiaControl.CompareSize(SKSize, SKSize, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareVectors_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_">SkiaControl.CompareVectors(Vector2, Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreEqual_System_Double_System_Double_System_Double_">SkiaControl.AreEqual(double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreEqual_System_Single_System_Single_System_Single_">SkiaControl.AreEqual(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreVectorsEqual_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_">SkiaControl.AreVectorsEqual(Vector2, Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDirectionType_System_Numerics_Vector2_DrawnUi_Draw_DirectionType_System_Single_">SkiaControl.GetDirectionType(Vector2, DirectionType, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDirectionType_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_">SkiaControl.GetDirectionType(Vector2, Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreClose_System_Single_System_Single_">SkiaControl.AreClose(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreClose_System_Double_System_Double_">SkiaControl.AreClose(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsOne_System_Double_">SkiaControl.IsOne(double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.navigationproperty">VisualElement.NavigationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.styleproperty">VisualElement.StyleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparentproperty">VisualElement.InputTransparentProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledproperty">VisualElement.IsEnabledProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.xproperty">VisualElement.XProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.yproperty">VisualElement.YProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorxproperty">VisualElement.AnchorXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchoryproperty">VisualElement.AnchorYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationxproperty">VisualElement.TranslationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationyproperty">VisualElement.TranslationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthproperty">VisualElement.WidthProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightproperty">VisualElement.HeightProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationproperty">VisualElement.RotationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationxproperty">VisualElement.RotationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationyproperty">VisualElement.RotationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleproperty">VisualElement.ScaleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalexproperty">VisualElement.ScaleXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleyproperty">VisualElement.ScaleYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clipproperty">VisualElement.ClipProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visualproperty">VisualElement.VisualProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisibleproperty">VisualElement.IsVisibleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacityproperty">VisualElement.OpacityProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolorproperty">VisualElement.BackgroundColorProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundproperty">VisualElement.BackgroundProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviorsproperty">VisualElement.BehaviorsProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggersproperty">VisualElement.TriggersProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequestproperty">VisualElement.WidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequestproperty">VisualElement.HeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequestproperty">VisualElement.MinimumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequestproperty">VisualElement.MinimumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequestproperty">VisualElement.MaximumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequestproperty">VisualElement.MaximumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocusedproperty">VisualElement.IsFocusedProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirectionproperty">VisualElement.FlowDirectionProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.windowproperty">VisualElement.WindowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadowproperty">VisualElement.ShadowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindexproperty">VisualElement.ZIndexProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin">VisualElement.BatchBegin()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit">VisualElement.BatchCommit()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus">VisualElement.Focus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)">VisualElement.Measure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)">VisualElement.Measure(double, double, MeasureFlags)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus">VisualElement.Unfocus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered">VisualElement.OnChildrenReordered()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure">VisualElement.OnMeasure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onsizeallocated">VisualElement.OnSizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated">VisualElement.SizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.changevisualstate">VisualElement.ChangeVisualState()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty">VisualElement.RefreshIsEnabledProperty()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange">VisualElement.Arrange(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrangeoverride">VisualElement.ArrangeOverride(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout">VisualElement.Layout(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride">VisualElement.InvalidateMeasureOverride()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureoverride">VisualElement.MeasureOverride(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor">VisualElement.MapBackgroundColor(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource">VisualElement.MapBackgroundImageSource(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visual">VisualElement.Visual</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirection">VisualElement.FlowDirection</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.window">VisualElement.Window</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorx">VisualElement.AnchorX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchory">VisualElement.AnchorY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolor">VisualElement.BackgroundColor</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.background">VisualElement.Background</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviors">VisualElement.Behaviors</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.bounds">VisualElement.Bounds</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequest">VisualElement.HeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparent">VisualElement.InputTransparent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabled">VisualElement.IsEnabled</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledcore">VisualElement.IsEnabledCore</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocused">VisualElement.IsFocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisible">VisualElement.IsVisible</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequest">VisualElement.MinimumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequest">VisualElement.MinimumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequest">VisualElement.MaximumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequest">VisualElement.MaximumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacity">VisualElement.Opacity</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotation">VisualElement.Rotation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationx">VisualElement.RotationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationy">VisualElement.RotationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalex">VisualElement.ScaleX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaley">VisualElement.ScaleY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationx">VisualElement.TranslationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationy">VisualElement.TranslationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggers">VisualElement.Triggers</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequest">VisualElement.WidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clip">VisualElement.Clip</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.resources">VisualElement.Resources</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.frame">VisualElement.Frame</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.handler">VisualElement.Handler</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadow">VisualElement.Shadow</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindex">VisualElement.ZIndex</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.desiredsize">VisualElement.DesiredSize</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isloaded">VisualElement.IsLoaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.childrenreordered">VisualElement.ChildrenReordered</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focused">VisualElement.Focused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureinvalidated">VisualElement.MeasureInvalidated</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizechanged">VisualElement.SizeChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocused">VisualElement.Unfocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.loaded">VisualElement.Loaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unloaded">VisualElement.Unloaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.onparentset">NavigableElement.OnParentSet()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.navigation">NavigableElement.Navigation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.style">StyleableElement.Style</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.styleclass">StyleableElement.StyleClass</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.class">StyleableElement.class</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty">Element.AutomationIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty">Element.ClassIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild">Element.InsertLogicalChild(int, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild">Element.AddLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild">Element.RemoveLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren">Element.ClearLogicalChildren()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname">Element.FindByName(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource">Element.RemoveDynamicResource(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource">Element.SetDynamicResource(BindableProperty, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging">Element.OnParentChanging(ParentChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged">Element.OnParentChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging">Element.OnHandlerChanging(HandlerChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged">Element.OnHandlerChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree">Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren">Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid">Element.AutomationId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid">Element.ClassId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects">Element.Effects</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id">Element.Id</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid">Element.StyleId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded">Element.ChildAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved">Element.ChildRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded">Element.DescendantAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved">Element.DescendantRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging">Element.ParentChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">Element.HandlerChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">Element.HandlerChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty">BindableObject.BindingContextProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)">BindableObject.ClearValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.ClearValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue">BindableObject.GetValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset">BindableObject.IsSet(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding">BindableObject.RemoveBinding(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding">BindableObject.SetBinding(BindableProperty, BindingBase)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings">BindableObject.ApplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging">BindableObject.OnPropertyChanging(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings">BindableObject.UnapplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)">BindableObject.SetValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)">BindableObject.SetValue(BindablePropertyKey, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)">BindableObject.CoerceValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.CoerceValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher">BindableObject.Dispatcher</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext">BindableObject.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged">BindableObject.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging">BindableObject.PropertyChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged">BindableObject.BindingContextChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_">DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_AnimateWith_DrawnUi_Draw_SkiaControl_System_Func_DrawnUi_Draw_SkiaControl_System_Threading_Tasks_Task____">AnimateExtensions.AnimateWith(SkiaControl, params Func&lt;SkiaControl, Task&gt;[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_FadeIn_DrawnUi_Draw_SkiaControl_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">AnimateExtensions.FadeIn(SkiaControl, float, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_FadeOut_DrawnUi_Draw_SkiaControl_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">AnimateExtensions.FadeOut(SkiaControl, float, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_Translate_DrawnUi_Draw_SkiaControl_System_Numerics_Vector2_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">AnimateExtensions.Translate(SkiaControl, Vector2, float, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_AnimateRangeAsync_DrawnUi_Draw_SkiaControl_System_Action_System_Double__System_Double_System_Double_System_UInt32_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">DrawnExtensions.AnimateRangeAsync(SkiaControl, Action&lt;double&gt;, double, double, uint, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParentByType__1_DrawnUi_Draw_SkiaControl_">StaticResourcesExtensions.FindParentByType&lt;T&gt;(SkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Adapt__1___0_System_Action___0__">FluentExtensions.Adapt&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__">FluentExtensions.AssignNative&lt;T&gt;(T, out T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignParent__1___0_DrawnUi_Draw_SkiaControl_">FluentExtensions.AssignParent&lt;T&gt;(T, SkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Assign__1___0___0__">FluentExtensions.Assign&lt;T&gt;(T, out T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_ComponentModel_INotifyPropertyChanged_System_String_Microsoft_Maui_Controls_BindingMode_">FluentExtensions.BindProperty&lt;T&gt;(T, BindableProperty, INotifyPropertyChanged, string, BindingMode)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_BindingMode_">FluentExtensions.BindProperty&lt;T&gt;(T, BindableProperty, string, BindingMode)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__2___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_IValueConverter_System_Object_Microsoft_Maui_Controls_BindingMode_">FluentExtensions.BindProperty&lt;T, TProperty&gt;(T, BindableProperty, string, IValueConverter, object, BindingMode)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_CenterX__1___0_">FluentExtensions.CenterX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_CenterY__1___0_">FluentExtensions.CenterY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Center__1___0_">FluentExtensions.Center&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_EndX__1___0_">FluentExtensions.EndX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_EndY__1___0_">FluentExtensions.EndY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_FillX__1___0_">FluentExtensions.FillX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_FillY__1___0_">FluentExtensions.FillY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Fill__1___0_">FluentExtensions.Fill&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Initialize__1___0_System_Action___0__">FluentExtensions.Initialize&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveBindingContextOn__3___0___1_System_Action___0___1___2_System_String__System_Boolean_">FluentExtensions.ObserveBindingContextOn&lt;T, TTarget, TSource&gt;(T, TTarget, Action&lt;T, TTarget, TSource, string&gt;, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveBindingContext__2___0_System_Action___0___1_System_String__System_Boolean_">FluentExtensions.ObserveBindingContext&lt;T, TSource&gt;(T, Action&lt;T, TSource, string&gt;, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveOn__3___0___1_System_Func___2__System_String_System_Action___0_System_String__System_String___">FluentExtensions.ObserveOn&lt;T, TParent, TTarget&gt;(T, TParent, Func&lt;TTarget&gt;, string, Action&lt;T, string&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObservePropertiesOn__3___0___1_System_Func___2__System_String_System_Collections_Generic_IEnumerable_System_String__System_Action___0__">FluentExtensions.ObservePropertiesOn&lt;T, TParent, TTarget&gt;(T, TParent, Func&lt;TTarget&gt;, string, IEnumerable&lt;string&gt;, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveProperties__2___0___1_System_Collections_Generic_IEnumerable_System_String__System_Action___0__">FluentExtensions.ObserveProperties&lt;T, TSource&gt;(T, TSource, IEnumerable&lt;string&gt;, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObservePropertyOn__3___0___1_System_Func___2__System_String_System_String_System_Action___0__">FluentExtensions.ObservePropertyOn&lt;T, TParent, TTarget&gt;(T, TParent, Func&lt;TTarget&gt;, string, string, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveProperty__2___0___1_System_String_System_Action___0__">FluentExtensions.ObserveProperty&lt;T, TSource&gt;(T, TSource, string, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveSelf__1___0_System_Action___0_System_String__">FluentExtensions.ObserveSelf&lt;T&gt;(T, Action&lt;T, string&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Observe__2___0_System_Func___1__System_Action___0_System_String__System_String___">FluentExtensions.Observe&lt;T, TSource&gt;(T, Func&lt;TSource&gt;, Action&lt;T, string&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Observe__2___0___1_System_Action___0_System_String__System_String___">FluentExtensions.Observe&lt;T, TSource&gt;(T, TSource, Action&lt;T, string&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnBindingContextSet__1___0_System_Action___0_System_Object__System_String___">FluentExtensions.OnBindingContextSet&lt;T&gt;(T, Action&lt;T, object&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnLongPressing__1___0_System_Action___0__">FluentExtensions.OnLongPressing&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnPaint__1___0_System_Action___0_DrawnUi_Draw_DrawingContext__">FluentExtensions.OnPaint&lt;T&gt;(T, Action&lt;T, DrawingContext&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnTapped__1___0_System_Action___0__">FluentExtensions.OnTapped&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_">FluentExtensions.SetGrid&lt;T&gt;(T, int, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_System_Int32_System_Int32_">FluentExtensions.SetGrid&lt;T&gt;(T, int, int, int, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_StartX__1___0_">FluentExtensions.StartX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_StartY__1___0_">FluentExtensions.StartY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithBackgroundColor__1___0_Microsoft_Maui_Graphics_Color_">FluentExtensions.WithBackgroundColor&lt;T&gt;(T, Color)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithCache__1___0_DrawnUi_Draw_SkiaCacheType_">FluentExtensions.WithCache&lt;T&gt;(T, SkiaCacheType)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithChildren__1___0_DrawnUi_Draw_SkiaControl___">FluentExtensions.WithChildren&lt;T&gt;(T, params SkiaControl[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumnDefinitions_DrawnUi_Draw_SkiaLayout_System_String_">FluentExtensions.WithColumnDefinitions(SkiaLayout, string)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumnSpan__1___0_System_Int32_">FluentExtensions.WithColumnSpan&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumn__1___0_System_Int32_">FluentExtensions.WithColumn&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithEnabled__1___0_System_Boolean_">FluentExtensions.WithEnabled&lt;T&gt;(T, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithGestures__1___0_System_Func___0_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_DrawnUi_Draw_ISkiaGestureListener__">FluentExtensions.WithGestures&lt;T&gt;(T, Func&lt;T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithHeight__1___0_System_Double_">FluentExtensions.WithHeight&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithHorizontalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_">FluentExtensions.WithHorizontalOptions&lt;T&gt;(T, LayoutOptions)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithItemsSource__1___0_System_Collections_IList_">FluentExtensions.WithItemsSource&lt;T&gt;(T, IList)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithMargin__1___0_Microsoft_Maui_Thickness_">FluentExtensions.WithMargin&lt;T&gt;(T, Thickness)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithMargin__1___0_System_Double_">FluentExtensions.WithMargin&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithMargin__1___0_System_Double_System_Double_">FluentExtensions.WithMargin&lt;T&gt;(T, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithMargin__1___0_System_Double_System_Double_System_Double_System_Double_">FluentExtensions.WithMargin&lt;T&gt;(T, double, double, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithOpacity__1___0_System_Double_">FluentExtensions.WithOpacity&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_">FluentExtensions.WithPadding&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_System_Double_">FluentExtensions.WithPadding&lt;T&gt;(T, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_System_Double_System_Double_System_Double_">FluentExtensions.WithPadding&lt;T&gt;(T, double, double, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithParent__1___0_DrawnUi_Draw_IDrawnBase_">FluentExtensions.WithParent&lt;T&gt;(T, IDrawnBase)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRotation__1___0_System_Double_">FluentExtensions.WithRotation&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRowDefinitions_DrawnUi_Draw_SkiaLayout_System_String_">FluentExtensions.WithRowDefinitions(SkiaLayout, string)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRowSpan__1___0_System_Int32_">FluentExtensions.WithRowSpan&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRow__1___0_System_Int32_">FluentExtensions.WithRow&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithScale__1___0_System_Double_">FluentExtensions.WithScale&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithScale__1___0_System_Double_System_Double_">FluentExtensions.WithScale&lt;T&gt;(T, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithSpacing__1___0_System_Double_">FluentExtensions.WithSpacing&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithTag__1___0_System_String_">FluentExtensions.WithTag&lt;T&gt;(T, string)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithType__1___0_DrawnUi_Draw_LayoutType_">FluentExtensions.WithType&lt;T&gt;(T, LayoutType)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithVerticalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_">FluentExtensions.WithVerticalOptions&lt;T&gt;(T, LayoutOptions)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithVisibility__1___0_System_Boolean_">FluentExtensions.WithVisibility&lt;T&gt;(T, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithWidth__1___0_System_Double_">FluentExtensions.WithWidth&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_">StaticResourcesExtensions.FindParent&lt;T&gt;(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_">InternalExtensions.FindMauiContext(Element, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_">InternalExtensions.GetParentsPath(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_">StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_">InternalExtensions.DisposeControlAndChildren(IView)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_SkiaLayout__ctor_" data-uid="DrawnUi.Draw.SkiaLayout.#ctor*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout__ctor" data-uid="DrawnUi.Draw.SkiaLayout.#ctor">
  SkiaLayout()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L270"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaLayout()</code></pre>
  </div>













  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_SkiaLayout_ColumnDefinitionsProperty" data-uid="DrawnUi.Draw.SkiaLayout.ColumnDefinitionsProperty">
  ColumnDefinitionsProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L259"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ColumnDefinitionsProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_ColumnSpacingProperty" data-uid="DrawnUi.Draw.SkiaLayout.ColumnSpacingProperty">
  ColumnSpacingProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L250"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ColumnSpacingProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_DefaultColumnDefinitionProperty" data-uid="DrawnUi.Draw.SkiaLayout.DefaultColumnDefinitionProperty">
  DefaultColumnDefinitionProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L203"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty DefaultColumnDefinitionProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_DefaultRowDefinitionProperty" data-uid="DrawnUi.Draw.SkiaLayout.DefaultRowDefinitionProperty">
  DefaultRowDefinitionProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L219"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty DefaultRowDefinitionProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_DynamicColumnsProperty" data-uid="DrawnUi.Draw.SkiaLayout.DynamicColumnsProperty">
  DynamicColumnsProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L510"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty DynamicColumnsProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_EmptyViewProperty" data-uid="DrawnUi.Draw.SkiaLayout.EmptyViewProperty">
  EmptyViewProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1336"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty EmptyViewProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_GridStructure" data-uid="DrawnUi.Draw.SkiaLayout.GridStructure">
  GridStructure
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L195"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaLayout.SkiaGridStructure GridStructure</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a>.<a class="xref" href="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.html">SkiaGridStructure</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_GridStructureMeasured" data-uid="DrawnUi.Draw.SkiaLayout.GridStructureMeasured">
  GridStructureMeasured
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L197"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaLayout.SkiaGridStructure GridStructureMeasured</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a>.<a class="xref" href="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.html">SkiaGridStructure</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_ItemTemplatePoolSizeProperty" data-uid="DrawnUi.Draw.SkiaLayout.ItemTemplatePoolSizeProperty">
  ItemTemplatePoolSizeProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1321"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ItemTemplatePoolSizeProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_ItemsSourceProperty" data-uid="DrawnUi.Draw.SkiaLayout.ItemsSourceProperty">
  ItemsSourceProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1354"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ItemsSourceProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_LineBreaks" data-uid="DrawnUi.Draw.SkiaLayout.LineBreaks">
  LineBreaks
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L344"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected List&lt;int&gt; LineBreaks</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureItemsStrategyProperty" data-uid="DrawnUi.Draw.SkiaLayout.MeasureItemsStrategyProperty">
  MeasureItemsStrategyProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1308"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty MeasureItemsStrategyProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_RecyclingBufferProperty" data-uid="DrawnUi.Draw.SkiaLayout.RecyclingBufferProperty">
  RecyclingBufferProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L316"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty RecyclingBufferProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_RecyclingTemplateProperty" data-uid="DrawnUi.Draw.SkiaLayout.RecyclingTemplateProperty">
  RecyclingTemplateProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L116"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty RecyclingTemplateProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_RowDefinitionsProperty" data-uid="DrawnUi.Draw.SkiaLayout.RowDefinitionsProperty">
  RowDefinitionsProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L285"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty RowDefinitionsProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_RowSpacingProperty" data-uid="DrawnUi.Draw.SkiaLayout.RowSpacingProperty">
  RowSpacingProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L241"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty RowSpacingProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_SplitAlignProperty" data-uid="DrawnUi.Draw.SkiaLayout.SplitAlignProperty">
  SplitAlignProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L479"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty SplitAlignProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_SplitProperty" data-uid="DrawnUi.Draw.SkiaLayout.SplitProperty">
  SplitProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L463"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty SplitProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_SplitSpaceProperty" data-uid="DrawnUi.Draw.SkiaLayout.SplitSpaceProperty">
  SplitSpaceProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L494"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty SplitSpaceProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_TemplatedFooterProperty" data-uid="DrawnUi.Draw.SkiaLayout.TemplatedFooterProperty">
  TemplatedFooterProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L167"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty TemplatedFooterProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_TemplatedHeaderProperty" data-uid="DrawnUi.Draw.SkiaLayout.TemplatedHeaderProperty">
  TemplatedHeaderProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L154"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty TemplatedHeaderProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_TypeProperty" data-uid="DrawnUi.Draw.SkiaLayout.TypeProperty">
  TypeProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1274"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty TypeProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_VirtualisationInflatedProperty" data-uid="DrawnUi.Draw.SkiaLayout.VirtualisationInflatedProperty">
  VirtualisationInflatedProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L296"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty VirtualisationInflatedProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_VirtualizationProperty" data-uid="DrawnUi.Draw.SkiaLayout.VirtualizationProperty">
  VirtualizationProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L280"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty VirtualizationProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout__emptyView" data-uid="DrawnUi.Draw.SkiaLayout._emptyView">
  _emptyView
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L373"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SkiaControl _emptyView</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout__lockTemplates" data-uid="DrawnUi.Draw.SkiaLayout._lockTemplates">
  _lockTemplates
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L929"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected readonly object _lockTemplates</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_lockMeasureLayout" data-uid="DrawnUi.Draw.SkiaLayout.lockMeasureLayout">
  lockMeasureLayout
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L849"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected object lockMeasureLayout</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_templatesInvalidated" data-uid="DrawnUi.Draw.SkiaLayout.templatesInvalidated">
  templatesInvalidated
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L607"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected bool templatesInvalidated</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_SkiaLayout_ApplyNewItemsSource_" data-uid="DrawnUi.Draw.SkiaLayout.ApplyNewItemsSource*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ApplyNewItemsSource" data-uid="DrawnUi.Draw.SkiaLayout.ApplyNewItemsSource">
  ApplyNewItemsSource
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1423"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ApplyNewItemsSource { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_ChildrenFactory_" data-uid="DrawnUi.Draw.SkiaLayout.ChildrenFactory*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ChildrenFactory" data-uid="DrawnUi.Draw.SkiaLayout.ChildrenFactory">
  ChildrenFactory
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L460"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ViewsAdapter ChildrenFactory { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ViewsAdapter.html">ViewsAdapter</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_ChildrenGrid_" data-uid="DrawnUi.Draw.SkiaLayout.ChildrenGrid*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ChildrenGrid" data-uid="DrawnUi.Draw.SkiaLayout.ChildrenGrid">
  ChildrenGrid
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L336"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected List&lt;ISkiaControl&gt; ChildrenGrid { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_ColumnDefinitions_" data-uid="DrawnUi.Draw.SkiaLayout.ColumnDefinitions*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ColumnDefinitions" data-uid="DrawnUi.Draw.SkiaLayout.ColumnDefinitions">
  ColumnDefinitions
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L278"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[TypeConverter(typeof(ColumnDefinitionCollectionTypeConverter))]
public ColumnDefinitionCollection ColumnDefinitions { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.columndefinitioncollection">ColumnDefinitionCollection</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_ColumnSpacing_" data-uid="DrawnUi.Draw.SkiaLayout.ColumnSpacing*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ColumnSpacing" data-uid="DrawnUi.Draw.SkiaLayout.ColumnSpacing">
  ColumnSpacing
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L253"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the amount of space left between columns in the GridLayout.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double ColumnSpacing { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_DebugString_" data-uid="DrawnUi.Draw.SkiaLayout.DebugString*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DebugString" data-uid="DrawnUi.Draw.SkiaLayout.DebugString">
  DebugString
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L440"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string DebugString { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_DefaultColumnDefinition_" data-uid="DrawnUi.Draw.SkiaLayout.DefaultColumnDefinition*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DefaultColumnDefinition" data-uid="DrawnUi.Draw.SkiaLayout.DefaultColumnDefinition">
  DefaultColumnDefinition
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L212"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Will use this to create a missing but required ColumnDefinition</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[TypeConverter(typeof(ColumnDefinitionTypeConverter))]
public ColumnDefinition DefaultColumnDefinition { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.columndefinition">ColumnDefinition</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_DefaultRowDefinition_" data-uid="DrawnUi.Draw.SkiaLayout.DefaultRowDefinition*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DefaultRowDefinition" data-uid="DrawnUi.Draw.SkiaLayout.DefaultRowDefinition">
  DefaultRowDefinition
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L228"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Will use this to create a missing but required RowDefinition</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[TypeConverter(typeof(RowDefinitionTypeConverter))]
public RowDefinition DefaultRowDefinition { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.rowdefinition">RowDefinition</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_DynamicColumns_" data-uid="DrawnUi.Draw.SkiaLayout.DynamicColumns*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DynamicColumns" data-uid="DrawnUi.Draw.SkiaLayout.DynamicColumns">
  DynamicColumns
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L519"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>If true, will not create additional columns to match SplitMax if there are less real columns, and take additional space for drawing</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool DynamicColumns { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_EmptyView_" data-uid="DrawnUi.Draw.SkiaLayout.EmptyView*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_EmptyView" data-uid="DrawnUi.Draw.SkiaLayout.EmptyView">
  EmptyView
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1348"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl EmptyView { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_EstimatedTotalItems_" data-uid="DrawnUi.Draw.SkiaLayout.EstimatedTotalItems*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_EstimatedTotalItems" data-uid="DrawnUi.Draw.SkiaLayout.EstimatedTotalItems">
  EstimatedTotalItems
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs/#L507"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int EstimatedTotalItems { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_FirstMeasuredIndex_" data-uid="DrawnUi.Draw.SkiaLayout.FirstMeasuredIndex*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_FirstMeasuredIndex" data-uid="DrawnUi.Draw.SkiaLayout.FirstMeasuredIndex">
  FirstMeasuredIndex
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs/#L308"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int FirstMeasuredIndex { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_FirstVisibleIndex_" data-uid="DrawnUi.Draw.SkiaLayout.FirstVisibleIndex*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_FirstVisibleIndex" data-uid="DrawnUi.Draw.SkiaLayout.FirstVisibleIndex">
  FirstVisibleIndex
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs/#L312"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int FirstVisibleIndex { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_InvalidatedChildren_" data-uid="DrawnUi.Draw.SkiaLayout.InvalidatedChildren*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_InvalidatedChildren" data-uid="DrawnUi.Draw.SkiaLayout.InvalidatedChildren">
  InvalidatedChildren
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1056"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Will be modified by InvalidateByChild</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected HashSet&lt;SkiaControl&gt; InvalidatedChildren { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.hashset-1">HashSet</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_InvalidatedChildrenInternal_" data-uid="DrawnUi.Draw.SkiaLayout.InvalidatedChildrenInternal*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_InvalidatedChildrenInternal" data-uid="DrawnUi.Draw.SkiaLayout.InvalidatedChildrenInternal">
  InvalidatedChildrenInternal
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1036"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Will use this when drawing</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected HashSet&lt;SkiaControl&gt; InvalidatedChildrenInternal { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.hashset-1">HashSet</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_IsEmpty_" data-uid="DrawnUi.Draw.SkiaLayout.IsEmpty*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_IsEmpty" data-uid="DrawnUi.Draw.SkiaLayout.IsEmpty">
  IsEmpty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L395"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsEmpty { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_IsStack_" data-uid="DrawnUi.Draw.SkiaLayout.IsStack*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_IsStack" data-uid="DrawnUi.Draw.SkiaLayout.IsStack">
  IsStack
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1269"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Column/Row/Stack</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsStack { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_IsTemplated_" data-uid="DrawnUi.Draw.SkiaLayout.IsTemplated*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_IsTemplated" data-uid="DrawnUi.Draw.SkiaLayout.IsTemplated">
  IsTemplated
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L181"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool IsTemplated { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_ItemTemplatePoolSize_" data-uid="DrawnUi.Draw.SkiaLayout.ItemTemplatePoolSize*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ItemTemplatePoolSize" data-uid="DrawnUi.Draw.SkiaLayout.ItemTemplatePoolSize">
  ItemTemplatePoolSize
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1330"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Default is -1, the number od template instances will not be less than data collection count. You can manually set to to a specific number to fill your viewport etc. Beware that if you set this to a number that will not be enough to fill the viewport binding contexts will contasntly be changing triggering screen update.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int ItemTemplatePoolSize { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_ItemsSource_" data-uid="DrawnUi.Draw.SkiaLayout.ItemsSource*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ItemsSource" data-uid="DrawnUi.Draw.SkiaLayout.ItemsSource">
  ItemsSource
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1362"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IList ItemsSource { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_LastMeasuredIndex_" data-uid="DrawnUi.Draw.SkiaLayout.LastMeasuredIndex*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_LastMeasuredIndex" data-uid="DrawnUi.Draw.SkiaLayout.LastMeasuredIndex">
  LastMeasuredIndex
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs/#L310"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int LastMeasuredIndex { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_LastVisibleIndex_" data-uid="DrawnUi.Draw.SkiaLayout.LastVisibleIndex*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_LastVisibleIndex" data-uid="DrawnUi.Draw.SkiaLayout.LastVisibleIndex">
  LastVisibleIndex
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs/#L314"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int LastVisibleIndex { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_LatestMeasuredStackStructure_" data-uid="DrawnUi.Draw.SkiaLayout.LatestMeasuredStackStructure*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_LatestMeasuredStackStructure" data-uid="DrawnUi.Draw.SkiaLayout.LatestMeasuredStackStructure">
  LatestMeasuredStackStructure
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L30"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LayoutStructure LatestMeasuredStackStructure { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_LatestStackStructure_" data-uid="DrawnUi.Draw.SkiaLayout.LatestStackStructure*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_LatestStackStructure" data-uid="DrawnUi.Draw.SkiaLayout.LatestStackStructure">
  LatestStackStructure
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L19"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LayoutStructure LatestStackStructure { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_MeasureItemsStrategy_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureItemsStrategy*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureItemsStrategy" data-uid="DrawnUi.Draw.SkiaLayout.MeasureItemsStrategy">
  MeasureItemsStrategy
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1315"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MeasuringStrategy MeasureItemsStrategy { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.MeasuringStrategy.html">MeasuringStrategy</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_RecyclingBuffer_" data-uid="DrawnUi.Draw.SkiaLayout.RecyclingBuffer*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_RecyclingBuffer" data-uid="DrawnUi.Draw.SkiaLayout.RecyclingBuffer">
  RecyclingBuffer
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L326"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Extra buffer zone for avoiding recycling<br>
Default is 500pts - increase for less jaggy scroll, decrease for more memory efficiency</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double RecyclingBuffer { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_RecyclingTemplate_" data-uid="DrawnUi.Draw.SkiaLayout.RecyclingTemplate*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_RecyclingTemplate" data-uid="DrawnUi.Draw.SkiaLayout.RecyclingTemplate">
  RecyclingTemplate
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L127"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>In case of ItemsSource+ItemTemplate set will define should we reuse already created views: hidden items views will be reused for currently visible items on screen.
If set to true inside a SkiaScrollLooped will cause it to redraw constantly even when idle because of the looped scroll mechanics.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public RecyclingTemplate RecyclingTemplate { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.RecyclingTemplate.html">RecyclingTemplate</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_RowDefinitions_" data-uid="DrawnUi.Draw.SkiaLayout.RowDefinitions*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_RowDefinitions" data-uid="DrawnUi.Draw.SkiaLayout.RowDefinitions">
  RowDefinitions
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L302"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[TypeConverter(typeof(RowDefinitionCollectionTypeConverter))]
public RowDefinitionCollection RowDefinitions { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.rowdefinitioncollection">RowDefinitionCollection</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_RowSpacing_" data-uid="DrawnUi.Draw.SkiaLayout.RowSpacing*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_RowSpacing" data-uid="DrawnUi.Draw.SkiaLayout.RowSpacing">
  RowSpacing
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L244"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the amount of space left between rows in the GridLayout.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double RowSpacing { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_ShouldInvalidateByChildren_" data-uid="DrawnUi.Draw.SkiaLayout.ShouldInvalidateByChildren*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ShouldInvalidateByChildren" data-uid="DrawnUi.Draw.SkiaLayout.ShouldInvalidateByChildren">
  ShouldInvalidateByChildren
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L70"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool ShouldInvalidateByChildren { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_Split_" data-uid="DrawnUi.Draw.SkiaLayout.Split*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_Split" data-uid="DrawnUi.Draw.SkiaLayout.Split">
  Split
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L473"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>For Wrap number of columns/rows to split into, If 0 will use auto, if 1+ will have 1+ columns.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Split { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_SplitAlign_" data-uid="DrawnUi.Draw.SkiaLayout.SplitAlign*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SplitAlign" data-uid="DrawnUi.Draw.SkiaLayout.SplitAlign">
  SplitAlign
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L488"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Whether should keep same column width among rows</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool SplitAlign { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_SplitSpace_" data-uid="DrawnUi.Draw.SkiaLayout.SplitSpace*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SplitSpace" data-uid="DrawnUi.Draw.SkiaLayout.SplitSpace">
  SplitSpace
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L504"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>How to distribute free space between children TODO</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SpaceDistribution SplitSpace { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SpaceDistribution.html">SpaceDistribution</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_StackStructure_" data-uid="DrawnUi.Draw.SkiaLayout.StackStructure*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_StackStructure" data-uid="DrawnUi.Draw.SkiaLayout.StackStructure">
  StackStructure
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L12"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Used for StackLayout (Stack, Row) kind of layout</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LayoutStructure StackStructure { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_StackStructureMeasured_" data-uid="DrawnUi.Draw.SkiaLayout.StackStructureMeasured*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_StackStructureMeasured" data-uid="DrawnUi.Draw.SkiaLayout.StackStructureMeasured">
  StackStructureMeasured
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L17"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>When measuring we set this, and it will be swapped with StackStructure upon drawing so we don't affect the drawing if measuring in background.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LayoutStructure StackStructureMeasured { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_TemplatedFooter_" data-uid="DrawnUi.Draw.SkiaLayout.TemplatedFooter*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_TemplatedFooter" data-uid="DrawnUi.Draw.SkiaLayout.TemplatedFooter">
  TemplatedFooter
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L174"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Kind of BindableLayout.DrawnTemplate</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl TemplatedFooter { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_TemplatedHeader_" data-uid="DrawnUi.Draw.SkiaLayout.TemplatedHeader*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_TemplatedHeader" data-uid="DrawnUi.Draw.SkiaLayout.TemplatedHeader">
  TemplatedHeader
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L161"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Kind of BindableLayout.DrawnTemplate</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl TemplatedHeader { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_Type_" data-uid="DrawnUi.Draw.SkiaLayout.Type*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_Type" data-uid="DrawnUi.Draw.SkiaLayout.Type">
  Type
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1279"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LayoutType Type { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.LayoutType.html">LayoutType</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_ViewportWasChanged_" data-uid="DrawnUi.Draw.SkiaLayout.ViewportWasChanged*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ViewportWasChanged" data-uid="DrawnUi.Draw.SkiaLayout.ViewportWasChanged">
  ViewportWasChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L537"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected bool ViewportWasChanged { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_Virtualisation_" data-uid="DrawnUi.Draw.SkiaLayout.Virtualisation*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_Virtualisation" data-uid="DrawnUi.Draw.SkiaLayout.Virtualisation">
  Virtualisation
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L290"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Default is Enabled, children get the visible viewport area for rendering and can virtualize.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public VirtualisationType Virtualisation { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.VirtualisationType.html">VirtualisationType</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_VirtualisationInflated_" data-uid="DrawnUi.Draw.SkiaLayout.VirtualisationInflated*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_VirtualisationInflated" data-uid="DrawnUi.Draw.SkiaLayout.VirtualisationInflated">
  VirtualisationInflated
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L309"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>How much of the hidden content out of visible bounds should be considered visible for rendering,
default is 0 pts.
Basically how much should be expand in every direction of the visible area prior to checking if content falls
into its bounds for rendering controlled with Virtualisation.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double VirtualisationInflated { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_SkiaLayout_ActualizeSubviews_" data-uid="DrawnUi.Draw.SkiaLayout.ActualizeSubviews*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ActualizeSubviews" data-uid="DrawnUi.Draw.SkiaLayout.ActualizeSubviews">
  ActualizeSubviews()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L647"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ActualizeSubviews()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_ApplyBindingContext_" data-uid="DrawnUi.Draw.SkiaLayout.ApplyBindingContext*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ApplyBindingContext" data-uid="DrawnUi.Draw.SkiaLayout.ApplyBindingContext">
  ApplyBindingContext()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L55"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p><a href="https://github.com/taublast/DrawnUi/issues/92#issuecomment-2408805077">https://github.com/taublast/DrawnUi/issues/92#issuecomment-2408805077</a></p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void ApplyBindingContext()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_ApplyIsEmpty_" data-uid="DrawnUi.Draw.SkiaLayout.ApplyIsEmpty*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ApplyIsEmpty_System_Boolean_" data-uid="DrawnUi.Draw.SkiaLayout.ApplyIsEmpty(System.Boolean)">
  ApplyIsEmpty(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L412"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void ApplyIsEmpty(bool value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_ApplyItemsSource_" data-uid="DrawnUi.Draw.SkiaLayout.ApplyItemsSource*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ApplyItemsSource" data-uid="DrawnUi.Draw.SkiaLayout.ApplyItemsSource">
  ApplyItemsSource()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1433"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Invalidate and re-apply ItemsSource</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ApplyItemsSource()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_ApplyMeasureResult_" data-uid="DrawnUi.Draw.SkiaLayout.ApplyMeasureResult*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ApplyMeasureResult" data-uid="DrawnUi.Draw.SkiaLayout.ApplyMeasureResult">
  ApplyMeasureResult()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1010"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Normally get a a Measure by parent then parent calls Draw and we can apply the measure result.
But in a case we have measured us ourselves inside PreArrange etc we must call ApplyMeasureResult because this would happen after the Draw and not before.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void ApplyMeasureResult()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_BreakLine_" data-uid="DrawnUi.Draw.SkiaLayout.BreakLine*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_BreakLine" data-uid="DrawnUi.Draw.SkiaLayout.BreakLine">
  BreakLine()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L339"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void BreakLine()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_BuildGridLayout_" data-uid="DrawnUi.Draw.SkiaLayout.BuildGridLayout*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_BuildGridLayout_SkiaSharp_SKSize_" data-uid="DrawnUi.Draw.SkiaLayout.BuildGridLayout(SkiaSharp.SKSize)">
  BuildGridLayout(SKSize)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L149"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void BuildGridLayout(SKSize constraints)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>constraints</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_CheckAndSetupIfEmpty_" data-uid="DrawnUi.Draw.SkiaLayout.CheckAndSetupIfEmpty*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_CheckAndSetupIfEmpty" data-uid="DrawnUi.Draw.SkiaLayout.CheckAndSetupIfEmpty">
  CheckAndSetupIfEmpty()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L422"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual bool CheckAndSetupIfEmpty()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_Clear_" data-uid="DrawnUi.Draw.SkiaLayout.Clear*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_Clear" data-uid="DrawnUi.Draw.SkiaLayout.Clear">
  Clear()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Maui.cs/#L9"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_CreateTemplatesInBackground_" data-uid="DrawnUi.Draw.SkiaLayout.CreateTemplatesInBackground*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_CreateTemplatesInBackground" data-uid="DrawnUi.Draw.SkiaLayout.CreateTemplatesInBackground">
  CreateTemplatesInBackground()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L709"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Task CreateTemplatesInBackground()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_Draw_" data-uid="DrawnUi.Draw.SkiaLayout.Draw*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_Draw_DrawnUi_Draw_DrawingContext_" data-uid="DrawnUi.Draw.SkiaLayout.Draw(DrawnUi.Draw.DrawingContext)">
  Draw(DrawingContext)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1038"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void Draw(DrawingContext context)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_DrawChild_" data-uid="DrawnUi.Draw.SkiaLayout.DrawChild*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DrawChild_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_ISkiaControl_" data-uid="DrawnUi.Draw.SkiaLayout.DrawChild(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.ISkiaControl)">
  DrawChild(DrawingContext, ISkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L539"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual bool DrawChild(DrawingContext ctx, ISkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>ctx</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_DrawChildrenGrid_" data-uid="DrawnUi.Draw.SkiaLayout.DrawChildrenGrid*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DrawChildrenGrid_DrawnUi_Draw_DrawingContext_" data-uid="DrawnUi.Draw.SkiaLayout.DrawChildrenGrid(DrawnUi.Draw.DrawingContext)">
  DrawChildrenGrid(DrawingContext)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L91"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Returns number of drawn children</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual int DrawChildrenGrid(DrawingContext context)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_DrawList_" data-uid="DrawnUi.Draw.SkiaLayout.DrawList*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DrawList_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_LayoutStructure_" data-uid="DrawnUi.Draw.SkiaLayout.DrawList(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.LayoutStructure)">
  DrawList(DrawingContext, LayoutStructure)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs/#L324"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Renders Templated Column/Row todo in some cases..</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual int DrawList(DrawingContext ctx, LayoutStructure structure)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>ctx</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
    <dt><code>structure</code> <a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_DrawRenderObject_" data-uid="DrawnUi.Draw.SkiaLayout.DrawRenderObject*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DrawRenderObject_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_" data-uid="DrawnUi.Draw.SkiaLayout.DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)">
  DrawRenderObject(DrawingContext, CachedObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L366"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Drawing cache, applying clip and transforms as well</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void DrawRenderObject(DrawingContext context, CachedObject cache)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
    <dt><code>cache</code> <a class="xref" href="DrawnUi.Draw.CachedObject.html">CachedObject</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_DrawStack_" data-uid="DrawnUi.Draw.SkiaLayout.DrawStack*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DrawStack_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_LayoutStructure_" data-uid="DrawnUi.Draw.SkiaLayout.DrawStack(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.LayoutStructure)">
  DrawStack(DrawingContext, LayoutStructure)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L1736"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Renders stack/wrap layout.
Returns number of drawn children.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual int DrawStack(DrawingContext ctx, LayoutStructure structure)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>ctx</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
    <dt><code>structure</code> <a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_DrawViews_" data-uid="DrawnUi.Draw.SkiaLayout.DrawViews*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_DrawViews_DrawnUi_Draw_DrawingContext_" data-uid="DrawnUi.Draw.SkiaLayout.DrawViews(DrawnUi.Draw.DrawingContext)">
  DrawViews(DrawingContext)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1232"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Base method will call RenderViewsList.
Return number of drawn views.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override int DrawViews(DrawingContext context)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetCacheDebugInfo_" data-uid="DrawnUi.Draw.SkiaLayout.GetCacheDebugInfo*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetCacheDebugInfo" data-uid="DrawnUi.Draw.SkiaLayout.GetCacheDebugInfo">
  GetCacheDebugInfo()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1528"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Get debug information about cached views</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string GetCacheDebugInfo()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetChildAt_" data-uid="DrawnUi.Draw.SkiaLayout.GetChildAt*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetChildAt_System_Single_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.GetChildAt(System.Single,System.Single)">
  GetChildAt(float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L241"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetChildAt(float x, float y)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>x</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>y</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetChildIndexAt_" data-uid="DrawnUi.Draw.SkiaLayout.GetChildIndexAt*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetChildIndexAt_SkiaSharp_SKPoint_" data-uid="DrawnUi.Draw.SkiaLayout.GetChildIndexAt(SkiaSharp.SKPoint)">
  GetChildIndexAt(SKPoint)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1624"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The point here is the position inside parent, can be offscreen</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainsPointResult GetChildIndexAt(SKPoint point)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>point</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ContainsPointResult.html">ContainsPointResult</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetChildRect_" data-uid="DrawnUi.Draw.SkiaLayout.GetChildRect*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetChildRect_DrawnUi_Draw_ISkiaControl_" data-uid="DrawnUi.Draw.SkiaLayout.GetChildRect(DrawnUi.Draw.ISkiaControl)">
  GetChildRect(ISkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L197"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKRect GetChildRect(ISkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetChildRect_" data-uid="DrawnUi.Draw.SkiaLayout.GetChildRect*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetChildRect_System_Int32_" data-uid="DrawnUi.Draw.SkiaLayout.GetChildRect(System.Int32)">
  GetChildRect(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L184"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKRect GetChildRect(int index)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>index</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetColumn_" data-uid="DrawnUi.Draw.SkiaLayout.GetColumn*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetColumn_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.SkiaLayout.GetColumn(Microsoft.Maui.Controls.BindableObject)">
  GetColumn(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L154"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the column of the child element.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetColumn(BindableObject bindable)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The column that the child element is in.</p>
</dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetColumnSpan_" data-uid="DrawnUi.Draw.SkiaLayout.GetColumnSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetColumnSpan_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.SkiaLayout.GetColumnSpan(Microsoft.Maui.Controls.BindableObject)">
  GetColumnSpan(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L159"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the row span of the child element.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetColumnSpan(BindableObject bindable)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The row that the child element is in.</p>
</dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetMeasuredContentEnd_" data-uid="DrawnUi.Draw.SkiaLayout.GetMeasuredContentEnd*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetMeasuredContentEnd" data-uid="DrawnUi.Draw.SkiaLayout.GetMeasuredContentEnd">
  GetMeasuredContentEnd()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs/#L510"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double GetMeasuredContentEnd()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetOnScreenVisibleArea_" data-uid="DrawnUi.Draw.SkiaLayout.GetOnScreenVisibleArea*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_" data-uid="DrawnUi.Draw.SkiaLayout.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)">
  GetOnScreenVisibleArea(DrawingContext, Vector2)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L347"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>For virtualization. For this method to be conditional we introduced the <code>pixelsDestination</code>
parameter so that the Parent could return different visible areas upon context.
Normally pass your current destination you are drawing into as this parameter.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override ScaledRect GetOnScreenVisibleArea(DrawingContext context, Vector2 inflateByPixels = default)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
    <dt><code>inflateByPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.numerics.vector2">Vector2</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledRect.html">ScaledRect</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetRow_" data-uid="DrawnUi.Draw.SkiaLayout.GetRow*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetRow_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.SkiaLayout.GetRow(Microsoft.Maui.Controls.BindableObject)">
  GetRow(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L164"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the row of the child element.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetRow(BindableObject bindable)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>An integer that represents the row in which the item will appear.</p>
</dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetRowSpan_" data-uid="DrawnUi.Draw.SkiaLayout.GetRowSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetRowSpan_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.SkiaLayout.GetRowSpan(Microsoft.Maui.Controls.BindableObject)">
  GetRowSpan(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L169"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the row span of the child element.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetRowSpan(BindableObject bindable)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The row that the child element is in.</p>
</dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetSizeKey_" data-uid="DrawnUi.Draw.SkiaLayout.GetSizeKey*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetSizeKey_SkiaSharp_SKSize_" data-uid="DrawnUi.Draw.SkiaLayout.GetSizeKey(SkiaSharp.SKSize)">
  GetSizeKey(SKSize)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L134"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetSizeKey(SKSize size)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>size</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetSpacingForIndex_" data-uid="DrawnUi.Draw.SkiaLayout.GetSpacingForIndex*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetSpacingForIndex_System_Int32_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.GetSpacingForIndex(System.Int32,System.Single)">
  GetSpacingForIndex(int, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L122"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected float GetSpacingForIndex(int forIndex, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>forIndex</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetTemplatesPoolLimit_" data-uid="DrawnUi.Draw.SkiaLayout.GetTemplatesPoolLimit*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetTemplatesPoolLimit" data-uid="DrawnUi.Draw.SkiaLayout.GetTemplatesPoolLimit">
  GetTemplatesPoolLimit()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L670"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual int GetTemplatesPoolLimit()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetTemplatesPoolPrefill_" data-uid="DrawnUi.Draw.SkiaLayout.GetTemplatesPoolPrefill*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetTemplatesPoolPrefill" data-uid="DrawnUi.Draw.SkiaLayout.GetTemplatesPoolPrefill">
  GetTemplatesPoolPrefill()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L656"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual int GetTemplatesPoolPrefill()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetVisibleChildIndexAt_" data-uid="DrawnUi.Draw.SkiaLayout.GetVisibleChildIndexAt*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetVisibleChildIndexAt_SkiaSharp_SKPoint_" data-uid="DrawnUi.Draw.SkiaLayout.GetVisibleChildIndexAt(SkiaSharp.SKPoint)">
  GetVisibleChildIndexAt(SKPoint)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1607"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The point here is the rendering location, always on screen</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ContainsPointResult GetVisibleChildIndexAt(SKPoint point)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>point</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ContainsPointResult.html">ContainsPointResult</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_GetVisualChildren_" data-uid="DrawnUi.Draw.SkiaLayout.GetVisualChildren*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_GetVisualChildren" data-uid="DrawnUi.Draw.SkiaLayout.GetVisualChildren">
  GetVisualChildren()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Maui.cs/#L67"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>For Xaml HotReload. This is semetimes not called when we add and remove views.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override IReadOnlyList&lt;IVisualTreeElement&gt; GetVisualChildren()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_Invalidate_" data-uid="DrawnUi.Draw.SkiaLayout.Invalidate*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_Invalidate" data-uid="DrawnUi.Draw.SkiaLayout.Invalidate">
  Invalidate()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L699"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Base calls InvalidateInternal and InvalidateParent</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Invalidate()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_Invalidate_" data-uid="DrawnUi.Draw.SkiaLayout.Invalidate*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_Invalidate_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_" data-uid="DrawnUi.Draw.SkiaLayout.Invalidate(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)">
  Invalidate(BindableObject, object, object)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L335"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected static void Invalidate(BindableObject bindable, object oldValue, object newValue)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>oldValue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
    <dt><code>newValue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_InvalidateByChild_" data-uid="DrawnUi.Draw.SkiaLayout.InvalidateByChild*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_InvalidateByChild_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.SkiaLayout.InvalidateByChild(DrawnUi.Draw.SkiaControl)">
  InvalidateByChild(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1058"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>To be able to fast track dirty children</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void InvalidateByChild(SkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_InvalidateInternal_" data-uid="DrawnUi.Draw.SkiaLayout.InvalidateInternal*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_InvalidateInternal" data-uid="DrawnUi.Draw.SkiaLayout.InvalidateInternal">
  InvalidateInternal()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L600"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Soft invalidation, without requiring update. So next time we try to draw this one it will recalc everything.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void InvalidateInternal()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_InvalidateViewsList_" data-uid="DrawnUi.Draw.SkiaLayout.InvalidateViewsList*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_InvalidateViewsList" data-uid="DrawnUi.Draw.SkiaLayout.InvalidateViewsList">
  InvalidateViewsList()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L640"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void InvalidateViewsList()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_InvalidateWithChildren_" data-uid="DrawnUi.Draw.SkiaLayout.InvalidateWithChildren*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_InvalidateWithChildren" data-uid="DrawnUi.Draw.SkiaLayout.InvalidateWithChildren">
  InvalidateWithChildren()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L609"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void InvalidateWithChildren()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_IsGestureForChild_" data-uid="DrawnUi.Draw.SkiaLayout.IsGestureForChild*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_IsGestureForChild_DrawnUi_Draw_SkiaControlWithRect_SkiaSharp_SKPoint_" data-uid="DrawnUi.Draw.SkiaLayout.IsGestureForChild(DrawnUi.Draw.SkiaControlWithRect,SkiaSharp.SKPoint)">
  IsGestureForChild(SkiaControlWithRect, SKPoint)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L36"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool IsGestureForChild(SkiaControlWithRect child, SKPoint point)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControlWithRect.html">SkiaControlWithRect</a></dt>
    <dd></dd>
    <dt><code>point</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_ItemsSourceCollectionChanged_" data-uid="DrawnUi.Draw.SkiaLayout.ItemsSourceCollectionChanged*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ItemsSourceCollectionChanged_System_Object_System_Collections_Specialized_NotifyCollectionChangedEventArgs_" data-uid="DrawnUi.Draw.SkiaLayout.ItemsSourceCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
  ItemsSourceCollectionChanged(object, NotifyCollectionChangedEventArgs)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1461"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Enhanced collection change handler with smart handling and fallback</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void ItemsSourceCollectionChanged(object sender, NotifyCollectionChangedEventArgs args)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>sender</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
    <dt><code>args</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs">NotifyCollectionChangedEventArgs</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_LayoutCell_" data-uid="DrawnUi.Draw.SkiaLayout.LayoutCell*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_LayoutCell_DrawnUi_Draw_ScaledSize_DrawnUi_Draw_ControlInStack_DrawnUi_Draw_SkiaControl_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.LayoutCell(DrawnUi.Draw.ScaledSize,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,SkiaSharp.SKRect,System.Single)">
  LayoutCell(ScaledSize, ControlInStack, SkiaControl, SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L57"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void LayoutCell(ScaledSize measured, ControlInStack cell, SkiaControl child, SKRect rectForChildrenPixels, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>measured</code> <a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
    <dt><code>cell</code> <a class="xref" href="DrawnUi.Draw.ControlInStack.html">ControlInStack</a></dt>
    <dd></dd>
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>rectForChildrenPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_MeasureAbsolute_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureAbsolute*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureAbsolute_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureAbsolute(SkiaSharp.SKRect,System.Single)">
  MeasureAbsolute(SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L726"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Measure children inside absolute layout</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override ScaledSize MeasureAbsolute(SKRect rectForChildrenPixels, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rectForChildrenPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_MeasureAdditionalItems_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureAdditionalItems*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureAdditionalItems_System_Int32_System_Int32_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureAdditionalItems(System.Int32,System.Int32,System.Single)">
  MeasureAdditionalItems(int, int, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs/#L622"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int MeasureAdditionalItems(int batchSize, int aheadCount, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>batchSize</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>aheadCount</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_MeasureAndArrangeCell_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureAndArrangeCell*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureAndArrangeCell_SkiaSharp_SKRect_DrawnUi_Draw_ControlInStack_DrawnUi_Draw_SkiaControl_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureAndArrangeCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,SkiaSharp.SKRect,System.Single)">
  MeasureAndArrangeCell(SKRect, ControlInStack, SkiaControl, SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L41"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected ScaledSize MeasureAndArrangeCell(SKRect destination, ControlInStack cell, SkiaControl child, SKRect rectForChildrenPixels, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>destination</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>cell</code> <a class="xref" href="DrawnUi.Draw.ControlInStack.html">ControlInStack</a></dt>
    <dd></dd>
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>rectForChildrenPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_MeasureGrid_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureGrid*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureGrid_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureGrid(SkiaSharp.SKRect,System.Single)">
  MeasureGrid(SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L19"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Measures the grid layout and ensures columns fill available width when NeedAutoWidth is false</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ScaledSize MeasureGrid(SKRect rectForChildrenPixels, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rectForChildrenPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd><p>Available rectangle in pixels</p>
</dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Rendering scale</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd><p>Measured size of the grid</p>
</dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_MeasureLayout_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureLayout*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureLayout_DrawnUi_Draw_MeasureRequest_System_Boolean_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureLayout(DrawnUi.Draw.MeasureRequest,System.Boolean)">
  MeasureLayout(MeasureRequest, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L851"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ScaledSize MeasureLayout(MeasureRequest request, bool force)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>request</code> <a class="xref" href="DrawnUi.Draw.MeasureRequest.html">MeasureRequest</a></dt>
    <dd></dd>
    <dt><code>force</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_MeasureList_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureList*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureList_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureList(SkiaSharp.SKRect,System.Single)">
  MeasureList(SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs/#L19"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Measuring column/row list with todo MeasureVisible</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ScaledSize MeasureList(SKRect rectForChildrenPixels, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rectForChildrenPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_MeasureStackLegacy_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureStackLegacy*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureStackLegacy_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureStackLegacy(SkiaSharp.SKRect,System.Single)">
  MeasureStackLegacy(SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L317"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Measuring column/row</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ScaledSize MeasureStackLegacy(SKRect rectForChildrenPixels, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rectForChildrenPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_MeasureStackNonTemplated_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureStackNonTemplated*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureStackNonTemplated_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureStackNonTemplated(SkiaSharp.SKRect,System.Single)">
  MeasureStackNonTemplated(SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L841"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Measuring column/row with 3-pass approach to handle Fill options correctly</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ScaledSize MeasureStackNonTemplated(SKRect rectForChildrenPixels, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rectForChildrenPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_MeasureStackTemplated_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureStackTemplated*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureStackTemplated_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureStackTemplated(SkiaSharp.SKRect,System.Single)">
  MeasureStackTemplated(SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L951"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Measuring column/row for templated for fastest way possible</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ScaledSize MeasureStackTemplated(SKRect rectForChildrenPixels, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rectForChildrenPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_MeasureWrap_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureWrap*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_MeasureWrap_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.MeasureWrap(SkiaSharp.SKRect,System.Single)">
  MeasureWrap(SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Wrap.cs/#L14"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>TODO for templated measure only visible?! and just reserve predicted scroll amount for scrolling</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual ScaledSize MeasureWrap(SKRect rectForChildrenPixels, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rectForChildrenPixels</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_OnAppeared_" data-uid="DrawnUi.Draw.SkiaLayout.OnAppeared*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnAppeared" data-uid="DrawnUi.Draw.SkiaLayout.OnAppeared">
  OnAppeared()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1595"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This event can sometimes be called without prior OnAppearing</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnAppeared()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnAppearing_" data-uid="DrawnUi.Draw.SkiaLayout.OnAppearing*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnAppearing" data-uid="DrawnUi.Draw.SkiaLayout.OnAppearing">
  OnAppearing()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1587"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This can sometimes be omitted,</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnAppearing()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnBeforeDrawingVisibleChildren_" data-uid="DrawnUi.Draw.SkiaLayout.OnBeforeDrawingVisibleChildren*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnBeforeDrawingVisibleChildren_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_LayoutStructure_System_Collections_Generic_List_DrawnUi_Draw_ControlInStack__" data-uid="DrawnUi.Draw.SkiaLayout.OnBeforeDrawingVisibleChildren(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.LayoutStructure,System.Collections.Generic.List{DrawnUi.Draw.ControlInStack})">
  OnBeforeDrawingVisibleChildren(DrawingContext, LayoutStructure, List&lt;ControlInStack&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs/#L1727"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Can be called by some layouts after they calculated the list of visible children to be drawn, but have not drawn them yet</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnBeforeDrawingVisibleChildren(DrawingContext ctx, LayoutStructure structure, List&lt;ControlInStack&gt; visibleElements)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>ctx</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
    <dt><code>structure</code> <a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></dt>
    <dd></dd>
    <dt><code>visibleElements</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.ControlInStack.html">ControlInStack</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_OnChildrenChanged_" data-uid="DrawnUi.Draw.SkiaLayout.OnChildrenChanged*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnChildrenChanged" data-uid="DrawnUi.Draw.SkiaLayout.OnChildrenChanged">
  OnChildrenChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L685"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Happens when child was added or removed, will call Invalidate() in base</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void OnChildrenChanged()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnDisappeared_" data-uid="DrawnUi.Draw.SkiaLayout.OnDisappeared*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnDisappeared" data-uid="DrawnUi.Draw.SkiaLayout.OnDisappeared">
  OnDisappeared()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1599"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnDisappeared()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnDisappearing_" data-uid="DrawnUi.Draw.SkiaLayout.OnDisappearing*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnDisappearing" data-uid="DrawnUi.Draw.SkiaLayout.OnDisappearing">
  OnDisappearing()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1591"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnDisappearing()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnDisposing_" data-uid="DrawnUi.Draw.SkiaLayout.OnDisposing*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnDisposing" data-uid="DrawnUi.Draw.SkiaLayout.OnDisposing">
  OnDisposing()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1139"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Base performs some cleanup actions with Superview</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void OnDisposing()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnFirstDrawn_" data-uid="DrawnUi.Draw.SkiaLayout.OnFirstDrawn*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnFirstDrawn" data-uid="DrawnUi.Draw.SkiaLayout.OnFirstDrawn">
  OnFirstDrawn()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Wrap.cs/#L22"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnFirstDrawn()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnFocusChanged_" data-uid="DrawnUi.Draw.SkiaLayout.OnFocusChanged*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnFocusChanged_System_Boolean_" data-uid="DrawnUi.Draw.SkiaLayout.OnFocusChanged(System.Boolean)">
  OnFocusChanged(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L264"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This will be called only for views registered at Superview.FocusedChild.
The view must return true of false to indicate if it accepts focus.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual bool OnFocusChanged(bool focus)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>focus</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_OnItemSourceChanged_" data-uid="DrawnUi.Draw.SkiaLayout.OnItemSourceChanged*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnItemSourceChanged" data-uid="DrawnUi.Draw.SkiaLayout.OnItemSourceChanged">
  OnItemSourceChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1425"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnItemSourceChanged()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnItemTemplateChanged_" data-uid="DrawnUi.Draw.SkiaLayout.OnItemTemplateChanged*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnItemTemplateChanged" data-uid="DrawnUi.Draw.SkiaLayout.OnItemTemplateChanged">
  OnItemTemplateChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1417"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void OnItemTemplateChanged()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnLayoutReady_" data-uid="DrawnUi.Draw.SkiaLayout.OnLayoutReady*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnLayoutReady" data-uid="DrawnUi.Draw.SkiaLayout.OnLayoutReady">
  OnLayoutReady()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1582"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Layout was changed with dimensions above zero. Rather a helper method, can you more generic OnLayoutChanged().</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnLayoutReady()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnLoaded_" data-uid="DrawnUi.Draw.SkiaLayout.OnLoaded*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnLoaded" data-uid="DrawnUi.Draw.SkiaLayout.OnLoaded">
  OnLoaded()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1603"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>IInsideViewport interface: loaded is called when the view is created, but not yet visible</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnLoaded()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnMeasured_" data-uid="DrawnUi.Draw.SkiaLayout.OnMeasured*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnMeasured" data-uid="DrawnUi.Draw.SkiaLayout.OnMeasured">
  OnMeasured()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L593"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnMeasured()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnMeasuring_" data-uid="DrawnUi.Draw.SkiaLayout.OnMeasuring*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnMeasuring_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.OnMeasuring(System.Single,System.Single,System.Single)">
  OnMeasuring(float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L938"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>If you call this while measurement is in process (IsMeasuring==True) will return last measured value.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override ScaledSize OnMeasuring(float widthConstraint, float heightConstraint, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>widthConstraint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>heightConstraint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_OnPrintDebug_" data-uid="DrawnUi.Draw.SkiaLayout.OnPrintDebug*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnPrintDebug" data-uid="DrawnUi.Draw.SkiaLayout.OnPrintDebug">
  OnPrintDebug()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1560"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Enhanced debug printing</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void OnPrintDebug()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnTemplatesAvailable_" data-uid="DrawnUi.Draw.SkiaLayout.OnTemplatesAvailable*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnTemplatesAvailable" data-uid="DrawnUi.Draw.SkiaLayout.OnTemplatesAvailable">
  OnTemplatesAvailable()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L100"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Will be called by views adapter upot succsessfull execution of InitializeTemplates.
When using InitializeTemplatesInBackground this is your callbacl to wait for.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnTemplatesAvailable()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_OnViewportWasChanged_" data-uid="DrawnUi.Draw.SkiaLayout.OnViewportWasChanged*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_OnViewportWasChanged_DrawnUi_Draw_ScaledRect_" data-uid="DrawnUi.Draw.SkiaLayout.OnViewportWasChanged(DrawnUi.Draw.ScaledRect)">
  OnViewportWasChanged(ScaledRect)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L529"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnViewportWasChanged(ScaledRect viewport)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>viewport</code> <a class="xref" href="DrawnUi.Draw.ScaledRect.html">ScaledRect</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_Paint_" data-uid="DrawnUi.Draw.SkiaLayout.Paint*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_Paint_DrawnUi_Draw_DrawingContext_" data-uid="DrawnUi.Draw.SkiaLayout.Paint(DrawnUi.Draw.DrawingContext)">
  Paint(DrawingContext)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1074"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This is the main drawing routine you should override to draw something.
Base one paints background color inside DrawingRect that was defined by Arrange inside base.Draw.
Pass arguments if you want to use some time-frozen data for painting at any time from any thread..</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void Paint(DrawingContext ctx)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>ctx</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_PreArrange_" data-uid="DrawnUi.Draw.SkiaLayout.PreArrange*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_PreArrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.PreArrange(SkiaSharp.SKRect,System.Single,System.Single,System.Single)">
  PreArrange(SKRect, float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L13"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Returns false if should not render</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool PreArrange(SKRect destination, float widthRequest, float heightRequest, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>destination</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>widthRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>heightRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_PropagateVisibilityChanged_" data-uid="DrawnUi.Draw.SkiaLayout.PropagateVisibilityChanged*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_PropagateVisibilityChanged_System_Boolean_" data-uid="DrawnUi.Draw.SkiaLayout.PropagateVisibilityChanged(System.Boolean)">
  PropagateVisibilityChanged(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Wrap.cs/#L36"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void PropagateVisibilityChanged(bool newvalue)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>newvalue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_RefreshAllViews_" data-uid="DrawnUi.Draw.SkiaLayout.RefreshAllViews*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_RefreshAllViews" data-uid="DrawnUi.Draw.SkiaLayout.RefreshAllViews">
  RefreshAllViews()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1513"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Force a full refresh of all cached views (useful for debugging)</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RefreshAllViews()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_ReportHotreloadChildAdded_" data-uid="DrawnUi.Draw.SkiaLayout.ReportHotreloadChildAdded*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ReportHotreloadChildAdded_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.SkiaLayout.ReportHotreloadChildAdded(DrawnUi.Draw.SkiaControl)">
  ReportHotreloadChildAdded(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Maui.cs/#L45"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void ReportHotreloadChildAdded(SkiaControl child)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>child</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_ReportHotreloadChildRemoved_" data-uid="DrawnUi.Draw.SkiaLayout.ReportHotreloadChildRemoved*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ReportHotreloadChildRemoved_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.SkiaLayout.ReportHotreloadChildRemoved(DrawnUi.Draw.SkiaControl)">
  ReportHotreloadChildRemoved(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Maui.cs/#L16"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void ReportHotreloadChildRemoved(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_ResetScroll_" data-uid="DrawnUi.Draw.SkiaLayout.ResetScroll*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_ResetScroll" data-uid="DrawnUi.Draw.SkiaLayout.ResetScroll">
  ResetScroll()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L1450"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ResetScroll()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_SetChildren_" data-uid="DrawnUi.Draw.SkiaLayout.SetChildren*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__" data-uid="DrawnUi.Draw.SkiaLayout.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})">
  SetChildren(IEnumerable&lt;SkiaControl&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L585"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void SetChildren(IEnumerable&lt;SkiaControl&gt; views)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>views</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_SetColumn_" data-uid="DrawnUi.Draw.SkiaLayout.SetColumn*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SetColumn_Microsoft_Maui_Controls_BindableObject_System_Int32_" data-uid="DrawnUi.Draw.SkiaLayout.SetColumn(Microsoft.Maui.Controls.BindableObject,System.Int32)">
  SetColumn(BindableObject, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L174"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetColumn(BindableObject bindable, int value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_SetColumnSpan_" data-uid="DrawnUi.Draw.SkiaLayout.SetColumnSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SetColumnSpan_Microsoft_Maui_Controls_BindableObject_System_Int32_" data-uid="DrawnUi.Draw.SkiaLayout.SetColumnSpan(Microsoft.Maui.Controls.BindableObject,System.Int32)">
  SetColumnSpan(BindableObject, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L179"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetColumnSpan(BindableObject bindable, int value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_SetMeasured_" data-uid="DrawnUi.Draw.SkiaLayout.SetMeasured*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SetMeasured_System_Single_System_Single_System_Boolean_System_Boolean_System_Single_" data-uid="DrawnUi.Draw.SkiaLayout.SetMeasured(System.Single,System.Single,System.Boolean,System.Boolean,System.Single)">
  SetMeasured(float, float, bool, bool, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L107"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Parameters in PIXELS. sets IsLayoutDirty = true;</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override ScaledSize SetMeasured(float width, float height, bool widthCut, bool heightCut, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>width</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>height</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>widthCut</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>heightCut</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_SetRow_" data-uid="DrawnUi.Draw.SkiaLayout.SetRow*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SetRow_Microsoft_Maui_Controls_BindableObject_System_Int32_" data-uid="DrawnUi.Draw.SkiaLayout.SetRow(Microsoft.Maui.Controls.BindableObject,System.Int32)">
  SetRow(BindableObject, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L184"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetRow(BindableObject bindable, int value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_SetRowSpan_" data-uid="DrawnUi.Draw.SkiaLayout.SetRowSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SetRowSpan_Microsoft_Maui_Controls_BindableObject_System_Int32_" data-uid="DrawnUi.Draw.SkiaLayout.SetRowSpan(Microsoft.Maui.Controls.BindableObject,System.Int32)">
  SetRowSpan(BindableObject, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L189"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetRowSpan(BindableObject bindable, int value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_SetupViews_" data-uid="DrawnUi.Draw.SkiaLayout.SetupViews*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SetupViews" data-uid="DrawnUi.Draw.SkiaLayout.SetupViews">
  SetupViews()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L375"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void SetupViews()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_UpdateRowColumnBindingContexts_" data-uid="DrawnUi.Draw.SkiaLayout.UpdateRowColumnBindingContexts*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_UpdateRowColumnBindingContexts" data-uid="DrawnUi.Draw.SkiaLayout.UpdateRowColumnBindingContexts">
  UpdateRowColumnBindingContexts()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L354"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void UpdateRowColumnBindingContexts()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaLayout_UpdateSizeChangedHandlers_" data-uid="DrawnUi.Draw.SkiaLayout.UpdateSizeChangedHandlers*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_UpdateSizeChangedHandlers_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_" data-uid="DrawnUi.Draw.SkiaLayout.UpdateSizeChangedHandlers(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)">
  UpdateSizeChangedHandlers(BindableObject, object, object)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.cs/#L310"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected static void UpdateSizeChangedHandlers(BindableObject bindable, object oldValue, object newValue)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>bindable</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>oldValue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
    <dt><code>newValue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="events">Events
</h2>



  <h3 id="DrawnUi_Draw_SkiaLayout_IsEmptyChanged" data-uid="DrawnUi.Draw.SkiaLayout.IsEmptyChanged">
  IsEmptyChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.cs/#L409"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;bool&gt; IsEmptyChanged</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a>&gt;</dt>
    <dd></dd>
  </dl>








</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Wrap.cs/#L5" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
