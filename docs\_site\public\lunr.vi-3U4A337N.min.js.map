{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.vi.js"], "sourcesContent": ["/*!\n * Lunr languages, `Vietnamese` language\n * https://github.com/MihaiValentin/lunr-languages\n *\n * Copyright 2017, Keerati Thiwan<PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.vi = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.vi.stopWordFilter,\n        lunr.vi.trimmer\n      );\n    };\n\n    /* lunr trimmer function */\n    lunr.vi.wordCharacters = \"[\" +\n      \"A-Za-z\" +\n      \"\\u0300\\u0350\" + // dấu huyền\n      \"\\u0301\\u0351\" + // dấu sắc\n      \"\\u0309\" + // dấu hỏi\n      \"\\u0323\" + // dấu nặng\n      \"\\u0303\\u0343\" + // dấu ngã\n      \"\\u00C2\\u00E2\" + // Â\n      \"\\u00CA\\u00EA\" + // Ê\n      \"\\u00D4\\u00F4\" + // Ô\n      \"\\u0102-\\u0103\" + // Ă\n      \"\\u0110-\\u0111\" + // Đ\n      \"\\u01A0-\\u01A1\" + // Ơ\n      \"\\u01AF-\\u01B0\" + // Ư\n      \"]\";\n    lunr.vi.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.vi.wordCharacters);\n    lunr.Pipeline.registerFunction(lunr.vi.trimmer, 'trimmer-vi');\n    lunr.vi.stopWordFilter = lunr.generateStopWordFilter('là cái nhưng mà'.split(' '));\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,CACF,EAGAA,EAAK,GAAG,eAAiB,uIAezBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAC5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAC5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,gCAAkB,MAAM,GAAG,CAAC,CACnF,CACF,CAAC", "names": ["require_lunr_vi", "__commonJSMin", "exports", "module", "root", "factory", "lunr"]}