import{a as T}from"./chunk-OSRY5VT3.min.js";var Y=T((b,k)=>{(function(n,e){typeof define=="function"&&define.amd?define(e):typeof b=="object"?k.exports=e():e()(n.lunr)})(b,function(){return function(n){if(typeof n>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof n.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");n.it=function(){this.pipeline.reset(),this.pipeline.add(n.it.trimmer,n.it.stopWordFilter,n.it.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(n.it.stemmer))},n.it.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",n.it.trimmer=n.trimmerSupport.generateTrimmer(n.it.wordCharacters),n.Pipeline.registerFunction(n.it.trimmer,"trimmer-it"),n.it.stemmer=function(){var e=n.stemmerSupport.Among,F=n.stemmerSupport.SnowballProgram,u=new function(){var m=[new e("",-1,7),new e("qu",0,6),new e("\xE1",0,1),new e("\xE9",0,2),new e("\xED",0,3),new e("\xF3",0,4),new e("\xFA",0,5)],A=[new e("",-1,3),new e("I",0,1),new e("U",0,2)],q=[new e("la",-1,-1),new e("cela",0,-1),new e("gliela",0,-1),new e("mela",0,-1),new e("tela",0,-1),new e("vela",0,-1),new e("le",-1,-1),new e("cele",6,-1),new e("gliele",6,-1),new e("mele",6,-1),new e("tele",6,-1),new e("vele",6,-1),new e("ne",-1,-1),new e("cene",12,-1),new e("gliene",12,-1),new e("mene",12,-1),new e("sene",12,-1),new e("tene",12,-1),new e("vene",12,-1),new e("ci",-1,-1),new e("li",-1,-1),new e("celi",20,-1),new e("glieli",20,-1),new e("meli",20,-1),new e("teli",20,-1),new e("veli",20,-1),new e("gli",20,-1),new e("mi",-1,-1),new e("si",-1,-1),new e("ti",-1,-1),new e("vi",-1,-1),new e("lo",-1,-1),new e("celo",31,-1),new e("glielo",31,-1),new e("melo",31,-1),new e("telo",31,-1),new e("velo",31,-1)],E=[new e("ando",-1,1),new e("endo",-1,1),new e("ar",-1,2),new e("er",-1,2),new e("ir",-1,2)],C=[new e("ic",-1,-1),new e("abil",-1,-1),new e("os",-1,-1),new e("iv",-1,1)],B=[new e("ic",-1,1),new e("abil",-1,1),new e("iv",-1,1)],D=[new e("ica",-1,1),new e("logia",-1,3),new e("osa",-1,1),new e("ista",-1,1),new e("iva",-1,9),new e("anza",-1,1),new e("enza",-1,5),new e("ice",-1,1),new e("atrice",7,1),new e("iche",-1,1),new e("logie",-1,3),new e("abile",-1,1),new e("ibile",-1,1),new e("usione",-1,4),new e("azione",-1,2),new e("uzione",-1,4),new e("atore",-1,2),new e("ose",-1,1),new e("ante",-1,1),new e("mente",-1,1),new e("amente",19,7),new e("iste",-1,1),new e("ive",-1,9),new e("anze",-1,1),new e("enze",-1,5),new e("ici",-1,1),new e("atrici",25,1),new e("ichi",-1,1),new e("abili",-1,1),new e("ibili",-1,1),new e("ismi",-1,1),new e("usioni",-1,4),new e("azioni",-1,2),new e("uzioni",-1,4),new e("atori",-1,2),new e("osi",-1,1),new e("anti",-1,1),new e("amenti",-1,6),new e("imenti",-1,6),new e("isti",-1,1),new e("ivi",-1,9),new e("ico",-1,1),new e("ismo",-1,1),new e("oso",-1,1),new e("amento",-1,6),new e("imento",-1,6),new e("ivo",-1,9),new e("it\xE0",-1,8),new e("ist\xE0",-1,1),new e("ist\xE8",-1,1),new e("ist\xEC",-1,1)],x=[new e("isca",-1,1),new e("enda",-1,1),new e("ata",-1,1),new e("ita",-1,1),new e("uta",-1,1),new e("ava",-1,1),new e("eva",-1,1),new e("iva",-1,1),new e("erebbe",-1,1),new e("irebbe",-1,1),new e("isce",-1,1),new e("ende",-1,1),new e("are",-1,1),new e("ere",-1,1),new e("ire",-1,1),new e("asse",-1,1),new e("ate",-1,1),new e("avate",16,1),new e("evate",16,1),new e("ivate",16,1),new e("ete",-1,1),new e("erete",20,1),new e("irete",20,1),new e("ite",-1,1),new e("ereste",-1,1),new e("ireste",-1,1),new e("ute",-1,1),new e("erai",-1,1),new e("irai",-1,1),new e("isci",-1,1),new e("endi",-1,1),new e("erei",-1,1),new e("irei",-1,1),new e("assi",-1,1),new e("ati",-1,1),new e("iti",-1,1),new e("eresti",-1,1),new e("iresti",-1,1),new e("uti",-1,1),new e("avi",-1,1),new e("evi",-1,1),new e("ivi",-1,1),new e("isco",-1,1),new e("ando",-1,1),new e("endo",-1,1),new e("Yamo",-1,1),new e("iamo",-1,1),new e("avamo",-1,1),new e("evamo",-1,1),new e("ivamo",-1,1),new e("eremo",-1,1),new e("iremo",-1,1),new e("assimo",-1,1),new e("ammo",-1,1),new e("emmo",-1,1),new e("eremmo",54,1),new e("iremmo",54,1),new e("immo",-1,1),new e("ano",-1,1),new e("iscano",58,1),new e("avano",58,1),new e("evano",58,1),new e("ivano",58,1),new e("eranno",-1,1),new e("iranno",-1,1),new e("ono",-1,1),new e("iscono",65,1),new e("arono",65,1),new e("erono",65,1),new e("irono",65,1),new e("erebbero",-1,1),new e("irebbero",-1,1),new e("assero",-1,1),new e("essero",-1,1),new e("issero",-1,1),new e("ato",-1,1),new e("ito",-1,1),new e("uto",-1,1),new e("avo",-1,1),new e("evo",-1,1),new e("ivo",-1,1),new e("ar",-1,1),new e("ir",-1,1),new e("er\xE0",-1,1),new e("ir\xE0",-1,1),new e("er\xF2",-1,1),new e("ir\xF2",-1,1)],a=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,128,128,8,2,1],P=[17,65,0,0,0,0,0,0,0,0,0,0,0,0,0,128,128,8,2],z=[17],_,v,o,r=new F;this.setCurrent=function(i){r.setCurrent(i)},this.getCurrent=function(){return r.getCurrent()};function d(i,t,l){return r.eq_s(1,i)&&(r.ket=r.cursor,r.in_grouping(a,97,249))?(r.slice_from(t),r.cursor=l,!0):!1}function S(){for(var i,t=r.cursor,l,f,h;;){if(r.bra=r.cursor,i=r.find_among(m,7),i)switch(r.ket=r.cursor,i){case 1:r.slice_from("\xE0");continue;case 2:r.slice_from("\xE8");continue;case 3:r.slice_from("\xEC");continue;case 4:r.slice_from("\xF2");continue;case 5:r.slice_from("\xF9");continue;case 6:r.slice_from("qU");continue;case 7:if(r.cursor>=r.limit)break;r.cursor++;continue}break}for(r.cursor=t;;)for(l=r.cursor;f=r.cursor,!(r.in_grouping(a,97,249)&&(r.bra=r.cursor,h=r.cursor,d("u","U",f)||(r.cursor=h,d("i","I",f))));){if(r.cursor=f,r.cursor>=r.limit){r.cursor=l;return}r.cursor++}}function g(i){if(r.cursor=i,!r.in_grouping(a,97,249))return!1;for(;!r.out_grouping(a,97,249);){if(r.cursor>=r.limit)return!1;r.cursor++}return!0}function I(){if(r.in_grouping(a,97,249)){var i=r.cursor;if(r.out_grouping(a,97,249)){for(;!r.in_grouping(a,97,249);){if(r.cursor>=r.limit)return g(i);r.cursor++}return!0}return g(i)}return!1}function W(){var i=r.cursor,t;if(!I()){if(r.cursor=i,!r.out_grouping(a,97,249))return;if(t=r.cursor,r.out_grouping(a,97,249)){for(;!r.in_grouping(a,97,249);){if(r.cursor>=r.limit){r.cursor=t,r.in_grouping(a,97,249)&&r.cursor<r.limit&&r.cursor++;return}r.cursor++}o=r.cursor;return}if(r.cursor=t,!r.in_grouping(a,97,249)||r.cursor>=r.limit)return;r.cursor++}o=r.cursor}function p(){for(;!r.in_grouping(a,97,249);){if(r.cursor>=r.limit)return!1;r.cursor++}for(;!r.out_grouping(a,97,249);){if(r.cursor>=r.limit)return!1;r.cursor++}return!0}function L(){var i=r.cursor;o=r.limit,v=o,_=o,W(),r.cursor=i,p()&&(v=r.cursor,p()&&(_=r.cursor))}function y(){for(var i;r.bra=r.cursor,i=r.find_among(A,3),!!i;)switch(r.ket=r.cursor,i){case 1:r.slice_from("i");break;case 2:r.slice_from("u");break;case 3:if(r.cursor>=r.limit)return;r.cursor++;break}}function c(){return o<=r.cursor}function R(){return v<=r.cursor}function s(){return _<=r.cursor}function U(){var i;if(r.ket=r.cursor,r.find_among_b(q,37)&&(r.bra=r.cursor,i=r.find_among_b(E,5),i&&c()))switch(i){case 1:r.slice_del();break;case 2:r.slice_from("e");break}}function V(){var i;if(r.ket=r.cursor,i=r.find_among_b(D,51),!i)return!1;switch(r.bra=r.cursor,i){case 1:if(!s())return!1;r.slice_del();break;case 2:if(!s())return!1;r.slice_del(),r.ket=r.cursor,r.eq_s_b(2,"ic")&&(r.bra=r.cursor,s()&&r.slice_del());break;case 3:if(!s())return!1;r.slice_from("log");break;case 4:if(!s())return!1;r.slice_from("u");break;case 5:if(!s())return!1;r.slice_from("ente");break;case 6:if(!c())return!1;r.slice_del();break;case 7:if(!R())return!1;r.slice_del(),r.ket=r.cursor,i=r.find_among_b(C,4),i&&(r.bra=r.cursor,s()&&(r.slice_del(),i==1&&(r.ket=r.cursor,r.eq_s_b(2,"at")&&(r.bra=r.cursor,s()&&r.slice_del()))));break;case 8:if(!s())return!1;r.slice_del(),r.ket=r.cursor,i=r.find_among_b(B,3),i&&(r.bra=r.cursor,i==1&&s()&&r.slice_del());break;case 9:if(!s())return!1;r.slice_del(),r.ket=r.cursor,r.eq_s_b(2,"at")&&(r.bra=r.cursor,s()&&(r.slice_del(),r.ket=r.cursor,r.eq_s_b(2,"ic")&&(r.bra=r.cursor,s()&&r.slice_del())));break}return!0}function j(){var i,t;r.cursor>=o&&(t=r.limit_backward,r.limit_backward=o,r.ket=r.cursor,i=r.find_among_b(x,87),i&&(r.bra=r.cursor,i==1&&r.slice_del()),r.limit_backward=t)}function G(){var i=r.limit-r.cursor;if(r.ket=r.cursor,r.in_grouping_b(P,97,242)&&(r.bra=r.cursor,c()&&(r.slice_del(),r.ket=r.cursor,r.eq_s_b(1,"i")&&(r.bra=r.cursor,c())))){r.slice_del();return}r.cursor=r.limit-i}function O(){G(),r.ket=r.cursor,r.eq_s_b(1,"h")&&(r.bra=r.cursor,r.in_grouping_b(z,99,103)&&c()&&r.slice_del())}this.stem=function(){var i=r.cursor;return S(),r.cursor=i,L(),r.limit_backward=i,r.cursor=r.limit,U(),r.cursor=r.limit,V()||(r.cursor=r.limit,j()),r.cursor=r.limit,O(),r.cursor=r.limit_backward,y(),!0}};return function(w){return typeof w.update=="function"?w.update(function(m){return u.setCurrent(m),u.stem(),u.getCurrent()}):(u.setCurrent(w),u.stem(),u.getCurrent())}}(),n.Pipeline.registerFunction(n.it.stemmer,"stemmer-it"),n.it.stopWordFilter=n.generateStopWordFilter("a abbia abbiamo abbiano abbiate ad agl agli ai al all alla alle allo anche avemmo avendo avesse avessero avessi avessimo aveste avesti avete aveva avevamo avevano avevate avevi avevo avrai avranno avrebbe avrebbero avrei avremmo avremo avreste avresti avrete avr\xE0 avr\xF2 avuta avute avuti avuto c che chi ci coi col come con contro cui da dagl dagli dai dal dall dalla dalle dallo degl degli dei del dell della delle dello di dov dove e ebbe ebbero ebbi ed era erano eravamo eravate eri ero essendo faccia facciamo facciano facciate faccio facemmo facendo facesse facessero facessi facessimo faceste facesti faceva facevamo facevano facevate facevi facevo fai fanno farai faranno farebbe farebbero farei faremmo faremo fareste faresti farete far\xE0 far\xF2 fece fecero feci fosse fossero fossi fossimo foste fosti fu fui fummo furono gli ha hai hanno ho i il in io l la le lei li lo loro lui ma mi mia mie miei mio ne negl negli nei nel nell nella nelle nello noi non nostra nostre nostri nostro o per perch\xE9 pi\xF9 quale quanta quante quanti quanto quella quelle quelli quello questa queste questi questo sarai saranno sarebbe sarebbero sarei saremmo saremo sareste saresti sarete sar\xE0 sar\xF2 se sei si sia siamo siano siate siete sono sta stai stando stanno starai staranno starebbe starebbero starei staremmo staremo stareste staresti starete star\xE0 star\xF2 stava stavamo stavano stavate stavi stavo stemmo stesse stessero stessi stessimo steste stesti stette stettero stetti stia stiamo stiano stiate sto su sua sue sugl sugli sui sul sull sulla sulle sullo suo suoi ti tra tu tua tue tuo tuoi tutti tutto un una uno vi voi vostra vostre vostri vostro \xE8".split(" ")),n.Pipeline.registerFunction(n.it.stopWordFilter,"stopWordFilter-it")}})});export default Y();
/*! Bundled license information:

lunr-languages/lunr.it.js:
  (*!
   * Lunr languages, `Italian` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.it-VQNLJLPR.min.js.map
