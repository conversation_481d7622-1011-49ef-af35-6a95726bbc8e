<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Enum <PERSON> | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Enum <PERSON> | DrawnUi Documentation ">
      
      <meta name="description" content="These are platform-independent. They correspond to JavaScript keys.">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_MauiKey.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.MauiKey%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.MauiKey">




  <h1 id="DrawnUi_Draw_MauiKey" data-uid="DrawnUi.Draw.MauiKey" class="text-break">
Enum MauiKey  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Keyboard/MauiKeys.cs/#L6"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>These are platform-independent. They correspond to JavaScript keys.</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public enum MauiKey</code></pre>
  </div>








  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>

  <h2 id="fields">Fields
</h2>
  <dl class="parameters">
    <dt id="DrawnUi_Draw_MauiKey_AltLeft"><code>AltLeft = 8</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_AltRight"><code>AltRight = 9</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ArrowDown"><code>ArrowDown = 22</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ArrowLeft"><code>ArrowLeft = 19</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ArrowRight"><code>ArrowRight = 21</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ArrowUp"><code>ArrowUp = 20</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_AudioVolumeDown"><code>AudioVolumeDown = 95</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_AudioVolumeMute"><code>AudioVolumeMute = 94</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_AudioVolumeUp"><code>AudioVolumeUp = 96</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Backquote"><code>Backquote = 106</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Backslash"><code>Backslash = 108</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Backspace"><code>Backspace = 1</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_BracketLeft"><code>BracketLeft = 107</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_BracketRight"><code>BracketRight = 109</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_CapsLock"><code>CapsLock = 11</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Comma"><code>Comma = 102</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ContextMenu"><code>ContextMenu = 64</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ControlLeft"><code>ControlLeft = 6</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ControlRight"><code>ControlRight = 7</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Delete"><code>Delete = 25</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit0"><code>Digit0 = 26</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit1"><code>Digit1 = 27</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit2"><code>Digit2 = 28</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit3"><code>Digit3 = 29</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit4"><code>Digit4 = 30</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit5"><code>Digit5 = 31</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit6"><code>Digit6 = 32</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit7"><code>Digit7 = 33</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit8"><code>Digit8 = 34</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Digit9"><code>Digit9 = 35</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_End"><code>End = 16</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Enter"><code>Enter = 3</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Equal"><code>Equal = 101</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Escape"><code>Escape = 12</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F1"><code>F1 = 80</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F10"><code>F10 = 89</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F11"><code>F11 = 90</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F12"><code>F12 = 91</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F2"><code>F2 = 81</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F3"><code>F3 = 82</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F4"><code>F4 = 83</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F5"><code>F5 = 84</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F6"><code>F6 = 85</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F7"><code>F7 = 86</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F8"><code>F8 = 87</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_F9"><code>F9 = 88</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Home"><code>Home = 17</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Insert"><code>Insert = 24</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_IntBackslash"><code>IntBackslash = 18</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyA"><code>KeyA = 36</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyB"><code>KeyB = 37</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyC"><code>KeyC = 38</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyD"><code>KeyD = 39</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyE"><code>KeyE = 40</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyF"><code>KeyF = 41</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyG"><code>KeyG = 42</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyH"><code>KeyH = 43</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyI"><code>KeyI = 44</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyJ"><code>KeyJ = 45</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyK"><code>KeyK = 46</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyL"><code>KeyL = 47</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyM"><code>KeyM = 48</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyN"><code>KeyN = 49</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyO"><code>KeyO = 50</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyP"><code>KeyP = 51</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyQ"><code>KeyQ = 52</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyR"><code>KeyR = 53</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyS"><code>KeyS = 54</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyT"><code>KeyT = 55</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyU"><code>KeyU = 56</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyV"><code>KeyV = 57</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyW"><code>KeyW = 58</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyX"><code>KeyX = 59</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyY"><code>KeyY = 60</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_KeyZ"><code>KeyZ = 61</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_LaunchApplication1"><code>LaunchApplication1 = 98</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_LaunchApplication2"><code>LaunchApplication2 = 99</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_LaunchMediaPlayer"><code>LaunchMediaPlayer = 97</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_MetaLeft"><code>MetaLeft = 62</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_MetaRight"><code>MetaRight = 63</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Minus"><code>Minus = 103</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_NumLock"><code>NumLock = 92</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad0"><code>Numpad0 = 65</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad1"><code>Numpad1 = 66</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad2"><code>Numpad2 = 67</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad3"><code>Numpad3 = 68</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad4"><code>Numpad4 = 69</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad5"><code>Numpad5 = 70</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad6"><code>Numpad6 = 71</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad7"><code>Numpad7 = 72</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad8"><code>Numpad8 = 73</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Numpad9"><code>Numpad9 = 74</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_NumpadAdd"><code>NumpadAdd = 76</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_NumpadDecimal"><code>NumpadDecimal = 78</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_NumpadDivide"><code>NumpadDivide = 79</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_NumpadMultiply"><code>NumpadMultiply = 75</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_NumpadSubtract"><code>NumpadSubtract = 77</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_PageDown"><code>PageDown = 15</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_PageUp"><code>PageUp = 14</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Pause"><code>Pause = 10</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Period"><code>Period = 104</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_PrintScreen"><code>PrintScreen = 23</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Quote"><code>Quote = 110</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ScrollLock"><code>ScrollLock = 93</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Semicolon"><code>Semicolon = 100</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ShiftLeft"><code>ShiftLeft = 4</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_ShiftRight"><code>ShiftRight = 5</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Slash"><code>Slash = 105</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Space"><code>Space = 13</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Tab"><code>Tab = 2</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_MauiKey_Unknown"><code>Unknown = 0</code></dt>
  
  <dd></dd>
  </dl>



</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Keyboard/MauiKeys.cs/#L6" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
