{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.sv.js"], "sourcesContent": ["/*!\n * Lunr languages, `Swedish` language\n * https://github.com/Mihai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.sv = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.sv.trimmer,\n        lunr.sv.stopWordFilter,\n        lunr.sv.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.sv.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.sv.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.sv.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.sv.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.sv.trimmer, 'trimmer-sv');\n\n    /* lunr stemmer function */\n    lunr.sv.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function SwedishStemmer() {\n          var a_0 = [new Among(\"a\", -1, 1), new Among(\"arna\", 0, 1),\n              new Among(\"erna\", 0, 1), new Among(\"heterna\", 2, 1),\n              new Among(\"orna\", 0, 1), new Among(\"ad\", -1, 1),\n              new Among(\"e\", -1, 1), new Among(\"ade\", 6, 1),\n              new Among(\"ande\", 6, 1), new Among(\"arne\", 6, 1),\n              new Among(\"are\", 6, 1), new Among(\"aste\", 6, 1),\n              new Among(\"en\", -1, 1), new Among(\"anden\", 12, 1),\n              new Among(\"aren\", 12, 1), new Among(\"heten\", 12, 1),\n              new Among(\"ern\", -1, 1), new Among(\"ar\", -1, 1),\n              new Among(\"er\", -1, 1), new Among(\"heter\", 18, 1),\n              new Among(\"or\", -1, 1), new Among(\"s\", -1, 2),\n              new Among(\"as\", 21, 1), new Among(\"arnas\", 22, 1),\n              new Among(\"ernas\", 22, 1), new Among(\"ornas\", 22, 1),\n              new Among(\"es\", 21, 1), new Among(\"ades\", 26, 1),\n              new Among(\"andes\", 26, 1), new Among(\"ens\", 21, 1),\n              new Among(\"arens\", 29, 1), new Among(\"hetens\", 29, 1),\n              new Among(\"erns\", 21, 1), new Among(\"at\", -1, 1),\n              new Among(\"andet\", -1, 1), new Among(\"het\", -1, 1),\n              new Among(\"ast\", -1, 1)\n            ],\n            a_1 = [new Among(\"dd\", -1, -1),\n              new Among(\"gd\", -1, -1), new Among(\"nn\", -1, -1),\n              new Among(\"dt\", -1, -1), new Among(\"gt\", -1, -1),\n              new Among(\"kt\", -1, -1), new Among(\"tt\", -1, -1)\n            ],\n            a_2 = [\n              new Among(\"ig\", -1, 1), new Among(\"lig\", 0, 1),\n              new Among(\"els\", -1, 1), new Among(\"fullt\", -1, 3),\n              new Among(\"l\\u00F6st\", -1, 2)\n            ],\n            g_v = [17, 65, 16, 1, 0, 0, 0, 0,\n              0, 0, 0, 0, 0, 0, 0, 0, 24, 0, 32\n            ],\n            g_s_ending = [119, 127, 149],\n            I_x, I_p1, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function r_mark_regions() {\n            var v_1, c = sbp.cursor + 3;\n            I_p1 = sbp.limit;\n            if (0 <= c || c <= sbp.limit) {\n              I_x = c;\n              while (true) {\n                v_1 = sbp.cursor;\n                if (sbp.in_grouping(g_v, 97, 246)) {\n                  sbp.cursor = v_1;\n                  break;\n                }\n                sbp.cursor = v_1;\n                if (sbp.cursor >= sbp.limit)\n                  return;\n                sbp.cursor++;\n              }\n              while (!sbp.out_grouping(g_v, 97, 246)) {\n                if (sbp.cursor >= sbp.limit)\n                  return;\n                sbp.cursor++;\n              }\n              I_p1 = sbp.cursor;\n              if (I_p1 < I_x)\n                I_p1 = I_x;\n            }\n          }\n\n          function r_main_suffix() {\n            var among_var, v_2 = sbp.limit_backward;\n            if (sbp.cursor >= I_p1) {\n              sbp.limit_backward = I_p1;\n              sbp.cursor = sbp.limit;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_0, 37);\n              sbp.limit_backward = v_2;\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                    if (sbp.in_grouping_b(g_s_ending, 98, 121))\n                      sbp.slice_del();\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_consonant_pair() {\n            var v_1 = sbp.limit_backward;\n            if (sbp.cursor >= I_p1) {\n              sbp.limit_backward = I_p1;\n              sbp.cursor = sbp.limit;\n              if (sbp.find_among_b(a_1, 7)) {\n                sbp.cursor = sbp.limit;\n                sbp.ket = sbp.cursor;\n                if (sbp.cursor > sbp.limit_backward) {\n                  sbp.bra = --sbp.cursor;\n                  sbp.slice_del();\n                }\n              }\n              sbp.limit_backward = v_1;\n            }\n          }\n\n          function r_other_suffix() {\n            var among_var, v_2;\n            if (sbp.cursor >= I_p1) {\n              v_2 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.cursor = sbp.limit;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_2, 5);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                    sbp.slice_from(\"l\\u00F6s\");\n                    break;\n                  case 3:\n                    sbp.slice_from(\"full\");\n                    break;\n                }\n              }\n              sbp.limit_backward = v_2;\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            r_main_suffix();\n            sbp.cursor = sbp.limit;\n            r_consonant_pair();\n            sbp.cursor = sbp.limit;\n            r_other_suffix();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.sv.stemmer, 'stemmer-sv');\n\n    lunr.sv.stopWordFilter = lunr.generateStopWordFilter('alla allt att av blev bli blir blivit de dem den denna deras dess dessa det detta dig din dina ditt du där då efter ej eller en er era ert ett från för ha hade han hans har henne hennes hon honom hur här i icke ingen inom inte jag ju kan kunde man med mellan men mig min mina mitt mot mycket ni nu när någon något några och om oss på samma sedan sig sin sina sitta själv skulle som så sådan sådana sådant till under upp ut utan vad var vara varför varit varje vars vart vem vi vid vilka vilkas vilken vilket vår våra vårt än är åt över'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.sv.stopWordFilter, 'stopWordFilter-sv');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAA0B,CACjC,IAAIC,EAAM,CAAC,IAAIH,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EACpD,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,UAAW,EAAG,CAAC,EAClD,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,EAAG,CAAC,EAC5C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAC/C,IAAIA,EAAM,MAAO,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAC9C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAClD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAChD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,IAAK,GAAI,CAAC,EAC5C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAChD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACjD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACpD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC/C,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,CACxB,EACAI,EAAM,CAAC,IAAIJ,EAAM,KAAM,GAAI,EAAE,EAC3B,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,CACjD,EACAK,EAAM,CACJ,IAAIL,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,EAAG,CAAC,EAC7C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,UAAa,GAAI,CAAC,CAC9B,EACAM,EAAM,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAC7B,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,EACjC,EACAC,EAAa,CAAC,IAAK,IAAK,GAAG,EAC3BC,EAAKC,EAAMC,EAAM,IAAIT,EACvB,KAAK,WAAa,SAASU,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,GAAiB,CACxB,IAAIC,EAAKC,EAAIJ,EAAI,OAAS,EAE1B,GADAD,EAAOC,EAAI,MACP,GAAKI,GAAKA,GAAKJ,EAAI,MAAO,CAE5B,IADAF,EAAMM,IACO,CAEX,GADAD,EAAMH,EAAI,OACNA,EAAI,YAAYJ,EAAK,GAAI,GAAG,EAAG,CACjCI,EAAI,OAASG,EACb,KACF,CAEA,GADAH,EAAI,OAASG,EACTH,EAAI,QAAUA,EAAI,MACpB,OACFA,EAAI,QACN,CACA,KAAO,CAACA,EAAI,aAAaJ,EAAK,GAAI,GAAG,GAAG,CACtC,GAAII,EAAI,QAAUA,EAAI,MACpB,OACFA,EAAI,QACN,CACAD,EAAOC,EAAI,OACPD,EAAOD,IACTC,EAAOD,EACX,CACF,CAEA,SAASO,GAAgB,CACvB,IAAIC,EAAWC,EAAMP,EAAI,eACzB,GAAIA,EAAI,QAAUD,IAChBC,EAAI,eAAiBD,EACrBC,EAAI,OAASA,EAAI,MACjBA,EAAI,IAAMA,EAAI,OACdM,EAAYN,EAAI,aAAaP,EAAK,EAAE,EACpCO,EAAI,eAAiBO,EACjBD,GAEF,OADAN,EAAI,IAAMA,EAAI,OACNM,EAAW,CACjB,IAAK,GACHN,EAAI,UAAU,EACd,MACF,IAAK,GACCA,EAAI,cAAcH,EAAY,GAAI,GAAG,GACvCG,EAAI,UAAU,EAChB,KACJ,CAGN,CAEA,SAASQ,GAAmB,CAC1B,IAAIL,EAAMH,EAAI,eACVA,EAAI,QAAUD,IAChBC,EAAI,eAAiBD,EACrBC,EAAI,OAASA,EAAI,MACbA,EAAI,aAAaN,EAAK,CAAC,IACzBM,EAAI,OAASA,EAAI,MACjBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAASA,EAAI,iBACnBA,EAAI,IAAM,EAAEA,EAAI,OAChBA,EAAI,UAAU,IAGlBA,EAAI,eAAiBG,EAEzB,CAEA,SAASM,GAAiB,CACxB,IAAIH,EAAWC,EACf,GAAIP,EAAI,QAAUD,EAAM,CAMtB,GALAQ,EAAMP,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,OAASA,EAAI,MACjBA,EAAI,IAAMA,EAAI,OACdM,EAAYN,EAAI,aAAaL,EAAK,CAAC,EAC/BW,EAEF,OADAN,EAAI,IAAMA,EAAI,OACNM,EAAW,CACjB,IAAK,GACHN,EAAI,UAAU,EACd,MACF,IAAK,GACHA,EAAI,WAAW,QAAU,EACzB,MACF,IAAK,GACHA,EAAI,WAAW,MAAM,EACrB,KACJ,CAEFA,EAAI,eAAiBO,CACvB,CACF,CACA,KAAK,KAAO,UAAW,CACrB,IAAIJ,EAAMH,EAAI,OACd,OAAAE,EAAe,EACfF,EAAI,eAAiBG,EACrBH,EAAI,OAASA,EAAI,MACjBK,EAAc,EACdL,EAAI,OAASA,EAAI,MACjBQ,EAAiB,EACjBR,EAAI,OAASA,EAAI,MACjBS,EAAe,EACR,EACT,CACF,EAGF,OAAO,SAASC,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAAST,EAAM,CACjC,OAAAT,EAAG,WAAWS,CAAI,EAClBT,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWkB,CAAK,EACnBlB,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,+lBAA0hB,MAAM,GAAG,CAAC,EAEzlBA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_sv", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "g_v", "g_s_ending", "I_x", "I_p1", "sbp", "word", "r_mark_regions", "v_1", "c", "r_main_suffix", "among_var", "v_2", "r_consonant_pair", "r_other_suffix", "token"]}