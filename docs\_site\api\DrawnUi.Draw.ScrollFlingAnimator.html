<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class ScrollFlingAnimator | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class ScrollFlingAnimator | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ScrollFlingAnimator.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ScrollFlingAnimator%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.ScrollFlingAnimator">



  <h1 id="DrawnUi_Draw_ScrollFlingAnimator" data-uid="DrawnUi.Draw.ScrollFlingAnimator" class="text-break">
Class ScrollFlingAnimator  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class ScrollFlingAnimator : SkiaValueAnimator, ISkiaAnimator, IDisposable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="DrawnUi.Draw.AnimatorBase.html">AnimatorBase</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html">SkiaValueAnimator</a></div>
      <div><span class="xref">ScrollFlingAnimator</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose">SkiaValueAnimator.Dispose()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop">SkiaValueAnimator.Stop()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_">SkiaValueAnimator.RunAsync(Action, CancellationToken)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_">SkiaValueAnimator.OnRunningStateChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_">SkiaValueAnimator.Seek(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_CycleFInished">SkiaValueAnimator.CycleFInished</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Finished">SkiaValueAnimator.Finished</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning">SkiaValueAnimator.FinishedRunning()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FrameTimeInterpolator">SkiaValueAnimator.FrameTimeInterpolator</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_">SkiaValueAnimator.TickFrame(long)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Repeat">SkiaValueAnimator.Repeat</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mValue">SkiaValueAnimator.mValue</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mStartValueIsSet">SkiaValueAnimator.mStartValueIsSet</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMaxValue">SkiaValueAnimator.mMaxValue</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMinValue">SkiaValueAnimator.mMinValue</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Easing">SkiaValueAnimator.Easing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Speed">SkiaValueAnimator.Speed</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TransformReportedValue_System_Int64_">SkiaValueAnimator.TransformReportedValue(long)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Debug">SkiaValueAnimator.Debug</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds">SkiaValueAnimator.GetNanoseconds()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ElapsedMs">SkiaValueAnimator.ElapsedMs</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Progress">SkiaValueAnimator.Progress</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnUpdated">SkiaValueAnimator.OnUpdated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart">SkiaValueAnimator.ClampOnStart()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_">SkiaValueAnimator.SetValue(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_">SkiaValueAnimator.SetSpeed(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPostAnimator">AnimatorBase.IsPostAnimator</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsHiddenInViewTree">AnimatorBase.IsHiddenInViewTree</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_">AnimatorBase.Radians(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_runDelayMs">AnimatorBase.runDelayMs</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register">AnimatorBase.Register()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister">AnimatorBase.Unregister()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel">AnimatorBase.Cancel()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause">AnimatorBase.Pause()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume">AnimatorBase.Resume()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPaused">AnimatorBase.IsPaused</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStop">AnimatorBase.OnStop</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStart">AnimatorBase.OnStart</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Parent">AnimatorBase.Parent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsDeactivated">AnimatorBase.IsDeactivated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_LastFrameTimeNanos">AnimatorBase.LastFrameTimeNanos</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_StartFrameTimeNanos">AnimatorBase.StartFrameTimeNanos</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Uid">AnimatorBase.Uid</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsRunning">AnimatorBase.IsRunning</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_WasStarted">AnimatorBase.WasStarted</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_ScrollFlingAnimator__ctor_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.#ctor*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator__ctor_DrawnUi_Draw_IDrawnBase_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.#ctor(DrawnUi.Draw.IDrawnBase)">
  ScrollFlingAnimator(IDrawnBase)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L107"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ScrollFlingAnimator(IDrawnBase parent)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_ScrollFlingAnimator_CurrentVelocity_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.CurrentVelocity*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator_CurrentVelocity" data-uid="DrawnUi.Draw.ScrollFlingAnimator.CurrentVelocity">
  CurrentVelocity
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L54"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float CurrentVelocity { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ScrollFlingAnimator_Parameters_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.Parameters*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator_Parameters" data-uid="DrawnUi.Draw.ScrollFlingAnimator.Parameters">
  Parameters
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L53"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DecelerationTimingParameters Parameters { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.DecelerationTimingParameters.html">DecelerationTimingParameters</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ScrollFlingAnimator_SelfFinished_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.SelfFinished*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator_SelfFinished" data-uid="DrawnUi.Draw.ScrollFlingAnimator.SelfFinished">
  SelfFinished
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool SelfFinished { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ScrollFlingAnimator_ValueThreshold_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.ValueThreshold*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator_ValueThreshold" data-uid="DrawnUi.Draw.ScrollFlingAnimator.ValueThreshold">
  ValueThreshold
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ValueThreshold { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_ScrollFlingAnimator_InitializeWithDestination_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.InitializeWithDestination*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator_InitializeWithDestination_System_Single_System_Single_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.InitializeWithDestination(System.Single,System.Single,System.Single,System.Single,System.Single)">
  InitializeWithDestination(float, float, float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L44"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Initialize to reach a specific destination in given time</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InitializeWithDestination(float position, float target, float timeSecs, float deceleration = 0.998, float valueThreshold = 0.1)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>position</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Starting position</p>
</dd>
    <dt><code>target</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Target position</p>
</dd>
    <dt><code>timeSecs</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Duration in seconds</p>
</dd>
    <dt><code>deceleration</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Deceleration rate</p>
</dd>
    <dt><code>valueThreshold</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Stop when value change per frame is below this</p>
</dd>
  </dl>












  <a id="DrawnUi_Draw_ScrollFlingAnimator_InitializeWithVelocity_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.InitializeWithVelocity*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator_InitializeWithVelocity_System_Single_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.InitializeWithVelocity(System.Single,System.Single,System.Single,System.Single)">
  InitializeWithVelocity(float, float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L26"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Initialize with velocity and optional value threshold for early termination</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InitializeWithVelocity(float position, float velocity, float deceleration = 0.998, float valueThreshold = 1.85)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>position</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Starting position</p>
</dd>
    <dt><code>velocity</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Initial velocity</p>
</dd>
    <dt><code>deceleration</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Deceleration rate</p>
</dd>
    <dt><code>valueThreshold</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>Stop when value change per frame is below this</p>
</dd>
  </dl>












  <a id="DrawnUi_Draw_ScrollFlingAnimator_RunAsync_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.RunAsync*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator_RunAsync_System_Single_System_Single_System_Single_System_Threading_CancellationToken_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.RunAsync(System.Single,System.Single,System.Single,System.Threading.CancellationToken)">
  RunAsync(float, float, float, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L14"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task RunAsync(float position, float velocity, float deceleration = 0.998, CancellationToken cancellationToken = default)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>position</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>velocity</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>deceleration</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>cancellationToken</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_ScrollFlingAnimator_Start_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.Start*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator_Start_System_Double_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.Start(System.Double)">
  Start(double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L56"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Start(double delayMs = 0)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>delayMs</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ScrollFlingAnimator_UpdateValue_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.UpdateValue*"></a>

  <h3 id="DrawnUi_Draw_ScrollFlingAnimator_UpdateValue_System_Int64_System_Int64_" data-uid="DrawnUi.Draw.ScrollFlingAnimator.UpdateValue(System.Int64,System.Int64)">
  UpdateValue(long, long)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L63"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Update mValue using time distance between rendered frames.
Return true if anims is finished.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override bool UpdateValue(long deltaT, long deltaFromStart)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>deltaT</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
    <dt><code>deltaFromStart</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/ScrollFlingAnimator.cs/#L5" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
