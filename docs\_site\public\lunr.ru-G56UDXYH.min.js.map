{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.ru.js"], "sourcesContent": ["/*!\n * Lunr languages, `Russian` language\n * https://github.com/Mihai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.ru = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.ru.trimmer,\n        lunr.ru.stopWordFilter,\n        lunr.ru.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.ru.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.ru.wordCharacters = \"\\u0400-\\u0484\\u0487-\\u052F\\u1D2B\\u1D78\\u2DE0-\\u2DFF\\uA640-\\uA69F\\uFE2E\\uFE2F\";\n    lunr.ru.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.ru.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.ru.trimmer, 'trimmer-ru');\n\n    /* lunr stemmer function */\n    lunr.ru.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function RussianStemmer() {\n          var a_0 = [new Among(\"\\u0432\", -1, 1), new Among(\"\\u0438\\u0432\", 0, 2),\n              new Among(\"\\u044B\\u0432\", 0, 2),\n              new Among(\"\\u0432\\u0448\\u0438\", -1, 1),\n              new Among(\"\\u0438\\u0432\\u0448\\u0438\", 3, 2),\n              new Among(\"\\u044B\\u0432\\u0448\\u0438\", 3, 2),\n              new Among(\"\\u0432\\u0448\\u0438\\u0441\\u044C\", -1, 1),\n              new Among(\"\\u0438\\u0432\\u0448\\u0438\\u0441\\u044C\", 6, 2),\n              new Among(\"\\u044B\\u0432\\u0448\\u0438\\u0441\\u044C\", 6, 2)\n            ],\n            a_1 = [\n              new Among(\"\\u0435\\u0435\", -1, 1), new Among(\"\\u0438\\u0435\", -1, 1),\n              new Among(\"\\u043E\\u0435\", -1, 1), new Among(\"\\u044B\\u0435\", -1, 1),\n              new Among(\"\\u0438\\u043C\\u0438\", -1, 1),\n              new Among(\"\\u044B\\u043C\\u0438\", -1, 1),\n              new Among(\"\\u0435\\u0439\", -1, 1), new Among(\"\\u0438\\u0439\", -1, 1),\n              new Among(\"\\u043E\\u0439\", -1, 1), new Among(\"\\u044B\\u0439\", -1, 1),\n              new Among(\"\\u0435\\u043C\", -1, 1), new Among(\"\\u0438\\u043C\", -1, 1),\n              new Among(\"\\u043E\\u043C\", -1, 1), new Among(\"\\u044B\\u043C\", -1, 1),\n              new Among(\"\\u0435\\u0433\\u043E\", -1, 1),\n              new Among(\"\\u043E\\u0433\\u043E\", -1, 1),\n              new Among(\"\\u0435\\u043C\\u0443\", -1, 1),\n              new Among(\"\\u043E\\u043C\\u0443\", -1, 1),\n              new Among(\"\\u0438\\u0445\", -1, 1), new Among(\"\\u044B\\u0445\", -1, 1),\n              new Among(\"\\u0435\\u044E\", -1, 1), new Among(\"\\u043E\\u044E\", -1, 1),\n              new Among(\"\\u0443\\u044E\", -1, 1), new Among(\"\\u044E\\u044E\", -1, 1),\n              new Among(\"\\u0430\\u044F\", -1, 1), new Among(\"\\u044F\\u044F\", -1, 1)\n            ],\n            a_2 = [\n              new Among(\"\\u0435\\u043C\", -1, 1), new Among(\"\\u043D\\u043D\", -1, 1),\n              new Among(\"\\u0432\\u0448\", -1, 1),\n              new Among(\"\\u0438\\u0432\\u0448\", 2, 2),\n              new Among(\"\\u044B\\u0432\\u0448\", 2, 2), new Among(\"\\u0449\", -1, 1),\n              new Among(\"\\u044E\\u0449\", 5, 1),\n              new Among(\"\\u0443\\u044E\\u0449\", 6, 2)\n            ],\n            a_3 = [\n              new Among(\"\\u0441\\u044C\", -1, 1), new Among(\"\\u0441\\u044F\", -1, 1)\n            ],\n            a_4 = [\n              new Among(\"\\u043B\\u0430\", -1, 1),\n              new Among(\"\\u0438\\u043B\\u0430\", 0, 2),\n              new Among(\"\\u044B\\u043B\\u0430\", 0, 2),\n              new Among(\"\\u043D\\u0430\", -1, 1),\n              new Among(\"\\u0435\\u043D\\u0430\", 3, 2),\n              new Among(\"\\u0435\\u0442\\u0435\", -1, 1),\n              new Among(\"\\u0438\\u0442\\u0435\", -1, 2),\n              new Among(\"\\u0439\\u0442\\u0435\", -1, 1),\n              new Among(\"\\u0435\\u0439\\u0442\\u0435\", 7, 2),\n              new Among(\"\\u0443\\u0439\\u0442\\u0435\", 7, 2),\n              new Among(\"\\u043B\\u0438\", -1, 1),\n              new Among(\"\\u0438\\u043B\\u0438\", 10, 2),\n              new Among(\"\\u044B\\u043B\\u0438\", 10, 2), new Among(\"\\u0439\", -1, 1),\n              new Among(\"\\u0435\\u0439\", 13, 2), new Among(\"\\u0443\\u0439\", 13, 2),\n              new Among(\"\\u043B\", -1, 1), new Among(\"\\u0438\\u043B\", 16, 2),\n              new Among(\"\\u044B\\u043B\", 16, 2), new Among(\"\\u0435\\u043C\", -1, 1),\n              new Among(\"\\u0438\\u043C\", -1, 2), new Among(\"\\u044B\\u043C\", -1, 2),\n              new Among(\"\\u043D\", -1, 1), new Among(\"\\u0435\\u043D\", 22, 2),\n              new Among(\"\\u043B\\u043E\", -1, 1),\n              new Among(\"\\u0438\\u043B\\u043E\", 24, 2),\n              new Among(\"\\u044B\\u043B\\u043E\", 24, 2),\n              new Among(\"\\u043D\\u043E\", -1, 1),\n              new Among(\"\\u0435\\u043D\\u043E\", 27, 2),\n              new Among(\"\\u043D\\u043D\\u043E\", 27, 1),\n              new Among(\"\\u0435\\u0442\", -1, 1),\n              new Among(\"\\u0443\\u0435\\u0442\", 30, 2),\n              new Among(\"\\u0438\\u0442\", -1, 2), new Among(\"\\u044B\\u0442\", -1, 2),\n              new Among(\"\\u044E\\u0442\", -1, 1),\n              new Among(\"\\u0443\\u044E\\u0442\", 34, 2),\n              new Among(\"\\u044F\\u0442\", -1, 2), new Among(\"\\u043D\\u044B\", -1, 1),\n              new Among(\"\\u0435\\u043D\\u044B\", 37, 2),\n              new Among(\"\\u0442\\u044C\", -1, 1),\n              new Among(\"\\u0438\\u0442\\u044C\", 39, 2),\n              new Among(\"\\u044B\\u0442\\u044C\", 39, 2),\n              new Among(\"\\u0435\\u0448\\u044C\", -1, 1),\n              new Among(\"\\u0438\\u0448\\u044C\", -1, 2), new Among(\"\\u044E\", -1, 2),\n              new Among(\"\\u0443\\u044E\", 44, 2)\n            ],\n            a_5 = [\n              new Among(\"\\u0430\", -1, 1), new Among(\"\\u0435\\u0432\", -1, 1),\n              new Among(\"\\u043E\\u0432\", -1, 1), new Among(\"\\u0435\", -1, 1),\n              new Among(\"\\u0438\\u0435\", 3, 1), new Among(\"\\u044C\\u0435\", 3, 1),\n              new Among(\"\\u0438\", -1, 1), new Among(\"\\u0435\\u0438\", 6, 1),\n              new Among(\"\\u0438\\u0438\", 6, 1),\n              new Among(\"\\u0430\\u043C\\u0438\", 6, 1),\n              new Among(\"\\u044F\\u043C\\u0438\", 6, 1),\n              new Among(\"\\u0438\\u044F\\u043C\\u0438\", 10, 1),\n              new Among(\"\\u0439\", -1, 1), new Among(\"\\u0435\\u0439\", 12, 1),\n              new Among(\"\\u0438\\u0435\\u0439\", 13, 1),\n              new Among(\"\\u0438\\u0439\", 12, 1), new Among(\"\\u043E\\u0439\", 12, 1),\n              new Among(\"\\u0430\\u043C\", -1, 1), new Among(\"\\u0435\\u043C\", -1, 1),\n              new Among(\"\\u0438\\u0435\\u043C\", 18, 1),\n              new Among(\"\\u043E\\u043C\", -1, 1), new Among(\"\\u044F\\u043C\", -1, 1),\n              new Among(\"\\u0438\\u044F\\u043C\", 21, 1), new Among(\"\\u043E\", -1, 1),\n              new Among(\"\\u0443\", -1, 1), new Among(\"\\u0430\\u0445\", -1, 1),\n              new Among(\"\\u044F\\u0445\", -1, 1),\n              new Among(\"\\u0438\\u044F\\u0445\", 26, 1), new Among(\"\\u044B\", -1, 1),\n              new Among(\"\\u044C\", -1, 1), new Among(\"\\u044E\", -1, 1),\n              new Among(\"\\u0438\\u044E\", 30, 1), new Among(\"\\u044C\\u044E\", 30, 1),\n              new Among(\"\\u044F\", -1, 1), new Among(\"\\u0438\\u044F\", 33, 1),\n              new Among(\"\\u044C\\u044F\", 33, 1)\n            ],\n            a_6 = [\n              new Among(\"\\u043E\\u0441\\u0442\", -1, 1),\n              new Among(\"\\u043E\\u0441\\u0442\\u044C\", -1, 1)\n            ],\n            a_7 = [\n              new Among(\"\\u0435\\u0439\\u0448\\u0435\", -1, 1),\n              new Among(\"\\u043D\", -1, 2), new Among(\"\\u0435\\u0439\\u0448\", -1, 1),\n              new Among(\"\\u044C\", -1, 3)\n            ],\n            g_v = [33, 65, 8, 232],\n            I_p2, I_pV, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function habr3() {\n            while (!sbp.in_grouping(g_v, 1072, 1103)) {\n              if (sbp.cursor >= sbp.limit)\n                return false;\n              sbp.cursor++;\n            }\n            return true;\n          }\n\n          function habr4() {\n            while (!sbp.out_grouping(g_v, 1072, 1103)) {\n              if (sbp.cursor >= sbp.limit)\n                return false;\n              sbp.cursor++;\n            }\n            return true;\n          }\n\n          function r_mark_regions() {\n            I_pV = sbp.limit;\n            I_p2 = I_pV;\n            if (habr3()) {\n              I_pV = sbp.cursor;\n              if (habr4())\n                if (habr3())\n                  if (habr4())\n                    I_p2 = sbp.cursor;\n            }\n          }\n\n          function r_R2() {\n            return I_p2 <= sbp.cursor;\n          }\n\n          function habr2(a, n) {\n            var among_var, v_1;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a, n);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              switch (among_var) {\n                case 1:\n                  v_1 = sbp.limit - sbp.cursor;\n                  if (!sbp.eq_s_b(1, \"\\u0430\")) {\n                    sbp.cursor = sbp.limit - v_1;\n                    if (!sbp.eq_s_b(1, \"\\u044F\"))\n                      return false;\n                  }\n                  case 2:\n                    sbp.slice_del();\n                    break;\n              }\n              return true;\n            }\n            return false;\n          }\n\n          function r_perfective_gerund() {\n            return habr2(a_0, 9);\n          }\n\n          function habr1(a, n) {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a, n);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (among_var == 1)\n                sbp.slice_del();\n              return true;\n            }\n            return false;\n          }\n\n          function r_adjective() {\n            return habr1(a_1, 26);\n          }\n\n          function r_adjectival() {\n            var among_var;\n            if (r_adjective()) {\n              habr2(a_2, 8);\n              return true;\n            }\n            return false;\n          }\n\n          function r_reflexive() {\n            return habr1(a_3, 2);\n          }\n\n          function r_verb() {\n            return habr2(a_4, 46);\n          }\n\n          function r_noun() {\n            habr1(a_5, 36);\n          }\n\n          function r_derivational() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_6, 2);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R2() && among_var == 1)\n                sbp.slice_del();\n            }\n          }\n\n          function r_tidy_up() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_7, 4);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              switch (among_var) {\n                case 1:\n                  sbp.slice_del();\n                  sbp.ket = sbp.cursor;\n                  if (!sbp.eq_s_b(1, \"\\u043D\"))\n                    break;\n                  sbp.bra = sbp.cursor;\n                case 2:\n                  if (!sbp.eq_s_b(1, \"\\u043D\"))\n                    break;\n                case 3:\n                  sbp.slice_del();\n                  break;\n              }\n            }\n          }\n          this.stem = function() {\n            r_mark_regions();\n            sbp.cursor = sbp.limit;\n            if (sbp.cursor < I_pV)\n              return false;\n            sbp.limit_backward = I_pV;\n            if (!r_perfective_gerund()) {\n              sbp.cursor = sbp.limit;\n              if (!r_reflexive())\n                sbp.cursor = sbp.limit;\n              if (!r_adjectival()) {\n                sbp.cursor = sbp.limit;\n                if (!r_verb()) {\n                  sbp.cursor = sbp.limit;\n                  r_noun();\n                }\n              }\n            }\n            sbp.cursor = sbp.limit;\n            sbp.ket = sbp.cursor;\n            if (sbp.eq_s_b(1, \"\\u0438\")) {\n              sbp.bra = sbp.cursor;\n              sbp.slice_del();\n            } else\n              sbp.cursor = sbp.limit;\n            r_derivational();\n            sbp.cursor = sbp.limit;\n            r_tidy_up();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.ru.stemmer, 'stemmer-ru');\n\n    lunr.ru.stopWordFilter = lunr.generateStopWordFilter('алло без близко более больше будем будет будете будешь будто буду будут будь бы бывает бывь был была были было быть в важная важное важные важный вам вами вас ваш ваша ваше ваши вверх вдали вдруг ведь везде весь вниз внизу во вокруг вон восемнадцатый восемнадцать восемь восьмой вот впрочем времени время все всегда всего всем всеми всему всех всею всю всюду вся всё второй вы г где говорил говорит год года году да давно даже далеко дальше даром два двадцатый двадцать две двенадцатый двенадцать двух девятнадцатый девятнадцать девятый девять действительно дел день десятый десять для до довольно долго должно другая другие других друго другое другой е его ее ей ему если есть еще ещё ею её ж же жизнь за занят занята занято заняты затем зато зачем здесь значит и из или им именно иметь ими имя иногда их к каждая каждое каждые каждый кажется как какая какой кем когда кого ком кому конечно которая которого которой которые который которых кроме кругом кто куда лет ли лишь лучше люди м мало между меля менее меньше меня миллионов мимо мира мне много многочисленная многочисленное многочисленные многочисленный мной мною мог могут мож может можно можхо мои мой мор мочь моя моё мы на наверху над надо назад наиболее наконец нам нами нас начала наш наша наше наши не него недавно недалеко нее ней нельзя нем немного нему непрерывно нередко несколько нет нею неё ни нибудь ниже низко никогда никуда ними них ничего но ну нужно нх о об оба обычно один одиннадцатый одиннадцать однажды однако одного одной около он она они оно опять особенно от отовсюду отсюда очень первый перед по под пожалуйста позже пока пор пора после посреди потом потому почему почти прекрасно при про просто против процентов пятнадцатый пятнадцать пятый пять раз разве рано раньше рядом с сам сама сами самим самими самих само самого самой самом самому саму свое своего своей свои своих свою сеаой себе себя сегодня седьмой сейчас семнадцатый семнадцать семь сих сказал сказала сказать сколько слишком сначала снова со собой собою совсем спасибо стал суть т та так такая также такие такое такой там твой твоя твоё те тебе тебя тем теми теперь тех то тобой тобою тогда того тоже только том тому тот тою третий три тринадцатый тринадцать ту туда тут ты тысяч у уж уже уметь хорошо хотеть хоть хотя хочешь часто чаще чего человек чем чему через четвертый четыре четырнадцатый четырнадцать что чтоб чтобы чуть шестнадцатый шестнадцать шестой шесть эта эти этим этими этих это этого этой этом этому этот эту я ﻿а'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.ru.stopWordFilter, 'stopWordFilter-ru');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,+EACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAA0B,CACjC,IAAIC,EAAM,CAAC,IAAIH,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,EAAG,CAAC,EACjE,IAAIA,EAAM,eAAgB,EAAG,CAAC,EAC9B,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,2BAA4B,EAAG,CAAC,EAC1C,IAAIA,EAAM,2BAA4B,EAAG,CAAC,EAC1C,IAAIA,EAAM,iCAAkC,GAAI,CAAC,EACjD,IAAIA,EAAM,uCAAwC,EAAG,CAAC,EACtD,IAAIA,EAAM,uCAAwC,EAAG,CAAC,CACxD,EACAI,EAAM,CACJ,IAAIJ,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,CACnE,EACAK,EAAM,CACJ,IAAIL,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,EAAG,CAAC,EACpC,IAAIA,EAAM,qBAAsB,EAAG,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EAChE,IAAIA,EAAM,eAAgB,EAAG,CAAC,EAC9B,IAAIA,EAAM,qBAAsB,EAAG,CAAC,CACtC,EACAM,EAAM,CACJ,IAAIN,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,CACnE,EACAO,EAAM,CACJ,IAAIP,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,EAAG,CAAC,EACpC,IAAIA,EAAM,qBAAsB,EAAG,CAAC,EACpC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,EAAG,CAAC,EACpC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,2BAA4B,EAAG,CAAC,EAC1C,IAAIA,EAAM,2BAA4B,EAAG,CAAC,EAC1C,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC3D,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC3D,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,CACjC,EACAQ,EAAM,CACJ,IAAIR,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC3D,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EAC3D,IAAIA,EAAM,eAAgB,EAAG,CAAC,EAAG,IAAIA,EAAM,eAAgB,EAAG,CAAC,EAC/D,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,EAAG,CAAC,EAC1D,IAAIA,EAAM,eAAgB,EAAG,CAAC,EAC9B,IAAIA,EAAM,qBAAsB,EAAG,CAAC,EACpC,IAAIA,EAAM,qBAAsB,EAAG,CAAC,EACpC,IAAIA,EAAM,2BAA4B,GAAI,CAAC,EAC3C,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC3D,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACjE,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC3D,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC/B,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACjE,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAC3D,IAAIA,EAAM,eAAgB,GAAI,CAAC,CACjC,EACAS,EAAM,CACJ,IAAIT,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,2BAA4B,GAAI,CAAC,CAC7C,EACAU,EAAM,CACJ,IAAIV,EAAM,2BAA4B,GAAI,CAAC,EAC3C,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACjE,IAAIA,EAAM,SAAU,GAAI,CAAC,CAC3B,EACAW,EAAM,CAAC,GAAI,GAAI,EAAG,GAAG,EACrBC,EAAMC,EAAMC,EAAM,IAAIb,EACxB,KAAK,WAAa,SAASc,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,GAAQ,CACf,KAAO,CAACF,EAAI,YAAYH,EAAK,KAAM,IAAI,GAAG,CACxC,GAAIG,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASG,GAAQ,CACf,KAAO,CAACH,EAAI,aAAaH,EAAK,KAAM,IAAI,GAAG,CACzC,GAAIG,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASI,GAAiB,CACxBL,EAAOC,EAAI,MACXF,EAAOC,EACHG,EAAM,IACRH,EAAOC,EAAI,OACPG,EAAM,GACJD,EAAM,GACJC,EAAM,IACRL,EAAOE,EAAI,QAErB,CAEA,SAASK,GAAO,CACd,OAAOP,GAAQE,EAAI,MACrB,CAEA,SAASM,EAAMC,EAAGC,EAAG,CACnB,IAAIC,EAAWC,EAGf,GAFAV,EAAI,IAAMA,EAAI,OACdS,EAAYT,EAAI,aAAaO,EAAGC,CAAC,EAC7BC,EAAW,CAEb,OADAT,EAAI,IAAMA,EAAI,OACNS,EAAW,CACjB,IAAK,GAEH,GADAC,EAAMV,EAAI,MAAQA,EAAI,OAClB,CAACA,EAAI,OAAO,EAAG,QAAQ,IACzBA,EAAI,OAASA,EAAI,MAAQU,EACrB,CAACV,EAAI,OAAO,EAAG,QAAQ,GACzB,MAAO,GAEX,IAAK,GACHA,EAAI,UAAU,EACd,KACN,CACA,MAAO,EACT,CACA,MAAO,EACT,CAEA,SAASW,GAAsB,CAC7B,OAAOL,EAAMjB,EAAK,CAAC,CACrB,CAEA,SAASuB,EAAML,EAAGC,EAAG,CACnB,IAAIC,EAGJ,OAFAT,EAAI,IAAMA,EAAI,OACdS,EAAYT,EAAI,aAAaO,EAAGC,CAAC,EAC7BC,GACFT,EAAI,IAAMA,EAAI,OACVS,GAAa,GACfT,EAAI,UAAU,EACT,IAEF,EACT,CAEA,SAASa,GAAc,CACrB,OAAOD,EAAMtB,EAAK,EAAE,CACtB,CAEA,SAASwB,GAAe,CACtB,IAAIL,EACJ,OAAII,EAAY,GACdP,EAAMf,EAAK,CAAC,EACL,IAEF,EACT,CAEA,SAASwB,GAAc,CACrB,OAAOH,EAAMpB,EAAK,CAAC,CACrB,CAEA,SAASwB,GAAS,CAChB,OAAOV,EAAMb,EAAK,EAAE,CACtB,CAEA,SAASwB,GAAS,CAChBL,EAAMlB,EAAK,EAAE,CACf,CAEA,SAASwB,GAAiB,CACxB,IAAIT,EACJT,EAAI,IAAMA,EAAI,OACdS,EAAYT,EAAI,aAAaL,EAAK,CAAC,EAC/Bc,IACFT,EAAI,IAAMA,EAAI,OACVK,EAAK,GAAKI,GAAa,GACzBT,EAAI,UAAU,EAEpB,CAEA,SAASmB,GAAY,CACnB,IAAIV,EAGJ,GAFAT,EAAI,IAAMA,EAAI,OACdS,EAAYT,EAAI,aAAaJ,EAAK,CAAC,EAC/Ba,EAEF,OADAT,EAAI,IAAMA,EAAI,OACNS,EAAW,CACjB,IAAK,GAGH,GAFAT,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACV,CAACA,EAAI,OAAO,EAAG,QAAQ,EACzB,MACFA,EAAI,IAAMA,EAAI,OAChB,IAAK,GACH,GAAI,CAACA,EAAI,OAAO,EAAG,QAAQ,EACzB,MACJ,IAAK,GACHA,EAAI,UAAU,EACd,KACJ,CAEJ,CACA,KAAK,KAAO,UAAW,CAGrB,OAFAI,EAAe,EACfJ,EAAI,OAASA,EAAI,MACbA,EAAI,OAASD,EACR,IACTC,EAAI,eAAiBD,EAChBY,EAAoB,IACvBX,EAAI,OAASA,EAAI,MACZe,EAAY,IACff,EAAI,OAASA,EAAI,OACdc,EAAa,IAChBd,EAAI,OAASA,EAAI,MACZgB,EAAO,IACVhB,EAAI,OAASA,EAAI,MACjBiB,EAAO,KAIbjB,EAAI,OAASA,EAAI,MACjBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,QAAQ,GACxBA,EAAI,IAAMA,EAAI,OACdA,EAAI,UAAU,GAEdA,EAAI,OAASA,EAAI,MACnBkB,EAAe,EACflB,EAAI,OAASA,EAAI,MACjBmB,EAAU,EACH,GACT,CACF,EAGF,OAAO,SAASC,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASnB,EAAM,CACjC,OAAAb,EAAG,WAAWa,CAAI,EAClBb,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWgC,CAAK,EACnBhC,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,y+YAAk7E,MAAM,GAAG,CAAC,EAEj/EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_ru", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6", "a_7", "g_v", "I_p2", "I_pV", "sbp", "word", "habr3", "habr4", "r_mark_regions", "r_R2", "habr2", "a", "n", "among_var", "v_1", "r_perfective_gerund", "habr1", "r_adjective", "r_adjectival", "r_reflexive", "r_verb", "r_noun", "r_derivational", "r_tidy_up", "token"]}