{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.sa.js"], "sourcesContent": ["/*!\n * Lunr languages, `Sanskrit` language\n * https://github.com/MiKr13/lunr-languages\n *\n * Copyright 2023, India\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.sa = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.sa.trimmer,\n        lunr.sa.stopWordFilter,\n        lunr.sa.stemmer\n      );\n\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.sa.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.sa.wordCharacters = \"\\u0900-\\u0903\\u0904-\\u090f\\u0910-\\u091f\\u0920-\\u092f\\u0930-\\u093f\\u0940-\\u094f\\u0950-\\u095f\\u0960-\\u096f\\u0970-\\u097f\\uA8E0-\\uA8F1\\uA8F2-\\uA8F7\\uA8F8-\\uA8FB\\uA8FC-\\uA8FD\\uA8FE-\\uA8FF\\u11B00-\\u11B09\";\n    lunr.sa.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.sa.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.sa.trimmer, 'trimmer-sa');\n    /* lunr stop word filter */\n    lunr.sa.stopWordFilter = lunr.generateStopWordFilter(\n      'तथा अयम्‌ एकम्‌ इत्यस्मिन्‌ तथा तत्‌ वा अयम्‌ इत्यस्य ते आहूत उपरि तेषाम्‌  किन्तु तेषाम्‌ तदा इत्यनेन अधिकः इत्यस्य तत्‌ केचन बहवः द्वि तथा महत्वपूर्णः अयम्‌ अस्य  विषये अयं अस्ति तत्‌ प्रथमः विषये इत्युपरि इत्युपरि इतर अधिकतमः अधिकः अपि सामान्यतया ठ इतरेतर नूतनम्‌ द  न्यूनम्‌ कश्चित्‌ वा विशालः द  सः अस्ति तदनुसारम् तत्र अस्ति केवलम्‌ अपि अत्र सर्वे विविधाः तत्‌ बहवः यतः इदानीम्‌ द  दक्षिण इत्यस्मै तस्य उपरि नथ अतीव कार्यम्‌ सर्वे एकैकम्‌ इत्यादि। एते सन्ति  उत इत्थम्‌ मध्ये एतदर्थं . स कस्य प्रथमः श्री. करोति अस्मिन् प्रकारः निर्मिता कालः तत्र कर्तुं  समान अधुना ते सन्ति स एकः अस्ति सः अर्थात् तेषां कृते . स्थितम्  विशेषः अग्रिम तेषाम्‌ समान स्रोतः ख म समान इदानीमपि अधिकतया करोतु ते समान इत्यस्य वीथी सह यस्मिन्  कृतवान्‌ धृतः तदा पुनः पूर्वं सः आगतः किम्‌ कुल इतर पुरा  मात्रा स विषये उ अतएव अपि नगरस्य  उपरि यतः प्रतिशतं  कतरः कालः साधनानि भूत तथापि जात सम्बन्धि अन्यत्‌ ग अतः अस्माकं स्वकीयाः अस्माकं इदानीं अन्तः इत्यादयः भवन्तः इत्यादयः एते एताः तस्य अस्य इदम् एते तेषां तेषां तेषां तान् तेषां तेषां तेषां समानः सः एकः च तादृशाः बहवः अन्ये च वदन्ति यत् कियत् कस्मै  कस्मै  यस्मै  यस्मै  यस्मै  यस्मै न अतिनीचः किन्तु प्रथमं सम्पूर्णतया  ततः चिरकालानन्तरं पुस्तकं सम्पूर्णतया अन्तः  किन्तु अत्र वा इह इव श्रद्धाय अवशिष्यते  परन्तु अन्ये वर्गाः सन्ति ते सन्ति शक्नुवन्ति सर्वे मिलित्वा सर्वे एकत्र\"'.split(' '));\n    /* lunr stemmer function */\n    lunr.sa.stemmer = (function() {\n\n      return function(word) {\n        // for lunr version 2\n        if (typeof word.update === \"function\") {\n          return word.update(function(word) {\n            return word;\n          })\n        } else { // for lunr version <= 1\n          return word;\n        }\n\n      }\n    })();\n\n    var segmenter = lunr.wordcut;\n    segmenter.init();\n    lunr.sa.tokenizer = function(obj) {\n      if (!arguments.length || obj == null || obj == undefined) return []\n      if (Array.isArray(obj)) return obj.map(function(t) {\n        return isLunr2 ? new lunr.Token(t.toLowerCase()) : t.toLowerCase()\n      });\n\n      var str = obj.toString().toLowerCase().replace(/^\\s+/, '');\n      return segmenter.cut(str).split('|');\n    }\n\n    lunr.Pipeline.registerFunction(lunr.sa.stemmer, 'stemmer-sa');\n    lunr.Pipeline.registerFunction(lunr.sa.stopWordFilter, 'stopWordFilter-sa');\n\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAEI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,wMACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAC5B,+gNAAoyC,MAAM,GAAG,CAAC,EAEhzCA,EAAK,GAAG,QAAW,UAAW,CAE5B,OAAO,SAASC,EAAM,CAEpB,OAAI,OAAOA,EAAK,QAAW,WAClBA,EAAK,OAAO,SAASA,EAAM,CAChC,OAAOA,CACT,CAAC,EAEMA,CAGX,CACF,EAAG,EAEH,IAAIC,EAAYF,EAAK,QACrBE,EAAU,KAAK,EACfF,EAAK,GAAG,UAAY,SAASG,EAAK,CAChC,GAAI,CAAC,UAAU,QAAUA,GAAO,MAAQA,GAAO,KAAW,MAAO,CAAC,EAClE,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOA,EAAI,IAAI,SAASC,EAAG,CACjD,OAAO,QAAU,IAAIJ,EAAK,MAAMI,EAAE,YAAY,CAAC,EAAIA,EAAE,YAAY,CACnE,CAAC,EAED,IAAIC,EAAMF,EAAI,SAAS,EAAE,YAAY,EAAE,QAAQ,OAAQ,EAAE,EACzD,OAAOD,EAAU,IAAIG,CAAG,EAAE,MAAM,GAAG,CACrC,EAEAL,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAC5DA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAE5E,CACF,CAAC", "names": ["require_lunr_sa", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "word", "segmenter", "obj", "t", "str"]}