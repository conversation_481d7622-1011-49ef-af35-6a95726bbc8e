import{a as G}from"./chunk-OSRY5VT3.min.js";var H=G((d,p)=>{(function(n,e){typeof define=="function"&&define.amd?define(e):typeof d=="object"?p.exports=e():e()(n.lunr)})(d,function(){return function(n){if(typeof n>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof n.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");n.es=function(){this.pipeline.reset(),this.pipeline.add(n.es.trimmer,n.es.stopWordFilter,n.es.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(n.es.stemmer))},n.es.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",n.es.trimmer=n.trimmerSupport.generateTrimmer(n.es.wordCharacters),n.Pipeline.registerFunction(n.es.trimmer,"trimmer-es"),n.es.stemmer=function(){var e=n.stemmerSupport.Among,k=n.stemmerSupport.SnowballProgram,o=new function(){var f=[new e("",-1,6),new e("\xE1",0,1),new e("\xE9",0,2),new e("\xED",0,3),new e("\xF3",0,4),new e("\xFA",0,5)],D=[new e("la",-1,-1),new e("sela",0,-1),new e("le",-1,-1),new e("me",-1,-1),new e("se",-1,-1),new e("lo",-1,-1),new e("selo",5,-1),new e("las",-1,-1),new e("selas",7,-1),new e("les",-1,-1),new e("los",-1,-1),new e("selos",10,-1),new e("nos",-1,-1)],y=[new e("ando",-1,6),new e("iendo",-1,6),new e("yendo",-1,7),new e("\xE1ndo",-1,2),new e("i\xE9ndo",-1,1),new e("ar",-1,6),new e("er",-1,6),new e("ir",-1,6),new e("\xE1r",-1,3),new e("\xE9r",-1,4),new e("\xEDr",-1,5)],F=[new e("ic",-1,-1),new e("ad",-1,-1),new e("os",-1,-1),new e("iv",-1,1)],A=[new e("able",-1,1),new e("ible",-1,1),new e("ante",-1,1)],C=[new e("ic",-1,1),new e("abil",-1,1),new e("iv",-1,1)],q=[new e("ica",-1,1),new e("ancia",-1,2),new e("encia",-1,5),new e("adora",-1,2),new e("osa",-1,1),new e("ista",-1,1),new e("iva",-1,9),new e("anza",-1,1),new e("log\xEDa",-1,3),new e("idad",-1,8),new e("able",-1,1),new e("ible",-1,1),new e("ante",-1,2),new e("mente",-1,7),new e("amente",13,6),new e("aci\xF3n",-1,2),new e("uci\xF3n",-1,4),new e("ico",-1,1),new e("ismo",-1,1),new e("oso",-1,1),new e("amiento",-1,1),new e("imiento",-1,1),new e("ivo",-1,9),new e("ador",-1,2),new e("icas",-1,1),new e("ancias",-1,2),new e("encias",-1,5),new e("adoras",-1,2),new e("osas",-1,1),new e("istas",-1,1),new e("ivas",-1,9),new e("anzas",-1,1),new e("log\xEDas",-1,3),new e("idades",-1,8),new e("ables",-1,1),new e("ibles",-1,1),new e("aciones",-1,2),new e("uciones",-1,4),new e("adores",-1,2),new e("antes",-1,2),new e("icos",-1,1),new e("ismos",-1,1),new e("osos",-1,1),new e("amientos",-1,1),new e("imientos",-1,1),new e("ivos",-1,9)],B=[new e("ya",-1,1),new e("ye",-1,1),new e("yan",-1,1),new e("yen",-1,1),new e("yeron",-1,1),new e("yendo",-1,1),new e("yo",-1,1),new e("yas",-1,1),new e("yes",-1,1),new e("yais",-1,1),new e("yamos",-1,1),new e("y\xF3",-1,1)],x=[new e("aba",-1,2),new e("ada",-1,2),new e("ida",-1,2),new e("ara",-1,2),new e("iera",-1,2),new e("\xEDa",-1,2),new e("ar\xEDa",5,2),new e("er\xEDa",5,2),new e("ir\xEDa",5,2),new e("ad",-1,2),new e("ed",-1,2),new e("id",-1,2),new e("ase",-1,2),new e("iese",-1,2),new e("aste",-1,2),new e("iste",-1,2),new e("an",-1,2),new e("aban",16,2),new e("aran",16,2),new e("ieran",16,2),new e("\xEDan",16,2),new e("ar\xEDan",20,2),new e("er\xEDan",20,2),new e("ir\xEDan",20,2),new e("en",-1,1),new e("asen",24,2),new e("iesen",24,2),new e("aron",-1,2),new e("ieron",-1,2),new e("ar\xE1n",-1,2),new e("er\xE1n",-1,2),new e("ir\xE1n",-1,2),new e("ado",-1,2),new e("ido",-1,2),new e("ando",-1,2),new e("iendo",-1,2),new e("ar",-1,2),new e("er",-1,2),new e("ir",-1,2),new e("as",-1,2),new e("abas",39,2),new e("adas",39,2),new e("idas",39,2),new e("aras",39,2),new e("ieras",39,2),new e("\xEDas",39,2),new e("ar\xEDas",45,2),new e("er\xEDas",45,2),new e("ir\xEDas",45,2),new e("es",-1,1),new e("ases",49,2),new e("ieses",49,2),new e("abais",-1,2),new e("arais",-1,2),new e("ierais",-1,2),new e("\xEDais",-1,2),new e("ar\xEDais",55,2),new e("er\xEDais",55,2),new e("ir\xEDais",55,2),new e("aseis",-1,2),new e("ieseis",-1,2),new e("asteis",-1,2),new e("isteis",-1,2),new e("\xE1is",-1,2),new e("\xE9is",-1,1),new e("ar\xE9is",64,2),new e("er\xE9is",64,2),new e("ir\xE9is",64,2),new e("ados",-1,2),new e("idos",-1,2),new e("amos",-1,2),new e("\xE1bamos",70,2),new e("\xE1ramos",70,2),new e("i\xE9ramos",70,2),new e("\xEDamos",70,2),new e("ar\xEDamos",74,2),new e("er\xEDamos",74,2),new e("ir\xEDamos",74,2),new e("emos",-1,1),new e("aremos",78,2),new e("eremos",78,2),new e("iremos",78,2),new e("\xE1semos",78,2),new e("i\xE9semos",78,2),new e("imos",-1,2),new e("ar\xE1s",-1,2),new e("er\xE1s",-1,2),new e("ir\xE1s",-1,2),new e("\xEDs",-1,2),new e("ar\xE1",-1,2),new e("er\xE1",-1,2),new e("ir\xE1",-1,2),new e("ar\xE9",-1,2),new e("er\xE9",-1,2),new e("ir\xE9",-1,2),new e("i\xF3",-1,2)],P=[new e("a",-1,1),new e("e",-1,2),new e("o",-1,1),new e("os",-1,1),new e("\xE1",-1,1),new e("\xE9",-1,2),new e("\xED",-1,1),new e("\xF3",-1,1)],u=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,0,1,17,4,10],l,b,t,s=new k;this.setCurrent=function(r){s.setCurrent(r)},this.getCurrent=function(){return s.getCurrent()};function _(){if(s.out_grouping(u,97,252)){for(;!s.in_grouping(u,97,252);){if(s.cursor>=s.limit)return!0;s.cursor++}return!1}return!0}function S(){if(s.in_grouping(u,97,252)){var r=s.cursor;if(_()){if(s.cursor=r,!s.in_grouping(u,97,252))return!0;for(;!s.out_grouping(u,97,252);){if(s.cursor>=s.limit)return!0;s.cursor++}}return!1}return!0}function W(){var r=s.cursor,a;if(S()){if(s.cursor=r,!s.out_grouping(u,97,252))return;if(a=s.cursor,_()){if(s.cursor=a,!s.in_grouping(u,97,252)||s.cursor>=s.limit)return;s.cursor++}}t=s.cursor}function h(){for(;!s.in_grouping(u,97,252);){if(s.cursor>=s.limit)return!1;s.cursor++}for(;!s.out_grouping(u,97,252);){if(s.cursor>=s.limit)return!1;s.cursor++}return!0}function L(){var r=s.cursor;t=s.limit,b=t,l=t,W(),s.cursor=r,h()&&(b=s.cursor,h()&&(l=s.cursor))}function z(){for(var r;;){if(s.bra=s.cursor,r=s.find_among(f,6),r)switch(s.ket=s.cursor,r){case 1:s.slice_from("a");continue;case 2:s.slice_from("e");continue;case 3:s.slice_from("i");continue;case 4:s.slice_from("o");continue;case 5:s.slice_from("u");continue;case 6:if(s.cursor>=s.limit)break;s.cursor++;continue}break}}function m(){return t<=s.cursor}function I(){return b<=s.cursor}function i(){return l<=s.cursor}function R(){var r;if(s.ket=s.cursor,s.find_among_b(D,13)&&(s.bra=s.cursor,r=s.find_among_b(y,11),r&&m()))switch(r){case 1:s.bra=s.cursor,s.slice_from("iendo");break;case 2:s.bra=s.cursor,s.slice_from("ando");break;case 3:s.bra=s.cursor,s.slice_from("ar");break;case 4:s.bra=s.cursor,s.slice_from("er");break;case 5:s.bra=s.cursor,s.slice_from("ir");break;case 6:s.slice_del();break;case 7:s.eq_s_b(1,"u")&&s.slice_del();break}}function v(r,a){if(!i())return!0;s.slice_del(),s.ket=s.cursor;var w=s.find_among_b(r,a);return w&&(s.bra=s.cursor,w==1&&i()&&s.slice_del()),!1}function E(r){return i()?(s.slice_del(),s.ket=s.cursor,s.eq_s_b(2,r)&&(s.bra=s.cursor,i()&&s.slice_del()),!1):!0}function V(){var r;if(s.ket=s.cursor,r=s.find_among_b(q,46),r){switch(s.bra=s.cursor,r){case 1:if(!i())return!1;s.slice_del();break;case 2:if(E("ic"))return!1;break;case 3:if(!i())return!1;s.slice_from("log");break;case 4:if(!i())return!1;s.slice_from("u");break;case 5:if(!i())return!1;s.slice_from("ente");break;case 6:if(!I())return!1;s.slice_del(),s.ket=s.cursor,r=s.find_among_b(F,4),r&&(s.bra=s.cursor,i()&&(s.slice_del(),r==1&&(s.ket=s.cursor,s.eq_s_b(2,"at")&&(s.bra=s.cursor,i()&&s.slice_del()))));break;case 7:if(v(A,3))return!1;break;case 8:if(v(C,3))return!1;break;case 9:if(E("at"))return!1;break}return!0}return!1}function j(){var r,a;if(s.cursor>=t&&(a=s.limit_backward,s.limit_backward=t,s.ket=s.cursor,r=s.find_among_b(B,12),s.limit_backward=a,r)){if(s.bra=s.cursor,r==1){if(!s.eq_s_b(1,"u"))return!1;s.slice_del()}return!0}return!1}function T(){var r,a,w,g;if(s.cursor>=t&&(a=s.limit_backward,s.limit_backward=t,s.ket=s.cursor,r=s.find_among_b(x,96),s.limit_backward=a,r))switch(s.bra=s.cursor,r){case 1:w=s.limit-s.cursor,s.eq_s_b(1,"u")?(g=s.limit-s.cursor,s.eq_s_b(1,"g")?s.cursor=s.limit-g:s.cursor=s.limit-w):s.cursor=s.limit-w,s.bra=s.cursor;case 2:s.slice_del();break}}function Z(){var r,a;if(s.ket=s.cursor,r=s.find_among_b(P,8),r)switch(s.bra=s.cursor,r){case 1:m()&&s.slice_del();break;case 2:m()&&(s.slice_del(),s.ket=s.cursor,s.eq_s_b(1,"u")&&(s.bra=s.cursor,a=s.limit-s.cursor,s.eq_s_b(1,"g")&&(s.cursor=s.limit-a,m()&&s.slice_del())));break}}this.stem=function(){var r=s.cursor;return L(),s.limit_backward=r,s.cursor=s.limit,R(),s.cursor=s.limit,V()||(s.cursor=s.limit,j()||(s.cursor=s.limit,T())),s.cursor=s.limit,Z(),s.cursor=s.limit_backward,z(),!0}};return function(c){return typeof c.update=="function"?c.update(function(f){return o.setCurrent(f),o.stem(),o.getCurrent()}):(o.setCurrent(c),o.stem(),o.getCurrent())}}(),n.Pipeline.registerFunction(n.es.stemmer,"stemmer-es"),n.es.stopWordFilter=n.generateStopWordFilter("a al algo algunas algunos ante antes como con contra cual cuando de del desde donde durante e el ella ellas ellos en entre era erais eran eras eres es esa esas ese eso esos esta estaba estabais estaban estabas estad estada estadas estado estados estamos estando estar estaremos estar\xE1 estar\xE1n estar\xE1s estar\xE9 estar\xE9is estar\xEDa estar\xEDais estar\xEDamos estar\xEDan estar\xEDas estas este estemos esto estos estoy estuve estuviera estuvierais estuvieran estuvieras estuvieron estuviese estuvieseis estuviesen estuvieses estuvimos estuviste estuvisteis estuvi\xE9ramos estuvi\xE9semos estuvo est\xE1 est\xE1bamos est\xE1is est\xE1n est\xE1s est\xE9 est\xE9is est\xE9n est\xE9s fue fuera fuerais fueran fueras fueron fuese fueseis fuesen fueses fui fuimos fuiste fuisteis fu\xE9ramos fu\xE9semos ha habida habidas habido habidos habiendo habremos habr\xE1 habr\xE1n habr\xE1s habr\xE9 habr\xE9is habr\xEDa habr\xEDais habr\xEDamos habr\xEDan habr\xEDas hab\xE9is hab\xEDa hab\xEDais hab\xEDamos hab\xEDan hab\xEDas han has hasta hay haya hayamos hayan hayas hay\xE1is he hemos hube hubiera hubierais hubieran hubieras hubieron hubiese hubieseis hubiesen hubieses hubimos hubiste hubisteis hubi\xE9ramos hubi\xE9semos hubo la las le les lo los me mi mis mucho muchos muy m\xE1s m\xED m\xEDa m\xEDas m\xEDo m\xEDos nada ni no nos nosotras nosotros nuestra nuestras nuestro nuestros o os otra otras otro otros para pero poco por porque que quien quienes qu\xE9 se sea seamos sean seas seremos ser\xE1 ser\xE1n ser\xE1s ser\xE9 ser\xE9is ser\xEDa ser\xEDais ser\xEDamos ser\xEDan ser\xEDas se\xE1is sido siendo sin sobre sois somos son soy su sus suya suyas suyo suyos s\xED tambi\xE9n tanto te tendremos tendr\xE1 tendr\xE1n tendr\xE1s tendr\xE9 tendr\xE9is tendr\xEDa tendr\xEDais tendr\xEDamos tendr\xEDan tendr\xEDas tened tenemos tenga tengamos tengan tengas tengo teng\xE1is tenida tenidas tenido tenidos teniendo ten\xE9is ten\xEDa ten\xEDais ten\xEDamos ten\xEDan ten\xEDas ti tiene tienen tienes todo todos tu tus tuve tuviera tuvierais tuvieran tuvieras tuvieron tuviese tuvieseis tuviesen tuvieses tuvimos tuviste tuvisteis tuvi\xE9ramos tuvi\xE9semos tuvo tuya tuyas tuyo tuyos t\xFA un una uno unos vosotras vosotros vuestra vuestras vuestro vuestros y ya yo \xE9l \xE9ramos".split(" ")),n.Pipeline.registerFunction(n.es.stopWordFilter,"stopWordFilter-es")}})});export default H();
/*! Bundled license information:

lunr-languages/lunr.es.js:
  (*!
   * Lunr languages, `Spanish` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.es-ZH6Q76E6.min.js.map
