import{a as Y}from"./chunk-OSRY5VT3.min.js";var R=Y((g,B)=>{(function(u,r){typeof define=="function"&&define.amd?define(r):typeof g=="object"?B.exports=r():r()(u.lunr)})(g,function(){return function(u){if(typeof u>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof u.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");console.warn('[Lunr Languages] Please use the "nl" instead of the "du". The "nl" code is the standard code for Dutch language, and "du" will be removed in the next major versions.'),u.du=function(){this.pipeline.reset(),this.pipeline.add(u.du.trimmer,u.du.stopWordFilter,u.du.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(u.du.stemmer))},u.du.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",u.du.trimmer=u.trimmerSupport.generateTrimmer(u.du.wordCharacters),u.Pipeline.registerFunction(u.du.trimmer,"trimmer-du"),u.du.stemmer=function(){var r=u.stemmerSupport.Among,D=u.stemmerSupport.SnowballProgram,c=new function(){var d=[new r("",-1,6),new r("\xE1",0,1),new r("\xE4",0,1),new r("\xE9",0,2),new r("\xEB",0,2),new r("\xED",0,3),new r("\xEF",0,3),new r("\xF3",0,4),new r("\xF6",0,4),new r("\xFA",0,5),new r("\xFC",0,5)],E=[new r("",-1,3),new r("I",0,2),new r("Y",0,1)],j=[new r("dd",-1,-1),new r("kk",-1,-1),new r("tt",-1,-1)],q=[new r("ene",-1,2),new r("se",-1,3),new r("en",-1,2),new r("heden",2,1),new r("s",-1,3)],z=[new r("end",-1,1),new r("ig",-1,2),new r("ing",-1,1),new r("lijk",-1,3),new r("baar",-1,4),new r("bar",-1,5)],P=[new r("aa",-1,-1),new r("ee",-1,-1),new r("oo",-1,-1),new r("uu",-1,-1)],s=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,128],x=[1,0,0,17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,128],S=[17,67,16,1,0,0,0,0,0,0,0,0,0,0,0,0,128],l,a,w,e=new D;this.setCurrent=function(i){e.setCurrent(i)},this.getCurrent=function(){return e.getCurrent()};function y(){for(var i,t=e.cursor,n,f;;){if(e.bra=e.cursor,i=e.find_among(d,11),i)switch(e.ket=e.cursor,i){case 1:e.slice_from("a");continue;case 2:e.slice_from("e");continue;case 3:e.slice_from("i");continue;case 4:e.slice_from("o");continue;case 5:e.slice_from("u");continue;case 6:if(e.cursor>=e.limit)break;e.cursor++;continue}break}for(e.cursor=t,e.bra=t,e.eq_s(1,"y")?(e.ket=e.cursor,e.slice_from("Y")):e.cursor=t;;)if(n=e.cursor,e.in_grouping(s,97,232)){if(f=e.cursor,e.bra=f,e.eq_s(1,"i"))e.ket=e.cursor,e.in_grouping(s,97,232)&&(e.slice_from("I"),e.cursor=n);else if(e.cursor=f,e.eq_s(1,"y"))e.ket=e.cursor,e.slice_from("Y"),e.cursor=n;else if(p(n))break}else if(p(n))break}function p(i){return e.cursor=i,i>=e.limit?!0:(e.cursor++,!1)}function L(){a=e.limit,l=a,h()||(a=e.cursor,a<3&&(a=3),h()||(l=e.cursor))}function h(){for(;!e.in_grouping(s,97,232);){if(e.cursor>=e.limit)return!0;e.cursor++}for(;!e.out_grouping(s,97,232);){if(e.cursor>=e.limit)return!0;e.cursor++}return!1}function I(){for(var i;;)if(e.bra=e.cursor,i=e.find_among(E,3),i)switch(e.ket=e.cursor,i){case 1:e.slice_from("y");break;case 2:e.slice_from("i");break;case 3:if(e.cursor>=e.limit)return;e.cursor++;break}}function m(){return a<=e.cursor}function o(){return l<=e.cursor}function b(){var i=e.limit-e.cursor;e.find_among_b(j,3)&&(e.cursor=e.limit-i,e.ket=e.cursor,e.cursor>e.limit_backward&&(e.cursor--,e.bra=e.cursor,e.slice_del()))}function k(){var i;w=!1,e.ket=e.cursor,e.eq_s_b(1,"e")&&(e.bra=e.cursor,m()&&(i=e.limit-e.cursor,e.out_grouping_b(s,97,232)&&(e.cursor=e.limit-i,e.slice_del(),w=!0,b())))}function v(){var i;m()&&(i=e.limit-e.cursor,e.out_grouping_b(s,97,232)&&(e.cursor=e.limit-i,e.eq_s_b(3,"gem")||(e.cursor=e.limit-i,e.slice_del(),b())))}function W(){var i,t=e.limit-e.cursor,n,f,F,A,C;if(e.ket=e.cursor,i=e.find_among_b(q,5),i)switch(e.bra=e.cursor,i){case 1:m()&&e.slice_from("heid");break;case 2:v();break;case 3:m()&&e.out_grouping_b(S,97,232)&&e.slice_del();break}if(e.cursor=e.limit-t,k(),e.cursor=e.limit-t,e.ket=e.cursor,e.eq_s_b(4,"heid")&&(e.bra=e.cursor,o()&&(n=e.limit-e.cursor,e.eq_s_b(1,"c")||(e.cursor=e.limit-n,e.slice_del(),e.ket=e.cursor,e.eq_s_b(2,"en")&&(e.bra=e.cursor,v())))),e.cursor=e.limit-t,e.ket=e.cursor,i=e.find_among_b(z,6),i)switch(e.bra=e.cursor,i){case 1:if(o()){if(e.slice_del(),f=e.limit-e.cursor,e.ket=e.cursor,e.eq_s_b(2,"ig")&&(e.bra=e.cursor,o()&&(F=e.limit-e.cursor,!e.eq_s_b(1,"e")))){e.cursor=e.limit-F,e.slice_del();break}e.cursor=e.limit-f,b()}break;case 2:o()&&(A=e.limit-e.cursor,e.eq_s_b(1,"e")||(e.cursor=e.limit-A,e.slice_del()));break;case 3:o()&&(e.slice_del(),k());break;case 4:o()&&e.slice_del();break;case 5:o()&&w&&e.slice_del();break}e.cursor=e.limit-t,e.out_grouping_b(x,73,232)&&(C=e.limit-e.cursor,e.find_among_b(P,4)&&e.out_grouping_b(s,97,232)&&(e.cursor=e.limit-C,e.ket=e.cursor,e.cursor>e.limit_backward&&(e.cursor--,e.bra=e.cursor,e.slice_del())))}this.stem=function(){var i=e.cursor;return y(),e.cursor=i,L(),e.limit_backward=i,e.cursor=e.limit,W(),e.cursor=e.limit_backward,I(),!0}};return function(_){return typeof _.update=="function"?_.update(function(d){return c.setCurrent(d),c.stem(),c.getCurrent()}):(c.setCurrent(_),c.stem(),c.getCurrent())}}(),u.Pipeline.registerFunction(u.du.stemmer,"stemmer-du"),u.du.stopWordFilter=u.generateStopWordFilter(" aan al alles als altijd andere ben bij daar dan dat de der deze die dit doch doen door dus een eens en er ge geen geweest haar had heb hebben heeft hem het hier hij hoe hun iemand iets ik in is ja je kan kon kunnen maar me meer men met mij mijn moet na naar niet niets nog nu of om omdat onder ons ook op over reeds te tegen toch toen tot u uit uw van veel voor want waren was wat werd wezen wie wil worden wordt zal ze zelf zich zij zijn zo zonder zou".split(" ")),u.Pipeline.registerFunction(u.du.stopWordFilter,"stopWordFilter-du")}})});export default R();
/*! Bundled license information:

lunr-languages/lunr.du.js:
  (*!
   * Lunr languages, `Dutch` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.du-NO4L2LL3.min.js.map
