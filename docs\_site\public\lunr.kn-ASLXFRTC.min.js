import{a as o}from"./chunk-OSRY5VT3.min.js";var p=o((n,u)=>{(function(e,r){typeof define=="function"&&define.amd?define(r):typeof n=="object"?u.exports=r():r()(e.lunr)})(n,function(){return function(e){if(typeof e>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof e.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");e.kn=function(){this.pipeline.reset(),this.pipeline.add(e.kn.trimmer,e.kn.stopWordFilter,e.kn.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(e.kn.stemmer))},e.kn.wordCharacters="\u0C80-\u0C84\u0C85-\u0C94\u0C95-\u0CB9\u0CBE-\u0CCC\u0CBC-\u0CBD\u0CD5-\u0CD6\u0CDD-\u0CDE\u0CE0-\u0CE1\u0CE2-\u0CE3\u0CE4\u0CE5\u0CE6-\u0CEF\u0CF1-\u0CF3",e.kn.trimmer=e.trimmerSupport.generateTrimmer(e.kn.wordCharacters),e.Pipeline.registerFunction(e.kn.trimmer,"trimmer-kn"),e.kn.stopWordFilter=e.generateStopWordFilter("\u0CAE\u0CA4\u0CCD\u0CA4\u0CC1 \u0C88 \u0C92\u0C82\u0CA6\u0CC1 \u0CB0\u0CB2\u0CCD\u0CB2\u0CBF \u0CB9\u0CBE\u0C97\u0CC2 \u0C8E\u0C82\u0CA6\u0CC1 \u0C85\u0CA5\u0CB5\u0CBE \u0C87\u0CA6\u0CC1 \u0CB0 \u0C85\u0CB5\u0CB0\u0CC1 \u0C8E\u0C82\u0CAC \u0CAE\u0CC7\u0CB2\u0CC6 \u0C85\u0CB5\u0CB0 \u0CA4\u0CA8\u0CCD\u0CA8 \u0C86\u0CA6\u0CB0\u0CC6 \u0CA4\u0CAE\u0CCD\u0CAE \u0CA8\u0C82\u0CA4\u0CB0 \u0CAE\u0CC2\u0CB2\u0C95 \u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CC1 \u0CA8 \u0C86 \u0C95\u0CC6\u0CB2\u0CB5\u0CC1 \u0C85\u0CA8\u0CC7\u0C95 \u0C8E\u0CB0\u0CA1\u0CC1 \u0CB9\u0CBE\u0C97\u0CC1 \u0CAA\u0CCD\u0CB0\u0CAE\u0CC1\u0C96 \u0C87\u0CA6\u0CA8\u0CCD\u0CA8\u0CC1 \u0C87\u0CA6\u0CB0 \u0CB8\u0CC1\u0CAE\u0CBE\u0CB0\u0CC1 \u0C85\u0CA6\u0CB0 \u0C85\u0CA6\u0CC1 \u0CAE\u0CCA\u0CA6\u0CB2 \u0CAC\u0C97\u0CCD\u0C97\u0CC6 \u0CA8\u0CB2\u0CCD\u0CB2\u0CBF \u0CB0\u0C82\u0CA6\u0CC1 \u0C87\u0CA4\u0CB0 \u0C85\u0CA4\u0CCD\u0CAF\u0C82\u0CA4 \u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CBF\u0CA8 \u0CB8\u0CB9 \u0CB8\u0CBE\u0CAE\u0CBE\u0CA8\u0CCD\u0CAF\u0CB5\u0CBE\u0C97\u0CBF \u0CA8\u0CC7 \u0CB9\u0CB2\u0CB5\u0CBE\u0CB0\u0CC1 \u0CB9\u0CCA\u0CB8 \u0CA6\u0CBF \u0C95\u0CA1\u0CBF\u0CAE\u0CC6 \u0CAF\u0CBE\u0CB5\u0CC1\u0CA6\u0CC7 \u0CB9\u0CCA\u0C82\u0CA6\u0CBF\u0CA6\u0CC6 \u0CA6\u0CCA\u0CA1\u0CCD\u0CA1 \u0C85\u0CA8\u0CCD\u0CA8\u0CC1 \u0C87\u0CB5\u0CB0\u0CC1 \u0CAA\u0CCD\u0CB0\u0C95\u0CBE\u0CB0 \u0C87\u0CA6\u0CC6 \u0CAE\u0CBE\u0CA4\u0CCD\u0CB0 \u0C95\u0CC2\u0CA1 \u0C87\u0CB2\u0CCD\u0CB2\u0CBF \u0C8E\u0CB2\u0CCD\u0CB2\u0CBE \u0CB5\u0CBF\u0CB5\u0CBF\u0CA7 \u0C85\u0CA6\u0CA8\u0CCD\u0CA8\u0CC1 \u0CB9\u0CB2\u0CB5\u0CC1 \u0CB0\u0CBF\u0C82\u0CA6 \u0C95\u0CC7\u0CB5\u0CB2 \u0CA6 \u0CA6\u0C95\u0CCD\u0CB7\u0CBF\u0CA3 \u0C97\u0CC6 \u0C85\u0CB5\u0CA8 \u0C85\u0CA4\u0CBF \u0CA8\u0CC6\u0CAF \u0CAC\u0CB9\u0CB3 \u0C95\u0CC6\u0CB2\u0CB8 \u0C8E\u0CB2\u0CCD\u0CB2 \u0CAA\u0CCD\u0CB0\u0CA4\u0CBF \u0C87\u0CA4\u0CCD\u0CAF\u0CBE\u0CA6\u0CBF \u0C87\u0CB5\u0CC1 \u0CAC\u0CC7\u0CB0\u0CC6 \u0CB9\u0CC0\u0C97\u0CC6 \u0CA8\u0CA1\u0CC1\u0CB5\u0CC6 \u0C87\u0CA6\u0C95\u0CCD\u0C95\u0CC6 \u0C8E\u0CB8\u0CCD \u0C87\u0CB5\u0CB0 \u0CAE\u0CCA\u0CA6\u0CB2\u0CC1 \u0CB6\u0CCD\u0CB0\u0CC0 \u0CAE\u0CBE\u0CA1\u0CC1\u0CB5 \u0C87\u0CA6\u0CB0\u0CB2\u0CCD\u0CB2\u0CBF \u0CB0\u0CC0\u0CA4\u0CBF\u0CAF \u0CAE\u0CBE\u0CA1\u0CBF\u0CA6 \u0C95\u0CBE\u0CB2 \u0C85\u0CB2\u0CCD\u0CB2\u0CBF \u0CAE\u0CBE\u0CA1\u0CB2\u0CC1 \u0C85\u0CA6\u0CC7 \u0C88\u0C97 \u0C85\u0CB5\u0CC1 \u0C97\u0CB3\u0CC1 \u0C8E \u0C8E\u0C82\u0CAC\u0CC1\u0CA6\u0CC1 \u0C85\u0CB5\u0CA8\u0CC1 \u0C85\u0C82\u0CA6\u0CB0\u0CC6 \u0C85\u0CB5\u0CB0\u0CBF\u0C97\u0CC6 \u0C87\u0CB0\u0CC1\u0CB5 \u0CB5\u0CBF\u0CB6\u0CC7\u0CB7 \u0CAE\u0CC1\u0C82\u0CA6\u0CC6 \u0C85\u0CB5\u0CC1\u0C97\u0CB3 \u0CAE\u0CC1\u0C82\u0CA4\u0CBE\u0CA6 \u0CAE\u0CC2\u0CB2 \u0CAC\u0CBF \u0CAE\u0CC0 \u0C92\u0C82\u0CA6\u0CC7 \u0C87\u0CA8\u0CCD\u0CA8\u0CC2 \u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CBE\u0C97\u0CBF \u0CAE\u0CBE\u0CA1\u0CBF \u0C85\u0CB5\u0CB0\u0CA8\u0CCD\u0CA8\u0CC1 \u0C87\u0CA6\u0CC7 \u0CAF \u0CB0\u0CC0\u0CA4\u0CBF\u0CAF\u0CB2\u0CCD\u0CB2\u0CBF \u0C9C\u0CCA\u0CA4\u0CC6 \u0C85\u0CA6\u0CB0\u0CB2\u0CCD\u0CB2\u0CBF \u0CAE\u0CBE\u0CA1\u0CBF\u0CA6\u0CB0\u0CC1 \u0CA8\u0CA1\u0CC6\u0CA6 \u0C86\u0C97 \u0CAE\u0CA4\u0CCD\u0CA4\u0CC6 \u0CAA\u0CC2\u0CB0\u0CCD\u0CB5 \u0C86\u0CA4 \u0CAC\u0C82\u0CA6 \u0CAF\u0CBE\u0CB5 \u0C92\u0C9F\u0CCD\u0C9F\u0CC1 \u0C87\u0CA4\u0CB0\u0CC6 \u0CB9\u0CBF\u0C82\u0CA6\u0CC6 \u0CAA\u0CCD\u0CB0\u0CAE\u0CBE\u0CA3\u0CA6 \u0C97\u0CB3\u0CA8\u0CCD\u0CA8\u0CC1 \u0C95\u0CC1\u0CB0\u0CBF\u0CA4\u0CC1 \u0CAF\u0CC1 \u0C86\u0CA6\u0CCD\u0CA6\u0CB0\u0CBF\u0C82\u0CA6 \u0C85\u0CB2\u0CCD\u0CB2\u0CA6\u0CC6 \u0CA8\u0C97\u0CB0\u0CA6 \u0CAE\u0CC7\u0CB2\u0CBF\u0CA8 \u0C8F\u0C95\u0CC6\u0C82\u0CA6\u0CB0\u0CC6 \u0CB0\u0CB7\u0CCD\u0C9F\u0CC1 \u0C8E\u0C82\u0CAC\u0CC1\u0CA6\u0CA8\u0CCD\u0CA8\u0CC1 \u0CAC\u0CBE\u0CB0\u0CBF \u0C8E\u0C82\u0CA6\u0CB0\u0CC6 \u0CB9\u0CBF\u0C82\u0CA6\u0CBF\u0CA8 \u0C86\u0CA6\u0CB0\u0CC2 \u0C86\u0CA6 \u0CB8\u0C82\u0CAC\u0C82\u0CA7\u0CBF\u0CB8\u0CBF\u0CA6 \u0CAE\u0CA4\u0CCD\u0CA4\u0CCA\u0C82\u0CA6\u0CC1 \u0CB8\u0CBF \u0C86\u0CA4\u0CA8 ".split(" ")),e.kn.stemmer=function(){return function(t){return typeof t.update=="function"?t.update(function(i){return i}):t}}();var r=e.wordcut;r.init(),e.kn.tokenizer=function(t){if(!arguments.length||t==null||t==null)return[];if(Array.isArray(t))return t.map(function(s){return isLunr2?new e.Token(s.toLowerCase()):s.toLowerCase()});var i=t.toString().toLowerCase().replace(/^\s+/,"");return r.cut(i).split("|")},e.Pipeline.registerFunction(e.kn.stemmer,"stemmer-kn"),e.Pipeline.registerFunction(e.kn.stopWordFilter,"stopWordFilter-kn")}})});export default p();
/*! Bundled license information:

lunr-languages/lunr.kn.js:
  (*!
   * Lunr languages, `Kannada` language
   * https://github.com/MiKr13/lunr-languages
   *
   * Copyright 2023, India
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.kn-ASLXFRTC.min.js.map
