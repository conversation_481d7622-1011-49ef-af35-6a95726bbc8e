{"version": 3, "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/pieDiagram-YF2LJOPJ.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-TMUBEWPD.mjs\";\nimport {\n  cleanAndMerge,\n  parseFontSize\n} from \"./chunk-7DKRZKHE.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-EJ4ZWXGL.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/diagrams/pie/pieParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/pie/pieDb.ts\nvar DEFAULT_PIE_CONFIG = defaultConfig_default.pie;\nvar DEFAULT_PIE_DB = {\n  sections: /* @__PURE__ */ new Map(),\n  showData: false,\n  config: DEFAULT_PIE_CONFIG\n};\nvar sections = DEFAULT_PIE_DB.sections;\nvar showData = DEFAULT_PIE_DB.showData;\nvar config = structuredClone(DEFAULT_PIE_CONFIG);\nvar getConfig2 = /* @__PURE__ */ __name(() => structuredClone(config), \"getConfig\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  sections = /* @__PURE__ */ new Map();\n  showData = DEFAULT_PIE_DB.showData;\n  clear();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ __name(({ label, value }) => {\n  if (!sections.has(label)) {\n    sections.set(label, value);\n    log.debug(`added new section: ${label}, with value: ${value}`);\n  }\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ __name(() => sections, \"getSections\");\nvar setShowData = /* @__PURE__ */ __name((toggle) => {\n  showData = toggle;\n}, \"setShowData\");\nvar getShowData = /* @__PURE__ */ __name(() => showData, \"getShowData\");\nvar db = {\n  getConfig: getConfig2,\n  clear: clear2,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  setShowData,\n  getShowData\n};\n\n// src/diagrams/pie/pieParser.ts\nvar populateDb = /* @__PURE__ */ __name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  db2.setShowData(ast.showData);\n  ast.sections.map(db2.addSection);\n}, \"populateDb\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"pie\", input);\n    log.debug(ast);\n    populateDb(ast, db);\n  }, \"parse\")\n};\n\n// src/diagrams/pie/pieStyles.ts\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .pieCircle{\n    stroke: ${options.pieStrokeColor};\n    stroke-width : ${options.pieStrokeWidth};\n    opacity : ${options.pieOpacity};\n  }\n  .pieOuterCircle{\n    stroke: ${options.pieOuterStrokeColor};\n    stroke-width: ${options.pieOuterStrokeWidth};\n    fill: none;\n  }\n  .pieTitleText {\n    text-anchor: middle;\n    font-size: ${options.pieTitleTextSize};\n    fill: ${options.pieTitleTextColor};\n    font-family: ${options.fontFamily};\n  }\n  .slice {\n    font-family: ${options.fontFamily};\n    fill: ${options.pieSectionTextColor};\n    font-size:${options.pieSectionTextSize};\n    // fill: white;\n  }\n  .legend text {\n    fill: ${options.pieLegendTextColor};\n    font-family: ${options.fontFamily};\n    font-size: ${options.pieLegendTextSize};\n  }\n`, \"getStyles\");\nvar pieStyles_default = getStyles;\n\n// src/diagrams/pie/pieRenderer.ts\nimport { arc, pie as d3pie, scaleOrdinal } from \"d3\";\nvar createPieArcs = /* @__PURE__ */ __name((sections2) => {\n  const pieData = [...sections2.entries()].map((element) => {\n    return {\n      label: element[0],\n      value: element[1]\n    };\n  }).sort((a, b) => {\n    return b.value - a.value;\n  });\n  const pie = d3pie().value(\n    (d3Section) => d3Section.value\n  );\n  return pie(pieData);\n}, \"createPieArcs\");\nvar draw = /* @__PURE__ */ __name((text, id, _version, diagObj) => {\n  log.debug(\"rendering pie chart\\n\" + text);\n  const db2 = diagObj.db;\n  const globalConfig = getConfig();\n  const pieConfig = cleanAndMerge(db2.getConfig(), globalConfig.pie);\n  const MARGIN = 40;\n  const LEGEND_RECT_SIZE = 18;\n  const LEGEND_SPACING = 4;\n  const height = 450;\n  const pieWidth = height;\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\");\n  group.attr(\"transform\", \"translate(\" + pieWidth / 2 + \",\" + height / 2 + \")\");\n  const { themeVariables } = globalConfig;\n  let [outerStrokeWidth] = parseFontSize(themeVariables.pieOuterStrokeWidth);\n  outerStrokeWidth ??= 2;\n  const textPosition = pieConfig.textPosition;\n  const radius = Math.min(pieWidth, height) / 2 - MARGIN;\n  const arcGenerator = arc().innerRadius(0).outerRadius(radius);\n  const labelArcGenerator = arc().innerRadius(radius * textPosition).outerRadius(radius * textPosition);\n  group.append(\"circle\").attr(\"cx\", 0).attr(\"cy\", 0).attr(\"r\", radius + outerStrokeWidth / 2).attr(\"class\", \"pieOuterCircle\");\n  const sections2 = db2.getSections();\n  const arcs = createPieArcs(sections2);\n  const myGeneratedColors = [\n    themeVariables.pie1,\n    themeVariables.pie2,\n    themeVariables.pie3,\n    themeVariables.pie4,\n    themeVariables.pie5,\n    themeVariables.pie6,\n    themeVariables.pie7,\n    themeVariables.pie8,\n    themeVariables.pie9,\n    themeVariables.pie10,\n    themeVariables.pie11,\n    themeVariables.pie12\n  ];\n  const color = scaleOrdinal(myGeneratedColors);\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"path\").attr(\"d\", arcGenerator).attr(\"fill\", (datum) => {\n    return color(datum.data.label);\n  }).attr(\"class\", \"pieCircle\");\n  let sum = 0;\n  sections2.forEach((section) => {\n    sum += section;\n  });\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"text\").text((datum) => {\n    return (datum.data.value / sum * 100).toFixed(0) + \"%\";\n  }).attr(\"transform\", (datum) => {\n    return \"translate(\" + labelArcGenerator.centroid(datum) + \")\";\n  }).style(\"text-anchor\", \"middle\").attr(\"class\", \"slice\");\n  group.append(\"text\").text(db2.getDiagramTitle()).attr(\"x\", 0).attr(\"y\", -(height - 50) / 2).attr(\"class\", \"pieTitleText\");\n  const legend = group.selectAll(\".legend\").data(color.domain()).enter().append(\"g\").attr(\"class\", \"legend\").attr(\"transform\", (_datum, index) => {\n    const height2 = LEGEND_RECT_SIZE + LEGEND_SPACING;\n    const offset = height2 * color.domain().length / 2;\n    const horizontal = 12 * LEGEND_RECT_SIZE;\n    const vertical = index * height2 - offset;\n    return \"translate(\" + horizontal + \",\" + vertical + \")\";\n  });\n  legend.append(\"rect\").attr(\"width\", LEGEND_RECT_SIZE).attr(\"height\", LEGEND_RECT_SIZE).style(\"fill\", color).style(\"stroke\", color);\n  legend.data(arcs).append(\"text\").attr(\"x\", LEGEND_RECT_SIZE + LEGEND_SPACING).attr(\"y\", LEGEND_RECT_SIZE - LEGEND_SPACING).text((datum) => {\n    const { label, value } = datum.data;\n    if (db2.getShowData()) {\n      return `${label} [${value}]`;\n    }\n    return label;\n  });\n  const longestTextWidth = Math.max(\n    ...legend.selectAll(\"text\").nodes().map((node) => node?.getBoundingClientRect().width ?? 0)\n  );\n  const totalWidth = pieWidth + MARGIN + LEGEND_RECT_SIZE + LEGEND_SPACING + longestTextWidth;\n  svg.attr(\"viewBox\", `0 0 ${totalWidth} ${height}`);\n  configureSvgSize(svg, height, totalWidth, pieConfig.useMaxWidth);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/pie/pieDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles: pieStyles_default\n};\nexport {\n  diagram\n};\n"], "mappings": "wqBA6BA,IAAIA,EAAqBC,EAAsB,IAC3CC,EAAiB,CACnB,SAA0B,IAAI,IAC9B,SAAU,GACV,OAAQF,CACV,EACIG,EAAWD,EAAe,SAC1BE,EAAWF,EAAe,SAC1BG,GAAS,gBAAgBL,CAAkB,EAC3CM,GAA6BC,EAAO,IAAM,gBAAgBF,EAAM,EAAG,WAAW,EAC9EG,GAAyBD,EAAO,IAAM,CACxCJ,EAA2B,IAAI,IAC/BC,EAAWF,EAAe,SAC1BO,EAAM,CACR,EAAG,OAAO,EACNC,GAA6BH,EAAO,CAAC,CAAE,MAAAI,EAAO,MAAAC,CAAM,IAAM,CACvDT,EAAS,IAAIQ,CAAK,IACrBR,EAAS,IAAIQ,EAAOC,CAAK,EACzBC,EAAI,MAAM,sBAAsBF,CAAK,iBAAiBC,CAAK,EAAE,EAEjE,EAAG,YAAY,EACXE,GAA8BP,EAAO,IAAMJ,EAAU,aAAa,EAClEY,GAA8BR,EAAQS,GAAW,CACnDZ,EAAWY,CACb,EAAG,aAAa,EACZC,GAA8BV,EAAO,IAAMH,EAAU,aAAa,EAClEc,EAAK,CACP,UAAWZ,GACX,MAAOE,GACP,gBAAAW,EACA,gBAAAC,EACA,YAAAC,EACA,YAAAC,EACA,kBAAAC,EACA,kBAAAC,EACA,WAAAd,GACA,YAAAI,GACA,YAAAC,GACA,YAAAE,EACF,EAGIQ,GAA6BlB,EAAO,CAACmB,EAAKC,IAAQ,CACpDC,EAAiBF,EAAKC,CAAG,EACzBA,EAAI,YAAYD,EAAI,QAAQ,EAC5BA,EAAI,SAAS,IAAIC,EAAI,UAAU,CACjC,EAAG,YAAY,EACXE,GAAS,CACX,MAAuBtB,EAAO,MAAOuB,GAAU,CAC7C,IAAMJ,EAAM,MAAMK,EAAM,MAAOD,CAAK,EACpCjB,EAAI,MAAMa,CAAG,EACbD,GAAWC,EAAKR,CAAE,CACpB,EAAG,OAAO,CACZ,EAGIc,GAA4BzB,EAAQ0B,GAAY;AAAA;AAAA,cAEtCA,EAAQ,cAAc;AAAA,qBACfA,EAAQ,cAAc;AAAA,gBAC3BA,EAAQ,UAAU;AAAA;AAAA;AAAA,cAGpBA,EAAQ,mBAAmB;AAAA,oBACrBA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,iBAK9BA,EAAQ,gBAAgB;AAAA,YAC7BA,EAAQ,iBAAiB;AAAA,mBAClBA,EAAQ,UAAU;AAAA;AAAA;AAAA,mBAGlBA,EAAQ,UAAU;AAAA,YACzBA,EAAQ,mBAAmB;AAAA,gBACvBA,EAAQ,kBAAkB;AAAA;AAAA;AAAA;AAAA,YAI9BA,EAAQ,kBAAkB;AAAA,mBACnBA,EAAQ,UAAU;AAAA,iBACpBA,EAAQ,iBAAiB;AAAA;AAAA,EAEvC,WAAW,EACVC,GAAoBF,GAIpBG,GAAgC5B,EAAQ6B,GAAc,CACxD,IAAMC,EAAU,CAAC,GAAGD,EAAU,QAAQ,CAAC,EAAE,IAAKE,IACrC,CACL,MAAOA,EAAQ,CAAC,EAChB,MAAOA,EAAQ,CAAC,CAClB,EACD,EAAE,KAAK,CAACC,EAAGC,IACHA,EAAE,MAAQD,EAAE,KACpB,EAID,OAHYE,EAAM,EAAE,MACjBC,GAAcA,EAAU,KAC3B,EACWL,CAAO,CACpB,EAAG,eAAe,EACdM,GAAuBpC,EAAO,CAACqC,EAAMC,EAAIC,EAAUC,IAAY,CACjElC,EAAI,MAAM;AAAA,EAA0B+B,CAAI,EACxC,IAAMjB,EAAMoB,EAAQ,GACdC,EAAe1C,EAAU,EACzB2C,EAAYC,EAAcvB,EAAI,UAAU,EAAGqB,EAAa,GAAG,EAC3DG,EAAS,GACTC,EAAmB,GACnBC,EAAiB,EACjBC,EAAS,IACTC,EAAWD,EACXE,EAAMC,EAAiBZ,CAAE,EACzBa,EAAQF,EAAI,OAAO,GAAG,EAC5BE,EAAM,KAAK,YAAa,aAAeH,EAAW,EAAI,IAAMD,EAAS,EAAI,GAAG,EAC5E,GAAM,CAAE,eAAAK,CAAe,EAAIX,EACvB,CAACY,CAAgB,EAAIC,EAAcF,EAAe,mBAAmB,EACzEC,IAAqB,EACrB,IAAME,EAAeb,EAAU,aACzBc,EAAS,KAAK,IAAIR,EAAUD,CAAM,EAAI,EAAIH,EAC1Ca,EAAeC,EAAI,EAAE,YAAY,CAAC,EAAE,YAAYF,CAAM,EACtDG,EAAoBD,EAAI,EAAE,YAAYF,EAASD,CAAY,EAAE,YAAYC,EAASD,CAAY,EACpGJ,EAAM,OAAO,QAAQ,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,IAAKK,EAASH,EAAmB,CAAC,EAAE,KAAK,QAAS,gBAAgB,EAC1H,IAAMxB,EAAYT,EAAI,YAAY,EAC5BwC,EAAOhC,GAAcC,CAAS,EAC9BgC,GAAoB,CACxBT,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,MACfA,EAAe,MACfA,EAAe,KACjB,EACMU,EAAQC,EAAaF,EAAiB,EAC5CV,EAAM,UAAU,UAAU,EAAE,KAAKS,CAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,IAAKH,CAAY,EAAE,KAAK,OAASO,GAC3FF,EAAME,EAAM,KAAK,KAAK,CAC9B,EAAE,KAAK,QAAS,WAAW,EAC5B,IAAIC,EAAM,EACVpC,EAAU,QAASqC,GAAY,CAC7BD,GAAOC,CACT,CAAC,EACDf,EAAM,UAAU,UAAU,EAAE,KAAKS,CAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,KAAMI,IAC1DA,EAAM,KAAK,MAAQC,EAAM,KAAK,QAAQ,CAAC,EAAI,GACpD,EAAE,KAAK,YAAcD,GACb,aAAeL,EAAkB,SAASK,CAAK,EAAI,GAC3D,EAAE,MAAM,cAAe,QAAQ,EAAE,KAAK,QAAS,OAAO,EACvDb,EAAM,OAAO,MAAM,EAAE,KAAK/B,EAAI,gBAAgB,CAAC,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAK,EAAE2B,EAAS,IAAM,CAAC,EAAE,KAAK,QAAS,cAAc,EACxH,IAAMoB,EAAShB,EAAM,UAAU,SAAS,EAAE,KAAKW,EAAM,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK,QAAS,QAAQ,EAAE,KAAK,YAAa,CAACM,EAAQC,IAAU,CAC9I,IAAMC,EAAUzB,EAAmBC,EAC7ByB,GAASD,EAAUR,EAAM,OAAO,EAAE,OAAS,EAC3CU,GAAa,GAAK3B,EAClB4B,GAAWJ,EAAQC,EAAUC,GACnC,MAAO,aAAeC,GAAa,IAAMC,GAAW,GACtD,CAAC,EACDN,EAAO,OAAO,MAAM,EAAE,KAAK,QAAStB,CAAgB,EAAE,KAAK,SAAUA,CAAgB,EAAE,MAAM,OAAQiB,CAAK,EAAE,MAAM,SAAUA,CAAK,EACjIK,EAAO,KAAKP,CAAI,EAAE,OAAO,MAAM,EAAE,KAAK,IAAKf,EAAmBC,CAAc,EAAE,KAAK,IAAKD,EAAmBC,CAAc,EAAE,KAAMkB,GAAU,CACzI,GAAM,CAAE,MAAA5D,EAAO,MAAAC,CAAM,EAAI2D,EAAM,KAC/B,OAAI5C,EAAI,YAAY,EACX,GAAGhB,CAAK,KAAKC,CAAK,IAEpBD,CACT,CAAC,EACD,IAAMsE,GAAmB,KAAK,IAC5B,GAAGP,EAAO,UAAU,MAAM,EAAE,MAAM,EAAE,IAAKQ,GAASA,GAAM,sBAAsB,EAAE,OAAS,CAAC,CAC5F,EACMC,EAAa5B,EAAWJ,EAASC,EAAmBC,EAAiB4B,GAC3EzB,EAAI,KAAK,UAAW,OAAO2B,CAAU,IAAI7B,CAAM,EAAE,EACjD8B,EAAiB5B,EAAKF,EAAQ6B,EAAYlC,EAAU,WAAW,CACjE,EAAG,MAAM,EACLoC,GAAW,CAAE,KAAA1C,EAAK,EAGlB2C,GAAU,CACZ,OAAAzD,GACA,GAAAX,EACA,SAAAmE,GACA,OAAQnD,EACV", "names": ["DEFAULT_PIE_CONFIG", "defaultConfig_default", "DEFAULT_PIE_DB", "sections", "showData", "config", "getConfig2", "__name", "clear2", "clear", "addSection", "label", "value", "log", "getSections", "setShowData", "toggle", "getShowData", "db", "setDiagramTitle", "getDiagramTitle", "setAccTitle", "getAccTitle", "setAccDescription", "getAccDescription", "populateDb", "ast", "db2", "populateCommonDb", "parser", "input", "parse", "getStyles", "options", "pieStyles_default", "createPieArcs", "sections2", "pieData", "element", "a", "b", "pie_default", "d3Section", "draw", "text", "id", "_version", "diagObj", "globalConfig", "pieConfig", "cleanAndMerge", "MARGIN", "LEGEND_RECT_SIZE", "LEGEND_SPACING", "height", "<PERSON><PERSON><PERSON><PERSON>", "svg", "selectSvgElement", "group", "themeVariables", "outerStrokeWidth", "parseFontSize", "textPosition", "radius", "arcGenerator", "arc_default", "labelArcGenerator", "arcs", "myGeneratedColors", "color", "ordinal", "datum", "sum", "section", "legend", "_datum", "index", "height2", "offset", "horizontal", "vertical", "longestTextWidth", "node", "totalWidth", "configureSvgSize", "renderer", "diagram"]}