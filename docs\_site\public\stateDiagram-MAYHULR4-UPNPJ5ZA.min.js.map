{"version": 3, "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-MAYHULR4.mjs"], "sourcesContent": ["import {\n  stateDb_default,\n  stateDiagram_default,\n  styles_default\n} from \"./chunk-7U56Z5CX.mjs\";\nimport \"./chunk-5HRBRIJM.mjs\";\nimport \"./chunk-BO7VGL7K.mjs\";\nimport \"./chunk-66SQ7PYY.mjs\";\nimport \"./chunk-7NZE2EM7.mjs\";\nimport \"./chunk-OPO4IU42.mjs\";\nimport \"./chunk-3JNJP5BE.mjs\";\nimport \"./chunk-3X56UNUX.mjs\";\nimport \"./chunk-6JOS74DS.mjs\";\nimport {\n  utils_default\n} from \"./chunk-7DKRZKHE.mjs\";\nimport {\n  __name,\n  common_default,\n  configureSvgSize,\n  getConfig2 as getConfig,\n  log\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/diagrams/state/stateRenderer.js\nimport { select } from \"d3\";\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/diagrams/state/shapes.js\nimport { line, curveBasis } from \"d3\";\n\n// src/diagrams/state/id-cache.js\nvar idCache = {};\nvar set = /* @__PURE__ */ __name((key, val) => {\n  idCache[key] = val;\n}, \"set\");\nvar get = /* @__PURE__ */ __name((k) => idCache[k], \"get\");\nvar keys = /* @__PURE__ */ __name(() => Object.keys(idCache), \"keys\");\nvar size = /* @__PURE__ */ __name(() => keys().length, \"size\");\nvar id_cache_default = {\n  get,\n  set,\n  keys,\n  size\n};\n\n// src/diagrams/state/shapes.js\nvar drawStartState = /* @__PURE__ */ __name((g) => g.append(\"circle\").attr(\"class\", \"start-state\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit), \"drawStartState\");\nvar drawDivider = /* @__PURE__ */ __name((g) => g.append(\"line\").style(\"stroke\", \"grey\").style(\"stroke-dasharray\", \"3\").attr(\"x1\", getConfig().state.textHeight).attr(\"class\", \"divider\").attr(\"x2\", getConfig().state.textHeight * 2).attr(\"y1\", 0).attr(\"y2\", 0), \"drawDivider\");\nvar drawSimpleState = /* @__PURE__ */ __name((g, stateDef) => {\n  const state = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 2 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const classBox = state.node().getBBox();\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", classBox.width + 2 * getConfig().state.padding).attr(\"height\", classBox.height + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return state;\n}, \"drawSimpleState\");\nvar drawDescrState = /* @__PURE__ */ __name((g, stateDef) => {\n  const addTspan = /* @__PURE__ */ __name(function(textEl, txt, isFirst2) {\n    const tSpan = textEl.append(\"tspan\").attr(\"x\", 2 * getConfig().state.padding).text(txt);\n    if (!isFirst2) {\n      tSpan.attr(\"dy\", getConfig().state.textHeight);\n    }\n  }, \"addTspan\");\n  const title = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 1.3 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.descriptions[0]);\n  const titleBox = title.node().getBBox();\n  const titleHeight = titleBox.height;\n  const description = g.append(\"text\").attr(\"x\", getConfig().state.padding).attr(\n    \"y\",\n    titleHeight + getConfig().state.padding * 0.4 + getConfig().state.dividerMargin + getConfig().state.textHeight\n  ).attr(\"class\", \"state-description\");\n  let isFirst = true;\n  let isSecond = true;\n  stateDef.descriptions.forEach(function(descr) {\n    if (!isFirst) {\n      addTspan(description, descr, isSecond);\n      isSecond = false;\n    }\n    isFirst = false;\n  });\n  const descrLine = g.append(\"line\").attr(\"x1\", getConfig().state.padding).attr(\"y1\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"y2\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"class\", \"descr-divider\");\n  const descrBox = description.node().getBBox();\n  const width = Math.max(descrBox.width, titleBox.width);\n  descrLine.attr(\"x2\", width + 3 * getConfig().state.padding);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", width + 2 * getConfig().state.padding).attr(\"height\", descrBox.height + titleHeight + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"drawDescrState\");\nvar addTitleAndBox = /* @__PURE__ */ __name((g, stateDef, altBkg) => {\n  const pad = getConfig().state.padding;\n  const dblPad = 2 * getConfig().state.padding;\n  const orgBox = g.node().getBBox();\n  const orgWidth = orgBox.width;\n  const orgX = orgBox.x;\n  const title = g.append(\"text\").attr(\"x\", 0).attr(\"y\", getConfig().state.titleShift).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const titleBox = title.node().getBBox();\n  const titleWidth = titleBox.width + dblPad;\n  let width = Math.max(titleWidth, orgWidth);\n  if (width === orgWidth) {\n    width = width + dblPad;\n  }\n  let startX;\n  const graphBox = g.node().getBBox();\n  if (stateDef.doc) {\n  }\n  startX = orgX - pad;\n  if (titleWidth > orgWidth) {\n    startX = (orgWidth - width) / 2 + pad;\n  }\n  if (Math.abs(orgX - graphBox.x) < pad && titleWidth > orgWidth) {\n    startX = orgX - (titleWidth - orgWidth) / 2;\n  }\n  const lineY = 1 - getConfig().state.textHeight;\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\"y\", lineY).attr(\"class\", altBkg ? \"alt-composit\" : \"composit\").attr(\"width\", width).attr(\n    \"height\",\n    graphBox.height + getConfig().state.textHeight + getConfig().state.titleShift + 1\n  ).attr(\"rx\", \"0\");\n  title.attr(\"x\", startX + pad);\n  if (titleWidth <= orgWidth) {\n    title.attr(\"x\", orgX + (width - dblPad) / 2 - titleWidth / 2 + pad);\n  }\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", getConfig().state.textHeight * 3).attr(\"rx\", getConfig().state.radius);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", graphBox.height + 3 + 2 * getConfig().state.textHeight).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"addTitleAndBox\");\nvar drawEndState = /* @__PURE__ */ __name((g) => {\n  g.append(\"circle\").attr(\"class\", \"end-state-outer\").attr(\"r\", getConfig().state.sizeUnit + getConfig().state.miniPadding).attr(\n    \"cx\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  ).attr(\n    \"cy\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  );\n  return g.append(\"circle\").attr(\"class\", \"end-state-inner\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit + 2).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit + 2);\n}, \"drawEndState\");\nvar drawForkJoinState = /* @__PURE__ */ __name((g, stateDef) => {\n  let width = getConfig().state.forkWidth;\n  let height = getConfig().state.forkHeight;\n  if (stateDef.parentId) {\n    let tmp = width;\n    width = height;\n    height = tmp;\n  }\n  return g.append(\"rect\").style(\"stroke\", \"black\").style(\"fill\", \"black\").attr(\"width\", width).attr(\"height\", height).attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding);\n}, \"drawForkJoinState\");\nvar _drawLongText = /* @__PURE__ */ __name((_text, x, y, g) => {\n  let textHeight = 0;\n  const textElem = g.append(\"text\");\n  textElem.style(\"text-anchor\", \"start\");\n  textElem.attr(\"class\", \"noteText\");\n  let text = _text.replace(/\\r\\n/g, \"<br/>\");\n  text = text.replace(/\\n/g, \"<br/>\");\n  const lines = text.split(common_default.lineBreakRegex);\n  let tHeight = 1.25 * getConfig().state.noteMargin;\n  for (const line2 of lines) {\n    const txt = line2.trim();\n    if (txt.length > 0) {\n      const span = textElem.append(\"tspan\");\n      span.text(txt);\n      if (tHeight === 0) {\n        const textBounds = span.node().getBBox();\n        tHeight += textBounds.height;\n      }\n      textHeight += tHeight;\n      span.attr(\"x\", x + getConfig().state.noteMargin);\n      span.attr(\"y\", y + textHeight + 1.25 * getConfig().state.noteMargin);\n    }\n  }\n  return { textWidth: textElem.node().getBBox().width, textHeight };\n}, \"_drawLongText\");\nvar drawNote = /* @__PURE__ */ __name((text, g) => {\n  g.attr(\"class\", \"state-note\");\n  const note = g.append(\"rect\").attr(\"x\", 0).attr(\"y\", getConfig().state.padding);\n  const rectElem = g.append(\"g\");\n  const { textWidth, textHeight } = _drawLongText(text, 0, 0, rectElem);\n  note.attr(\"height\", textHeight + 2 * getConfig().state.noteMargin);\n  note.attr(\"width\", textWidth + getConfig().state.noteMargin * 2);\n  return note;\n}, \"drawNote\");\nvar drawState = /* @__PURE__ */ __name(function(elem, stateDef) {\n  const id = stateDef.id;\n  const stateInfo = {\n    id,\n    label: stateDef.id,\n    width: 0,\n    height: 0\n  };\n  const g = elem.append(\"g\").attr(\"id\", id).attr(\"class\", \"stateGroup\");\n  if (stateDef.type === \"start\") {\n    drawStartState(g);\n  }\n  if (stateDef.type === \"end\") {\n    drawEndState(g);\n  }\n  if (stateDef.type === \"fork\" || stateDef.type === \"join\") {\n    drawForkJoinState(g, stateDef);\n  }\n  if (stateDef.type === \"note\") {\n    drawNote(stateDef.note.text, g);\n  }\n  if (stateDef.type === \"divider\") {\n    drawDivider(g);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length === 0) {\n    drawSimpleState(g, stateDef);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length > 0) {\n    drawDescrState(g, stateDef);\n  }\n  const stateBox = g.node().getBBox();\n  stateInfo.width = stateBox.width + 2 * getConfig().state.padding;\n  stateInfo.height = stateBox.height + 2 * getConfig().state.padding;\n  id_cache_default.set(id, stateInfo);\n  return stateInfo;\n}, \"drawState\");\nvar edgeCount = 0;\nvar drawEdge = /* @__PURE__ */ __name(function(elem, path, relation) {\n  const getRelationType = /* @__PURE__ */ __name(function(type) {\n    switch (type) {\n      case stateDb_default.relationType.AGGREGATION:\n        return \"aggregation\";\n      case stateDb_default.relationType.EXTENSION:\n        return \"extension\";\n      case stateDb_default.relationType.COMPOSITION:\n        return \"composition\";\n      case stateDb_default.relationType.DEPENDENCY:\n        return \"dependency\";\n    }\n  }, \"getRelationType\");\n  path.points = path.points.filter((p) => !Number.isNaN(p.y));\n  const lineData = path.points;\n  const lineFunction = line().x(function(d) {\n    return d.x;\n  }).y(function(d) {\n    return d.y;\n  }).curve(curveBasis);\n  const svgPath = elem.append(\"path\").attr(\"d\", lineFunction(lineData)).attr(\"id\", \"edge\" + edgeCount).attr(\"class\", \"transition\");\n  let url = \"\";\n  if (getConfig().state.arrowMarkerAbsolute) {\n    url = window.location.protocol + \"//\" + window.location.host + window.location.pathname + window.location.search;\n    url = url.replace(/\\(/g, \"\\\\(\");\n    url = url.replace(/\\)/g, \"\\\\)\");\n  }\n  svgPath.attr(\n    \"marker-end\",\n    \"url(\" + url + \"#\" + getRelationType(stateDb_default.relationType.DEPENDENCY) + \"End)\"\n  );\n  if (relation.title !== void 0) {\n    const label = elem.append(\"g\").attr(\"class\", \"stateLabel\");\n    const { x, y } = utils_default.calcLabelPosition(path.points);\n    const rows = common_default.getRows(relation.title);\n    let titleHeight = 0;\n    const titleRows = [];\n    let maxWidth = 0;\n    let minX = 0;\n    for (let i = 0; i <= rows.length; i++) {\n      const title = label.append(\"text\").attr(\"text-anchor\", \"middle\").text(rows[i]).attr(\"x\", x).attr(\"y\", y + titleHeight);\n      const boundsTmp = title.node().getBBox();\n      maxWidth = Math.max(maxWidth, boundsTmp.width);\n      minX = Math.min(minX, boundsTmp.x);\n      log.info(boundsTmp.x, x, y + titleHeight);\n      if (titleHeight === 0) {\n        const titleBox = title.node().getBBox();\n        titleHeight = titleBox.height;\n        log.info(\"Title height\", titleHeight, y);\n      }\n      titleRows.push(title);\n    }\n    let boxHeight = titleHeight * rows.length;\n    if (rows.length > 1) {\n      const heightAdj = (rows.length - 1) * titleHeight * 0.5;\n      titleRows.forEach((title, i) => title.attr(\"y\", y + i * titleHeight - heightAdj));\n      boxHeight = titleHeight * rows.length;\n    }\n    const bounds = label.node().getBBox();\n    label.insert(\"rect\", \":first-child\").attr(\"class\", \"box\").attr(\"x\", x - maxWidth / 2 - getConfig().state.padding / 2).attr(\"y\", y - boxHeight / 2 - getConfig().state.padding / 2 - 3.5).attr(\"width\", maxWidth + getConfig().state.padding).attr(\"height\", boxHeight + getConfig().state.padding);\n    log.info(bounds);\n  }\n  edgeCount++;\n}, \"drawEdge\");\n\n// src/diagrams/state/stateRenderer.js\nvar conf;\nvar transformationLog = {};\nvar setConf = /* @__PURE__ */ __name(function() {\n}, \"setConf\");\nvar insertMarkers = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"dependencyEnd\").attr(\"refX\", 19).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 19,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertMarkers\");\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  conf = getConfig().state;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  log.debug(\"Rendering diagram \" + text);\n  const diagram2 = root.select(`[id='${id}']`);\n  insertMarkers(diagram2);\n  const rootDoc = diagObj.db.getRootDoc();\n  renderDoc(rootDoc, diagram2, void 0, false, root, doc, diagObj);\n  const padding = conf.padding;\n  const bounds = diagram2.node().getBBox();\n  const width = bounds.width + padding * 2;\n  const height = bounds.height + padding * 2;\n  const svgWidth = width * 1.75;\n  configureSvgSize(diagram2, height, svgWidth, conf.useMaxWidth);\n  diagram2.attr(\n    \"viewBox\",\n    `${bounds.x - conf.padding}  ${bounds.y - conf.padding} ` + width + \" \" + height\n  );\n}, \"draw\");\nvar getLabelWidth = /* @__PURE__ */ __name((text) => {\n  return text ? text.length * conf.fontSizeFactor : 1;\n}, \"getLabelWidth\");\nvar renderDoc = /* @__PURE__ */ __name((doc, diagram2, parentId, altBkg, root, domDocument, diagObj) => {\n  const graph = new graphlib.Graph({\n    compound: true,\n    multigraph: true\n  });\n  let i;\n  let edgeFreeDoc = true;\n  for (i = 0; i < doc.length; i++) {\n    if (doc[i].stmt === \"relation\") {\n      edgeFreeDoc = false;\n      break;\n    }\n  }\n  if (parentId) {\n    graph.setGraph({\n      rankdir: \"LR\",\n      multigraph: true,\n      compound: true,\n      // acyclicer: 'greedy',\n      ranker: \"tight-tree\",\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      isMultiGraph: true\n      // ranksep: 5,\n      // nodesep: 1\n    });\n  } else {\n    graph.setGraph({\n      rankdir: \"TB\",\n      multigraph: true,\n      compound: true,\n      // isCompound: true,\n      // acyclicer: 'greedy',\n      // ranker: 'longest-path'\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      ranker: \"tight-tree\",\n      // ranker: 'network-simplex'\n      isMultiGraph: true\n    });\n  }\n  graph.setDefaultEdgeLabel(function() {\n    return {};\n  });\n  diagObj.db.extract(doc);\n  const states = diagObj.db.getStates();\n  const relations = diagObj.db.getRelations();\n  const keys2 = Object.keys(states);\n  let first = true;\n  for (const key of keys2) {\n    const stateDef = states[key];\n    if (parentId) {\n      stateDef.parentId = parentId;\n    }\n    let node;\n    if (stateDef.doc) {\n      let sub = diagram2.append(\"g\").attr(\"id\", stateDef.id).attr(\"class\", \"stateGroup\");\n      node = renderDoc(stateDef.doc, sub, stateDef.id, !altBkg, root, domDocument, diagObj);\n      if (first) {\n        sub = addTitleAndBox(sub, stateDef, altBkg);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height + conf.padding / 2;\n        transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      } else {\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height;\n      }\n    } else {\n      node = drawState(diagram2, stateDef, graph);\n    }\n    if (stateDef.note) {\n      const noteDef = {\n        descriptions: [],\n        id: stateDef.id + \"-note\",\n        note: stateDef.note,\n        type: \"note\"\n      };\n      const note = drawState(diagram2, noteDef, graph);\n      if (stateDef.note.position === \"left of\") {\n        graph.setNode(node.id + \"-note\", note);\n        graph.setNode(node.id, node);\n      } else {\n        graph.setNode(node.id, node);\n        graph.setNode(node.id + \"-note\", note);\n      }\n      graph.setParent(node.id, node.id + \"-group\");\n      graph.setParent(node.id + \"-note\", node.id + \"-group\");\n    } else {\n      graph.setNode(node.id, node);\n    }\n  }\n  log.debug(\"Count=\", graph.nodeCount(), graph);\n  let cnt = 0;\n  relations.forEach(function(relation) {\n    cnt++;\n    log.debug(\"Setting edge\", relation);\n    graph.setEdge(\n      relation.id1,\n      relation.id2,\n      {\n        relation,\n        width: getLabelWidth(relation.title),\n        height: conf.labelHeight * common_default.getRows(relation.title).length,\n        labelpos: \"c\"\n      },\n      \"id\" + cnt\n    );\n  });\n  dagreLayout(graph);\n  log.debug(\"Graph after layout\", graph.nodes());\n  const svgElem = diagram2.node();\n  graph.nodes().forEach(function(v) {\n    if (v !== void 0 && graph.node(v) !== void 0) {\n      log.warn(\"Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\n        \"transform\",\n        \"translate(\" + (graph.node(v).x - graph.node(v).width / 2) + \",\" + (graph.node(v).y + (transformationLog[v] ? transformationLog[v].y : 0) - graph.node(v).height / 2) + \" )\"\n      );\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\"data-x-shift\", graph.node(v).x - graph.node(v).width / 2);\n      const dividers = domDocument.querySelectorAll(\"#\" + svgElem.id + \" #\" + v + \" .divider\");\n      dividers.forEach((divider) => {\n        const parent = divider.parentElement;\n        let pWidth = 0;\n        let pShift = 0;\n        if (parent) {\n          if (parent.parentElement) {\n            pWidth = parent.parentElement.getBBox().width;\n          }\n          pShift = parseInt(parent.getAttribute(\"data-x-shift\"), 10);\n          if (Number.isNaN(pShift)) {\n            pShift = 0;\n          }\n        }\n        divider.setAttribute(\"x1\", 0 - pShift + 8);\n        divider.setAttribute(\"x2\", pWidth - pShift - 8);\n      });\n    } else {\n      log.debug(\"No Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n    }\n  });\n  let stateBox = svgElem.getBBox();\n  graph.edges().forEach(function(e) {\n    if (e !== void 0 && graph.edge(e) !== void 0) {\n      log.debug(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n      drawEdge(diagram2, graph.edge(e), graph.edge(e).relation);\n    }\n  });\n  stateBox = svgElem.getBBox();\n  const stateInfo = {\n    id: parentId ? parentId : \"root\",\n    label: parentId ? parentId : \"root\",\n    width: 0,\n    height: 0\n  };\n  stateInfo.width = stateBox.width + 2 * conf.padding;\n  stateInfo.height = stateBox.height + 2 * conf.padding;\n  log.debug(\"Doc rendered\", stateInfo, graph);\n  return stateInfo;\n}, \"renderDoc\");\nvar stateRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/state/stateDiagram.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  db: stateDb_default,\n  renderer: stateRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n    stateDb_default.clear();\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "mappings": "8rBAiCA,IAAIA,EAAU,CAAC,EACXC,EAAsBC,EAAO,CAACC,EAAKC,IAAQ,CAC7CJ,EAAQG,CAAG,EAAIC,CACjB,EAAG,KAAK,EACJC,EAAsBH,EAAQI,GAAMN,EAAQM,CAAC,EAAG,KAAK,EACrDC,EAAuBL,EAAO,IAAM,OAAO,KAAKF,CAAO,EAAG,MAAM,EAChEQ,EAAuBN,EAAO,IAAMK,EAAK,EAAE,OAAQ,MAAM,EACzDE,EAAmB,CACrB,IAAAJ,EACA,IAAAJ,EACA,KAAAM,EACA,KAAAC,CACF,EAGIE,EAAiCR,EAAQS,GAAMA,EAAE,OAAO,QAAQ,EAAE,KAAK,QAAS,aAAa,EAAE,KAAK,IAAKC,EAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,QAAQ,EAAG,gBAAgB,EAC5RC,EAA8BX,EAAQS,GAAMA,EAAE,OAAO,MAAM,EAAE,MAAM,SAAU,MAAM,EAAE,MAAM,mBAAoB,GAAG,EAAE,KAAK,KAAMC,EAAU,EAAE,MAAM,UAAU,EAAE,KAAK,QAAS,SAAS,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,WAAa,CAAC,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,KAAM,CAAC,EAAG,aAAa,EAC7QE,EAAkCZ,EAAO,CAACS,EAAGI,IAAa,CAC5D,IAAMC,EAAQL,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,EAAIC,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,IAAKA,EAAU,EAAE,MAAM,WAAa,EAAIA,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,YAAaA,EAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,QAAS,aAAa,EAAE,KAAKG,EAAS,EAAE,EACrOE,EAAWD,EAAM,KAAK,EAAE,QAAQ,EACtC,OAAAL,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAKC,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,IAAKA,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,QAASK,EAAS,MAAQ,EAAIL,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,SAAUK,EAAS,OAAS,EAAIL,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,MAAM,EACrQI,CACT,EAAG,iBAAiB,EAChBE,EAAiChB,EAAO,CAACS,EAAGI,IAAa,CAC3D,IAAMI,EAA2BjB,EAAO,SAASkB,EAAQC,EAAKC,EAAU,CACtE,IAAMC,EAAQH,EAAO,OAAO,OAAO,EAAE,KAAK,IAAK,EAAIR,EAAU,EAAE,MAAM,OAAO,EAAE,KAAKS,CAAG,EACjFC,GACHC,EAAM,KAAK,KAAMX,EAAU,EAAE,MAAM,UAAU,CAEjD,EAAG,UAAU,EAEPY,EADQb,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,EAAIC,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,IAAKA,EAAU,EAAE,MAAM,WAAa,IAAMA,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,YAAaA,EAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,QAAS,aAAa,EAAE,KAAKG,EAAS,aAAa,CAAC,CAAC,EACnO,KAAK,EAAE,QAAQ,EAChCU,EAAcD,EAAS,OACvBE,EAAcf,EAAE,OAAO,MAAM,EAAE,KAAK,IAAKC,EAAU,EAAE,MAAM,OAAO,EAAE,KACxE,IACAa,EAAcb,EAAU,EAAE,MAAM,QAAU,GAAMA,EAAU,EAAE,MAAM,cAAgBA,EAAU,EAAE,MAAM,UACtG,EAAE,KAAK,QAAS,mBAAmB,EAC/Be,EAAU,GACVC,EAAW,GACfb,EAAS,aAAa,QAAQ,SAASc,EAAO,CACvCF,IACHR,EAASO,EAAaG,EAAOD,CAAQ,EACrCA,EAAW,IAEbD,EAAU,EACZ,CAAC,EACD,IAAMG,EAAYnB,EAAE,OAAO,MAAM,EAAE,KAAK,KAAMC,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUa,EAAcb,EAAU,EAAE,MAAM,cAAgB,CAAC,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUa,EAAcb,EAAU,EAAE,MAAM,cAAgB,CAAC,EAAE,KAAK,QAAS,eAAe,EACpRmB,EAAWL,EAAY,KAAK,EAAE,QAAQ,EACtCM,EAAQ,KAAK,IAAID,EAAS,MAAOP,EAAS,KAAK,EACrD,OAAAM,EAAU,KAAK,KAAME,EAAQ,EAAIpB,EAAU,EAAE,MAAM,OAAO,EAC1DD,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAKC,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,IAAKA,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,QAASoB,EAAQ,EAAIpB,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,SAAUmB,EAAS,OAASN,EAAc,EAAIb,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,MAAM,EAC1QD,CACT,EAAG,gBAAgB,EACfsB,EAAiC/B,EAAO,CAACS,EAAGI,EAAUmB,IAAW,CACnE,IAAMC,EAAMvB,EAAU,EAAE,MAAM,QACxBwB,EAAS,EAAIxB,EAAU,EAAE,MAAM,QAC/ByB,EAAS1B,EAAE,KAAK,EAAE,QAAQ,EAC1B2B,EAAWD,EAAO,MAClBE,EAAOF,EAAO,EACdG,EAAQ7B,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAKC,EAAU,EAAE,MAAM,UAAU,EAAE,KAAK,YAAaA,EAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,QAAS,aAAa,EAAE,KAAKG,EAAS,EAAE,EAEzK0B,EADWD,EAAM,KAAK,EAAE,QAAQ,EACV,MAAQJ,EAChCJ,EAAQ,KAAK,IAAIS,EAAYH,CAAQ,EACrCN,IAAUM,IACZN,EAAQA,EAAQI,GAElB,IAAIM,EACEC,EAAWhC,EAAE,KAAK,EAAE,QAAQ,EAC9BI,EAAS,IAEb2B,EAASH,EAAOJ,EACZM,EAAaH,IACfI,GAAUJ,EAAWN,GAAS,EAAIG,GAEhC,KAAK,IAAII,EAAOI,EAAS,CAAC,EAAIR,GAAOM,EAAaH,IACpDI,EAASH,GAAQE,EAAaH,GAAY,GAE5C,IAAMM,EAAQ,EAAIhC,EAAU,EAAE,MAAM,WACpC,OAAAD,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAK+B,CAAM,EAAE,KAAK,IAAKE,CAAK,EAAE,KAAK,QAASV,EAAS,eAAiB,UAAU,EAAE,KAAK,QAASF,CAAK,EAAE,KAC3I,SACAW,EAAS,OAAS/B,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,WAAa,CAClF,EAAE,KAAK,KAAM,GAAG,EAChB4B,EAAM,KAAK,IAAKE,EAASP,CAAG,EACxBM,GAAcH,GAChBE,EAAM,KAAK,IAAKD,GAAQP,EAAQI,GAAU,EAAIK,EAAa,EAAIN,CAAG,EAEpExB,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAK+B,CAAM,EAAE,KACjD,IACA9B,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,OAClF,EAAE,KAAK,QAASoB,CAAK,EAAE,KAAK,SAAUpB,EAAU,EAAE,MAAM,WAAa,CAAC,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,MAAM,EAC3GD,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAK+B,CAAM,EAAE,KACjD,IACA9B,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,OAClF,EAAE,KAAK,QAASoB,CAAK,EAAE,KAAK,SAAUW,EAAS,OAAS,EAAI,EAAI/B,EAAU,EAAE,MAAM,UAAU,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,MAAM,EAC1HD,CACT,EAAG,gBAAgB,EACfkC,GAA+B3C,EAAQS,IACzCA,EAAE,OAAO,QAAQ,EAAE,KAAK,QAAS,iBAAiB,EAAE,KAAK,IAAKC,EAAU,EAAE,MAAM,SAAWA,EAAU,EAAE,MAAM,WAAW,EAAE,KACxH,KACAA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,SAAWA,EAAU,EAAE,MAAM,WAC7E,EAAE,KACA,KACAA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,SAAWA,EAAU,EAAE,MAAM,WAC7E,EACOD,EAAE,OAAO,QAAQ,EAAE,KAAK,QAAS,iBAAiB,EAAE,KAAK,IAAKC,EAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,SAAW,CAAC,EAAE,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,SAAW,CAAC,GAC5O,cAAc,EACbkC,GAAoC5C,EAAO,CAACS,EAAGI,IAAa,CAC9D,IAAIiB,EAAQpB,EAAU,EAAE,MAAM,UAC1BmC,EAASnC,EAAU,EAAE,MAAM,WAC/B,GAAIG,EAAS,SAAU,CACrB,IAAIiC,EAAMhB,EACVA,EAAQe,EACRA,EAASC,CACX,CACA,OAAOrC,EAAE,OAAO,MAAM,EAAE,MAAM,SAAU,OAAO,EAAE,MAAM,OAAQ,OAAO,EAAE,KAAK,QAASqB,CAAK,EAAE,KAAK,SAAUe,CAAM,EAAE,KAAK,IAAKnC,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,IAAKA,EAAU,EAAE,MAAM,OAAO,CAC9L,EAAG,mBAAmB,EAClBqC,GAAgC/C,EAAO,CAACgD,EAAOC,EAAGC,EAAGzC,IAAM,CAC7D,IAAI0C,EAAa,EACXC,EAAW3C,EAAE,OAAO,MAAM,EAChC2C,EAAS,MAAM,cAAe,OAAO,EACrCA,EAAS,KAAK,QAAS,UAAU,EACjC,IAAIC,EAAOL,EAAM,QAAQ,QAAS,OAAO,EACzCK,EAAOA,EAAK,QAAQ,MAAO,OAAO,EAClC,IAAMC,EAAQD,EAAK,MAAME,EAAe,cAAc,EAClDC,EAAU,KAAO9C,EAAU,EAAE,MAAM,WACvC,QAAW+C,KAASH,EAAO,CACzB,IAAMnC,EAAMsC,EAAM,KAAK,EACvB,GAAItC,EAAI,OAAS,EAAG,CAClB,IAAMuC,EAAON,EAAS,OAAO,OAAO,EAEpC,GADAM,EAAK,KAAKvC,CAAG,EACTqC,IAAY,EAAG,CACjB,IAAMG,EAAaD,EAAK,KAAK,EAAE,QAAQ,EACvCF,GAAWG,EAAW,MACxB,CACAR,GAAcK,EACdE,EAAK,KAAK,IAAKT,EAAIvC,EAAU,EAAE,MAAM,UAAU,EAC/CgD,EAAK,KAAK,IAAKR,EAAIC,EAAa,KAAOzC,EAAU,EAAE,MAAM,UAAU,CACrE,CACF,CACA,MAAO,CAAE,UAAW0C,EAAS,KAAK,EAAE,QAAQ,EAAE,MAAO,WAAAD,CAAW,CAClE,EAAG,eAAe,EACdS,GAA2B5D,EAAO,CAACqD,EAAM5C,IAAM,CACjDA,EAAE,KAAK,QAAS,YAAY,EAC5B,IAAMoD,EAAOpD,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAKC,EAAU,EAAE,MAAM,OAAO,EACxEoD,EAAWrD,EAAE,OAAO,GAAG,EACvB,CAAE,UAAAsD,EAAW,WAAAZ,CAAW,EAAIJ,GAAcM,EAAM,EAAG,EAAGS,CAAQ,EACpE,OAAAD,EAAK,KAAK,SAAUV,EAAa,EAAIzC,EAAU,EAAE,MAAM,UAAU,EACjEmD,EAAK,KAAK,QAASE,EAAYrD,EAAU,EAAE,MAAM,WAAa,CAAC,EACxDmD,CACT,EAAG,UAAU,EACTG,EAA4BhE,EAAO,SAASiE,EAAMpD,EAAU,CAC9D,IAAMqD,EAAKrD,EAAS,GACdsD,EAAY,CAChB,GAAAD,EACA,MAAOrD,EAAS,GAChB,MAAO,EACP,OAAQ,CACV,EACMJ,EAAIwD,EAAK,OAAO,GAAG,EAAE,KAAK,KAAMC,CAAE,EAAE,KAAK,QAAS,YAAY,EAChErD,EAAS,OAAS,SACpBL,EAAeC,CAAC,EAEdI,EAAS,OAAS,OACpB8B,GAAalC,CAAC,GAEZI,EAAS,OAAS,QAAUA,EAAS,OAAS,SAChD+B,GAAkBnC,EAAGI,CAAQ,EAE3BA,EAAS,OAAS,QACpB+C,GAAS/C,EAAS,KAAK,KAAMJ,CAAC,EAE5BI,EAAS,OAAS,WACpBF,EAAYF,CAAC,EAEXI,EAAS,OAAS,WAAaA,EAAS,aAAa,SAAW,GAClED,EAAgBH,EAAGI,CAAQ,EAEzBA,EAAS,OAAS,WAAaA,EAAS,aAAa,OAAS,GAChEG,EAAeP,EAAGI,CAAQ,EAE5B,IAAMuD,EAAW3D,EAAE,KAAK,EAAE,QAAQ,EAClC,OAAA0D,EAAU,MAAQC,EAAS,MAAQ,EAAI1D,EAAU,EAAE,MAAM,QACzDyD,EAAU,OAASC,EAAS,OAAS,EAAI1D,EAAU,EAAE,MAAM,QAC3DH,EAAiB,IAAI2D,EAAIC,CAAS,EAC3BA,CACT,EAAG,WAAW,EACVE,EAAY,EACZC,GAA2BtE,EAAO,SAASiE,EAAMM,EAAMC,EAAU,CACnE,IAAMC,EAAkCzE,EAAO,SAAS0E,EAAM,CAC5D,OAAQA,EAAM,CACZ,KAAKC,EAAgB,aAAa,YAChC,MAAO,cACT,KAAKA,EAAgB,aAAa,UAChC,MAAO,YACT,KAAKA,EAAgB,aAAa,YAChC,MAAO,cACT,KAAKA,EAAgB,aAAa,WAChC,MAAO,YACX,CACF,EAAG,iBAAiB,EACpBJ,EAAK,OAASA,EAAK,OAAO,OAAQK,GAAM,CAAC,OAAO,MAAMA,EAAE,CAAC,CAAC,EAC1D,IAAMC,EAAWN,EAAK,OAChBO,EAAeC,EAAK,EAAE,EAAE,SAAS,EAAG,CACxC,OAAO,EAAE,CACX,CAAC,EAAE,EAAE,SAAS,EAAG,CACf,OAAO,EAAE,CACX,CAAC,EAAE,MAAMC,CAAU,EACbC,EAAUhB,EAAK,OAAO,MAAM,EAAE,KAAK,IAAKa,EAAaD,CAAQ,CAAC,EAAE,KAAK,KAAM,OAASR,CAAS,EAAE,KAAK,QAAS,YAAY,EAC3Ha,EAAM,GAUV,GATIxE,EAAU,EAAE,MAAM,sBACpBwE,EAAM,OAAO,SAAS,SAAW,KAAO,OAAO,SAAS,KAAO,OAAO,SAAS,SAAW,OAAO,SAAS,OAC1GA,EAAMA,EAAI,QAAQ,MAAO,KAAK,EAC9BA,EAAMA,EAAI,QAAQ,MAAO,KAAK,GAEhCD,EAAQ,KACN,aACA,OAASC,EAAM,IAAMT,EAAgBE,EAAgB,aAAa,UAAU,EAAI,MAClF,EACIH,EAAS,QAAU,OAAQ,CAC7B,IAAMW,EAAQlB,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,YAAY,EACnD,CAAE,EAAAhB,EAAG,EAAAC,CAAE,EAAIkC,EAAc,kBAAkBb,EAAK,MAAM,EACtDc,EAAO9B,EAAe,QAAQiB,EAAS,KAAK,EAC9CjD,EAAc,EACZ+D,EAAY,CAAC,EACfC,EAAW,EACXC,EAAO,EACX,QAASC,EAAI,EAAGA,GAAKJ,EAAK,OAAQI,IAAK,CACrC,IAAMnD,EAAQ6C,EAAM,OAAO,MAAM,EAAE,KAAK,cAAe,QAAQ,EAAE,KAAKE,EAAKI,CAAC,CAAC,EAAE,KAAK,IAAKxC,CAAC,EAAE,KAAK,IAAKC,EAAI3B,CAAW,EAC/GmE,EAAYpD,EAAM,KAAK,EAAE,QAAQ,EACvCiD,EAAW,KAAK,IAAIA,EAAUG,EAAU,KAAK,EAC7CF,EAAO,KAAK,IAAIA,EAAME,EAAU,CAAC,EACjCC,EAAI,KAAKD,EAAU,EAAGzC,EAAGC,EAAI3B,CAAW,EACpCA,IAAgB,IAElBA,EADiBe,EAAM,KAAK,EAAE,QAAQ,EACf,OACvBqD,EAAI,KAAK,eAAgBpE,EAAa2B,CAAC,GAEzCoC,EAAU,KAAKhD,CAAK,CACtB,CACA,IAAIsD,EAAYrE,EAAc8D,EAAK,OACnC,GAAIA,EAAK,OAAS,EAAG,CACnB,IAAMQ,GAAaR,EAAK,OAAS,GAAK9D,EAAc,GACpD+D,EAAU,QAAQ,CAAChD,EAAOmD,IAAMnD,EAAM,KAAK,IAAKY,EAAIuC,EAAIlE,EAAcsE,CAAS,CAAC,EAChFD,EAAYrE,EAAc8D,EAAK,MACjC,CACA,IAAMS,EAASX,EAAM,KAAK,EAAE,QAAQ,EACpCA,EAAM,OAAO,OAAQ,cAAc,EAAE,KAAK,QAAS,KAAK,EAAE,KAAK,IAAKlC,EAAIsC,EAAW,EAAI7E,EAAU,EAAE,MAAM,QAAU,CAAC,EAAE,KAAK,IAAKwC,EAAI0C,EAAY,EAAIlF,EAAU,EAAE,MAAM,QAAU,EAAI,GAAG,EAAE,KAAK,QAAS6E,EAAW7E,EAAU,EAAE,MAAM,OAAO,EAAE,KAAK,SAAUkF,EAAYlF,EAAU,EAAE,MAAM,OAAO,EACjSiF,EAAI,KAAKG,CAAM,CACjB,CACAzB,GACF,EAAG,UAAU,EAGT0B,EACAC,EAAoB,CAAC,EACrBC,GAA0BjG,EAAO,UAAW,CAChD,EAAG,SAAS,EACRkG,GAAgClG,EAAO,SAASiE,EAAM,CACxDA,EAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,eAAe,EAAE,KAAK,OAAQ,EAAE,EAAE,KAAK,OAAQ,CAAC,EAAE,KAAK,cAAe,EAAE,EAAE,KAAK,eAAgB,EAAE,EAAE,KAAK,SAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,2BAA2B,CAChO,EAAG,eAAe,EACdkC,GAAuBnG,EAAO,SAASqD,EAAMa,EAAIkC,EAAUC,EAAS,CACtEN,EAAOrF,EAAU,EAAE,MACnB,IAAM4F,EAAgB5F,EAAU,EAAE,cAC9B6F,EACAD,IAAkB,YACpBC,EAAiBC,EAAO,KAAOtC,CAAE,GAEnC,IAAMuC,EAAOH,IAAkB,UAAYE,EAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EAAIC,EAAO,MAAM,EAC3GE,EAAMJ,IAAkB,UAAYC,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAkB,SACtFZ,EAAI,MAAM,qBAAuBtC,CAAI,EACrC,IAAMsD,EAAWF,EAAK,OAAO,QAAQvC,CAAE,IAAI,EAC3CgC,GAAcS,CAAQ,EACtB,IAAMC,EAAUP,EAAQ,GAAG,WAAW,EACtCQ,EAAUD,EAASD,EAAU,OAAQ,GAAOF,EAAMC,EAAKL,CAAO,EAC9D,IAAMS,EAAUf,EAAK,QACfD,EAASa,EAAS,KAAK,EAAE,QAAQ,EACjC7E,EAAQgE,EAAO,MAAQgB,EAAU,EACjCjE,EAASiD,EAAO,OAASgB,EAAU,EACnCC,EAAWjF,EAAQ,KACzBkF,EAAiBL,EAAU9D,EAAQkE,EAAUhB,EAAK,WAAW,EAC7DY,EAAS,KACP,UACA,GAAGb,EAAO,EAAIC,EAAK,OAAO,KAAKD,EAAO,EAAIC,EAAK,OAAO,IAAMjE,EAAQ,IAAMe,CAC5E,CACF,EAAG,MAAM,EACLoE,GAAgCjH,EAAQqD,GACnCA,EAAOA,EAAK,OAAS0C,EAAK,eAAiB,EACjD,eAAe,EACdc,EAA4B7G,EAAO,CAAC0G,EAAKC,EAAUO,EAAUlF,EAAQyE,EAAMU,EAAad,IAAY,CACtG,IAAMe,EAAQ,IAAaC,EAAM,CAC/B,SAAU,GACV,WAAY,EACd,CAAC,EACG5B,EACA6B,EAAc,GAClB,IAAK7B,EAAI,EAAGA,EAAIiB,EAAI,OAAQjB,IAC1B,GAAIiB,EAAIjB,CAAC,EAAE,OAAS,WAAY,CAC9B6B,EAAc,GACd,KACF,CAEEJ,EACFE,EAAM,SAAS,CACb,QAAS,KACT,WAAY,GACZ,SAAU,GAEV,OAAQ,aACR,QAASE,EAAc,EAAIvB,EAAK,iBAChC,QAASuB,EAAc,EAAI,GAC3B,aAAc,EAGhB,CAAC,EAEDF,EAAM,SAAS,CACb,QAAS,KACT,WAAY,GACZ,SAAU,GAIV,QAASE,EAAc,EAAIvB,EAAK,iBAChC,QAASuB,EAAc,EAAI,GAC3B,OAAQ,aAER,aAAc,EAChB,CAAC,EAEHF,EAAM,oBAAoB,UAAW,CACnC,MAAO,CAAC,CACV,CAAC,EACDf,EAAQ,GAAG,QAAQK,CAAG,EACtB,IAAMa,EAASlB,EAAQ,GAAG,UAAU,EAC9BmB,EAAYnB,EAAQ,GAAG,aAAa,EACpCoB,EAAQ,OAAO,KAAKF,CAAM,EAC5BG,EAAQ,GACZ,QAAWzH,KAAOwH,EAAO,CACvB,IAAM5G,EAAW0G,EAAOtH,CAAG,EACvBiH,IACFrG,EAAS,SAAWqG,GAEtB,IAAIS,EACJ,GAAI9G,EAAS,IAAK,CAChB,IAAI+G,EAAMjB,EAAS,OAAO,GAAG,EAAE,KAAK,KAAM9F,EAAS,EAAE,EAAE,KAAK,QAAS,YAAY,EAEjF,GADA8G,EAAOd,EAAUhG,EAAS,IAAK+G,EAAK/G,EAAS,GAAI,CAACmB,EAAQyE,EAAMU,EAAad,CAAO,EAChFqB,EAAO,CACTE,EAAM7F,EAAe6F,EAAK/G,EAAUmB,CAAM,EAC1C,IAAI6F,EAAYD,EAAI,KAAK,EAAE,QAAQ,EACnCD,EAAK,MAAQE,EAAU,MACvBF,EAAK,OAASE,EAAU,OAAS9B,EAAK,QAAU,EAChDC,EAAkBnF,EAAS,EAAE,EAAI,CAAE,EAAGkF,EAAK,iBAAkB,CAC/D,KAAO,CACL,IAAI8B,EAAYD,EAAI,KAAK,EAAE,QAAQ,EACnCD,EAAK,MAAQE,EAAU,MACvBF,EAAK,OAASE,EAAU,MAC1B,CACF,MACEF,EAAO3D,EAAU2C,EAAU9F,EAAUuG,CAAK,EAE5C,GAAIvG,EAAS,KAAM,CACjB,IAAMiH,EAAU,CACd,aAAc,CAAC,EACf,GAAIjH,EAAS,GAAK,QAClB,KAAMA,EAAS,KACf,KAAM,MACR,EACMgD,EAAOG,EAAU2C,EAAUmB,EAASV,CAAK,EAC3CvG,EAAS,KAAK,WAAa,WAC7BuG,EAAM,QAAQO,EAAK,GAAK,QAAS9D,CAAI,EACrCuD,EAAM,QAAQO,EAAK,GAAIA,CAAI,IAE3BP,EAAM,QAAQO,EAAK,GAAIA,CAAI,EAC3BP,EAAM,QAAQO,EAAK,GAAK,QAAS9D,CAAI,GAEvCuD,EAAM,UAAUO,EAAK,GAAIA,EAAK,GAAK,QAAQ,EAC3CP,EAAM,UAAUO,EAAK,GAAK,QAASA,EAAK,GAAK,QAAQ,CACvD,MACEP,EAAM,QAAQO,EAAK,GAAIA,CAAI,CAE/B,CACAhC,EAAI,MAAM,SAAUyB,EAAM,UAAU,EAAGA,CAAK,EAC5C,IAAIW,EAAM,EACVP,EAAU,QAAQ,SAAShD,EAAU,CACnCuD,IACApC,EAAI,MAAM,eAAgBnB,CAAQ,EAClC4C,EAAM,QACJ5C,EAAS,IACTA,EAAS,IACT,CACE,SAAAA,EACA,MAAOyC,GAAczC,EAAS,KAAK,EACnC,OAAQuB,EAAK,YAAcxC,EAAe,QAAQiB,EAAS,KAAK,EAAE,OAClE,SAAU,GACZ,EACA,KAAOuD,CACT,CACF,CAAC,EACDC,EAAYZ,CAAK,EACjBzB,EAAI,MAAM,qBAAsByB,EAAM,MAAM,CAAC,EAC7C,IAAMa,EAAUtB,EAAS,KAAK,EAC9BS,EAAM,MAAM,EAAE,QAAQ,SAASc,EAAG,CAC5BA,IAAM,QAAUd,EAAM,KAAKc,CAAC,IAAM,QACpCvC,EAAI,KAAK,QAAUuC,EAAI,KAAO,KAAK,UAAUd,EAAM,KAAKc,CAAC,CAAC,CAAC,EAC3DzB,EAAK,OAAO,IAAMwB,EAAQ,GAAK,KAAOC,CAAC,EAAE,KACvC,YACA,cAAgBd,EAAM,KAAKc,CAAC,EAAE,EAAId,EAAM,KAAKc,CAAC,EAAE,MAAQ,GAAK,KAAOd,EAAM,KAAKc,CAAC,EAAE,GAAKlC,EAAkBkC,CAAC,EAAIlC,EAAkBkC,CAAC,EAAE,EAAI,GAAKd,EAAM,KAAKc,CAAC,EAAE,OAAS,GAAK,IAC1K,EACAzB,EAAK,OAAO,IAAMwB,EAAQ,GAAK,KAAOC,CAAC,EAAE,KAAK,eAAgBd,EAAM,KAAKc,CAAC,EAAE,EAAId,EAAM,KAAKc,CAAC,EAAE,MAAQ,CAAC,EACtFf,EAAY,iBAAiB,IAAMc,EAAQ,GAAK,KAAOC,EAAI,WAAW,EAC9E,QAASC,GAAY,CAC5B,IAAMC,EAASD,EAAQ,cACnBE,EAAS,EACTC,EAAS,EACTF,IACEA,EAAO,gBACTC,EAASD,EAAO,cAAc,QAAQ,EAAE,OAE1CE,EAAS,SAASF,EAAO,aAAa,cAAc,EAAG,EAAE,EACrD,OAAO,MAAME,CAAM,IACrBA,EAAS,IAGbH,EAAQ,aAAa,KAAM,EAAIG,EAAS,CAAC,EACzCH,EAAQ,aAAa,KAAME,EAASC,EAAS,CAAC,CAChD,CAAC,GAED3C,EAAI,MAAM,WAAauC,EAAI,KAAO,KAAK,UAAUd,EAAM,KAAKc,CAAC,CAAC,CAAC,CAEnE,CAAC,EACD,IAAI9D,EAAW6D,EAAQ,QAAQ,EAC/Bb,EAAM,MAAM,EAAE,QAAQ,SAASmB,EAAG,CAC5BA,IAAM,QAAUnB,EAAM,KAAKmB,CAAC,IAAM,SACpC5C,EAAI,MAAM,QAAU4C,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAUnB,EAAM,KAAKmB,CAAC,CAAC,CAAC,EAC7EjE,GAASqC,EAAUS,EAAM,KAAKmB,CAAC,EAAGnB,EAAM,KAAKmB,CAAC,EAAE,QAAQ,EAE5D,CAAC,EACDnE,EAAW6D,EAAQ,QAAQ,EAC3B,IAAM9D,EAAY,CAChB,GAAI+C,GAAsB,OAC1B,MAAOA,GAAsB,OAC7B,MAAO,EACP,OAAQ,CACV,EACA,OAAA/C,EAAU,MAAQC,EAAS,MAAQ,EAAI2B,EAAK,QAC5C5B,EAAU,OAASC,EAAS,OAAS,EAAI2B,EAAK,QAC9CJ,EAAI,MAAM,eAAgBxB,EAAWiD,CAAK,EACnCjD,CACT,EAAG,WAAW,EACVqE,GAAwB,CAC1B,QAAAvC,GACA,KAAAE,EACF,EAGIsC,GAAU,CACZ,OAAQC,EACR,GAAI/D,EACJ,SAAU6D,GACV,OAAQG,EACR,KAAsB3I,EAAQ4I,GAAQ,CAC/BA,EAAI,QACPA,EAAI,MAAQ,CAAC,GAEfA,EAAI,MAAM,oBAAsBA,EAAI,oBACpCjE,EAAgB,MAAM,CACxB,EAAG,MAAM,CACX", "names": ["idCache", "set", "__name", "key", "val", "get", "k", "keys", "size", "id_cache_default", "drawStartState", "g", "getConfig2", "drawDivider", "drawSimpleState", "stateDef", "state", "classBox", "drawDescrState", "addTspan", "textEl", "txt", "isFirst2", "tSpan", "titleBox", "titleHeight", "description", "<PERSON><PERSON><PERSON><PERSON>", "isSecond", "descr", "descrLine", "descrBox", "width", "addTitleAndBox", "altBkg", "pad", "dblPad", "orgBox", "orgWidth", "orgX", "title", "titleWidth", "startX", "graphBox", "lineY", "drawEndState", "drawForkJoinState", "height", "tmp", "_drawLongText", "_text", "x", "y", "textHeight", "textElem", "text", "lines", "common_default", "tHeight", "line2", "span", "textBounds", "drawNote", "note", "rectElem", "textWidth", "drawState", "elem", "id", "stateInfo", "stateBox", "edgeCount", "drawEdge", "path", "relation", "getRelationType", "type", "stateDb_default", "p", "lineData", "lineFunction", "line_default", "basis_default", "svgPath", "url", "label", "utils_default", "rows", "titleRows", "max<PERSON><PERSON><PERSON>", "minX", "i", "boundsTmp", "log", "boxHeight", "heightAdj", "bounds", "conf", "transformationLog", "setConf", "insertMarkers", "draw", "_version", "diagObj", "securityLevel", "sandboxElement", "select_default", "root", "doc", "diagram2", "rootDoc", "renderDoc", "padding", "svgWidth", "configureSvgSize", "<PERSON><PERSON><PERSON><PERSON>", "parentId", "domDocument", "graph", "Graph", "edgeFreeDoc", "states", "relations", "keys2", "first", "node", "sub", "boxBounds", "noteDef", "cnt", "layout", "svgElem", "v", "divider", "parent", "pWidth", "pShift", "e", "stateRenderer_default", "diagram", "stateDiagram_default", "styles_default", "cnf"]}