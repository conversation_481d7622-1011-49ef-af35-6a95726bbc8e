<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class VisualLayer | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class VisualLayer | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_VisualLayer.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.VisualLayer%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.VisualLayer">



  <h1 id="DrawnUi_Draw_VisualLayer" data-uid="DrawnUi.Draw.VisualLayer" class="text-break">
Class VisualLayer  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L3"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class VisualLayer</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">VisualLayer</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_VisualLayer__ctor_" data-uid="DrawnUi.Draw.VisualLayer.#ctor*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer__ctor" data-uid="DrawnUi.Draw.VisualLayer.#ctor">
  VisualLayer()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L284"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected VisualLayer()</code></pre>
  </div>













  <a id="DrawnUi_Draw_VisualLayer__ctor_" data-uid="DrawnUi.Draw.VisualLayer.#ctor*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer__ctor_DrawnUi_Draw_SkiaControl_DrawnUi_Draw_VisualLayer_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Draw.VisualLayer.#ctor(DrawnUi.Draw.SkiaControl,DrawnUi.Draw.VisualLayer,SkiaSharp.SKRect,System.Single)">
  VisualLayer(SkiaControl, VisualLayer, SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L245"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Create this ONLY when DrawingRect and RenderTransformMatrix are ready</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public VisualLayer(SkiaControl control, VisualLayer parent, SKRect destination, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></dt>
    <dd></dd>
    <dt><code>destination</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_VisualLayer_Cache_" data-uid="DrawnUi.Draw.VisualLayer.Cache*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_Cache" data-uid="DrawnUi.Draw.VisualLayer.Cache">
  Cache
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L40"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Cached rendering object, null means render control directly</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CachedObject Cache { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.CachedObject.html">CachedObject</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_Cached_" data-uid="DrawnUi.Draw.VisualLayer.Cached*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_Cached" data-uid="DrawnUi.Draw.VisualLayer.Cached">
  Cached
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L45"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Type of caching applied to this layer</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaCacheType Cached { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaCacheType.html">SkiaCacheType</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_Children_" data-uid="DrawnUi.Draw.VisualLayer.Children*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_Children" data-uid="DrawnUi.Draw.VisualLayer.Children">
  Children
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L35"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Child layers contained within this layer</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public List&lt;VisualLayer&gt; Children { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_Control_" data-uid="DrawnUi.Draw.VisualLayer.Control*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_Control" data-uid="DrawnUi.Draw.VisualLayer.Control">
  Control
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L30"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The SkiaControl this layer represents</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl Control { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_Destination_" data-uid="DrawnUi.Draw.VisualLayer.Destination*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_Destination" data-uid="DrawnUi.Draw.VisualLayer.Destination">
  Destination
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L50"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Layout bounds in local coordinates</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKRect Destination { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_HitBox_" data-uid="DrawnUi.Draw.VisualLayer.HitBox*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_HitBox" data-uid="DrawnUi.Draw.VisualLayer.HitBox">
  HitBox
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L114"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>A hitbox rather for internal use, because it is same as LastDrawnAt. For exact position on canvas use HitBoxWithTransforms.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ScaledRect HitBox { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledRect.html">ScaledRect</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_HitBoxWithTransforms_" data-uid="DrawnUi.Draw.VisualLayer.HitBoxWithTransforms*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_HitBoxWithTransforms" data-uid="DrawnUi.Draw.VisualLayer.HitBoxWithTransforms">
  HitBoxWithTransforms
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L119"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Exact position on canvas use HitBoxWithTransforms, all matrix transforms and Left, Top offset applied.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ScaledRect HitBoxWithTransforms { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ScaledRect.html">ScaledRect</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_IsFrozen_" data-uid="DrawnUi.Draw.VisualLayer.IsFrozen*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_IsFrozen" data-uid="DrawnUi.Draw.VisualLayer.IsFrozen">
  IsFrozen
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L25"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Child of a cached object</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsFrozen { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_OpacityTotal_" data-uid="DrawnUi.Draw.VisualLayer.OpacityTotal*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_OpacityTotal" data-uid="DrawnUi.Draw.VisualLayer.OpacityTotal">
  OpacityTotal
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L70"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Combined opacity including all parent opacities</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double OpacityTotal { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_Origin_" data-uid="DrawnUi.Draw.VisualLayer.Origin*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_Origin" data-uid="DrawnUi.Draw.VisualLayer.Origin">
  Origin
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L55"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Layout bounds in local coordinates at time of creation</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKRect Origin { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_RotationTotal_" data-uid="DrawnUi.Draw.VisualLayer.RotationTotal*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_RotationTotal" data-uid="DrawnUi.Draw.VisualLayer.RotationTotal">
  RotationTotal
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L75"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Total rotation in degrees extracted from transform matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double RotationTotal { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_ScaleTotal_" data-uid="DrawnUi.Draw.VisualLayer.ScaleTotal*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_ScaleTotal" data-uid="DrawnUi.Draw.VisualLayer.ScaleTotal">
  ScaleTotal
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L88"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Total scale factors extracted from transform matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKPoint ScaleTotal { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_Transforms_" data-uid="DrawnUi.Draw.VisualLayer.Transforms*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_Transforms" data-uid="DrawnUi.Draw.VisualLayer.Transforms">
  Transforms
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L60"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Local transformation matrix applied to this layer</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKMatrix Transforms { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix">SKMatrix</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_TransformsTotal_" data-uid="DrawnUi.Draw.VisualLayer.TransformsTotal*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_TransformsTotal" data-uid="DrawnUi.Draw.VisualLayer.TransformsTotal">
  TransformsTotal
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L65"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Combined transformation matrix including all parent transforms</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKMatrix TransformsTotal { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix">SKMatrix</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualLayer_TranslationTotal_" data-uid="DrawnUi.Draw.VisualLayer.TranslationTotal*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_TranslationTotal" data-uid="DrawnUi.Draw.VisualLayer.TranslationTotal">
  TranslationTotal
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L101"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Total translation extracted from transform matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKPoint TranslationTotal { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_VisualLayer_AttachFromCache_" data-uid="DrawnUi.Draw.VisualLayer.AttachFromCache*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_AttachFromCache_DrawnUi_Draw_CachedObject_" data-uid="DrawnUi.Draw.VisualLayer.AttachFromCache(DrawnUi.Draw.CachedObject)">
  AttachFromCache(CachedObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L160"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Attaches cached children and relocates them to account for new parent position</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AttachFromCache(CachedObject cache)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>cache</code> <a class="xref" href="DrawnUi.Draw.CachedObject.html">CachedObject</a></dt>
    <dd><p>Cached object containing children to attach</p>
</dd>
  </dl>












  <a id="DrawnUi_Draw_VisualLayer_CreateEmpty_" data-uid="DrawnUi.Draw.VisualLayer.CreateEmpty*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_CreateEmpty" data-uid="DrawnUi.Draw.VisualLayer.CreateEmpty">
  CreateEmpty()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L289"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static VisualLayer CreateEmpty()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_VisualLayer_DecomposeMatrix_" data-uid="DrawnUi.Draw.VisualLayer.DecomposeMatrix*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_DecomposeMatrix_SkiaSharp_SKMatrix_SkiaSharp_SKPoint__System_Single__SkiaSharp_SKPoint__" data-uid="DrawnUi.Draw.VisualLayer.DecomposeMatrix(SkiaSharp.SKMatrix,SkiaSharp.SKPoint@,System.Single@,SkiaSharp.SKPoint@)">
  DecomposeMatrix(SKMatrix, out SKPoint, out float, out SKPoint)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L141"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DecomposeMatrix(SKMatrix m, out SKPoint scale, out float rotation, out SKPoint translation)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>m</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix">SKMatrix</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a></dt>
    <dd></dd>
    <dt><code>rotation</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>translation</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_VisualLayer_Render_" data-uid="DrawnUi.Draw.VisualLayer.Render*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_Render_DrawnUi_Draw_DrawingContext_" data-uid="DrawnUi.Draw.VisualLayer.Render(DrawnUi.Draw.DrawingContext)">
  Render(DrawingContext)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L121"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Render(DrawingContext context)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_VisualLayer_TransformRect_" data-uid="DrawnUi.Draw.VisualLayer.TransformRect*"></a>

  <h3 id="DrawnUi_Draw_VisualLayer_TransformRect_SkiaSharp_SKRect_SkiaSharp_SKMatrix_" data-uid="DrawnUi.Draw.VisualLayer.TransformRect(SkiaSharp.SKRect,SkiaSharp.SKMatrix)">
  TransformRect(SKRect, SKMatrix)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L378"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Helper to transform a rectangle using a matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKRect TransformRect(SKRect rect, SKMatrix matrix)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rect</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>matrix</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix">SKMatrix</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualLayer.cs/#L3" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
