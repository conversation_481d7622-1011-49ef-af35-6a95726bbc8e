import{a as s}from"./chunk-EDJWACL4.min.js";import{a as i}from"./chunk-5IIW54K6.min.js";import{a as g}from"./chunk-WXIN66R4.min.js";import"./chunk-33FU46FA.min.js";import"./chunk-OZ2RCKQJ.min.js";import"./chunk-PDS7545E.min.js";import"./chunk-IJ4BRSPX.min.js";import"./chunk-UEFJDIUO.min.js";import"./chunk-BIJFJY5F.min.js";import"./chunk-U4DUTLYF.min.js";import"./chunk-IQQ46AC6.min.js";import{O as n,h as r,j as a}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import"./chunk-OSRY5VT3.min.js";var v={parse:r(async e=>{let t=await g("info",e);a.debug(t)},"parse")},d={version:s},m=r(()=>d.version,"getVersion"),c={getVersion:m},f=r((e,t,p)=>{a.debug(`rendering info diagram
`+e);let o=i(t);n(o,100,400,!0),o.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${p}`)},"draw"),l={draw:f},y={parser:v,db:c,renderer:l};export{y as diagram};
//# sourceMappingURL=infoDiagram-A4XQUW5V-SKLVFWJI.min.js.map
