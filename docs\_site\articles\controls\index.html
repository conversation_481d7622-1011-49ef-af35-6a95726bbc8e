<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Controls Overview | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Controls Overview | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="../toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/index.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="controls-overview">Controls Overview</h1>

<p>DrawnUi positions itsself as an angine providing a toolset to create and use custom drawn controls. Out-of-the box it provides you with base controls that can be used a lego-bricks to composite custom controls, and proposes some useful pre-made custom controls.</p>
<p>The main spirit is to have all controlls subclassable and customizable at the maximum possible extent.</p>
<p>DrawnUi provides a comprehensive set of UI controls rendered with SkiaSharp for optimal performance. All controls support platform-specific styling and extensive customization options.</p>
<h2 id="control-categories">Control Categories</h2>
<p>DrawnUi controls can be organized into several categories:</p>
<h3 id="aliases">Aliases</h3>
<p>There are controls that are aliases for other controls, te make porting existing native apps easier, to replace one name in code with another:</p>
<ul>
<li>SkiaFrame is an alias for SkiaShape of Rectangle type, MAUI Frame</li>
<li>SkiaStack is for SkiaLayout type Column with default horizontal Fill, MAUI VerticalStackLayout</li>
<li>SkiaRow is for SkiaLayout type Row, MAUI HorizontalStackLayout</li>
<li>SkiaLayer is for SkiaLayout type Absolute with default horizontal Fill, MAUI Grid with 1 col/row used for layering</li>
<li>SkiaGrid is for SkiaLayout type Grid with default horizontal Fill, MAUI Grid</li>
<li>SkiaWrap is for SkiaLayout type Wrap with default horizontal Fill, a bit similar to MAUI FlexLayout</li>
</ul>
<h3 id="layout-controls">Layout Controls</h3>
<ul>
<li><a href="layouts.html#skialayout">SkiaLayout</a>: Base layout container. supported types: Absolute, Grid, Column, Row, Wrap.</li>
<li>SkiaScroll: Scrolling container with virtualization support</li>
<li>SnappingLayout: Base class for snap points</li>
<li>SkiaDrawer: Swipe-in/out panel, subclassed SnappingLayout,</li>
<li>SkiaCarousel: Swipeable carousel, subclassed SnappingLayout</li>
<li><a href="layouts.html#contentlayout">ContentLayout</a>: Optimized for single child, SkiaShape derives from this one</li>
<li>SkiaShape: Base class for all shapes, can wrap other elements to be clipped inside</li>
</ul>
<h3 id="text-controls">Text Controls</h3>
<ul>
<li><a href="text.html#skialabel">SkiaLabel</a>: High-performance text rendering, supports spans</li>
<li><a href="text.html#skiamarkdownlabel">SkiaMarkdownLabel</a>: Markdown-capable text, unicode friendly, autofinds font its text (auto-creates spans). Use for text with complex formatting, smileys, and different languages</li>
</ul>
<h3 id="graphics-controls">Graphics Controls</h3>
<ul>
<li><a href="images.html#skiaimage">SkiaImage</a>: Image rendering with many options and filters</li>
<li><a href="images.html#skiasvg">SkiaSvg</a>: SVG rendering with many options</li>
<li><a href="images.html#skiagif">SkiaGif</a>: Animated GIF support - dedicated lightweight GIF-player with playback properties</li>
<li><a href="images.html#skiamediaimage">SkiaMediaImage</a>: Media image, subclassed SkiaImage for displaying any kind of images (image/animated gif/more..)</li>
<li><a href="animations.html#skialottie">SkiaLottie</a>: Lottie animation with tint customization, subclassed SkiaImage</li>
</ul>
<h3 id="button-controls">Button Controls</h3>
<p>It's important to notice that every control can behaive like a button with gestures attached, but here is a pre-made button control, providing different platforms looks via <code>ControlStyle</code> property:</p>
<ul>
<li><a href="buttons.html">SkiaButton</a>: Standard button with platform-specific styling</li>
</ul>
<h3 id="toggle-controls">Toggle Controls</h3>
<ul>
<li><a href="switches.html#skiaswitch">SkiaSwitch</a>: Platform-styled toggle switch to be able to toggle anything</li>
<li><a href="switches.html#skiacheckbox">SkiaCheckbox</a>: Platform-styled checkbox</li>
<li><a href="switches.html#skiatoggle">SkiaToggle</a>: Base toggle class for custom toggles</li>
<li><a href="switches.html#skiaradiobutton">SkiaRadioButton</a>: Radio button to select something unique from options, subclassed SkiaToggle</li>
</ul>
<h3 id="navigation-controls">Navigation Controls</h3>
<ul>
<li><a href="shell.html">SkiaShell</a>: Navigation framework for navigation inside a drawn app, subclassed SkiaLayout</li>
<li><a href="shell.html#skiaviewswitcher">SkiaViewSwitcher</a>: View switcher to switch your views, pop, push and slide, subclassed SkiaLayout</li>
</ul>
<h3 id="input-controls">Input Controls</h3>
<ul>
<li><a href="input.html#skiaslider">SkiaSlider</a>: Slider including range selection capability</li>
<li><a href="input.html#skiaprogress">SkiaProgress</a>: Progress indicator to show that you are actually doing something</li>
<li><a href="input.html#skiawheelpicker">SkiaWheelPicker</a>: iOS-look picker wheel</li>
<li><a href="input.html#skiaspinner">SkiaSpinner</a>: Spinner to test your luck</li>
</ul>
<h3 id="specialized-controls">Specialized Controls</h3>
<ul>
<li><a href="scroll.html#skiascrolllooped">SkiaScrollLooped</a>: Subclassed SkiaScroll for neverending scrolls</li>
<li><a href="layouts.html#skiadecoratedgrid">SkiaDecoratedGrid</a>: Grid able to draw shapes between rows and columns</li>
<li><a href="scroll.html#refreshindicator">RefreshIndicator</a>: Can use Lottie and anything as ActivityIndicator or for your scroll RefreshView</li>
<li><a href="shapes.html#skiahovermask">SkiaHoverMask</a>: Overlay a clipping shape</li>
<li><a href="shell.html#skiatabsselector">SkiaTabsSelector</a>: Create top and bottom tabs</li>
<li><a href="native-integration.html#skiacamera">SkiaCamera</a>: Camera control that draws on the canvas</li>
<li><a href="layouts.html#skiahotspot">SkiaHotspot</a>: Handle gestures in a lazy way</li>
<li><a href="layouts.html#skiabackdrop">SkiaBackdrop</a>: Apply effects to background below, like blur etc</li>
<li><a href="native-integration.html#skiamauielement">SkiaMauiElement</a>: Embed MAUI controls in your canvas</li>
</ul>
<h3 id="development-controls">Development Controls</h3>
<ul>
<li><a href="text.html#skialabelfps">SkiaLabelFps</a>: FPS display for development</li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/index.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
