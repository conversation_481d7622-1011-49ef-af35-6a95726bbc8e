{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.it.js"], "sourcesContent": ["/*!\n * Lunr languages, `Italian` language\n * https://github.com/Mihai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.it = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.it.trimmer,\n        lunr.it.stopWordFilter,\n        lunr.it.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.it.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.it.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.it.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.it.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.it.trimmer, 'trimmer-it');\n\n    /* lunr stemmer function */\n    lunr.it.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function ItalianStemmer() {\n          var a_0 = [new Among(\"\", -1, 7), new Among(\"qu\", 0, 6),\n              new Among(\"\\u00E1\", 0, 1), new Among(\"\\u00E9\", 0, 2),\n              new Among(\"\\u00ED\", 0, 3), new Among(\"\\u00F3\", 0, 4),\n              new Among(\"\\u00FA\", 0, 5)\n            ],\n            a_1 = [new Among(\"\", -1, 3),\n              new Among(\"I\", 0, 1), new Among(\"U\", 0, 2)\n            ],\n            a_2 = [\n              new Among(\"la\", -1, -1), new Among(\"cela\", 0, -1),\n              new Among(\"gliela\", 0, -1), new Among(\"mela\", 0, -1),\n              new Among(\"tela\", 0, -1), new Among(\"vela\", 0, -1),\n              new Among(\"le\", -1, -1), new Among(\"cele\", 6, -1),\n              new Among(\"gliele\", 6, -1), new Among(\"mele\", 6, -1),\n              new Among(\"tele\", 6, -1), new Among(\"vele\", 6, -1),\n              new Among(\"ne\", -1, -1), new Among(\"cene\", 12, -1),\n              new Among(\"gliene\", 12, -1), new Among(\"mene\", 12, -1),\n              new Among(\"sene\", 12, -1), new Among(\"tene\", 12, -1),\n              new Among(\"vene\", 12, -1), new Among(\"ci\", -1, -1),\n              new Among(\"li\", -1, -1), new Among(\"celi\", 20, -1),\n              new Among(\"glieli\", 20, -1), new Among(\"meli\", 20, -1),\n              new Among(\"teli\", 20, -1), new Among(\"veli\", 20, -1),\n              new Among(\"gli\", 20, -1), new Among(\"mi\", -1, -1),\n              new Among(\"si\", -1, -1), new Among(\"ti\", -1, -1),\n              new Among(\"vi\", -1, -1), new Among(\"lo\", -1, -1),\n              new Among(\"celo\", 31, -1), new Among(\"glielo\", 31, -1),\n              new Among(\"melo\", 31, -1), new Among(\"telo\", 31, -1),\n              new Among(\"velo\", 31, -1)\n            ],\n            a_3 = [new Among(\"ando\", -1, 1),\n              new Among(\"endo\", -1, 1), new Among(\"ar\", -1, 2),\n              new Among(\"er\", -1, 2), new Among(\"ir\", -1, 2)\n            ],\n            a_4 = [\n              new Among(\"ic\", -1, -1), new Among(\"abil\", -1, -1),\n              new Among(\"os\", -1, -1), new Among(\"iv\", -1, 1)\n            ],\n            a_5 = [\n              new Among(\"ic\", -1, 1), new Among(\"abil\", -1, 1),\n              new Among(\"iv\", -1, 1)\n            ],\n            a_6 = [new Among(\"ica\", -1, 1),\n              new Among(\"logia\", -1, 3), new Among(\"osa\", -1, 1),\n              new Among(\"ista\", -1, 1), new Among(\"iva\", -1, 9),\n              new Among(\"anza\", -1, 1), new Among(\"enza\", -1, 5),\n              new Among(\"ice\", -1, 1), new Among(\"atrice\", 7, 1),\n              new Among(\"iche\", -1, 1), new Among(\"logie\", -1, 3),\n              new Among(\"abile\", -1, 1), new Among(\"ibile\", -1, 1),\n              new Among(\"usione\", -1, 4), new Among(\"azione\", -1, 2),\n              new Among(\"uzione\", -1, 4), new Among(\"atore\", -1, 2),\n              new Among(\"ose\", -1, 1), new Among(\"ante\", -1, 1),\n              new Among(\"mente\", -1, 1), new Among(\"amente\", 19, 7),\n              new Among(\"iste\", -1, 1), new Among(\"ive\", -1, 9),\n              new Among(\"anze\", -1, 1), new Among(\"enze\", -1, 5),\n              new Among(\"ici\", -1, 1), new Among(\"atrici\", 25, 1),\n              new Among(\"ichi\", -1, 1), new Among(\"abili\", -1, 1),\n              new Among(\"ibili\", -1, 1), new Among(\"ismi\", -1, 1),\n              new Among(\"usioni\", -1, 4), new Among(\"azioni\", -1, 2),\n              new Among(\"uzioni\", -1, 4), new Among(\"atori\", -1, 2),\n              new Among(\"osi\", -1, 1), new Among(\"anti\", -1, 1),\n              new Among(\"amenti\", -1, 6), new Among(\"imenti\", -1, 6),\n              new Among(\"isti\", -1, 1), new Among(\"ivi\", -1, 9),\n              new Among(\"ico\", -1, 1), new Among(\"ismo\", -1, 1),\n              new Among(\"oso\", -1, 1), new Among(\"amento\", -1, 6),\n              new Among(\"imento\", -1, 6), new Among(\"ivo\", -1, 9),\n              new Among(\"it\\u00E0\", -1, 8), new Among(\"ist\\u00E0\", -1, 1),\n              new Among(\"ist\\u00E8\", -1, 1), new Among(\"ist\\u00EC\", -1, 1)\n            ],\n            a_7 = [\n              new Among(\"isca\", -1, 1), new Among(\"enda\", -1, 1),\n              new Among(\"ata\", -1, 1), new Among(\"ita\", -1, 1),\n              new Among(\"uta\", -1, 1), new Among(\"ava\", -1, 1),\n              new Among(\"eva\", -1, 1), new Among(\"iva\", -1, 1),\n              new Among(\"erebbe\", -1, 1), new Among(\"irebbe\", -1, 1),\n              new Among(\"isce\", -1, 1), new Among(\"ende\", -1, 1),\n              new Among(\"are\", -1, 1), new Among(\"ere\", -1, 1),\n              new Among(\"ire\", -1, 1), new Among(\"asse\", -1, 1),\n              new Among(\"ate\", -1, 1), new Among(\"avate\", 16, 1),\n              new Among(\"evate\", 16, 1), new Among(\"ivate\", 16, 1),\n              new Among(\"ete\", -1, 1), new Among(\"erete\", 20, 1),\n              new Among(\"irete\", 20, 1), new Among(\"ite\", -1, 1),\n              new Among(\"ereste\", -1, 1), new Among(\"ireste\", -1, 1),\n              new Among(\"ute\", -1, 1), new Among(\"erai\", -1, 1),\n              new Among(\"irai\", -1, 1), new Among(\"isci\", -1, 1),\n              new Among(\"endi\", -1, 1), new Among(\"erei\", -1, 1),\n              new Among(\"irei\", -1, 1), new Among(\"assi\", -1, 1),\n              new Among(\"ati\", -1, 1), new Among(\"iti\", -1, 1),\n              new Among(\"eresti\", -1, 1), new Among(\"iresti\", -1, 1),\n              new Among(\"uti\", -1, 1), new Among(\"avi\", -1, 1),\n              new Among(\"evi\", -1, 1), new Among(\"ivi\", -1, 1),\n              new Among(\"isco\", -1, 1), new Among(\"ando\", -1, 1),\n              new Among(\"endo\", -1, 1), new Among(\"Yamo\", -1, 1),\n              new Among(\"iamo\", -1, 1), new Among(\"avamo\", -1, 1),\n              new Among(\"evamo\", -1, 1), new Among(\"ivamo\", -1, 1),\n              new Among(\"eremo\", -1, 1), new Among(\"iremo\", -1, 1),\n              new Among(\"assimo\", -1, 1), new Among(\"ammo\", -1, 1),\n              new Among(\"emmo\", -1, 1), new Among(\"eremmo\", 54, 1),\n              new Among(\"iremmo\", 54, 1), new Among(\"immo\", -1, 1),\n              new Among(\"ano\", -1, 1), new Among(\"iscano\", 58, 1),\n              new Among(\"avano\", 58, 1), new Among(\"evano\", 58, 1),\n              new Among(\"ivano\", 58, 1), new Among(\"eranno\", -1, 1),\n              new Among(\"iranno\", -1, 1), new Among(\"ono\", -1, 1),\n              new Among(\"iscono\", 65, 1), new Among(\"arono\", 65, 1),\n              new Among(\"erono\", 65, 1), new Among(\"irono\", 65, 1),\n              new Among(\"erebbero\", -1, 1), new Among(\"irebbero\", -1, 1),\n              new Among(\"assero\", -1, 1), new Among(\"essero\", -1, 1),\n              new Among(\"issero\", -1, 1), new Among(\"ato\", -1, 1),\n              new Among(\"ito\", -1, 1), new Among(\"uto\", -1, 1),\n              new Among(\"avo\", -1, 1), new Among(\"evo\", -1, 1),\n              new Among(\"ivo\", -1, 1), new Among(\"ar\", -1, 1),\n              new Among(\"ir\", -1, 1), new Among(\"er\\u00E0\", -1, 1),\n              new Among(\"ir\\u00E0\", -1, 1), new Among(\"er\\u00F2\", -1, 1),\n              new Among(\"ir\\u00F2\", -1, 1)\n            ],\n            g_v = [17, 65, 16, 0, 0, 0, 0, 0, 0,\n              0, 0, 0, 0, 0, 0, 128, 128, 8, 2, 1\n            ],\n            g_AEIO = [17, 65, 0, 0, 0, 0,\n              0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 128, 8, 2\n            ],\n            g_CG = [17],\n            I_p2, I_p1, I_pV, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function habr1(c1, c2, v_1) {\n            if (sbp.eq_s(1, c1)) {\n              sbp.ket = sbp.cursor;\n              if (sbp.in_grouping(g_v, 97, 249)) {\n                sbp.slice_from(c2);\n                sbp.cursor = v_1;\n                return true;\n              }\n            }\n            return false;\n          }\n\n          function r_prelude() {\n            var among_var, v_1 = sbp.cursor,\n              v_2, v_3, v_4;\n            while (true) {\n              sbp.bra = sbp.cursor;\n              among_var = sbp.find_among(a_0, 7);\n              if (among_var) {\n                sbp.ket = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"\\u00E0\");\n                    continue;\n                  case 2:\n                    sbp.slice_from(\"\\u00E8\");\n                    continue;\n                  case 3:\n                    sbp.slice_from(\"\\u00EC\");\n                    continue;\n                  case 4:\n                    sbp.slice_from(\"\\u00F2\");\n                    continue;\n                  case 5:\n                    sbp.slice_from(\"\\u00F9\");\n                    continue;\n                  case 6:\n                    sbp.slice_from(\"qU\");\n                    continue;\n                  case 7:\n                    if (sbp.cursor >= sbp.limit)\n                      break;\n                    sbp.cursor++;\n                    continue;\n                }\n              }\n              break;\n            }\n            sbp.cursor = v_1;\n            while (true) {\n              v_2 = sbp.cursor;\n              while (true) {\n                v_3 = sbp.cursor;\n                if (sbp.in_grouping(g_v, 97, 249)) {\n                  sbp.bra = sbp.cursor;\n                  v_4 = sbp.cursor;\n                  if (habr1(\"u\", \"U\", v_3))\n                    break;\n                  sbp.cursor = v_4;\n                  if (habr1(\"i\", \"I\", v_3))\n                    break;\n                }\n                sbp.cursor = v_3;\n                if (sbp.cursor >= sbp.limit) {\n                  sbp.cursor = v_2;\n                  return;\n                }\n                sbp.cursor++;\n              }\n            }\n          }\n\n          function habr2(v_1) {\n            sbp.cursor = v_1;\n            if (!sbp.in_grouping(g_v, 97, 249))\n              return false;\n            while (!sbp.out_grouping(g_v, 97, 249)) {\n              if (sbp.cursor >= sbp.limit)\n                return false;\n              sbp.cursor++;\n            }\n            return true;\n          }\n\n          function habr3() {\n            if (sbp.in_grouping(g_v, 97, 249)) {\n              var v_1 = sbp.cursor;\n              if (sbp.out_grouping(g_v, 97, 249)) {\n                while (!sbp.in_grouping(g_v, 97, 249)) {\n                  if (sbp.cursor >= sbp.limit)\n                    return habr2(v_1);\n                  sbp.cursor++;\n                }\n                return true;\n              }\n              return habr2(v_1);\n            }\n            return false;\n          }\n\n          function habr4() {\n            var v_1 = sbp.cursor,\n              v_2;\n            if (!habr3()) {\n              sbp.cursor = v_1;\n              if (!sbp.out_grouping(g_v, 97, 249))\n                return;\n              v_2 = sbp.cursor;\n              if (sbp.out_grouping(g_v, 97, 249)) {\n                while (!sbp.in_grouping(g_v, 97, 249)) {\n                  if (sbp.cursor >= sbp.limit) {\n                    sbp.cursor = v_2;\n                    if (sbp.in_grouping(g_v, 97, 249) &&\n                      sbp.cursor < sbp.limit)\n                      sbp.cursor++;\n                    return;\n                  }\n                  sbp.cursor++;\n                }\n                I_pV = sbp.cursor;\n                return;\n              }\n              sbp.cursor = v_2;\n              if (!sbp.in_grouping(g_v, 97, 249) || sbp.cursor >= sbp.limit)\n                return;\n              sbp.cursor++;\n            }\n            I_pV = sbp.cursor;\n          }\n\n          function habr5() {\n            while (!sbp.in_grouping(g_v, 97, 249)) {\n              if (sbp.cursor >= sbp.limit)\n                return false;\n              sbp.cursor++;\n            }\n            while (!sbp.out_grouping(g_v, 97, 249)) {\n              if (sbp.cursor >= sbp.limit)\n                return false;\n              sbp.cursor++;\n            }\n            return true;\n          }\n\n          function r_mark_regions() {\n            var v_1 = sbp.cursor;\n            I_pV = sbp.limit;\n            I_p1 = I_pV;\n            I_p2 = I_pV;\n            habr4();\n            sbp.cursor = v_1;\n            if (habr5()) {\n              I_p1 = sbp.cursor;\n              if (habr5())\n                I_p2 = sbp.cursor;\n            }\n          }\n\n          function r_postlude() {\n            var among_var;\n            while (true) {\n              sbp.bra = sbp.cursor;\n              among_var = sbp.find_among(a_1, 3);\n              if (!among_var)\n                break;\n              sbp.ket = sbp.cursor;\n              switch (among_var) {\n                case 1:\n                  sbp.slice_from(\"i\");\n                  break;\n                case 2:\n                  sbp.slice_from(\"u\");\n                  break;\n                case 3:\n                  if (sbp.cursor >= sbp.limit)\n                    return;\n                  sbp.cursor++;\n                  break;\n              }\n            }\n          }\n\n          function r_RV() {\n            return I_pV <= sbp.cursor;\n          }\n\n          function r_R1() {\n            return I_p1 <= sbp.cursor;\n          }\n\n          function r_R2() {\n            return I_p2 <= sbp.cursor;\n          }\n\n          function r_attached_pronoun() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            if (sbp.find_among_b(a_2, 37)) {\n              sbp.bra = sbp.cursor;\n              among_var = sbp.find_among_b(a_3, 5);\n              if (among_var && r_RV()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                    sbp.slice_from(\"e\");\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_standard_suffix() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_6, 51);\n            if (!among_var)\n              return false;\n            sbp.bra = sbp.cursor;\n            switch (among_var) {\n              case 1:\n                if (!r_R2())\n                  return false;\n                sbp.slice_del();\n                break;\n              case 2:\n                if (!r_R2())\n                  return false;\n                sbp.slice_del();\n                sbp.ket = sbp.cursor;\n                if (sbp.eq_s_b(2, \"ic\")) {\n                  sbp.bra = sbp.cursor;\n                  if (r_R2())\n                    sbp.slice_del();\n                }\n                break;\n              case 3:\n                if (!r_R2())\n                  return false;\n                sbp.slice_from(\"log\");\n                break;\n              case 4:\n                if (!r_R2())\n                  return false;\n                sbp.slice_from(\"u\");\n                break;\n              case 5:\n                if (!r_R2())\n                  return false;\n                sbp.slice_from(\"ente\");\n                break;\n              case 6:\n                if (!r_RV())\n                  return false;\n                sbp.slice_del();\n                break;\n              case 7:\n                if (!r_R1())\n                  return false;\n                sbp.slice_del();\n                sbp.ket = sbp.cursor;\n                among_var = sbp.find_among_b(a_4, 4);\n                if (among_var) {\n                  sbp.bra = sbp.cursor;\n                  if (r_R2()) {\n                    sbp.slice_del();\n                    if (among_var == 1) {\n                      sbp.ket = sbp.cursor;\n                      if (sbp.eq_s_b(2, \"at\")) {\n                        sbp.bra = sbp.cursor;\n                        if (r_R2())\n                          sbp.slice_del();\n                      }\n                    }\n                  }\n                }\n                break;\n              case 8:\n                if (!r_R2())\n                  return false;\n                sbp.slice_del();\n                sbp.ket = sbp.cursor;\n                among_var = sbp.find_among_b(a_5, 3);\n                if (among_var) {\n                  sbp.bra = sbp.cursor;\n                  if (among_var == 1)\n                    if (r_R2())\n                      sbp.slice_del();\n                }\n                break;\n              case 9:\n                if (!r_R2())\n                  return false;\n                sbp.slice_del();\n                sbp.ket = sbp.cursor;\n                if (sbp.eq_s_b(2, \"at\")) {\n                  sbp.bra = sbp.cursor;\n                  if (r_R2()) {\n                    sbp.slice_del();\n                    sbp.ket = sbp.cursor;\n                    if (sbp.eq_s_b(2, \"ic\")) {\n                      sbp.bra = sbp.cursor;\n                      if (r_R2())\n                        sbp.slice_del();\n                    }\n                  }\n                }\n                break;\n            }\n            return true;\n          }\n\n          function r_verb_suffix() {\n            var among_var, v_1;\n            if (sbp.cursor >= I_pV) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_pV;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_7, 87);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                if (among_var == 1)\n                  sbp.slice_del();\n              }\n              sbp.limit_backward = v_1;\n            }\n          }\n\n          function habr6() {\n            var v_1 = sbp.limit - sbp.cursor;\n            sbp.ket = sbp.cursor;\n            if (sbp.in_grouping_b(g_AEIO, 97, 242)) {\n              sbp.bra = sbp.cursor;\n              if (r_RV()) {\n                sbp.slice_del();\n                sbp.ket = sbp.cursor;\n                if (sbp.eq_s_b(1, \"i\")) {\n                  sbp.bra = sbp.cursor;\n                  if (r_RV()) {\n                    sbp.slice_del();\n                    return;\n                  }\n                }\n              }\n            }\n            sbp.cursor = sbp.limit - v_1;\n          }\n\n          function r_vowel_suffix() {\n            habr6();\n            sbp.ket = sbp.cursor;\n            if (sbp.eq_s_b(1, \"h\")) {\n              sbp.bra = sbp.cursor;\n              if (sbp.in_grouping_b(g_CG, 99, 103))\n                if (r_RV())\n                  sbp.slice_del();\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_prelude();\n            sbp.cursor = v_1;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            r_attached_pronoun();\n            sbp.cursor = sbp.limit;\n            if (!r_standard_suffix()) {\n              sbp.cursor = sbp.limit;\n              r_verb_suffix();\n            }\n            sbp.cursor = sbp.limit;\n            r_vowel_suffix();\n            sbp.cursor = sbp.limit_backward;\n            r_postlude();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.it.stemmer, 'stemmer-it');\n\n    lunr.it.stopWordFilter = lunr.generateStopWordFilter('a abbia abbiamo abbiano abbiate ad agl agli ai al all alla alle allo anche avemmo avendo avesse avessero avessi avessimo aveste avesti avete aveva avevamo avevano avevate avevi avevo avrai avranno avrebbe avrebbero avrei avremmo avremo avreste avresti avrete avrà avrò avuta avute avuti avuto c che chi ci coi col come con contro cui da dagl dagli dai dal dall dalla dalle dallo degl degli dei del dell della delle dello di dov dove e ebbe ebbero ebbi ed era erano eravamo eravate eri ero essendo faccia facciamo facciano facciate faccio facemmo facendo facesse facessero facessi facessimo faceste facesti faceva facevamo facevano facevate facevi facevo fai fanno farai faranno farebbe farebbero farei faremmo faremo fareste faresti farete farà farò fece fecero feci fosse fossero fossi fossimo foste fosti fu fui fummo furono gli ha hai hanno ho i il in io l la le lei li lo loro lui ma mi mia mie miei mio ne negl negli nei nel nell nella nelle nello noi non nostra nostre nostri nostro o per perché più quale quanta quante quanti quanto quella quelle quelli quello questa queste questi questo sarai saranno sarebbe sarebbero sarei saremmo saremo sareste saresti sarete sarà sarò se sei si sia siamo siano siate siete sono sta stai stando stanno starai staranno starebbe starebbero starei staremmo staremo stareste staresti starete starà starò stava stavamo stavano stavate stavi stavo stemmo stesse stessero stessi stessimo steste stesti stette stettero stetti stia stiamo stiano stiate sto su sua sue sugl sugli sui sul sull sulla sulle sullo suo suoi ti tra tu tua tue tuo tuoi tutti tutto un una uno vi voi vostra vostre vostri vostro è'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.it.stopWordFilter, 'stopWordFilter-it');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAA0B,CACjC,IAAIC,EAAM,CAAC,IAAIH,EAAM,GAAI,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,EAAG,CAAC,EACjD,IAAIA,EAAM,OAAU,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,EACnD,IAAIA,EAAM,OAAU,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,EACnD,IAAIA,EAAM,OAAU,EAAG,CAAC,CAC1B,EACAI,EAAM,CAAC,IAAIJ,EAAM,GAAI,GAAI,CAAC,EACxB,IAAIA,EAAM,IAAK,EAAG,CAAC,EAAG,IAAIA,EAAM,IAAK,EAAG,CAAC,CAC3C,EACAK,EAAM,CACJ,IAAIL,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EAChD,IAAIA,EAAM,SAAU,EAAG,EAAE,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EACnD,IAAIA,EAAM,OAAQ,EAAG,EAAE,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EACjD,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EAChD,IAAIA,EAAM,SAAU,EAAG,EAAE,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EACnD,IAAIA,EAAM,OAAQ,EAAG,EAAE,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EACjD,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EACjD,IAAIA,EAAM,SAAU,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EACrD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EACnD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EACjD,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EACjD,IAAIA,EAAM,SAAU,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EACrD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EACnD,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAChD,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAU,GAAI,EAAE,EACrD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EACnD,IAAIA,EAAM,OAAQ,GAAI,EAAE,CAC1B,EACAM,EAAM,CAAC,IAAIN,EAAM,OAAQ,GAAI,CAAC,EAC5B,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC/C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,CAC/C,EACAO,EAAM,CACJ,IAAIP,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EACjD,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,CAChD,EACAQ,EAAM,CACJ,IAAIR,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,KAAM,GAAI,CAAC,CACvB,EACAS,EAAM,CAAC,IAAIT,EAAM,MAAO,GAAI,CAAC,EAC3B,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,EAAG,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAClD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACpD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACpD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EAClD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAClD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACpD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EAClD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAClD,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EAC1D,IAAIA,EAAM,UAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,CAC7D,EACAU,EAAM,CACJ,IAAIV,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACjD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAClD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACnD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACnD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACnD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EAClD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACpD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAClD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACpD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,CAAC,EACzD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAClD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EACnD,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EACzD,IAAIA,EAAM,SAAY,GAAI,CAAC,CAC7B,EACAW,EAAM,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAChC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,IAAK,IAAK,EAAG,EAAG,CACpC,EACAC,EAAS,CAAC,GAAI,GAAI,EAAG,EAAG,EAAG,EACzB,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,IAAK,IAAK,EAAG,CAC1C,EACAC,EAAO,CAAC,EAAE,EACVC,EAAMC,EAAMC,EAAMC,EAAM,IAAIhB,EAC9B,KAAK,WAAa,SAASiB,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,EAAMC,EAAIC,EAAIC,EAAK,CAC1B,OAAIL,EAAI,KAAK,EAAGG,CAAE,IAChBH,EAAI,IAAMA,EAAI,OACVA,EAAI,YAAYN,EAAK,GAAI,GAAG,IAC9BM,EAAI,WAAWI,CAAE,EACjBJ,EAAI,OAASK,EACN,IAGJ,EACT,CAEA,SAASC,GAAY,CAGnB,QAFIC,EAAWF,EAAML,EAAI,OACvBQ,EAAKC,EAAKC,IACC,CAGX,GAFAV,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,WAAWd,EAAK,CAAC,EAC7BqB,EAEF,OADAP,EAAI,IAAMA,EAAI,OACNO,EAAW,CACjB,IAAK,GACHP,EAAI,WAAW,MAAQ,EACvB,SACF,IAAK,GACHA,EAAI,WAAW,MAAQ,EACvB,SACF,IAAK,GACHA,EAAI,WAAW,MAAQ,EACvB,SACF,IAAK,GACHA,EAAI,WAAW,MAAQ,EACvB,SACF,IAAK,GACHA,EAAI,WAAW,MAAQ,EACvB,SACF,IAAK,GACHA,EAAI,WAAW,IAAI,EACnB,SACF,IAAK,GACH,GAAIA,EAAI,QAAUA,EAAI,MACpB,MACFA,EAAI,SACJ,QACJ,CAEF,KACF,CAEA,IADAA,EAAI,OAASK,IAGX,IADAG,EAAMR,EAAI,OAERS,EAAMT,EAAI,OACN,EAAAA,EAAI,YAAYN,EAAK,GAAI,GAAG,IAC9BM,EAAI,IAAMA,EAAI,OACdU,EAAMV,EAAI,OACNE,EAAM,IAAK,IAAKO,CAAG,IAEvBT,EAAI,OAASU,EACTR,EAAM,IAAK,IAAKO,CAAG,MARd,CAYX,GADAT,EAAI,OAASS,EACTT,EAAI,QAAUA,EAAI,MAAO,CAC3BA,EAAI,OAASQ,EACb,MACF,CACAR,EAAI,QACN,CAEJ,CAEA,SAASW,EAAMN,EAAK,CAElB,GADAL,EAAI,OAASK,EACT,CAACL,EAAI,YAAYN,EAAK,GAAI,GAAG,EAC/B,MAAO,GACT,KAAO,CAACM,EAAI,aAAaN,EAAK,GAAI,GAAG,GAAG,CACtC,GAAIM,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASY,GAAQ,CACf,GAAIZ,EAAI,YAAYN,EAAK,GAAI,GAAG,EAAG,CACjC,IAAIW,EAAML,EAAI,OACd,GAAIA,EAAI,aAAaN,EAAK,GAAI,GAAG,EAAG,CAClC,KAAO,CAACM,EAAI,YAAYN,EAAK,GAAI,GAAG,GAAG,CACrC,GAAIM,EAAI,QAAUA,EAAI,MACpB,OAAOW,EAAMN,CAAG,EAClBL,EAAI,QACN,CACA,MAAO,EACT,CACA,OAAOW,EAAMN,CAAG,CAClB,CACA,MAAO,EACT,CAEA,SAASQ,GAAQ,CACf,IAAIR,EAAML,EAAI,OACZQ,EACF,GAAI,CAACI,EAAM,EAAG,CAEZ,GADAZ,EAAI,OAASK,EACT,CAACL,EAAI,aAAaN,EAAK,GAAI,GAAG,EAChC,OAEF,GADAc,EAAMR,EAAI,OACNA,EAAI,aAAaN,EAAK,GAAI,GAAG,EAAG,CAClC,KAAO,CAACM,EAAI,YAAYN,EAAK,GAAI,GAAG,GAAG,CACrC,GAAIM,EAAI,QAAUA,EAAI,MAAO,CAC3BA,EAAI,OAASQ,EACTR,EAAI,YAAYN,EAAK,GAAI,GAAG,GAC9BM,EAAI,OAASA,EAAI,OACjBA,EAAI,SACN,MACF,CACAA,EAAI,QACN,CACAD,EAAOC,EAAI,OACX,MACF,CAEA,GADAA,EAAI,OAASQ,EACT,CAACR,EAAI,YAAYN,EAAK,GAAI,GAAG,GAAKM,EAAI,QAAUA,EAAI,MACtD,OACFA,EAAI,QACN,CACAD,EAAOC,EAAI,MACb,CAEA,SAASc,GAAQ,CACf,KAAO,CAACd,EAAI,YAAYN,EAAK,GAAI,GAAG,GAAG,CACrC,GAAIM,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,KAAO,CAACA,EAAI,aAAaN,EAAK,GAAI,GAAG,GAAG,CACtC,GAAIM,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASe,GAAiB,CACxB,IAAIV,EAAML,EAAI,OACdD,EAAOC,EAAI,MACXF,EAAOC,EACPF,EAAOE,EACPc,EAAM,EACNb,EAAI,OAASK,EACTS,EAAM,IACRhB,EAAOE,EAAI,OACPc,EAAM,IACRjB,EAAOG,EAAI,QAEjB,CAEA,SAASgB,GAAa,CAEpB,QADIT,EAEFP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,WAAWb,EAAK,CAAC,EAC7B,EAACoB,GAGL,OADAP,EAAI,IAAMA,EAAI,OACNO,EAAW,CACjB,IAAK,GACHP,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACH,GAAIA,EAAI,QAAUA,EAAI,MACpB,OACFA,EAAI,SACJ,KACJ,CAEJ,CAEA,SAASiB,GAAO,CACd,OAAOlB,GAAQC,EAAI,MACrB,CAEA,SAASkB,GAAO,CACd,OAAOpB,GAAQE,EAAI,MACrB,CAEA,SAASmB,GAAO,CACd,OAAOtB,GAAQG,EAAI,MACrB,CAEA,SAASoB,GAAqB,CAC5B,IAAIb,EAEJ,GADAP,EAAI,IAAMA,EAAI,OACVA,EAAI,aAAaZ,EAAK,EAAE,IAC1BY,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaX,EAAK,CAAC,EAC/BkB,GAAaU,EAAK,GACpB,OAAQV,EAAW,CACjB,IAAK,GACHP,EAAI,UAAU,EACd,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,KACJ,CAGN,CAEA,SAASqB,GAAoB,CAC3B,IAAId,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaR,EAAK,EAAE,EAChC,CAACe,EACH,MAAO,GAET,OADAP,EAAI,IAAMA,EAAI,OACNO,EAAW,CACjB,IAAK,GACH,GAAI,CAACY,EAAK,EACR,MAAO,GACTnB,EAAI,UAAU,EACd,MACF,IAAK,GACH,GAAI,CAACmB,EAAK,EACR,MAAO,GACTnB,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVmB,EAAK,GACPnB,EAAI,UAAU,GAElB,MACF,IAAK,GACH,GAAI,CAACmB,EAAK,EACR,MAAO,GACTnB,EAAI,WAAW,KAAK,EACpB,MACF,IAAK,GACH,GAAI,CAACmB,EAAK,EACR,MAAO,GACTnB,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACH,GAAI,CAACmB,EAAK,EACR,MAAO,GACTnB,EAAI,WAAW,MAAM,EACrB,MACF,IAAK,GACH,GAAI,CAACiB,EAAK,EACR,MAAO,GACTjB,EAAI,UAAU,EACd,MACF,IAAK,GACH,GAAI,CAACkB,EAAK,EACR,MAAO,GACTlB,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaV,EAAK,CAAC,EAC/BiB,IACFP,EAAI,IAAMA,EAAI,OACVmB,EAAK,IACPnB,EAAI,UAAU,EACVO,GAAa,IACfP,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVmB,EAAK,GACPnB,EAAI,UAAU,MAKxB,MACF,IAAK,GACH,GAAI,CAACmB,EAAK,EACR,MAAO,GACTnB,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaT,EAAK,CAAC,EAC/BgB,IACFP,EAAI,IAAMA,EAAI,OACVO,GAAa,GACXY,EAAK,GACPnB,EAAI,UAAU,GAEpB,MACF,IAAK,GACH,GAAI,CAACmB,EAAK,EACR,MAAO,GACTnB,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVmB,EAAK,IACPnB,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVmB,EAAK,GACPnB,EAAI,UAAU,KAItB,KACJ,CACA,MAAO,EACT,CAEA,SAASsB,GAAgB,CACvB,IAAIf,EAAWF,EACXL,EAAI,QAAUD,IAChBM,EAAML,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaP,EAAK,EAAE,EAChCc,IACFP,EAAI,IAAMA,EAAI,OACVO,GAAa,GACfP,EAAI,UAAU,GAElBA,EAAI,eAAiBK,EAEzB,CAEA,SAASkB,GAAQ,CACf,IAAIlB,EAAML,EAAI,MAAQA,EAAI,OAE1B,GADAA,EAAI,IAAMA,EAAI,OACVA,EAAI,cAAcL,EAAQ,GAAI,GAAG,IACnCK,EAAI,IAAMA,EAAI,OACViB,EAAK,IACPjB,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,IACnBA,EAAI,IAAMA,EAAI,OACViB,EAAK,KAAG,CACVjB,EAAI,UAAU,EACd,MACF,CAINA,EAAI,OAASA,EAAI,MAAQK,CAC3B,CAEA,SAASmB,GAAiB,CACxBD,EAAM,EACNvB,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,IACnBA,EAAI,IAAMA,EAAI,OACVA,EAAI,cAAcJ,EAAM,GAAI,GAAG,GAC7BqB,EAAK,GACPjB,EAAI,UAAU,EAEtB,CACA,KAAK,KAAO,UAAW,CACrB,IAAIK,EAAML,EAAI,OACd,OAAAM,EAAU,EACVN,EAAI,OAASK,EACbU,EAAe,EACff,EAAI,eAAiBK,EACrBL,EAAI,OAASA,EAAI,MACjBoB,EAAmB,EACnBpB,EAAI,OAASA,EAAI,MACZqB,EAAkB,IACrBrB,EAAI,OAASA,EAAI,MACjBsB,EAAc,GAEhBtB,EAAI,OAASA,EAAI,MACjBwB,EAAe,EACfxB,EAAI,OAASA,EAAI,eACjBgB,EAAW,EACJ,EACT,CACF,EAGF,OAAO,SAASS,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASxB,EAAM,CACjC,OAAAhB,EAAG,WAAWgB,CAAI,EAClBhB,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWwC,CAAK,EACnBxC,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,8oDAA6mD,MAAM,GAAG,CAAC,EAE5qDA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_it", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6", "a_7", "g_v", "g_AEIO", "g_CG", "I_p2", "I_p1", "I_pV", "sbp", "word", "habr1", "c1", "c2", "v_1", "r_prelude", "among_var", "v_2", "v_3", "v_4", "habr2", "habr3", "habr4", "habr5", "r_mark_regions", "r_postlude", "r_RV", "r_R1", "r_R2", "r_attached_pronoun", "r_standard_suffix", "r_verb_suffix", "habr6", "r_vowel_suffix", "token"]}