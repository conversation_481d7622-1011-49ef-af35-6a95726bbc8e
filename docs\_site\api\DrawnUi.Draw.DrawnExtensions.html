<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class DrawnExtensions | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class DrawnExtensions | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_DrawnExtensions.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.DrawnExtensions%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.DrawnExtensions">



  <h1 id="DrawnUi_Draw_DrawnExtensions" data-uid="DrawnUi.Draw.DrawnExtensions" class="text-break">
Class DrawnExtensions  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class DrawnExtensions</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">DrawnExtensions</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_DrawnExtensions_StartupSettings_" data-uid="DrawnUi.Draw.DrawnExtensions.StartupSettings*"></a>

  <h3 id="DrawnUi_Draw_DrawnExtensions_StartupSettings" data-uid="DrawnUi.Draw.DrawnExtensions.StartupSettings">
  StartupSettings
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs/#L18"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static DrawnUiStartupSettings StartupSettings { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.DrawnUiStartupSettings.html">DrawnUiStartupSettings</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_DrawnExtensions_AddFont_" data-uid="DrawnUi.Draw.DrawnExtensions.AddFont*"></a>

  <h3 id="DrawnUi_Draw_DrawnExtensions_AddFont_Microsoft_Maui_Hosting_IFontCollection_System_String_System_String_DrawnUi_Draw_FontWeight_" data-uid="DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,DrawnUi.Draw.FontWeight)">
  AddFont(IFontCollection, string, string, FontWeight)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs/#L453"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IFontCollection AddFont(this IFontCollection fontCollection, string filename, string alias, FontWeight weight)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>fontCollection</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting.ifontcollection">IFontCollection</a></dt>
    <dd></dd>
    <dt><code>filename</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>alias</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>weight</code> <a class="xref" href="DrawnUi.Draw.FontWeight.html">FontWeight</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting.ifontcollection">IFontCollection</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_DrawnExtensions_AddFont_" data-uid="DrawnUi.Draw.DrawnExtensions.AddFont*"></a>

  <h3 id="DrawnUi_Draw_DrawnExtensions_AddFont_Microsoft_Maui_Hosting_IFontCollection_System_String_System_String_System_Int32_" data-uid="DrawnUi.Draw.DrawnExtensions.AddFont(Microsoft.Maui.Hosting.IFontCollection,System.String,System.String,System.Int32)">
  AddFont(IFontCollection, string, string, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs/#L443"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IFontCollection AddFont(this IFontCollection fontCollection, string filename, string alias, int weight)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>fontCollection</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting.ifontcollection">IFontCollection</a></dt>
    <dd></dd>
    <dt><code>filename</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>alias</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>weight</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting.ifontcollection">IFontCollection</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_DrawnExtensions_AnimateRangeAsync_" data-uid="DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync*"></a>

  <h3 id="DrawnUi_Draw_DrawnExtensions_AnimateRangeAsync_DrawnUi_Draw_SkiaControl_System_Action_System_Double__System_Double_System_Double_System_UInt32_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_" data-uid="DrawnUi.Draw.DrawnExtensions.AnimateRangeAsync(DrawnUi.Draw.SkiaControl,System.Action{System.Double},System.Double,System.Double,System.UInt32,Microsoft.Maui.Easing,System.Threading.CancellationTokenSource)">
  AnimateRangeAsync(SkiaControl, Action&lt;double&gt;, double, double, uint, Easing, CancellationTokenSource)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs/#L467"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Task AnimateRangeAsync(this SkiaControl owner, Action&lt;double&gt; callback, double start, double end, uint length = 250, Easing easing = null, CancellationTokenSource _cancelTranslate = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>owner</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>callback</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-1">Action</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a>&gt;</dt>
    <dd></dd>
    <dt><code>start</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
    <dt><code>end</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
    <dt><code>length</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.uint32">uint</a></dt>
    <dd></dd>
    <dt><code>easing</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.easing">Easing</a></dt>
    <dd></dd>
    <dt><code>_cancelTranslate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource">CancellationTokenSource</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_" data-uid="DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild*"></a>

  <h3 id="DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_" data-uid="DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)">
  GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs/#L504"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (float RatioX, float RatioY) GetVelocityRatioForChild(this IDrawnBase container, ISkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>container</code> <a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></dt>
    <dd></dd>
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt>(<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-system.single,system.single-.x">X</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-system.single,system.single-.y">Y</a>)</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_DrawnExtensions_UseDrawnUi_" data-uid="DrawnUi.Draw.DrawnExtensions.UseDrawnUi*"></a>

  <h3 id="DrawnUi_Draw_DrawnExtensions_UseDrawnUi_Microsoft_Maui_Hosting_MauiAppBuilder_DrawnUi_Draw_DrawnUiStartupSettings_" data-uid="DrawnUi.Draw.DrawnExtensions.UseDrawnUi(Microsoft.Maui.Hosting.MauiAppBuilder,DrawnUi.Draw.DrawnUiStartupSettings)">
  UseDrawnUi(MauiAppBuilder, DrawnUiStartupSettings)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs/#L20"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MauiAppBuilder UseDrawnUi(this MauiAppBuilder builder, DrawnUiStartupSettings settings = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>builder</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting.mauiappbuilder">MauiAppBuilder</a></dt>
    <dd></dd>
    <dt><code>settings</code> <a class="xref" href="DrawnUi.Draw.DrawnUiStartupSettings.html">DrawnUiStartupSettings</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hosting.mauiappbuilder">MauiAppBuilder</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/DrawnExtensions.cs/#L11" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
