{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.jp.js"], "sourcesContent": ["// jp is the country code, while ja is the language code\n// a new lunr.ja.js has been created, but in order to\n// keep the backward compatibility, we'll leave the lunr.jp.js\n// here for a while, and just make it use the new lunr.ja.js\nmodule.exports = require('./lunr.ja');"], "mappings": "wFAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,CAIAA,EAAO,QAAU", "names": ["require_lunr_jp", "__commonJSMin", "exports", "module"]}