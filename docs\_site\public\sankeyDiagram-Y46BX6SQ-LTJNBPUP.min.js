import{$ as At,Ca as Ct,N as xt,P as kt,S as _t,T as vt,U as bt,V as St,W as wt,X as Lt,Y as Et,Z as Q,h as y,ia as q,ka as Tt}from"./chunk-U3SD26FK.min.js";import"./chunk-OSRY5VT3.min.js";function X(t,n){let o;if(n===void 0)for(let l of t)l!=null&&(o<l||o===void 0&&l>=l)&&(o=l);else{let l=-1;for(let f of t)(f=n(f,++l,t))!=null&&(o<f||o===void 0&&f>=f)&&(o=f)}return o}function W(t,n){let o;if(n===void 0)for(let l of t)l!=null&&(o>l||o===void 0&&l>=l)&&(o=l);else{let l=-1;for(let f of t)(f=n(f,++l,t))!=null&&(o>f||o===void 0&&f>=f)&&(o=f)}return o}function F(t,n){let o=0;if(n===void 0)for(let l of t)(l=+l)&&(o+=l);else{let l=-1;for(let f of t)(f=+n(f,++l,t))&&(o+=f)}return o}function Bt(t){return t.target.depth}function st(t){return t.depth}function it(t,n){return n-1-t.height}function G(t,n){return t.sourceLinks.length?t.depth:n-1}function at(t){return t.targetLinks.length?t.depth:t.sourceLinks.length?W(t.sourceLinks,Bt)-1:0}function U(t){return function(){return t}}function Mt(t,n){return K(t.source,n.source)||t.index-n.index}function Ot(t,n){return K(t.target,n.target)||t.index-n.index}function K(t,n){return t.y0-n.y0}function lt(t){return t.value}function $t(t){return t.index}function Vt(t){return t.nodes}function Wt(t){return t.links}function It(t,n){let o=t.get(n);if(!o)throw new Error("missing: "+n);return o}function Nt({nodes:t}){for(let n of t){let o=n.y0,l=o;for(let f of n.sourceLinks)f.y0=o+f.width/2,o+=f.width;for(let f of n.targetLinks)f.y1=l+f.width/2,l+=f.width}}function Z(){let t=0,n=0,o=1,l=1,f=24,S=8,m,x=$t,r=G,i,a,k=Vt,_=Wt,d=6;function v(){let e={nodes:k.apply(null,arguments),links:_.apply(null,arguments)};return C(e),T(e),M(e),P(e),z(e),Nt(e),e}v.update=function(e){return Nt(e),e},v.nodeId=function(e){return arguments.length?(x=typeof e=="function"?e:U(e),v):x},v.nodeAlign=function(e){return arguments.length?(r=typeof e=="function"?e:U(e),v):r},v.nodeSort=function(e){return arguments.length?(i=e,v):i},v.nodeWidth=function(e){return arguments.length?(f=+e,v):f},v.nodePadding=function(e){return arguments.length?(S=m=+e,v):S},v.nodes=function(e){return arguments.length?(k=typeof e=="function"?e:U(e),v):k},v.links=function(e){return arguments.length?(_=typeof e=="function"?e:U(e),v):_},v.linkSort=function(e){return arguments.length?(a=e,v):a},v.size=function(e){return arguments.length?(t=n=0,o=+e[0],l=+e[1],v):[o-t,l-n]},v.extent=function(e){return arguments.length?(t=+e[0][0],o=+e[1][0],n=+e[0][1],l=+e[1][1],v):[[t,n],[o,l]]},v.iterations=function(e){return arguments.length?(d=+e,v):d};function C({nodes:e,links:u}){for(let[h,s]of e.entries())s.index=h,s.sourceLinks=[],s.targetLinks=[];let c=new Map(e.map((h,s)=>[x(h,s,e),h]));for(let[h,s]of u.entries()){s.index=h;let{source:g,target:b}=s;typeof g!="object"&&(g=s.source=It(c,g)),typeof b!="object"&&(b=s.target=It(c,b)),g.sourceLinks.push(s),b.targetLinks.push(s)}if(a!=null)for(let{sourceLinks:h,targetLinks:s}of e)h.sort(a),s.sort(a)}function T({nodes:e}){for(let u of e)u.value=u.fixedValue===void 0?Math.max(F(u.sourceLinks,lt),F(u.targetLinks,lt)):u.fixedValue}function M({nodes:e}){let u=e.length,c=new Set(e),h=new Set,s=0;for(;c.size;){for(let g of c){g.depth=s;for(let{target:b}of g.sourceLinks)h.add(b)}if(++s>u)throw new Error("circular link");c=h,h=new Set}}function P({nodes:e}){let u=e.length,c=new Set(e),h=new Set,s=0;for(;c.size;){for(let g of c){g.height=s;for(let{source:b}of g.targetLinks)h.add(b)}if(++s>u)throw new Error("circular link");c=h,h=new Set}}function j({nodes:e}){let u=X(e,s=>s.depth)+1,c=(o-t-f)/(u-1),h=new Array(u);for(let s of e){let g=Math.max(0,Math.min(u-1,Math.floor(r.call(null,s,u))));s.layer=g,s.x0=t+g*c,s.x1=s.x0+f,h[g]?h[g].push(s):h[g]=[s]}if(i)for(let s of h)s.sort(i);return h}function B(e){let u=W(e,c=>(l-n-(c.length-1)*m)/F(c,lt));for(let c of e){let h=n;for(let s of c){s.y0=h,s.y1=h+s.value*u,h=s.y1+m;for(let g of s.sourceLinks)g.width=g.value*u}h=(l-h+m)/(c.length+1);for(let s=0;s<c.length;++s){let g=c[s];g.y0+=h*(s+1),g.y1+=h*(s+1)}J(c)}}function z(e){let u=j(e);m=Math.min(S,(l-n)/(X(u,c=>c.length)-1)),B(u);for(let c=0;c<d;++c){let h=Math.pow(.99,c),s=Math.max(1-h,(c+1)/d);O(u,h,s),w(u,h,s)}}function w(e,u,c){for(let h=1,s=e.length;h<s;++h){let g=e[h];for(let b of g){let $=0,N=0;for(let{source:L,value:ot}of b.targetLinks){let Y=ot*(b.layer-L.layer);$+=E(L,b)*Y,N+=Y}if(!(N>0))continue;let D=($/N-b.y0)*u;b.y0+=D,b.y1+=D,A(b)}i===void 0&&g.sort(K),R(g,c)}}function O(e,u,c){for(let h=e.length,s=h-2;s>=0;--s){let g=e[s];for(let b of g){let $=0,N=0;for(let{target:L,value:ot}of b.sourceLinks){let Y=ot*(L.layer-b.layer);$+=H(b,L)*Y,N+=Y}if(!(N>0))continue;let D=($/N-b.y0)*u;b.y0+=D,b.y1+=D,A(b)}i===void 0&&g.sort(K),R(g,c)}}function R(e,u){let c=e.length>>1,h=e[c];p(e,h.y0-m,c-1,u),I(e,h.y1+m,c+1,u),p(e,l,e.length-1,u),I(e,n,0,u)}function I(e,u,c,h){for(;c<e.length;++c){let s=e[c],g=(u-s.y0)*h;g>1e-6&&(s.y0+=g,s.y1+=g),u=s.y1+m}}function p(e,u,c,h){for(;c>=0;--c){let s=e[c],g=(s.y1-u)*h;g>1e-6&&(s.y0-=g,s.y1-=g),u=s.y0-m}}function A({sourceLinks:e,targetLinks:u}){if(a===void 0){for(let{source:{sourceLinks:c}}of u)c.sort(Ot);for(let{target:{targetLinks:c}}of e)c.sort(Mt)}}function J(e){if(a===void 0)for(let{sourceLinks:u,targetLinks:c}of e)u.sort(Ot),c.sort(Mt)}function E(e,u){let c=e.y0-(e.sourceLinks.length-1)*m/2;for(let{target:h,width:s}of e.sourceLinks){if(h===u)break;c+=s+m}for(let{source:h,width:s}of u.targetLinks){if(h===e)break;c-=s}return c}function H(e,u){let c=u.y0-(u.targetLinks.length-1)*m/2;for(let{source:h,width:s}of u.targetLinks){if(h===e)break;c+=s+m}for(let{target:h,width:s}of e.sourceLinks){if(h===u)break;c-=s}return c}return v}var ut=Math.PI,ft=2*ut,V=1e-6,Ft=ft-V;function ct(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function Pt(){return new ct}ct.prototype=Pt.prototype={constructor:ct,moveTo:function(t,n){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)},closePath:function(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(t,n){this._+="L"+(this._x1=+t)+","+(this._y1=+n)},quadraticCurveTo:function(t,n,o,l){this._+="Q"+ +t+","+ +n+","+(this._x1=+o)+","+(this._y1=+l)},bezierCurveTo:function(t,n,o,l,f,S){this._+="C"+ +t+","+ +n+","+ +o+","+ +l+","+(this._x1=+f)+","+(this._y1=+S)},arcTo:function(t,n,o,l,f){t=+t,n=+n,o=+o,l=+l,f=+f;var S=this._x1,m=this._y1,x=o-t,r=l-n,i=S-t,a=m-n,k=i*i+a*a;if(f<0)throw new Error("negative radius: "+f);if(this._x1===null)this._+="M"+(this._x1=t)+","+(this._y1=n);else if(k>V)if(!(Math.abs(a*x-r*i)>V)||!f)this._+="L"+(this._x1=t)+","+(this._y1=n);else{var _=o-S,d=l-m,v=x*x+r*r,C=_*_+d*d,T=Math.sqrt(v),M=Math.sqrt(k),P=f*Math.tan((ut-Math.acos((v+k-C)/(2*T*M)))/2),j=P/M,B=P/T;Math.abs(j-1)>V&&(this._+="L"+(t+j*i)+","+(n+j*a)),this._+="A"+f+","+f+",0,0,"+ +(a*_>i*d)+","+(this._x1=t+B*x)+","+(this._y1=n+B*r)}},arc:function(t,n,o,l,f,S){t=+t,n=+n,o=+o,S=!!S;var m=o*Math.cos(l),x=o*Math.sin(l),r=t+m,i=n+x,a=1^S,k=S?l-f:f-l;if(o<0)throw new Error("negative radius: "+o);this._x1===null?this._+="M"+r+","+i:(Math.abs(this._x1-r)>V||Math.abs(this._y1-i)>V)&&(this._+="L"+r+","+i),o&&(k<0&&(k=k%ft+ft),k>Ft?this._+="A"+o+","+o+",0,1,"+a+","+(t-m)+","+(n-x)+"A"+o+","+o+",0,1,"+a+","+(this._x1=r)+","+(this._y1=i):k>V&&(this._+="A"+o+","+o+",0,"+ +(k>=ut)+","+a+","+(this._x1=t+o*Math.cos(f))+","+(this._y1=n+o*Math.sin(f))))},rect:function(t,n,o,l){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)+"h"+ +o+"v"+ +l+"h"+-o+"Z"},toString:function(){return this._}};var ht=Pt;function dt(t){return function(){return t}}function Rt(t){return t[0]}function zt(t){return t[1]}var Dt=Array.prototype.slice;function Ut(t){return t.source}function Ht(t){return t.target}function Yt(t){var n=Ut,o=Ht,l=Rt,f=zt,S=null;function m(){var x,r=Dt.call(arguments),i=n.apply(this,r),a=o.apply(this,r);if(S||(S=x=ht()),t(S,+l.apply(this,(r[0]=i,r)),+f.apply(this,r),+l.apply(this,(r[0]=a,r)),+f.apply(this,r)),x)return S=null,x+""||null}return m.source=function(x){return arguments.length?(n=x,m):n},m.target=function(x){return arguments.length?(o=x,m):o},m.x=function(x){return arguments.length?(l=typeof x=="function"?x:dt(+x),m):l},m.y=function(x){return arguments.length?(f=typeof x=="function"?x:dt(+x),m):f},m.context=function(x){return arguments.length?(S=x??null,m):S},m}function qt(t,n,o,l,f){t.moveTo(n,o),t.bezierCurveTo(n=(n+l)/2,o,n,f,l,f)}function pt(){return Yt(qt)}function Xt(t){return[t.source.x1,t.y0]}function Gt(t){return[t.target.x0,t.y1]}function yt(){return pt().source(Xt).target(Gt)}var mt=function(){var t=y(function(x,r,i,a){for(i=i||{},a=x.length;a--;i[x[a]]=r);return i},"o"),n=[1,9],o=[1,10],l=[1,5,10,12],f={trace:y(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SANKEY:4,NEWLINE:5,csv:6,opt_eof:7,record:8,csv_tail:9,EOF:10,"field[source]":11,COMMA:12,"field[target]":13,"field[value]":14,field:15,escaped:16,non_escaped:17,DQUOTE:18,ESCAPED_TEXT:19,NON_ESCAPED_TEXT:20,$accept:0,$end:1},terminals_:{2:"error",4:"SANKEY",5:"NEWLINE",10:"EOF",11:"field[source]",12:"COMMA",13:"field[target]",14:"field[value]",18:"DQUOTE",19:"ESCAPED_TEXT",20:"NON_ESCAPED_TEXT"},productions_:[0,[3,4],[6,2],[9,2],[9,0],[7,1],[7,0],[8,5],[15,1],[15,1],[16,3],[17,1]],performAction:y(function(r,i,a,k,_,d,v){var C=d.length-1;switch(_){case 7:let T=k.findOrCreateNode(d[C-4].trim().replaceAll('""','"')),M=k.findOrCreateNode(d[C-2].trim().replaceAll('""','"')),P=parseFloat(d[C].trim());k.addLink(T,M,P);break;case 8:case 9:case 11:this.$=d[C];break;case 10:this.$=d[C-1];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},{5:[1,3]},{6:4,8:5,15:6,16:7,17:8,18:n,20:o},{1:[2,6],7:11,10:[1,12]},t(o,[2,4],{9:13,5:[1,14]}),{12:[1,15]},t(l,[2,8]),t(l,[2,9]),{19:[1,16]},t(l,[2,11]),{1:[2,1]},{1:[2,5]},t(o,[2,2]),{6:17,8:5,15:6,16:7,17:8,18:n,20:o},{15:18,16:7,17:8,18:n,20:o},{18:[1,19]},t(o,[2,3]),{12:[1,20]},t(l,[2,10]),{15:21,16:7,17:8,18:n,20:o},t([1,5,10],[2,7])],defaultActions:{11:[2,1],12:[2,5]},parseError:y(function(r,i){if(i.recoverable)this.trace(r);else{var a=new Error(r);throw a.hash=i,a}},"parseError"),parse:y(function(r){var i=this,a=[0],k=[],_=[null],d=[],v=this.table,C="",T=0,M=0,P=0,j=2,B=1,z=d.slice.call(arguments,1),w=Object.create(this.lexer),O={yy:{}};for(var R in this.yy)Object.prototype.hasOwnProperty.call(this.yy,R)&&(O.yy[R]=this.yy[R]);w.setInput(r,O.yy),O.yy.lexer=w,O.yy.parser=this,typeof w.yylloc>"u"&&(w.yylloc={});var I=w.yylloc;d.push(I);var p=w.options&&w.options.ranges;typeof O.yy.parseError=="function"?this.parseError=O.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function A(L){a.length=a.length-2*L,_.length=_.length-L,d.length=d.length-L}y(A,"popStack");function J(){var L;return L=k.pop()||w.lex()||B,typeof L!="number"&&(L instanceof Array&&(k=L,L=k.pop()),L=i.symbols_[L]||L),L}y(J,"lex");for(var E,H,e,u,c,h,s={},g,b,$,N;;){if(e=a[a.length-1],this.defaultActions[e]?u=this.defaultActions[e]:((E===null||typeof E>"u")&&(E=J()),u=v[e]&&v[e][E]),typeof u>"u"||!u.length||!u[0]){var D="";N=[];for(g in v[e])this.terminals_[g]&&g>j&&N.push("'"+this.terminals_[g]+"'");w.showPosition?D="Parse error on line "+(T+1)+`:
`+w.showPosition()+`
Expecting `+N.join(", ")+", got '"+(this.terminals_[E]||E)+"'":D="Parse error on line "+(T+1)+": Unexpected "+(E==B?"end of input":"'"+(this.terminals_[E]||E)+"'"),this.parseError(D,{text:w.match,token:this.terminals_[E]||E,line:w.yylineno,loc:I,expected:N})}if(u[0]instanceof Array&&u.length>1)throw new Error("Parse Error: multiple actions possible at state: "+e+", token: "+E);switch(u[0]){case 1:a.push(E),_.push(w.yytext),d.push(w.yylloc),a.push(u[1]),E=null,H?(E=H,H=null):(M=w.yyleng,C=w.yytext,T=w.yylineno,I=w.yylloc,P>0&&P--);break;case 2:if(b=this.productions_[u[1]][1],s.$=_[_.length-b],s._$={first_line:d[d.length-(b||1)].first_line,last_line:d[d.length-1].last_line,first_column:d[d.length-(b||1)].first_column,last_column:d[d.length-1].last_column},p&&(s._$.range=[d[d.length-(b||1)].range[0],d[d.length-1].range[1]]),h=this.performAction.apply(s,[C,M,T,O.yy,u[1],_,d].concat(z)),typeof h<"u")return h;b&&(a=a.slice(0,-1*b*2),_=_.slice(0,-1*b),d=d.slice(0,-1*b)),a.push(this.productions_[u[1]][0]),_.push(s.$),d.push(s._$),$=v[a[a.length-2]][a[a.length-1]],a.push($);break;case 3:return!0}}return!0},"parse")},S=function(){var x={EOF:1,parseError:y(function(i,a){if(this.yy.parser)this.yy.parser.parseError(i,a);else throw new Error(i)},"parseError"),setInput:y(function(r,i){return this.yy=i||this.yy||{},this._input=r,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:y(function(){var r=this._input[0];this.yytext+=r,this.yyleng++,this.offset++,this.match+=r,this.matched+=r;var i=r.match(/(?:\r\n?|\n).*/g);return i?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),r},"input"),unput:y(function(r){var i=r.length,a=r.split(/(?:\r\n?|\n)/g);this._input=r+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-i),this.offset-=i;var k=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var _=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===k.length?this.yylloc.first_column:0)+k[k.length-a.length].length-a[0].length:this.yylloc.first_column-i},this.options.ranges&&(this.yylloc.range=[_[0],_[0]+this.yyleng-i]),this.yyleng=this.yytext.length,this},"unput"),more:y(function(){return this._more=!0,this},"more"),reject:y(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:y(function(r){this.unput(this.match.slice(r))},"less"),pastInput:y(function(){var r=this.matched.substr(0,this.matched.length-this.match.length);return(r.length>20?"...":"")+r.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:y(function(){var r=this.match;return r.length<20&&(r+=this._input.substr(0,20-r.length)),(r.substr(0,20)+(r.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:y(function(){var r=this.pastInput(),i=new Array(r.length+1).join("-");return r+this.upcomingInput()+`
`+i+"^"},"showPosition"),test_match:y(function(r,i){var a,k,_;if(this.options.backtrack_lexer&&(_={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(_.yylloc.range=this.yylloc.range.slice(0))),k=r[0].match(/(?:\r\n?|\n).*/g),k&&(this.yylineno+=k.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:k?k[k.length-1].length-k[k.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+r[0].length},this.yytext+=r[0],this.match+=r[0],this.matches=r,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(r[0].length),this.matched+=r[0],a=this.performAction.call(this,this.yy,this,i,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack){for(var d in _)this[d]=_[d];return!1}return!1},"test_match"),next:y(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var r,i,a,k;this._more||(this.yytext="",this.match="");for(var _=this._currentRules(),d=0;d<_.length;d++)if(a=this._input.match(this.rules[_[d]]),a&&(!i||a[0].length>i[0].length)){if(i=a,k=d,this.options.backtrack_lexer){if(r=this.test_match(a,_[d]),r!==!1)return r;if(this._backtrack){i=!1;continue}else return!1}else if(!this.options.flex)break}return i?(r=this.test_match(i,_[k]),r!==!1?r:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:y(function(){var i=this.next();return i||this.lex()},"lex"),begin:y(function(i){this.conditionStack.push(i)},"begin"),popState:y(function(){var i=this.conditionStack.length-1;return i>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:y(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:y(function(i){return i=this.conditionStack.length-1-Math.abs(i||0),i>=0?this.conditionStack[i]:"INITIAL"},"topState"),pushState:y(function(i){this.begin(i)},"pushState"),stateStackSize:y(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:y(function(i,a,k,_){var d=_;switch(k){case 0:return this.pushState("csv"),4;break;case 1:return 10;case 2:return 5;case 3:return 12;case 4:return this.pushState("escaped_text"),18;break;case 5:return 20;case 6:return this.popState("escaped_text"),18;break;case 7:return 19}},"anonymous"),rules:[/^(?:sankey-beta\b)/i,/^(?:$)/i,/^(?:((\u000D\u000A)|(\u000A)))/i,/^(?:(\u002C))/i,/^(?:(\u0022))/i,/^(?:([\u0020-\u0021\u0023-\u002B\u002D-\u007E])*)/i,/^(?:(\u0022)(?!(\u0022)))/i,/^(?:(([\u0020-\u0021\u0023-\u002B\u002D-\u007E])|(\u002C)|(\u000D)|(\u000A)|(\u0022)(\u0022))*)/i],conditions:{csv:{rules:[1,2,3,4,5,6,7],inclusive:!1},escaped_text:{rules:[6,7],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7],inclusive:!0}}};return x}();f.lexer=S;function m(){this.yy={}}return y(m,"Parser"),m.prototype=f,f.Parser=m,new m}();mt.parser=mt;var tt=mt,nt=[],rt=[],et=new Map,Jt=y(()=>{nt=[],rt=[],et=new Map,_t()},"clear"),Qt=class{constructor(t,n,o=0){this.source=t,this.target=n,this.value=o}static{y(this,"SankeyLink")}},Kt=y((t,n,o)=>{nt.push(new Qt(t,n,o))},"addLink"),Zt=class{constructor(t){this.ID=t}static{y(this,"SankeyNode")}},te=y(t=>{t=xt.sanitizeText(t,Q());let n=et.get(t);return n===void 0&&(n=new Zt(t),et.set(t,n),rt.push(n)),n},"findOrCreateNode"),ee=y(()=>rt,"getNodes"),ne=y(()=>nt,"getLinks"),re=y(()=>({nodes:rt.map(t=>({id:t.ID})),links:nt.map(t=>({source:t.source.ID,target:t.target.ID,value:t.value}))}),"getGraph"),oe={nodesMap:et,getConfig:y(()=>Q().sankey,"getConfig"),getNodes:ee,getLinks:ne,getGraph:re,addLink:Kt,findOrCreateNode:te,getAccTitle:bt,setAccTitle:vt,getAccDescription:wt,setAccDescription:St,getDiagramTitle:Et,setDiagramTitle:Lt,clear:Jt},jt=class gt{static{y(this,"Uid")}static{this.count=0}static next(n){return new gt(n+ ++gt.count)}constructor(n){this.id=n,this.href=`#${n}`}toString(){return"url("+this.href+")"}},se={left:st,right:it,center:at,justify:G},ie=y(function(t,n,o,l){let{securityLevel:f,sankey:S}=Q(),m=At.sankey,x;f==="sandbox"&&(x=q("#i"+n));let r=f==="sandbox"?q(x.nodes()[0].contentDocument.body):q("body"),i=f==="sandbox"?r.select(`[id="${n}"]`):q(`[id="${n}"]`),a=S?.width??m.width,k=S?.height??m.width,_=S?.useMaxWidth??m.useMaxWidth,d=S?.nodeAlignment??m.nodeAlignment,v=S?.prefix??m.prefix,C=S?.suffix??m.suffix,T=S?.showValues??m.showValues,M=l.db.getGraph(),P=se[d];Z().nodeId(p=>p.id).nodeWidth(10).nodePadding(10+(T?15:0)).nodeAlign(P).extent([[0,0],[a,k]])(M);let z=Tt(Ct);i.append("g").attr("class","nodes").selectAll(".node").data(M.nodes).join("g").attr("class","node").attr("id",p=>(p.uid=jt.next("node-")).id).attr("transform",function(p){return"translate("+p.x0+","+p.y0+")"}).attr("x",p=>p.x0).attr("y",p=>p.y0).append("rect").attr("height",p=>p.y1-p.y0).attr("width",p=>p.x1-p.x0).attr("fill",p=>z(p.id));let w=y(({id:p,value:A})=>T?`${p}
${v}${Math.round(A*100)/100}${C}`:p,"getText");i.append("g").attr("class","node-labels").attr("font-family","sans-serif").attr("font-size",14).selectAll("text").data(M.nodes).join("text").attr("x",p=>p.x0<a/2?p.x1+6:p.x0-6).attr("y",p=>(p.y1+p.y0)/2).attr("dy",`${T?"0":"0.35"}em`).attr("text-anchor",p=>p.x0<a/2?"start":"end").text(w);let O=i.append("g").attr("class","links").attr("fill","none").attr("stroke-opacity",.5).selectAll(".link").data(M.links).join("g").attr("class","link").style("mix-blend-mode","multiply"),R=S?.linkColor??"gradient";if(R==="gradient"){let p=O.append("linearGradient").attr("id",A=>(A.uid=jt.next("linearGradient-")).id).attr("gradientUnits","userSpaceOnUse").attr("x1",A=>A.source.x1).attr("x2",A=>A.target.x0);p.append("stop").attr("offset","0%").attr("stop-color",A=>z(A.source.id)),p.append("stop").attr("offset","100%").attr("stop-color",A=>z(A.target.id))}let I;switch(R){case"gradient":I=y(p=>p.uid,"coloring");break;case"source":I=y(p=>z(p.source.id),"coloring");break;case"target":I=y(p=>z(p.target.id),"coloring");break;default:I=R}O.append("path").attr("d",yt()).attr("stroke",I).attr("stroke-width",p=>Math.max(1,p.width)),kt(void 0,i,0,_)},"draw"),ae={draw:ie},le=y(t=>t.replaceAll(/^[^\S\n\r]+|[^\S\n\r]+$/g,"").replaceAll(/([\n\r])+/g,`
`).trim(),"prepareTextForParsing"),ue=tt.parse.bind(tt);tt.parse=t=>ue(le(t));var Xe={parser:tt,db:oe,renderer:ae};export{Xe as diagram};
//# sourceMappingURL=sankeyDiagram-Y46BX6SQ-LTJNBPUP.min.js.map
