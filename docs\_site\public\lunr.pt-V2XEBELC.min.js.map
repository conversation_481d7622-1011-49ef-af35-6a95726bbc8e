{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.pt.js"], "sourcesContent": ["/*!\n * Lunr languages, `Portuguese` language\n * https://github.com/MihaiValentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.pt = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.pt.trimmer,\n        lunr.pt.stopWordFilter,\n        lunr.pt.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.pt.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.pt.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.pt.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.pt.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.pt.trimmer, 'trimmer-pt');\n\n    /* lunr stemmer function */\n    lunr.pt.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function PortugueseStemmer() {\n          var a_0 = [new Among(\"\", -1, 3), new Among(\"\\u00E3\", 0, 1),\n              new Among(\"\\u00F5\", 0, 2)\n            ],\n            a_1 = [new Among(\"\", -1, 3),\n              new Among(\"a~\", 0, 1), new Among(\"o~\", 0, 2)\n            ],\n            a_2 = [\n              new Among(\"ic\", -1, -1), new Among(\"ad\", -1, -1),\n              new Among(\"os\", -1, -1), new Among(\"iv\", -1, 1)\n            ],\n            a_3 = [\n              new Among(\"ante\", -1, 1), new Among(\"avel\", -1, 1),\n              new Among(\"\\u00EDvel\", -1, 1)\n            ],\n            a_4 = [new Among(\"ic\", -1, 1),\n              new Among(\"abil\", -1, 1), new Among(\"iv\", -1, 1)\n            ],\n            a_5 = [\n              new Among(\"ica\", -1, 1), new Among(\"\\u00E2ncia\", -1, 1),\n              new Among(\"\\u00EAncia\", -1, 4), new Among(\"ira\", -1, 9),\n              new Among(\"adora\", -1, 1), new Among(\"osa\", -1, 1),\n              new Among(\"ista\", -1, 1), new Among(\"iva\", -1, 8),\n              new Among(\"eza\", -1, 1), new Among(\"log\\u00EDa\", -1, 2),\n              new Among(\"idade\", -1, 7), new Among(\"ante\", -1, 1),\n              new Among(\"mente\", -1, 6), new Among(\"amente\", 12, 5),\n              new Among(\"\\u00E1vel\", -1, 1), new Among(\"\\u00EDvel\", -1, 1),\n              new Among(\"uci\\u00F3n\", -1, 3), new Among(\"ico\", -1, 1),\n              new Among(\"ismo\", -1, 1), new Among(\"oso\", -1, 1),\n              new Among(\"amento\", -1, 1), new Among(\"imento\", -1, 1),\n              new Among(\"ivo\", -1, 8), new Among(\"a\\u00E7a~o\", -1, 1),\n              new Among(\"ador\", -1, 1), new Among(\"icas\", -1, 1),\n              new Among(\"\\u00EAncias\", -1, 4), new Among(\"iras\", -1, 9),\n              new Among(\"adoras\", -1, 1), new Among(\"osas\", -1, 1),\n              new Among(\"istas\", -1, 1), new Among(\"ivas\", -1, 8),\n              new Among(\"ezas\", -1, 1), new Among(\"log\\u00EDas\", -1, 2),\n              new Among(\"idades\", -1, 7), new Among(\"uciones\", -1, 3),\n              new Among(\"adores\", -1, 1), new Among(\"antes\", -1, 1),\n              new Among(\"a\\u00E7o~es\", -1, 1), new Among(\"icos\", -1, 1),\n              new Among(\"ismos\", -1, 1), new Among(\"osos\", -1, 1),\n              new Among(\"amentos\", -1, 1), new Among(\"imentos\", -1, 1),\n              new Among(\"ivos\", -1, 8)\n            ],\n            a_6 = [new Among(\"ada\", -1, 1),\n              new Among(\"ida\", -1, 1), new Among(\"ia\", -1, 1),\n              new Among(\"aria\", 2, 1), new Among(\"eria\", 2, 1),\n              new Among(\"iria\", 2, 1), new Among(\"ara\", -1, 1),\n              new Among(\"era\", -1, 1), new Among(\"ira\", -1, 1),\n              new Among(\"ava\", -1, 1), new Among(\"asse\", -1, 1),\n              new Among(\"esse\", -1, 1), new Among(\"isse\", -1, 1),\n              new Among(\"aste\", -1, 1), new Among(\"este\", -1, 1),\n              new Among(\"iste\", -1, 1), new Among(\"ei\", -1, 1),\n              new Among(\"arei\", 16, 1), new Among(\"erei\", 16, 1),\n              new Among(\"irei\", 16, 1), new Among(\"am\", -1, 1),\n              new Among(\"iam\", 20, 1), new Among(\"ariam\", 21, 1),\n              new Among(\"eriam\", 21, 1), new Among(\"iriam\", 21, 1),\n              new Among(\"aram\", 20, 1), new Among(\"eram\", 20, 1),\n              new Among(\"iram\", 20, 1), new Among(\"avam\", 20, 1),\n              new Among(\"em\", -1, 1), new Among(\"arem\", 29, 1),\n              new Among(\"erem\", 29, 1), new Among(\"irem\", 29, 1),\n              new Among(\"assem\", 29, 1), new Among(\"essem\", 29, 1),\n              new Among(\"issem\", 29, 1), new Among(\"ado\", -1, 1),\n              new Among(\"ido\", -1, 1), new Among(\"ando\", -1, 1),\n              new Among(\"endo\", -1, 1), new Among(\"indo\", -1, 1),\n              new Among(\"ara~o\", -1, 1), new Among(\"era~o\", -1, 1),\n              new Among(\"ira~o\", -1, 1), new Among(\"ar\", -1, 1),\n              new Among(\"er\", -1, 1), new Among(\"ir\", -1, 1),\n              new Among(\"as\", -1, 1), new Among(\"adas\", 47, 1),\n              new Among(\"idas\", 47, 1), new Among(\"ias\", 47, 1),\n              new Among(\"arias\", 50, 1), new Among(\"erias\", 50, 1),\n              new Among(\"irias\", 50, 1), new Among(\"aras\", 47, 1),\n              new Among(\"eras\", 47, 1), new Among(\"iras\", 47, 1),\n              new Among(\"avas\", 47, 1), new Among(\"es\", -1, 1),\n              new Among(\"ardes\", 58, 1), new Among(\"erdes\", 58, 1),\n              new Among(\"irdes\", 58, 1), new Among(\"ares\", 58, 1),\n              new Among(\"eres\", 58, 1), new Among(\"ires\", 58, 1),\n              new Among(\"asses\", 58, 1), new Among(\"esses\", 58, 1),\n              new Among(\"isses\", 58, 1), new Among(\"astes\", 58, 1),\n              new Among(\"estes\", 58, 1), new Among(\"istes\", 58, 1),\n              new Among(\"is\", -1, 1), new Among(\"ais\", 71, 1),\n              new Among(\"eis\", 71, 1), new Among(\"areis\", 73, 1),\n              new Among(\"ereis\", 73, 1), new Among(\"ireis\", 73, 1),\n              new Among(\"\\u00E1reis\", 73, 1), new Among(\"\\u00E9reis\", 73, 1),\n              new Among(\"\\u00EDreis\", 73, 1), new Among(\"\\u00E1sseis\", 73, 1),\n              new Among(\"\\u00E9sseis\", 73, 1), new Among(\"\\u00EDsseis\", 73, 1),\n              new Among(\"\\u00E1veis\", 73, 1), new Among(\"\\u00EDeis\", 73, 1),\n              new Among(\"ar\\u00EDeis\", 84, 1), new Among(\"er\\u00EDeis\", 84, 1),\n              new Among(\"ir\\u00EDeis\", 84, 1), new Among(\"ados\", -1, 1),\n              new Among(\"idos\", -1, 1), new Among(\"amos\", -1, 1),\n              new Among(\"\\u00E1ramos\", 90, 1), new Among(\"\\u00E9ramos\", 90, 1),\n              new Among(\"\\u00EDramos\", 90, 1), new Among(\"\\u00E1vamos\", 90, 1),\n              new Among(\"\\u00EDamos\", 90, 1), new Among(\"ar\\u00EDamos\", 95, 1),\n              new Among(\"er\\u00EDamos\", 95, 1), new Among(\"ir\\u00EDamos\", 95, 1),\n              new Among(\"emos\", -1, 1), new Among(\"aremos\", 99, 1),\n              new Among(\"eremos\", 99, 1), new Among(\"iremos\", 99, 1),\n              new Among(\"\\u00E1ssemos\", 99, 1), new Among(\"\\u00EAssemos\", 99, 1),\n              new Among(\"\\u00EDssemos\", 99, 1), new Among(\"imos\", -1, 1),\n              new Among(\"armos\", -1, 1), new Among(\"ermos\", -1, 1),\n              new Among(\"irmos\", -1, 1), new Among(\"\\u00E1mos\", -1, 1),\n              new Among(\"ar\\u00E1s\", -1, 1), new Among(\"er\\u00E1s\", -1, 1),\n              new Among(\"ir\\u00E1s\", -1, 1), new Among(\"eu\", -1, 1),\n              new Among(\"iu\", -1, 1), new Among(\"ou\", -1, 1),\n              new Among(\"ar\\u00E1\", -1, 1), new Among(\"er\\u00E1\", -1, 1),\n              new Among(\"ir\\u00E1\", -1, 1)\n            ],\n            a_7 = [new Among(\"a\", -1, 1),\n              new Among(\"i\", -1, 1), new Among(\"o\", -1, 1),\n              new Among(\"os\", -1, 1), new Among(\"\\u00E1\", -1, 1),\n              new Among(\"\\u00ED\", -1, 1), new Among(\"\\u00F3\", -1, 1)\n            ],\n            a_8 = [\n              new Among(\"e\", -1, 1), new Among(\"\\u00E7\", -1, 2),\n              new Among(\"\\u00E9\", -1, 1), new Among(\"\\u00EA\", -1, 1)\n            ],\n            g_v = [17,\n              65, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19, 12, 2\n            ],\n            I_p2, I_p1, I_pV, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function r_prelude() {\n            var among_var;\n            while (true) {\n              sbp.bra = sbp.cursor;\n              among_var = sbp.find_among(a_0, 3);\n              if (among_var) {\n                sbp.ket = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"a~\");\n                    continue;\n                  case 2:\n                    sbp.slice_from(\"o~\");\n                    continue;\n                  case 3:\n                    if (sbp.cursor >= sbp.limit)\n                      break;\n                    sbp.cursor++;\n                    continue;\n                }\n              }\n              break;\n            }\n          }\n\n          function habr2() {\n            if (sbp.out_grouping(g_v, 97, 250)) {\n              while (!sbp.in_grouping(g_v, 97, 250)) {\n                if (sbp.cursor >= sbp.limit)\n                  return true;\n                sbp.cursor++;\n              }\n              return false;\n            }\n            return true;\n          }\n\n          function habr3() {\n            if (sbp.in_grouping(g_v, 97, 250)) {\n              while (!sbp.out_grouping(g_v, 97, 250)) {\n                if (sbp.cursor >= sbp.limit)\n                  return false;\n                sbp.cursor++;\n              }\n            }\n            I_pV = sbp.cursor;\n            return true;\n          }\n\n          function habr4() {\n            var v_1 = sbp.cursor,\n              v_2, v_3;\n            if (sbp.in_grouping(g_v, 97, 250)) {\n              v_2 = sbp.cursor;\n              if (habr2()) {\n                sbp.cursor = v_2;\n                if (habr3())\n                  return;\n              } else\n                I_pV = sbp.cursor;\n            }\n            sbp.cursor = v_1;\n            if (sbp.out_grouping(g_v, 97, 250)) {\n              v_3 = sbp.cursor;\n              if (habr2()) {\n                sbp.cursor = v_3;\n                if (!sbp.in_grouping(g_v, 97, 250) || sbp.cursor >= sbp.limit)\n                  return;\n                sbp.cursor++;\n              }\n              I_pV = sbp.cursor;\n            }\n          }\n\n          function habr5() {\n            while (!sbp.in_grouping(g_v, 97, 250)) {\n              if (sbp.cursor >= sbp.limit)\n                return false;\n              sbp.cursor++;\n            }\n            while (!sbp.out_grouping(g_v, 97, 250)) {\n              if (sbp.cursor >= sbp.limit)\n                return false;\n              sbp.cursor++;\n            }\n            return true;\n          }\n\n          function r_mark_regions() {\n            var v_1 = sbp.cursor;\n            I_pV = sbp.limit;\n            I_p1 = I_pV;\n            I_p2 = I_pV;\n            habr4();\n            sbp.cursor = v_1;\n            if (habr5()) {\n              I_p1 = sbp.cursor;\n              if (habr5())\n                I_p2 = sbp.cursor;\n            }\n          }\n\n          function r_postlude() {\n            var among_var;\n            while (true) {\n              sbp.bra = sbp.cursor;\n              among_var = sbp.find_among(a_1, 3);\n              if (among_var) {\n                sbp.ket = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"\\u00E3\");\n                    continue;\n                  case 2:\n                    sbp.slice_from(\"\\u00F5\");\n                    continue;\n                  case 3:\n                    if (sbp.cursor >= sbp.limit)\n                      break;\n                    sbp.cursor++;\n                    continue;\n                }\n              }\n              break;\n            }\n          }\n\n          function r_RV() {\n            return I_pV <= sbp.cursor;\n          }\n\n          function r_R1() {\n            return I_p1 <= sbp.cursor;\n          }\n\n          function r_R2() {\n            return I_p2 <= sbp.cursor;\n          }\n\n          function r_standard_suffix() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_5, 45);\n            if (!among_var)\n              return false;\n            sbp.bra = sbp.cursor;\n            switch (among_var) {\n              case 1:\n                if (!r_R2())\n                  return false;\n                sbp.slice_del();\n                break;\n              case 2:\n                if (!r_R2())\n                  return false;\n                sbp.slice_from(\"log\");\n                break;\n              case 3:\n                if (!r_R2())\n                  return false;\n                sbp.slice_from(\"u\");\n                break;\n              case 4:\n                if (!r_R2())\n                  return false;\n                sbp.slice_from(\"ente\");\n                break;\n              case 5:\n                if (!r_R1())\n                  return false;\n                sbp.slice_del();\n                sbp.ket = sbp.cursor;\n                among_var = sbp.find_among_b(a_2, 4);\n                if (among_var) {\n                  sbp.bra = sbp.cursor;\n                  if (r_R2()) {\n                    sbp.slice_del();\n                    if (among_var == 1) {\n                      sbp.ket = sbp.cursor;\n                      if (sbp.eq_s_b(2, \"at\")) {\n                        sbp.bra = sbp.cursor;\n                        if (r_R2())\n                          sbp.slice_del();\n                      }\n                    }\n                  }\n                }\n                break;\n              case 6:\n                if (!r_R2())\n                  return false;\n                sbp.slice_del();\n                sbp.ket = sbp.cursor;\n                among_var = sbp.find_among_b(a_3, 3);\n                if (among_var) {\n                  sbp.bra = sbp.cursor;\n                  if (among_var == 1)\n                    if (r_R2())\n                      sbp.slice_del();\n                }\n                break;\n              case 7:\n                if (!r_R2())\n                  return false;\n                sbp.slice_del();\n                sbp.ket = sbp.cursor;\n                among_var = sbp.find_among_b(a_4, 3);\n                if (among_var) {\n                  sbp.bra = sbp.cursor;\n                  if (among_var == 1)\n                    if (r_R2())\n                      sbp.slice_del();\n                }\n                break;\n              case 8:\n                if (!r_R2())\n                  return false;\n                sbp.slice_del();\n                sbp.ket = sbp.cursor;\n                if (sbp.eq_s_b(2, \"at\")) {\n                  sbp.bra = sbp.cursor;\n                  if (r_R2())\n                    sbp.slice_del();\n                }\n                break;\n              case 9:\n                if (!r_RV() || !sbp.eq_s_b(1, \"e\"))\n                  return false;\n                sbp.slice_from(\"ir\");\n                break;\n            }\n            return true;\n          }\n\n          function r_verb_suffix() {\n            var among_var, v_1;\n            if (sbp.cursor >= I_pV) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_pV;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_6, 120);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                if (among_var == 1)\n                  sbp.slice_del();\n                sbp.limit_backward = v_1;\n                return true;\n              }\n              sbp.limit_backward = v_1;\n            }\n            return false;\n          }\n\n          function r_residual_suffix() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_7, 7);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (among_var == 1)\n                if (r_RV())\n                  sbp.slice_del();\n            }\n          }\n\n          function habr6(c1, c2) {\n            if (sbp.eq_s_b(1, c1)) {\n              sbp.bra = sbp.cursor;\n              var v_1 = sbp.limit - sbp.cursor;\n              if (sbp.eq_s_b(1, c2)) {\n                sbp.cursor = sbp.limit - v_1;\n                if (r_RV())\n                  sbp.slice_del();\n                return false;\n              }\n            }\n            return true;\n          }\n\n          function r_residual_form() {\n            var among_var, v_1, v_2, v_3;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_8, 4);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              switch (among_var) {\n                case 1:\n                  if (r_RV()) {\n                    sbp.slice_del();\n                    sbp.ket = sbp.cursor;\n                    v_1 = sbp.limit - sbp.cursor;\n                    if (habr6(\"u\", \"g\"))\n                      habr6(\"i\", \"c\")\n                  }\n                  break;\n                case 2:\n                  sbp.slice_from(\"c\");\n                  break;\n              }\n            }\n          }\n\n          function habr1() {\n            if (!r_standard_suffix()) {\n              sbp.cursor = sbp.limit;\n              if (!r_verb_suffix()) {\n                sbp.cursor = sbp.limit;\n                r_residual_suffix();\n                return;\n              }\n            }\n            sbp.cursor = sbp.limit;\n            sbp.ket = sbp.cursor;\n            if (sbp.eq_s_b(1, \"i\")) {\n              sbp.bra = sbp.cursor;\n              if (sbp.eq_s_b(1, \"c\")) {\n                sbp.cursor = sbp.limit;\n                if (r_RV())\n                  sbp.slice_del();\n              }\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_prelude();\n            sbp.cursor = v_1;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            habr1();\n            sbp.cursor = sbp.limit;\n            r_residual_form();\n            sbp.cursor = sbp.limit_backward;\n            r_postlude();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.pt.stemmer, 'stemmer-pt');\n\n    lunr.pt.stopWordFilter = lunr.generateStopWordFilter('a ao aos aquela aquelas aquele aqueles aquilo as até com como da das de dela delas dele deles depois do dos e ela elas ele eles em entre era eram essa essas esse esses esta estamos estas estava estavam este esteja estejam estejamos estes esteve estive estivemos estiver estivera estiveram estiverem estivermos estivesse estivessem estivéramos estivéssemos estou está estávamos estão eu foi fomos for fora foram forem formos fosse fossem fui fôramos fôssemos haja hajam hajamos havemos hei houve houvemos houver houvera houveram houverei houverem houveremos houveria houveriam houvermos houverá houverão houveríamos houvesse houvessem houvéramos houvéssemos há hão isso isto já lhe lhes mais mas me mesmo meu meus minha minhas muito na nas nem no nos nossa nossas nosso nossos num numa não nós o os ou para pela pelas pelo pelos por qual quando que quem se seja sejam sejamos sem serei seremos seria seriam será serão seríamos seu seus somos sou sua suas são só também te tem temos tenha tenham tenhamos tenho terei teremos teria teriam terá terão teríamos teu teus teve tinha tinham tive tivemos tiver tivera tiveram tiverem tivermos tivesse tivessem tivéramos tivéssemos tu tua tuas tém tínhamos um uma você vocês vos à às éramos'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.pt.stopWordFilter, 'stopWordFilter-pt');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAA6B,CACpC,IAAIC,EAAM,CAAC,IAAIH,EAAM,GAAI,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,EACrD,IAAIA,EAAM,OAAU,EAAG,CAAC,CAC1B,EACAI,EAAM,CAAC,IAAIJ,EAAM,GAAI,GAAI,CAAC,EACxB,IAAIA,EAAM,KAAM,EAAG,CAAC,EAAG,IAAIA,EAAM,KAAM,EAAG,CAAC,CAC7C,EACAK,EAAM,CACJ,IAAIL,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,CAChD,EACAM,EAAM,CACJ,IAAIN,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,UAAa,GAAI,CAAC,CAC9B,EACAO,EAAM,CAAC,IAAIP,EAAM,KAAM,GAAI,CAAC,EAC1B,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,CACjD,EACAQ,EAAM,CACJ,IAAIR,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAc,GAAI,CAAC,EACtD,IAAIA,EAAM,WAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACtD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAc,GAAI,CAAC,EACtD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACpD,IAAIA,EAAM,UAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EAC3D,IAAIA,EAAM,WAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACtD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAc,GAAI,CAAC,EACtD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,YAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACxD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAe,GAAI,CAAC,EACxD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAW,GAAI,CAAC,EACtD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACpD,IAAIA,EAAM,YAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACxD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAW,GAAI,CAAC,EACvD,IAAIA,EAAM,OAAQ,GAAI,CAAC,CACzB,EACAS,EAAM,CAAC,IAAIT,EAAM,MAAO,GAAI,CAAC,EAC3B,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAC/C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC/C,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAChD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC7C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC/C,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC9C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,WAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAc,GAAI,CAAC,EAC7D,IAAIA,EAAM,WAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAe,GAAI,CAAC,EAC9D,IAAIA,EAAM,YAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAe,GAAI,CAAC,EAC/D,IAAIA,EAAM,WAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EAC5D,IAAIA,EAAM,YAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAe,GAAI,CAAC,EAC/D,IAAIA,EAAM,YAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACxD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,YAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAe,GAAI,CAAC,EAC/D,IAAIA,EAAM,YAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAe,GAAI,CAAC,EAC/D,IAAIA,EAAM,WAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAgB,GAAI,CAAC,EAC/D,IAAIA,EAAM,aAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACnD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,aAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAgB,GAAI,CAAC,EACjE,IAAIA,EAAM,aAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACzD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EACvD,IAAIA,EAAM,UAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EAC3D,IAAIA,EAAM,UAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EACpD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC7C,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EACzD,IAAIA,EAAM,SAAY,GAAI,CAAC,CAC7B,EACAU,EAAM,CAAC,IAAIV,EAAM,IAAK,GAAI,CAAC,EACzB,IAAIA,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,IAAK,GAAI,CAAC,EAC3C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,GAAI,CAAC,CACvD,EACAW,EAAM,CACJ,IAAIX,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,GAAI,CAAC,CACvD,EACAY,EAAM,CAAC,GACL,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,CAC5D,EACAC,EAAMC,EAAMC,EAAMC,EAAM,IAAIf,EAC9B,KAAK,WAAa,SAASgB,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,GAAY,CAEnB,QADIC,IACS,CAGX,GAFAH,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,WAAWb,EAAK,CAAC,EAC7BgB,EAEF,OADAH,EAAI,IAAMA,EAAI,OACNG,EAAW,CACjB,IAAK,GACHH,EAAI,WAAW,IAAI,EACnB,SACF,IAAK,GACHA,EAAI,WAAW,IAAI,EACnB,SACF,IAAK,GACH,GAAIA,EAAI,QAAUA,EAAI,MACpB,MACFA,EAAI,SACJ,QACJ,CAEF,KACF,CACF,CAEA,SAASI,GAAQ,CACf,GAAIJ,EAAI,aAAaJ,EAAK,GAAI,GAAG,EAAG,CAClC,KAAO,CAACI,EAAI,YAAYJ,EAAK,GAAI,GAAG,GAAG,CACrC,GAAII,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CACA,MAAO,EACT,CAEA,SAASK,GAAQ,CACf,GAAIL,EAAI,YAAYJ,EAAK,GAAI,GAAG,EAC9B,KAAO,CAACI,EAAI,aAAaJ,EAAK,GAAI,GAAG,GAAG,CACtC,GAAII,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CAEF,OAAAD,EAAOC,EAAI,OACJ,EACT,CAEA,SAASM,GAAQ,CACf,IAAIC,EAAMP,EAAI,OACZQ,EAAKC,EACP,GAAIT,EAAI,YAAYJ,EAAK,GAAI,GAAG,EAE9B,GADAY,EAAMR,EAAI,OACNI,EAAM,GAER,GADAJ,EAAI,OAASQ,EACTH,EAAM,EACR,YAEFN,EAAOC,EAAI,OAGf,GADAA,EAAI,OAASO,EACTP,EAAI,aAAaJ,EAAK,GAAI,GAAG,EAAG,CAElC,GADAa,EAAMT,EAAI,OACNI,EAAM,EAAG,CAEX,GADAJ,EAAI,OAASS,EACT,CAACT,EAAI,YAAYJ,EAAK,GAAI,GAAG,GAAKI,EAAI,QAAUA,EAAI,MACtD,OACFA,EAAI,QACN,CACAD,EAAOC,EAAI,MACb,CACF,CAEA,SAASU,GAAQ,CACf,KAAO,CAACV,EAAI,YAAYJ,EAAK,GAAI,GAAG,GAAG,CACrC,GAAII,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,KAAO,CAACA,EAAI,aAAaJ,EAAK,GAAI,GAAG,GAAG,CACtC,GAAII,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASW,GAAiB,CACxB,IAAIJ,EAAMP,EAAI,OACdD,EAAOC,EAAI,MACXF,EAAOC,EACPF,EAAOE,EACPO,EAAM,EACNN,EAAI,OAASO,EACTG,EAAM,IACRZ,EAAOE,EAAI,OACPU,EAAM,IACRb,EAAOG,EAAI,QAEjB,CAEA,SAASY,GAAa,CAEpB,QADIT,IACS,CAGX,GAFAH,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,WAAWZ,EAAK,CAAC,EAC7Be,EAEF,OADAH,EAAI,IAAMA,EAAI,OACNG,EAAW,CACjB,IAAK,GACHH,EAAI,WAAW,MAAQ,EACvB,SACF,IAAK,GACHA,EAAI,WAAW,MAAQ,EACvB,SACF,IAAK,GACH,GAAIA,EAAI,QAAUA,EAAI,MACpB,MACFA,EAAI,SACJ,QACJ,CAEF,KACF,CACF,CAEA,SAASa,GAAO,CACd,OAAOd,GAAQC,EAAI,MACrB,CAEA,SAASc,GAAO,CACd,OAAOhB,GAAQE,EAAI,MACrB,CAEA,SAASe,GAAO,CACd,OAAOlB,GAAQG,EAAI,MACrB,CAEA,SAASgB,GAAoB,CAC3B,IAAIb,EAGJ,GAFAH,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,aAAaR,EAAK,EAAE,EAChC,CAACW,EACH,MAAO,GAET,OADAH,EAAI,IAAMA,EAAI,OACNG,EAAW,CACjB,IAAK,GACH,GAAI,CAACY,EAAK,EACR,MAAO,GACTf,EAAI,UAAU,EACd,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,WAAW,KAAK,EACpB,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,WAAW,MAAM,EACrB,MACF,IAAK,GACH,GAAI,CAACc,EAAK,EACR,MAAO,GACTd,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,aAAaX,EAAK,CAAC,EAC/Bc,IACFH,EAAI,IAAMA,EAAI,OACVe,EAAK,IACPf,EAAI,UAAU,EACVG,GAAa,IACfH,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVe,EAAK,GACPf,EAAI,UAAU,MAKxB,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,aAAaV,EAAK,CAAC,EAC/Ba,IACFH,EAAI,IAAMA,EAAI,OACVG,GAAa,GACXY,EAAK,GACPf,EAAI,UAAU,GAEpB,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,aAAaT,EAAK,CAAC,EAC/BY,IACFH,EAAI,IAAMA,EAAI,OACVG,GAAa,GACXY,EAAK,GACPf,EAAI,UAAU,GAEpB,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVe,EAAK,GACPf,EAAI,UAAU,GAElB,MACF,IAAK,GACH,GAAI,CAACa,EAAK,GAAK,CAACb,EAAI,OAAO,EAAG,GAAG,EAC/B,MAAO,GACTA,EAAI,WAAW,IAAI,EACnB,KACJ,CACA,MAAO,EACT,CAEA,SAASiB,GAAgB,CACvB,IAAId,EAAWI,EACf,GAAIP,EAAI,QAAUD,EAAM,CAKtB,GAJAQ,EAAMP,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,aAAaP,EAAK,GAAG,EACjCU,EACF,OAAAH,EAAI,IAAMA,EAAI,OACVG,GAAa,GACfH,EAAI,UAAU,EAChBA,EAAI,eAAiBO,EACd,GAETP,EAAI,eAAiBO,CACvB,CACA,MAAO,EACT,CAEA,SAASW,GAAoB,CAC3B,IAAIf,EACJH,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,aAAaN,EAAK,CAAC,EAC/BS,IACFH,EAAI,IAAMA,EAAI,OACVG,GAAa,GACXU,EAAK,GACPb,EAAI,UAAU,EAEtB,CAEA,SAASmB,EAAMC,EAAIC,EAAI,CACrB,GAAIrB,EAAI,OAAO,EAAGoB,CAAE,EAAG,CACrBpB,EAAI,IAAMA,EAAI,OACd,IAAIO,EAAMP,EAAI,MAAQA,EAAI,OAC1B,GAAIA,EAAI,OAAO,EAAGqB,CAAE,EAClB,OAAArB,EAAI,OAASA,EAAI,MAAQO,EACrBM,EAAK,GACPb,EAAI,UAAU,EACT,EAEX,CACA,MAAO,EACT,CAEA,SAASsB,GAAkB,CACzB,IAAInB,EAAWI,EAAKC,EAAKC,EAGzB,GAFAT,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,aAAaL,EAAK,CAAC,EAC/BQ,EAEF,OADAH,EAAI,IAAMA,EAAI,OACNG,EAAW,CACjB,IAAK,GACCU,EAAK,IACPb,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACdO,EAAMP,EAAI,MAAQA,EAAI,OAClBmB,EAAM,IAAK,GAAG,GAChBA,EAAM,IAAK,GAAG,GAElB,MACF,IAAK,GACHnB,EAAI,WAAW,GAAG,EAClB,KACJ,CAEJ,CAEA,SAASuB,GAAQ,CACf,GAAI,CAACP,EAAkB,IACrBhB,EAAI,OAASA,EAAI,MACb,CAACiB,EAAc,GAAG,CACpBjB,EAAI,OAASA,EAAI,MACjBkB,EAAkB,EAClB,MACF,CAEFlB,EAAI,OAASA,EAAI,MACjBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,IACnBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,IACnBA,EAAI,OAASA,EAAI,MACba,EAAK,GACPb,EAAI,UAAU,GAGtB,CACA,KAAK,KAAO,UAAW,CACrB,IAAIO,EAAMP,EAAI,OACd,OAAAE,EAAU,EACVF,EAAI,OAASO,EACbI,EAAe,EACfX,EAAI,eAAiBO,EACrBP,EAAI,OAASA,EAAI,MACjBuB,EAAM,EACNvB,EAAI,OAASA,EAAI,MACjBsB,EAAgB,EAChBtB,EAAI,OAASA,EAAI,eACjBY,EAAW,EACJ,EACT,CACF,EAGF,OAAO,SAASY,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASvB,EAAM,CACjC,OAAAf,EAAG,WAAWe,CAAI,EAClBf,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWsC,CAAK,EACnBtC,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,6zCAAitC,MAAM,GAAG,CAAC,EAEhxCA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_pt", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6", "a_7", "a_8", "g_v", "I_p2", "I_p1", "I_pV", "sbp", "word", "r_prelude", "among_var", "habr2", "habr3", "habr4", "v_1", "v_2", "v_3", "habr5", "r_mark_regions", "r_postlude", "r_RV", "r_R1", "r_R2", "r_standard_suffix", "r_verb_suffix", "r_residual_suffix", "habr6", "c1", "c2", "r_residual_form", "habr1", "token"]}