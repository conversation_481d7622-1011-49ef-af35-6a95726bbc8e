import{a as F}from"./chunk-OSRY5VT3.min.js";var A=F((l,w)=>{(function(i,r){typeof define=="function"&&define.amd?define(r):typeof l=="object"?w.exports=r():r()(i.lunr)})(l,function(){return function(i){if(typeof i>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof i.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");i.no=function(){this.pipeline.reset(),this.pipeline.add(i.no.trimmer,i.no.stopWordFilter,i.no.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(i.no.stemmer))},i.no.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",i.no.trimmer=i.trimmerSupport.generateTrimmer(i.no.wordCharacters),i.Pipeline.registerFunction(i.no.trimmer,"trimmer-no"),i.no.stemmer=function(){var r=i.stemmerSupport.Among,f=i.stemmerSupport.SnowballProgram,s=new function(){var a=[new r("a",-1,1),new r("e",-1,1),new r("ede",1,1),new r("ande",1,1),new r("ende",1,1),new r("ane",1,1),new r("ene",1,1),new r("hetene",6,1),new r("erte",1,3),new r("en",-1,1),new r("heten",9,1),new r("ar",-1,1),new r("er",-1,1),new r("heter",12,1),new r("s",-1,2),new r("as",14,1),new r("es",14,1),new r("edes",16,1),new r("endes",16,1),new r("enes",16,1),new r("hetenes",19,1),new r("ens",14,1),new r("hetens",21,1),new r("ers",14,1),new r("ets",14,1),new r("et",-1,1),new r("het",25,1),new r("ert",-1,3),new r("ast",-1,1)],_=[new r("dt",-1,-1),new r("vt",-1,-1)],v=[new r("leg",-1,1),new r("eleg",0,1),new r("ig",-1,1),new r("eig",2,1),new r("lig",2,1),new r("elig",4,1),new r("els",-1,1),new r("lov",-1,1),new r("elov",7,1),new r("slov",7,1),new r("hetslov",9,1)],m=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,48,0,128],h=[119,125,149,1],d,o,e=new f;this.setCurrent=function(n){e.setCurrent(n)},this.getCurrent=function(){return e.getCurrent()};function k(){var n,t=e.cursor+3;if(o=e.limit,0<=t||t<=e.limit){for(d=t;;){if(n=e.cursor,e.in_grouping(m,97,248)){e.cursor=n;break}if(n>=e.limit)return;e.cursor=n+1}for(;!e.out_grouping(m,97,248);){if(e.cursor>=e.limit)return;e.cursor++}o=e.cursor,o<d&&(o=d)}}function p(){var n,t,c;if(e.cursor>=o&&(t=e.limit_backward,e.limit_backward=o,e.ket=e.cursor,n=e.find_among_b(a,29),e.limit_backward=t,n))switch(e.bra=e.cursor,n){case 1:e.slice_del();break;case 2:c=e.limit-e.cursor,e.in_grouping_b(h,98,122)?e.slice_del():(e.cursor=e.limit-c,e.eq_s_b(1,"k")&&e.out_grouping_b(m,97,248)&&e.slice_del());break;case 3:e.slice_from("er");break}}function g(){var n=e.limit-e.cursor,t;e.cursor>=o&&(t=e.limit_backward,e.limit_backward=o,e.ket=e.cursor,e.find_among_b(_,2)?(e.bra=e.cursor,e.limit_backward=t,e.cursor=e.limit-n,e.cursor>e.limit_backward&&(e.cursor--,e.bra=e.cursor,e.slice_del())):e.limit_backward=t)}function b(){var n,t;e.cursor>=o&&(t=e.limit_backward,e.limit_backward=o,e.ket=e.cursor,n=e.find_among_b(v,11),n?(e.bra=e.cursor,e.limit_backward=t,n==1&&e.slice_del()):e.limit_backward=t)}this.stem=function(){var n=e.cursor;return k(),e.limit_backward=n,e.cursor=e.limit,p(),e.cursor=e.limit,g(),e.cursor=e.limit,b(),!0}};return function(u){return typeof u.update=="function"?u.update(function(a){return s.setCurrent(a),s.stem(),s.getCurrent()}):(s.setCurrent(u),s.stem(),s.getCurrent())}}(),i.Pipeline.registerFunction(i.no.stemmer,"stemmer-no"),i.no.stopWordFilter=i.generateStopWordFilter("alle at av bare begge ble blei bli blir blitt b\xE5de b\xE5e da de deg dei deim deira deires dem den denne der dere deres det dette di din disse ditt du dykk dykkar d\xE5 eg ein eit eitt eller elles en enn er et ett etter for fordi fra f\xF8r ha hadde han hans har hennar henne hennes her hj\xE5 ho hoe honom hoss hossen hun hva hvem hver hvilke hvilken hvis hvor hvordan hvorfor i ikke ikkje ikkje ingen ingi inkje inn inni ja jeg kan kom korleis korso kun kunne kva kvar kvarhelst kven kvi kvifor man mange me med medan meg meget mellom men mi min mine mitt mot mykje ned no noe noen noka noko nokon nokor nokre n\xE5 n\xE5r og ogs\xE5 om opp oss over p\xE5 samme seg selv si si sia sidan siden sin sine sitt sj\xF8l skal skulle slik so som som somme somt s\xE5 s\xE5nn til um upp ut uten var vart varte ved vere verte vi vil ville vore vors vort v\xE5r v\xE6re v\xE6re v\xE6rt \xE5".split(" ")),i.Pipeline.registerFunction(i.no.stopWordFilter,"stopWordFilter-no")}})});export default A();
/*! Bundled license information:

lunr-languages/lunr.no.js:
  (*!
   * Lunr languages, `Norwegian` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.no-WPLSHWFO.min.js.map
