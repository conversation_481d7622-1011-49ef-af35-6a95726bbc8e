{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.hi.js"], "sourcesContent": ["/*!\n * Lunr languages, `Hindi` language\n * https://github.com/MiKr13/lunr-languages\n *\n * Copyright 2020, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.hi = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.hi.trimmer,\n        lunr.hi.stopWordFilter,\n        lunr.hi.stemmer\n      );\n\n      // change the tokenizer for japanese one\n      // if (isLunr2) { // for lunr version 2.0.0\n      //   this.tokenizer = lunr.hi.tokenizer;\n      // } else {\n      //   if (lunr.tokenizer) { // for lunr version 0.6.0\n      //     lunr.tokenizer = lunr.hi.tokenizer;\n      //   }\n      //   if (this.tokenizerFn) { // for lunr version 0.7.0 -> 1.0.0\n      //     this.tokenizerFn = lunr.hi.tokenizer;\n      //   }\n      // }\n\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.hi.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.hi.wordCharacters = \"\\u0900-\\u0903\\u0904-\\u090f\\u0910-\\u091f\\u0920-\\u092f\\u0930-\\u093f\\u0940-\\u094f\\u0950-\\u095f\\u0960-\\u096f\\u0970-\\u097fa-zA-Zａ-ｚＡ-Ｚ0-9０-９\";\n    // lunr.hi.wordCharacters = \"ऀँंःऄअआइईउऊऋऌऍऎएऐऑऒओऔकखगघङचछजझञटठडढणतथदधनऩपफबभमयरऱलळऴवशषसहऺऻ़ऽािीुूृॄॅॆेैॉॊोौ्ॎॏॐ॒॑॓॔ॕॖॗक़ख़ग़ज़ड़ढ़फ़य़ॠॡॢॣ।॥०१२३४५६७८९॰ॱॲॳॴॵॶॷॸॹॺॻॼॽॾॿa-zA-Zａ-ｚＡ-Ｚ0-9０-９\";\n    lunr.hi.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.hi.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.hi.trimmer, 'trimmer-hi');\n    /* lunr stop word filter */\n    lunr.hi.stopWordFilter = lunr.generateStopWordFilter(\n      'अत अपना अपनी अपने अभी अंदर आदि आप इत्यादि इन इनका इन्हीं इन्हें इन्हों इस इसका इसकी इसके इसमें इसी इसे उन उनका उनकी उनके उनको उन्हीं उन्हें उन्हों उस उसके उसी उसे एक एवं एस ऐसे और कई कर करता करते करना करने करें कहते कहा का काफ़ी कि कितना किन्हें किन्हों किया किर किस किसी किसे की कुछ कुल के को कोई कौन कौनसा गया घर जब जहाँ जा जितना जिन जिन्हें जिन्हों जिस जिसे जीधर जैसा जैसे जो तक तब तरह तिन तिन्हें तिन्हों तिस तिसे तो था थी थे दबारा दिया दुसरा दूसरे दो द्वारा न नके नहीं ना निहायत नीचे ने पर पहले पूरा पे फिर बनी बही बहुत बाद बाला बिलकुल भी भीतर मगर मानो मे में यदि यह यहाँ यही या यिह ये रखें रहा रहे ऱ्वासा लिए लिये लेकिन व वग़ैरह वर्ग वह वहाँ वहीं वाले वुह वे वो सकता सकते सबसे सभी साथ साबुत साभ सारा से सो संग ही हुआ हुई हुए है हैं हो होता होती होते होना होने'.split(' '));\n    /* lunr stemmer function */\n    lunr.hi.stemmer = (function() {\n\n      return function(word) {\n        // for lunr version 2\n        if (typeof word.update === \"function\") {\n          return word.update(function(word) {\n            return word;\n          })\n        } else { // for lunr version <= 1\n          return word;\n        }\n\n      }\n    })();\n\n    var segmenter = lunr.wordcut;\n    segmenter.init();\n    lunr.hi.tokenizer = function(obj) {\n      if (!arguments.length || obj == null || obj == undefined) return []\n      if (Array.isArray(obj)) return obj.map(function(t) {\n        return isLunr2 ? new lunr.Token(t.toLowerCase()) : t.toLowerCase()\n      });\n\n      var str = obj.toString().toLowerCase().replace(/^\\s+/, '');\n      return segmenter.cut(str).split('|');\n    }\n\n    lunr.Pipeline.registerFunction(lunr.hi.stemmer, 'stemmer-hi');\n    lunr.Pipeline.registerFunction(lunr.hi.stopWordFilter, 'stopWordFilter-hi');\n\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAcI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,wKAEzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAC5B,wpHAA8vB,MAAM,GAAG,CAAC,EAE1wBA,EAAK,GAAG,QAAW,UAAW,CAE5B,OAAO,SAASC,EAAM,CAEpB,OAAI,OAAOA,EAAK,QAAW,WAClBA,EAAK,OAAO,SAASA,EAAM,CAChC,OAAOA,CACT,CAAC,EAEMA,CAGX,CACF,EAAG,EAEH,IAAIC,EAAYF,EAAK,QACrBE,EAAU,KAAK,EACfF,EAAK,GAAG,UAAY,SAASG,EAAK,CAChC,GAAI,CAAC,UAAU,QAAUA,GAAO,MAAQA,GAAO,KAAW,MAAO,CAAC,EAClE,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOA,EAAI,IAAI,SAASC,EAAG,CACjD,OAAO,QAAU,IAAIJ,EAAK,MAAMI,EAAE,YAAY,CAAC,EAAIA,EAAE,YAAY,CACnE,CAAC,EAED,IAAIC,EAAMF,EAAI,SAAS,EAAE,YAAY,EAAE,QAAQ,OAAQ,EAAE,EACzD,OAAOD,EAAU,IAAIG,CAAG,EAAE,MAAM,GAAG,CACrC,EAEAL,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAC5DA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAE5E,CACF,CAAC", "names": ["require_lunr_hi", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "word", "segmenter", "obj", "t", "str"]}