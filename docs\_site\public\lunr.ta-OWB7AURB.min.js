import{a as s}from"./chunk-OSRY5VT3.min.js";var o=s((n,a)=>{(function(e,r){typeof define=="function"&&define.amd?define(r):typeof n=="object"?a.exports=r():r()(e.lunr)})(n,function(){return function(e){if(typeof e>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof e.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");e.ta=function(){this.pipeline.reset(),this.pipeline.add(e.ta.trimmer,e.ta.stopWordFilter,e.ta.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(e.ta.stemmer))},e.ta.wordCharacters="\u0B80-\u0B89\u0B8A-\u0B8F\u0B90-\u0B99\u0B9A-\u0B9F\u0BA0-\u0BA9\u0BAA-\u0BAF\u0BB0-\u0BB9\u0BBA-\u0BBF\u0BC0-\u0BC9\u0BCA-\u0BCF\u0BD0-\u0BD9\u0BDA-\u0BDF\u0BE0-\u0BE9\u0BEA-\u0BEF\u0BF0-\u0BF9\u0BFA-\u0BFFa-zA-Z\uFF41-\uFF5A\uFF21-\uFF3A0-9\uFF10-\uFF19",e.ta.trimmer=e.trimmerSupport.generateTrimmer(e.ta.wordCharacters),e.Pipeline.registerFunction(e.ta.trimmer,"trimmer-ta"),e.ta.stopWordFilter=e.generateStopWordFilter("\u0B85\u0B99\u0BCD\u0B95\u0BC1 \u0B85\u0B99\u0BCD\u0B95\u0BC7 \u0B85\u0BA4\u0BC1 \u0B85\u0BA4\u0BC8 \u0B85\u0BA8\u0BCD\u0BA4 \u0B85\u0BB5\u0BB0\u0BCD \u0B85\u0BB5\u0BB0\u0BCD\u0B95\u0BB3\u0BCD \u0B85\u0BB5\u0BB3\u0BCD \u0B85\u0BB5\u0BA9\u0BCD \u0B85\u0BB5\u0BC8 \u0B86\u0B95 \u0B86\u0B95\u0BB5\u0BC7 \u0B86\u0B95\u0BC8\u0BAF\u0BBE\u0BB2\u0BCD \u0B86\u0BA4\u0BB2\u0BBE\u0BB2\u0BCD \u0B86\u0BA4\u0BB2\u0BBF\u0BA9\u0BBE\u0BB2\u0BCD \u0B86\u0BA9\u0BBE\u0BB2\u0BC1\u0BAE\u0BCD \u0B86\u0BA9\u0BBE\u0BB2\u0BCD \u0B87\u0B99\u0BCD\u0B95\u0BC1 \u0B87\u0B99\u0BCD\u0B95\u0BC7 \u0B87\u0BA4\u0BC1 \u0B87\u0BA4\u0BC8 \u0B87\u0BA8\u0BCD\u0BA4 \u0B87\u0BAA\u0BCD\u0BAA\u0B9F\u0BBF \u0B87\u0BB5\u0BB0\u0BCD \u0B87\u0BB5\u0BB0\u0BCD\u0B95\u0BB3\u0BCD \u0B87\u0BB5\u0BB3\u0BCD \u0B87\u0BB5\u0BA9\u0BCD \u0B87\u0BB5\u0BC8 \u0B87\u0BB5\u0BCD\u0BB5\u0BB3\u0BB5\u0BC1 \u0B89\u0BA9\u0B95\u0BCD\u0B95\u0BC1 \u0B89\u0BA9\u0BA4\u0BC1 \u0B89\u0BA9\u0BCD \u0B89\u0BA9\u0BCD\u0BA9\u0BBE\u0BB2\u0BCD \u0B8E\u0B99\u0BCD\u0B95\u0BC1 \u0B8E\u0B99\u0BCD\u0B95\u0BC7 \u0B8E\u0BA4\u0BC1 \u0B8E\u0BA4\u0BC8 \u0B8E\u0BA8\u0BCD\u0BA4 \u0B8E\u0BAA\u0BCD\u0BAA\u0B9F\u0BBF \u0B8E\u0BB5\u0BB0\u0BCD \u0B8E\u0BB5\u0BB0\u0BCD\u0B95\u0BB3\u0BCD \u0B8E\u0BB5\u0BB3\u0BCD \u0B8E\u0BB5\u0BA9\u0BCD \u0B8E\u0BB5\u0BC8 \u0B8E\u0BB5\u0BCD\u0BB5\u0BB3\u0BB5\u0BC1 \u0B8E\u0BA9\u0B95\u0BCD\u0B95\u0BC1 \u0B8E\u0BA9\u0BA4\u0BC1 \u0B8E\u0BA9\u0BB5\u0BC7 \u0B8E\u0BA9\u0BCD \u0B8E\u0BA9\u0BCD\u0BA9 \u0B8E\u0BA9\u0BCD\u0BA9\u0BBE\u0BB2\u0BCD \u0B8F\u0BA4\u0BC1 \u0B8F\u0BA9\u0BCD \u0BA4\u0BA9\u0BA4\u0BC1 \u0BA4\u0BA9\u0BCD\u0BA9\u0BBE\u0BB2\u0BCD \u0BA4\u0BBE\u0BA9\u0BC7 \u0BA4\u0BBE\u0BA9\u0BCD \u0BA8\u0BBE\u0B99\u0BCD\u0B95\u0BB3\u0BCD \u0BA8\u0BBE\u0BAE\u0BCD \u0BA8\u0BBE\u0BA9\u0BCD \u0BA8\u0BC0 \u0BA8\u0BC0\u0B99\u0BCD\u0B95\u0BB3\u0BCD".split(" ")),e.ta.stemmer=function(){return function(t){return typeof t.update=="function"?t.update(function(i){return i}):t}}();var r=e.wordcut;r.init(),e.ta.tokenizer=function(t){if(!arguments.length||t==null||t==null)return[];if(Array.isArray(t))return t.map(function(u){return isLunr2?new e.Token(u.toLowerCase()):u.toLowerCase()});var i=t.toString().toLowerCase().replace(/^\s+/,"");return r.cut(i).split("|")},e.Pipeline.registerFunction(e.ta.stemmer,"stemmer-ta"),e.Pipeline.registerFunction(e.ta.stopWordFilter,"stopWordFilter-ta")}})});export default o();
/*! Bundled license information:

lunr-languages/lunr.ta.js:
  (*!
   * Lunr languages, `Tamil` language
   * https://github.com/tvmani/lunr-languages
   *
   * Copyright 2021, Manikandan Venkatasubban
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.ta-OWB7AURB.min.js.map
