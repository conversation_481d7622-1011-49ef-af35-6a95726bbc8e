{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.ar.js"], "sourcesContent": ["/*!\n * Lunr languages, `Arabic` language\n * https://github.com/MihaiValentin/lunr-languages\n *\n * Copyright 2018, Dalia Al-Shahrabi\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (2005)\n * <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> (2012)\n *\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.ar = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.ar.trimmer,\n        lunr.ar.stopWordFilter,\n        lunr.ar.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.ar.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.ar.wordCharacters = \"\\u0621-\\u065b\\u0671\\u0640\";\n    lunr.ar.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.ar.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.ar.trimmer, 'trimmer-ar');\n\n    /* lunr stemmer function */\n    lunr.ar.stemmer = (function() {\n      var self = this;\n      var word = '';\n      self.result = false;\n      self.preRemoved = false;\n      self.sufRemoved = false;\n\n      //prefix data\n      self.pre = {\n        pre1: 'ف ك ب و س ل ن ا ي ت',\n        pre2: 'ال لل',\n        pre3: 'بال وال فال تال كال ولل',\n        pre4: 'فبال كبال وبال وكال'\n      };\n\n      //suffix data\n      self.suf = {\n        suf1: 'ه ك ت ن ا ي',\n        suf2: 'نك نه ها وك يا اه ون ين تن تم نا وا ان كم كن ني نن ما هم هن تك ته ات يه',\n        suf3: 'تين كهم نيه نهم ونه وها يهم ونا ونك وني وهم تكم تنا تها تني تهم كما كها ناه نكم هنا تان يها',\n        suf4: 'كموه ناها ونني ونهم تكما تموه تكاه كماه ناكم ناهم نيها وننا'\n      }\n\n      //arabic language patterns and alternative mapping for patterns\n      self.patterns = JSON.parse('{\"pt43\":[{\"pt\":[{\"c\":\"ا\",\"l\":1}]},{\"pt\":[{\"c\":\"ا,ت,ن,ي\",\"l\":0}],\"mPt\":[{\"c\":\"ف\",\"l\":0,\"m\":1},{\"c\":\"ع\",\"l\":1,\"m\":2},{\"c\":\"ل\",\"l\":2,\"m\":3}]},{\"pt\":[{\"c\":\"و\",\"l\":2}],\"mPt\":[{\"c\":\"ف\",\"l\":0,\"m\":0},{\"c\":\"ع\",\"l\":1,\"m\":1},{\"c\":\"ل\",\"l\":2,\"m\":3}]},{\"pt\":[{\"c\":\"ا\",\"l\":2}]},{\"pt\":[{\"c\":\"ي\",\"l\":2}],\"mPt\":[{\"c\":\"ف\",\"l\":0,\"m\":0},{\"c\":\"ع\",\"l\":1,\"m\":1},{\"c\":\"ا\",\"l\":2},{\"c\":\"ل\",\"l\":3,\"m\":3}]},{\"pt\":[{\"c\":\"م\",\"l\":0}]}],\"pt53\":[{\"pt\":[{\"c\":\"ت\",\"l\":0},{\"c\":\"ا\",\"l\":2}]},{\"pt\":[{\"c\":\"ا,ن,ت,ي\",\"l\":0},{\"c\":\"ت\",\"l\":2}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ف\",\"l\":1,\"m\":1},{\"c\":\"ت\",\"l\":2},{\"c\":\"ع\",\"l\":3,\"m\":3},{\"c\":\"ا\",\"l\":4},{\"c\":\"ل\",\"l\":5,\"m\":4}]},{\"pt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ا\",\"l\":2}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ف\",\"l\":1,\"m\":1},{\"c\":\"ع\",\"l\":2,\"m\":3},{\"c\":\"ل\",\"l\":3,\"m\":4},{\"c\":\"ا\",\"l\":4},{\"c\":\"ل\",\"l\":5,\"m\":4}]},{\"pt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ا\",\"l\":3}],\"mPt\":[{\"c\":\"ف\",\"l\":0,\"m\":1},{\"c\":\"ع\",\"l\":1,\"m\":2},{\"c\":\"ل\",\"l\":2,\"m\":4}]},{\"pt\":[{\"c\":\"ا\",\"l\":3},{\"c\":\"ن\",\"l\":4}]},{\"pt\":[{\"c\":\"ت\",\"l\":0},{\"c\":\"ي\",\"l\":3}]},{\"pt\":[{\"c\":\"م\",\"l\":0},{\"c\":\"و\",\"l\":3}]},{\"pt\":[{\"c\":\"ا\",\"l\":1},{\"c\":\"و\",\"l\":3}]},{\"pt\":[{\"c\":\"و\",\"l\":1},{\"c\":\"ا\",\"l\":2}]},{\"pt\":[{\"c\":\"م\",\"l\":0},{\"c\":\"ا\",\"l\":3}]},{\"pt\":[{\"c\":\"م\",\"l\":0},{\"c\":\"ي\",\"l\":3}]},{\"pt\":[{\"c\":\"ا\",\"l\":2},{\"c\":\"ن\",\"l\":3}]},{\"pt\":[{\"c\":\"م\",\"l\":0},{\"c\":\"ن\",\"l\":1}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ن\",\"l\":1},{\"c\":\"ف\",\"l\":2,\"m\":2},{\"c\":\"ع\",\"l\":3,\"m\":3},{\"c\":\"ا\",\"l\":4},{\"c\":\"ل\",\"l\":5,\"m\":4}]},{\"pt\":[{\"c\":\"م\",\"l\":0},{\"c\":\"ت\",\"l\":2}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ف\",\"l\":1,\"m\":1},{\"c\":\"ت\",\"l\":2},{\"c\":\"ع\",\"l\":3,\"m\":3},{\"c\":\"ا\",\"l\":4},{\"c\":\"ل\",\"l\":5,\"m\":4}]},{\"pt\":[{\"c\":\"م\",\"l\":0},{\"c\":\"ا\",\"l\":2}]},{\"pt\":[{\"c\":\"م\",\"l\":1},{\"c\":\"ا\",\"l\":3}]},{\"pt\":[{\"c\":\"ي,ت,ا,ن\",\"l\":0},{\"c\":\"ت\",\"l\":1}],\"mPt\":[{\"c\":\"ف\",\"l\":0,\"m\":2},{\"c\":\"ع\",\"l\":1,\"m\":3},{\"c\":\"ا\",\"l\":2},{\"c\":\"ل\",\"l\":3,\"m\":4}]},{\"pt\":[{\"c\":\"ت,ي,ا,ن\",\"l\":0},{\"c\":\"ت\",\"l\":2}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ف\",\"l\":1,\"m\":1},{\"c\":\"ت\",\"l\":2},{\"c\":\"ع\",\"l\":3,\"m\":3},{\"c\":\"ا\",\"l\":4},{\"c\":\"ل\",\"l\":5,\"m\":4}]},{\"pt\":[{\"c\":\"ا\",\"l\":2},{\"c\":\"ي\",\"l\":3}]},{\"pt\":[{\"c\":\"ا,ي,ت,ن\",\"l\":0},{\"c\":\"ن\",\"l\":1}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ن\",\"l\":1},{\"c\":\"ف\",\"l\":2,\"m\":2},{\"c\":\"ع\",\"l\":3,\"m\":3},{\"c\":\"ا\",\"l\":4},{\"c\":\"ل\",\"l\":5,\"m\":4}]},{\"pt\":[{\"c\":\"ا\",\"l\":3},{\"c\":\"ء\",\"l\":4}]}],\"pt63\":[{\"pt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ت\",\"l\":2},{\"c\":\"ا\",\"l\":4}]},{\"pt\":[{\"c\":\"ا,ت,ن,ي\",\"l\":0},{\"c\":\"س\",\"l\":1},{\"c\":\"ت\",\"l\":2}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"س\",\"l\":1},{\"c\":\"ت\",\"l\":2},{\"c\":\"ف\",\"l\":3,\"m\":3},{\"c\":\"ع\",\"l\":4,\"m\":4},{\"c\":\"ا\",\"l\":5},{\"c\":\"ل\",\"l\":6,\"m\":5}]},{\"pt\":[{\"c\":\"ا,ن,ت,ي\",\"l\":0},{\"c\":\"و\",\"l\":3}]},{\"pt\":[{\"c\":\"م\",\"l\":0},{\"c\":\"س\",\"l\":1},{\"c\":\"ت\",\"l\":2}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"س\",\"l\":1},{\"c\":\"ت\",\"l\":2},{\"c\":\"ف\",\"l\":3,\"m\":3},{\"c\":\"ع\",\"l\":4,\"m\":4},{\"c\":\"ا\",\"l\":5},{\"c\":\"ل\",\"l\":6,\"m\":5}]},{\"pt\":[{\"c\":\"ي\",\"l\":1},{\"c\":\"ي\",\"l\":3},{\"c\":\"ا\",\"l\":4},{\"c\":\"ء\",\"l\":5}]},{\"pt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ن\",\"l\":1},{\"c\":\"ا\",\"l\":4}]}],\"pt54\":[{\"pt\":[{\"c\":\"ت\",\"l\":0}]},{\"pt\":[{\"c\":\"ا,ي,ت,ن\",\"l\":0}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ف\",\"l\":1,\"m\":1},{\"c\":\"ع\",\"l\":2,\"m\":2},{\"c\":\"ل\",\"l\":3,\"m\":3},{\"c\":\"ر\",\"l\":4,\"m\":4},{\"c\":\"ا\",\"l\":5},{\"c\":\"ر\",\"l\":6,\"m\":4}]},{\"pt\":[{\"c\":\"م\",\"l\":0}],\"mPt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ف\",\"l\":1,\"m\":1},{\"c\":\"ع\",\"l\":2,\"m\":2},{\"c\":\"ل\",\"l\":3,\"m\":3},{\"c\":\"ر\",\"l\":4,\"m\":4},{\"c\":\"ا\",\"l\":5},{\"c\":\"ر\",\"l\":6,\"m\":4}]},{\"pt\":[{\"c\":\"ا\",\"l\":2}]},{\"pt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ن\",\"l\":2}]}],\"pt64\":[{\"pt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ا\",\"l\":4}]},{\"pt\":[{\"c\":\"م\",\"l\":0},{\"c\":\"ت\",\"l\":1}]}],\"pt73\":[{\"pt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"س\",\"l\":1},{\"c\":\"ت\",\"l\":2},{\"c\":\"ا\",\"l\":5}]}],\"pt75\":[{\"pt\":[{\"c\":\"ا\",\"l\":0},{\"c\":\"ا\",\"l\":5}]}]}');\n\n      self.execArray = [\n        'cleanWord',\n        'removeDiacritics',\n        'cleanAlef',\n        'removeStopWords',\n        'normalizeHamzaAndAlef',\n        'removeStartWaw',\n        'removePre432',\n        'removeEndTaa',\n        'wordCheck'\n      ];\n\n      self.stem = function() {\n        var counter = 0;\n        self.result = false;\n        self.preRemoved = false;\n        self.sufRemoved = false;\n        while (counter < self.execArray.length && self.result != true) {\n          self.result = self[self.execArray[counter]]();\n          counter++;\n        }\n      }\n\n      self.setCurrent = function(word) {\n        self.word = word;\n      }\n\n      self.getCurrent = function() {\n        return self.word\n      }\n\n      /*remove elongating character and test that the word does not contain non-arabic characters.\n      If the word contains special characters, don't stem. */\n      self.cleanWord = function() {\n        var wordCharacters = \"\\u0621-\\u065b\\u0671\\u0640\";\n        var testRegex = new RegExp(\"[^\" + wordCharacters + \"]\");\n        self.word = self.word\n          .replace(new RegExp('\\u0640', 'g'), '');\n        if (testRegex.test(word)) {\n          return true;\n        }\n        return false;\n      }\n\n      self.removeDiacritics = function() {\n        var diacriticsRegex = new RegExp(\"[\\u064b-\\u065b]\");\n        self.word = self.word.replace(/[\\u064b-\\u065b]/gi, '');\n        return false;\n      }\n\n      /*Replace all variations of alef (آأإٱى) to a plain alef (ا)*/\n      self.cleanAlef = function() {\n        var alefRegex = new RegExp(\"[\\u0622\\u0623\\u0625\\u0671\\u0649]\");\n        self.word = self.word.replace(alefRegex, \"\\u0627\");\n        return false;\n      }\n\n      /* if the word is a stop word, don't stem*/\n      self.removeStopWords = function() {\n        var stopWords = '، اض امين اه اها اي ا اب اجل اجمع اخ اخذ اصبح اضحى اقبل اقل اكثر الا ام اما امامك امامك امسى اما ان انا انت انتم انتما انتن انت انشا انى او اوشك اولئك اولئكم اولاء اولالك اوه اي ايا اين اينما اي ان اي اف اذ اذا اذا اذما اذن الى اليكم اليكما اليكن اليك اليك الا اما ان انما اي اياك اياكم اياكما اياكن ايانا اياه اياها اياهم اياهما اياهن اياي ايه ان ا ابتدا اثر اجل احد اخرى اخلولق اذا اربعة ارتد استحال اطار اعادة اعلنت اف اكثر اكد الالاء الالى الا الاخيرة الان الاول الاولى التى التي الثاني الثانية الذاتي الذى الذي الذين السابق الف اللائي اللاتي اللتان اللتيا اللتين اللذان اللذين اللواتي الماضي المقبل الوقت الى اليوم اما امام امس ان انبرى انقلب انه انها او اول اي ايار ايام ايضا ب بات باسم بان بخ برس بسبب بس بشكل بضع بطان بعد بعض بك بكم بكما بكن بل بلى بما بماذا بمن بن بنا به بها بي بيد بين بس بله بئس تان تانك تبدل تجاه تحول تلقاء تلك تلكم تلكما تم تينك تين ته تي ثلاثة ثم ثم ثمة ثم جعل جلل جميع جير حار حاشا حاليا حاي حتى حرى حسب حم حوالى حول حيث حيثما حين حي حبذا حتى حذار خلا خلال دون دونك ذا ذات ذاك ذانك ذان ذلك ذلكم ذلكما ذلكن ذو ذوا ذواتا ذواتي ذيت ذينك ذين ذه ذي راح رجع رويدك ريث رب زيارة سبحان سرعان سنة سنوات سوف سوى ساء ساءما شبه شخصا شرع شتان صار صباح صفر صه صه ضد ضمن طاق طالما طفق طق ظل عاد عام عاما عامة عدا عدة عدد عدم عسى عشر عشرة علق على عليك عليه عليها عل عن عند عندما عوض عين عدس عما غدا غير  ف فان فلان فو فى في فيم فيما فيه فيها قال قام قبل قد قط قلما قوة كانما كاين كاي كاين كاد كان كانت كذا كذلك كرب كل كلا كلاهما كلتا كلم كليكما كليهما كلما كلا كم كما كي كيت كيف كيفما كان كخ لئن لا لات لاسيما لدن لدى لعمر لقاء لك لكم لكما لكن لكنما لكي لكيلا للامم لم لما لما لن لنا له لها لو لوكالة لولا لوما لي لست لست لستم لستما لستن لست لسن لعل لكن ليت ليس ليسا ليستا ليست ليسوا لسنا ما ماانفك مابرح مادام ماذا مازال مافتئ مايو متى مثل مذ مساء مع معاذ مقابل مكانكم مكانكما مكانكن مكانك مليار مليون مما ممن من منذ منها مه مهما من من نحن نحو نعم نفس نفسه نهاية نخ نعما نعم ها هاؤم هاك هاهنا هب هذا هذه هكذا هل هلم هلا هم هما هن هنا هناك هنالك هو هي هيا هيت هيا هؤلاء هاتان هاتين هاته هاتي هج هذا هذان هذين هذه هذي هيهات و وا واحد واضاف واضافت واكد وان واها واوضح وراءك وفي وقال وقالت وقد وقف وكان وكانت ولا ولم ومن وهو وهي ويكان وي وشكان يكون يمكن يوم ايان'.split(' ');\n        if (stopWords.indexOf(self.word) >= 0) {\n          return true;\n        }\n      }\n\n      /* changes ؤ ئ to ء and removes alef if at the end of the word*/\n      self.normalizeHamzaAndAlef = function() {\n        self.word = self.word.replace('\\u0624', '\\u0621');\n        self.word = self.word.replace('\\u0626', '\\u0621');\n        self.word = self.word.replace(/([\\u0627])\\1+/gi, '\\u0627');\n        return false;\n      }\n\n      /*remove end taa marboota ة*/\n      self.removeEndTaa = function() {\n        if (self.word.length > 2) {\n          self.word = self.word.replace(/[\\u0627]$/, '');\n          self.word = self.word.replace('\\u0629', '');\n          return false;\n        } else return true;\n      }\n\n      /* if the word starts with double waw وو keep only one of them */\n      self.removeStartWaw = function() {\n        if (self.word.length > 3 && self.word[0] == '\\u0648' && self.word[1] == '\\u0648') {\n          self.word = self.word.slice(1);\n        }\n        return false;\n      }\n\n      /* remove prefixes of size 4, 3 and 2 characters  */\n      self.removePre432 = function() {\n        var word = self.word;\n        if (self.word.length >= 7) {\n          var pre4Regex = new RegExp('^(' + self.pre.pre4.split(' ').join('|') + ')')\n          self.word = self.word.replace(pre4Regex, '');\n        }\n        if (self.word == word && self.word.length >= 6) {\n          var pre3Regex = new RegExp('^(' + self.pre.pre3.split(' ').join('|') + ')')\n          self.word = self.word.replace(pre3Regex, '');\n        }\n        if (self.word == word && self.word.length >= 5) {\n          var pre2Regex = new RegExp('^(' + self.pre.pre2.split(' ').join('|') + ')')\n          self.word = self.word.replace(pre2Regex, '');\n        }\n        if (word != self.word) self.preRemoved = true;\n        return false;\n      }\n\n      /* check the word against word patterns. If the word matches a pattern, map it to the \n      alternative pattern if available then stop stemming. */\n      self.patternCheck = function(pattern) {\n        var patternMatch = false;\n        for (var i = 0; i < pattern.length; i++) {\n          var currentPatternCheck = true;\n          for (var j = 0; j < pattern[i].pt.length; j++) {\n            var chars = pattern[i].pt[j].c.split(',');\n            var charMatch = false;\n            chars.forEach(function(el) {\n              if (self.word[pattern[i].pt[j].l] == el) {\n                charMatch = true;\n              }\n            })\n            if (!charMatch) {\n              currentPatternCheck = false;\n              break;\n            }\n          }\n          if (currentPatternCheck == true) {\n            if (pattern[i].mPt) {\n              var newWord = [];\n              for (var k = 0; k < pattern[i].mPt.length; k++) {\n                if (pattern[i].mPt[k].m != null) {\n                  newWord[pattern[i].mPt[k].l] = self.word[pattern[i].mPt[k].m]\n                } else {\n                  newWord[pattern[i].mPt[k].l] = pattern[i].mPt[k].c\n                }\n              }\n              self.word = newWord.join('');\n            }\n            self.result = true;\n            break;\n          }\n        }\n      }\n\n      /* remove prefixes of size 1 char*/\n      self.removePre1 = function() {\n        var word = self.word;\n        if (self.preRemoved == false)\n          if (self.word.length > 3) {\n            var pre1Regex = new RegExp('^(' + self.pre.pre1.split(' ').join('|') + ')')\n            self.word = self.word.replace(pre1Regex, '');\n          }\n        if (word != self.word) self.preRemoved = true;\n        return false;\n      }\n\n      /*remove suffixes of size 1 char */\n      self.removeSuf1 = function() {\n        var word = self.word;\n        if (self.sufRemoved == false)\n          if (self.word.length > 3) {\n            var suf1Regex = new RegExp('(' + self.suf.suf1.split(' ').join('|') + ')$')\n            self.word = self.word.replace(suf1Regex, '');\n          }\n        if (word != self.word) self.sufRemoved = true;\n        return false;\n      }\n\n      /*remove suffixes of size 4, 3 and 2 chars*/\n      self.removeSuf432 = function() {\n        var word = self.word;\n        if (self.word.length >= 6) {\n          var suf4Regex = new RegExp('(' + self.suf.suf4.split(' ').join('|') + ')$')\n          self.word = self.word.replace(suf4Regex, '');\n        }\n        if (self.word == word && self.word.length >= 5) {\n          var suf3Regex = new RegExp('(' + self.suf.suf3.split(' ').join('|') + ')$')\n          self.word = self.word.replace(suf3Regex, '');\n        }\n        if (self.word == word && self.word.length >= 4) {\n          var suf2Regex = new RegExp('(' + self.suf.suf2.split(' ').join('|') + ')$')\n          self.word = self.word.replace(suf2Regex, '');\n        }\n        if (word != self.word) self.sufRemoved = true;\n        return false;\n      }\n\n      /*check the word length and decide what is the next step accordingly*/\n      self.wordCheck = function() {\n        var word = self.word;\n        var word7Exec = [self.removeSuf432, self.removeSuf1, self.removePre1]\n        var counter = 0;\n        var patternChecked = false;\n        while (self.word.length >= 7 && !self.result && counter < word7Exec.length) {\n          if (self.word.length == 7 && !patternChecked) {\n            self.checkPattern73();\n            patternChecked = true;\n          } else {\n            word7Exec[counter]();\n            counter++;\n            patternChecked = false;\n          }\n        }\n\n        var word6Exec = [self.checkPattern63, self.removeSuf432, self.removeSuf1, self.removePre1, self.checkPattern64];\n        counter = 0;\n        while (self.word.length == 6 && !self.result && counter < word6Exec.length) {\n          word6Exec[counter]();\n          counter++;\n        }\n\n        var word5Exec = [self.checkPattern53, self.removeSuf432, self.removeSuf1, self.removePre1, self.checkPattern54];\n        counter = 0;\n        while (self.word.length == 5 && !self.result && counter < word5Exec.length) {\n          word5Exec[counter]();\n          counter++;\n        }\n\n        var word4Exec = [self.checkPattern43, self.removeSuf1, self.removePre1, self.removeSuf432];\n        counter = 0;\n        while (self.word.length == 4 && !self.result && counter < word4Exec.length) {\n          word4Exec[counter]();\n          counter++;\n        }\n        return true;\n      }\n\n      self.checkPattern43 = function() {\n        self.patternCheck(self.patterns.pt43)\n      }\n      self.checkPattern53 = function() {\n        self.patternCheck(self.patterns.pt53)\n      }\n      self.checkPattern54 = function() {\n        self.patternCheck(self.patterns.pt54)\n      }\n      self.checkPattern63 = function() {\n        self.patternCheck(self.patterns.pt63)\n      }\n      self.checkPattern64 = function() {\n        self.patternCheck(self.patterns.pt64)\n      }\n      self.checkPattern73 = function() {\n        self.patternCheck(self.patterns.pt73)\n      }\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            self.setCurrent(word);\n            self.stem();\n            return self.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          self.setCurrent(token);\n          self.stem();\n          return self.getCurrent();\n        }\n\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.ar.stemmer, 'stemmer-ar');\n\n    lunr.ar.stopWordFilter = lunr.generateStopWordFilter('، اض امين اه اها اي ا اب اجل اجمع اخ اخذ اصبح اضحى اقبل اقل اكثر الا ام اما امامك امامك امسى اما ان انا انت انتم انتما انتن انت انشا انى او اوشك اولئك اولئكم اولاء اولالك اوه اي ايا اين اينما اي ان اي اف اذ اذا اذا اذما اذن الى اليكم اليكما اليكن اليك اليك الا اما ان انما اي اياك اياكم اياكما اياكن ايانا اياه اياها اياهم اياهما اياهن اياي ايه ان ا ابتدا اثر اجل احد اخرى اخلولق اذا اربعة ارتد استحال اطار اعادة اعلنت اف اكثر اكد الالاء الالى الا الاخيرة الان الاول الاولى التى التي الثاني الثانية الذاتي الذى الذي الذين السابق الف اللائي اللاتي اللتان اللتيا اللتين اللذان اللذين اللواتي الماضي المقبل الوقت الى اليوم اما امام امس ان انبرى انقلب انه انها او اول اي ايار ايام ايضا ب بات باسم بان بخ برس بسبب بس بشكل بضع بطان بعد بعض بك بكم بكما بكن بل بلى بما بماذا بمن بن بنا به بها بي بيد بين بس بله بئس تان تانك تبدل تجاه تحول تلقاء تلك تلكم تلكما تم تينك تين ته تي ثلاثة ثم ثم ثمة ثم جعل جلل جميع جير حار حاشا حاليا حاي حتى حرى حسب حم حوالى حول حيث حيثما حين حي حبذا حتى حذار خلا خلال دون دونك ذا ذات ذاك ذانك ذان ذلك ذلكم ذلكما ذلكن ذو ذوا ذواتا ذواتي ذيت ذينك ذين ذه ذي راح رجع رويدك ريث رب زيارة سبحان سرعان سنة سنوات سوف سوى ساء ساءما شبه شخصا شرع شتان صار صباح صفر صه صه ضد ضمن طاق طالما طفق طق ظل عاد عام عاما عامة عدا عدة عدد عدم عسى عشر عشرة علق على عليك عليه عليها عل عن عند عندما عوض عين عدس عما غدا غير  ف فان فلان فو فى في فيم فيما فيه فيها قال قام قبل قد قط قلما قوة كانما كاين كاي كاين كاد كان كانت كذا كذلك كرب كل كلا كلاهما كلتا كلم كليكما كليهما كلما كلا كم كما كي كيت كيف كيفما كان كخ لئن لا لات لاسيما لدن لدى لعمر لقاء لك لكم لكما لكن لكنما لكي لكيلا للامم لم لما لما لن لنا له لها لو لوكالة لولا لوما لي لست لست لستم لستما لستن لست لسن لعل لكن ليت ليس ليسا ليستا ليست ليسوا لسنا ما ماانفك مابرح مادام ماذا مازال مافتئ مايو متى مثل مذ مساء مع معاذ مقابل مكانكم مكانكما مكانكن مكانك مليار مليون مما ممن من منذ منها مه مهما من من نحن نحو نعم نفس نفسه نهاية نخ نعما نعم ها هاؤم هاك هاهنا هب هذا هذه هكذا هل هلم هلا هم هما هن هنا هناك هنالك هو هي هيا هيت هيا هؤلاء هاتان هاتين هاته هاتي هج هذا هذان هذين هذه هذي هيهات وا واحد واضاف واضافت واكد وان واها واوضح وراءك وفي وقال وقالت وقد وقف وكان وكانت ولا ولم ومن وهو وهي ويكان وي وشكان يكون يمكن يوم ايان'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.ar.stopWordFilter, 'stopWordFilter-ar');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAyBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,4BACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAC5B,IAAIC,EAAO,KACPC,EAAO,GACX,OAAAD,EAAK,OAAS,GACdA,EAAK,WAAa,GAClBA,EAAK,WAAa,GAGlBA,EAAK,IAAM,CACT,KAAM,wEACN,KAAM,4BACN,KAAM,oHACN,KAAM,qGACR,EAGAA,EAAK,IAAM,CACT,KAAM,4CACN,KAAM,0TACN,KAAM,ubACN,KAAM,6SACR,EAGAA,EAAK,SAAW,KAAK,MAAM,u2IAAg8G,EAE39GA,EAAK,UAAY,CACf,YACA,mBACA,YACA,kBACA,wBACA,iBACA,eACA,eACA,WACF,EAEAA,EAAK,KAAO,UAAW,CACrB,IAAIE,EAAU,EAId,IAHAF,EAAK,OAAS,GACdA,EAAK,WAAa,GAClBA,EAAK,WAAa,GACXE,EAAUF,EAAK,UAAU,QAAUA,EAAK,QAAU,IACvDA,EAAK,OAASA,EAAKA,EAAK,UAAUE,CAAO,CAAC,EAAE,EAC5CA,GAEJ,EAEAF,EAAK,WAAa,SAASC,EAAM,CAC/BD,EAAK,KAAOC,CACd,EAEAD,EAAK,WAAa,UAAW,CAC3B,OAAOA,EAAK,IACd,EAIAA,EAAK,UAAY,UAAW,CAC1B,IAAIG,EAAiB,4BACjBC,EAAY,IAAI,OAAO,KAAOD,EAAiB,GAAG,EAGtD,OAFAH,EAAK,KAAOA,EAAK,KACd,QAAQ,IAAI,OAAO,SAAU,GAAG,EAAG,EAAE,EACpC,EAAAI,EAAU,KAAKH,CAAI,CAIzB,EAEAD,EAAK,iBAAmB,UAAW,CACjC,IAAIK,EAAkB,IAAI,OAAO,iBAAiB,EAClD,OAAAL,EAAK,KAAOA,EAAK,KAAK,QAAQ,oBAAqB,EAAE,EAC9C,EACT,EAGAA,EAAK,UAAY,UAAW,CAC1B,IAAIM,EAAY,IAAI,OAAO,kCAAkC,EAC7D,OAAAN,EAAK,KAAOA,EAAK,KAAK,QAAQM,EAAW,QAAQ,EAC1C,EACT,EAGAN,EAAK,gBAAkB,UAAW,CAChC,IAAIO,EAAY,u6UAAkoE,MAAM,GAAG,EAC3pE,GAAIA,EAAU,QAAQP,EAAK,IAAI,GAAK,EAClC,MAAO,EAEX,EAGAA,EAAK,sBAAwB,UAAW,CACtC,OAAAA,EAAK,KAAOA,EAAK,KAAK,QAAQ,SAAU,QAAQ,EAChDA,EAAK,KAAOA,EAAK,KAAK,QAAQ,SAAU,QAAQ,EAChDA,EAAK,KAAOA,EAAK,KAAK,QAAQ,kBAAmB,QAAQ,EAClD,EACT,EAGAA,EAAK,aAAe,UAAW,CAC7B,OAAIA,EAAK,KAAK,OAAS,GACrBA,EAAK,KAAOA,EAAK,KAAK,QAAQ,YAAa,EAAE,EAC7CA,EAAK,KAAOA,EAAK,KAAK,QAAQ,SAAU,EAAE,EACnC,IACK,EAChB,EAGAA,EAAK,eAAiB,UAAW,CAC/B,OAAIA,EAAK,KAAK,OAAS,GAAKA,EAAK,KAAK,CAAC,GAAK,UAAYA,EAAK,KAAK,CAAC,GAAK,WACtEA,EAAK,KAAOA,EAAK,KAAK,MAAM,CAAC,GAExB,EACT,EAGAA,EAAK,aAAe,UAAW,CAC7B,IAAIC,EAAOD,EAAK,KAChB,GAAIA,EAAK,KAAK,QAAU,EAAG,CACzB,IAAIQ,EAAY,IAAI,OAAO,KAAOR,EAAK,IAAI,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG,EAAI,GAAG,EAC1EA,EAAK,KAAOA,EAAK,KAAK,QAAQQ,EAAW,EAAE,CAC7C,CACA,GAAIR,EAAK,MAAQC,GAAQD,EAAK,KAAK,QAAU,EAAG,CAC9C,IAAIS,EAAY,IAAI,OAAO,KAAOT,EAAK,IAAI,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG,EAAI,GAAG,EAC1EA,EAAK,KAAOA,EAAK,KAAK,QAAQS,EAAW,EAAE,CAC7C,CACA,GAAIT,EAAK,MAAQC,GAAQD,EAAK,KAAK,QAAU,EAAG,CAC9C,IAAIU,EAAY,IAAI,OAAO,KAAOV,EAAK,IAAI,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG,EAAI,GAAG,EAC1EA,EAAK,KAAOA,EAAK,KAAK,QAAQU,EAAW,EAAE,CAC7C,CACA,OAAIT,GAAQD,EAAK,OAAMA,EAAK,WAAa,IAClC,EACT,EAIAA,EAAK,aAAe,SAASW,EAAS,CAEpC,QADIC,EAAe,GACVC,EAAI,EAAGA,EAAIF,EAAQ,OAAQE,IAAK,CAEvC,QADIC,EAAsB,GACjBC,EAAI,EAAGA,EAAIJ,EAAQE,CAAC,EAAE,GAAG,OAAQE,IAAK,CAC7C,IAAIC,EAAQL,EAAQE,CAAC,EAAE,GAAGE,CAAC,EAAE,EAAE,MAAM,GAAG,EACpCE,EAAY,GAMhB,GALAD,EAAM,QAAQ,SAASE,EAAI,CACrBlB,EAAK,KAAKW,EAAQE,CAAC,EAAE,GAAGE,CAAC,EAAE,CAAC,GAAKG,IACnCD,EAAY,GAEhB,CAAC,EACG,CAACA,EAAW,CACdH,EAAsB,GACtB,KACF,CACF,CACA,GAAIA,GAAuB,GAAM,CAC/B,GAAIH,EAAQE,CAAC,EAAE,IAAK,CAElB,QADIM,EAAU,CAAC,EACNC,EAAI,EAAGA,EAAIT,EAAQE,CAAC,EAAE,IAAI,OAAQO,IACrCT,EAAQE,CAAC,EAAE,IAAIO,CAAC,EAAE,GAAK,KACzBD,EAAQR,EAAQE,CAAC,EAAE,IAAIO,CAAC,EAAE,CAAC,EAAIpB,EAAK,KAAKW,EAAQE,CAAC,EAAE,IAAIO,CAAC,EAAE,CAAC,EAE5DD,EAAQR,EAAQE,CAAC,EAAE,IAAIO,CAAC,EAAE,CAAC,EAAIT,EAAQE,CAAC,EAAE,IAAIO,CAAC,EAAE,EAGrDpB,EAAK,KAAOmB,EAAQ,KAAK,EAAE,CAC7B,CACAnB,EAAK,OAAS,GACd,KACF,CACF,CACF,EAGAA,EAAK,WAAa,UAAW,CAC3B,IAAIC,EAAOD,EAAK,KAChB,GAAIA,EAAK,YAAc,IACjBA,EAAK,KAAK,OAAS,EAAG,CACxB,IAAIqB,EAAY,IAAI,OAAO,KAAOrB,EAAK,IAAI,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG,EAAI,GAAG,EAC1EA,EAAK,KAAOA,EAAK,KAAK,QAAQqB,EAAW,EAAE,CAC7C,CACF,OAAIpB,GAAQD,EAAK,OAAMA,EAAK,WAAa,IAClC,EACT,EAGAA,EAAK,WAAa,UAAW,CAC3B,IAAIC,EAAOD,EAAK,KAChB,GAAIA,EAAK,YAAc,IACjBA,EAAK,KAAK,OAAS,EAAG,CACxB,IAAIsB,EAAY,IAAI,OAAO,IAAMtB,EAAK,IAAI,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG,EAAI,IAAI,EAC1EA,EAAK,KAAOA,EAAK,KAAK,QAAQsB,EAAW,EAAE,CAC7C,CACF,OAAIrB,GAAQD,EAAK,OAAMA,EAAK,WAAa,IAClC,EACT,EAGAA,EAAK,aAAe,UAAW,CAC7B,IAAIC,EAAOD,EAAK,KAChB,GAAIA,EAAK,KAAK,QAAU,EAAG,CACzB,IAAIuB,EAAY,IAAI,OAAO,IAAMvB,EAAK,IAAI,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG,EAAI,IAAI,EAC1EA,EAAK,KAAOA,EAAK,KAAK,QAAQuB,EAAW,EAAE,CAC7C,CACA,GAAIvB,EAAK,MAAQC,GAAQD,EAAK,KAAK,QAAU,EAAG,CAC9C,IAAIwB,EAAY,IAAI,OAAO,IAAMxB,EAAK,IAAI,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG,EAAI,IAAI,EAC1EA,EAAK,KAAOA,EAAK,KAAK,QAAQwB,EAAW,EAAE,CAC7C,CACA,GAAIxB,EAAK,MAAQC,GAAQD,EAAK,KAAK,QAAU,EAAG,CAC9C,IAAIyB,EAAY,IAAI,OAAO,IAAMzB,EAAK,IAAI,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG,EAAI,IAAI,EAC1EA,EAAK,KAAOA,EAAK,KAAK,QAAQyB,EAAW,EAAE,CAC7C,CACA,OAAIxB,GAAQD,EAAK,OAAMA,EAAK,WAAa,IAClC,EACT,EAGAA,EAAK,UAAY,UAAW,CAK1B,QAJIC,EAAOD,EAAK,KACZ0B,EAAY,CAAC1B,EAAK,aAAcA,EAAK,WAAYA,EAAK,UAAU,EAChEE,EAAU,EACVyB,EAAiB,GACd3B,EAAK,KAAK,QAAU,GAAK,CAACA,EAAK,QAAUE,EAAUwB,EAAU,QAC9D1B,EAAK,KAAK,QAAU,GAAK,CAAC2B,GAC5B3B,EAAK,eAAe,EACpB2B,EAAiB,KAEjBD,EAAUxB,CAAO,EAAE,EACnBA,IACAyB,EAAiB,IAIrB,IAAIC,EAAY,CAAC5B,EAAK,eAAgBA,EAAK,aAAcA,EAAK,WAAYA,EAAK,WAAYA,EAAK,cAAc,EAE9G,IADAE,EAAU,EACHF,EAAK,KAAK,QAAU,GAAK,CAACA,EAAK,QAAUE,EAAU0B,EAAU,QAClEA,EAAU1B,CAAO,EAAE,EACnBA,IAGF,IAAI2B,EAAY,CAAC7B,EAAK,eAAgBA,EAAK,aAAcA,EAAK,WAAYA,EAAK,WAAYA,EAAK,cAAc,EAE9G,IADAE,EAAU,EACHF,EAAK,KAAK,QAAU,GAAK,CAACA,EAAK,QAAUE,EAAU2B,EAAU,QAClEA,EAAU3B,CAAO,EAAE,EACnBA,IAGF,IAAI4B,EAAY,CAAC9B,EAAK,eAAgBA,EAAK,WAAYA,EAAK,WAAYA,EAAK,YAAY,EAEzF,IADAE,EAAU,EACHF,EAAK,KAAK,QAAU,GAAK,CAACA,EAAK,QAAUE,EAAU4B,EAAU,QAClEA,EAAU5B,CAAO,EAAE,EACnBA,IAEF,MAAO,EACT,EAEAF,EAAK,eAAiB,UAAW,CAC/BA,EAAK,aAAaA,EAAK,SAAS,IAAI,CACtC,EACAA,EAAK,eAAiB,UAAW,CAC/BA,EAAK,aAAaA,EAAK,SAAS,IAAI,CACtC,EACAA,EAAK,eAAiB,UAAW,CAC/BA,EAAK,aAAaA,EAAK,SAAS,IAAI,CACtC,EACAA,EAAK,eAAiB,UAAW,CAC/BA,EAAK,aAAaA,EAAK,SAAS,IAAI,CACtC,EACAA,EAAK,eAAiB,UAAW,CAC/BA,EAAK,aAAaA,EAAK,SAAS,IAAI,CACtC,EACAA,EAAK,eAAiB,UAAW,CAC/BA,EAAK,aAAaA,EAAK,SAAS,IAAI,CACtC,EAGO,SAAS+B,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAAS9B,EAAM,CACjC,OAAAD,EAAK,WAAWC,CAAI,EACpBD,EAAK,KAAK,EACHA,EAAK,WAAW,CACzB,CAAC,GAEDA,EAAK,WAAW+B,CAAK,EACrB/B,EAAK,KAAK,EACHA,EAAK,WAAW,EAG3B,CACF,EAAG,EAEHD,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,g6UAAgoE,MAAM,GAAG,CAAC,EAE/rEA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_ar", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "self", "word", "counter", "wordCharacters", "testRegex", "diacriticsRegex", "alefRegex", "stopWords", "pre4Regex", "pre3Regex", "pre2Regex", "pattern", "patternMatch", "i", "currentPatternCheck", "j", "chars", "charMatch", "el", "newWord", "k", "pre1Regex", "suf1Regex", "suf4Regex", "suf3Regex", "suf2Regex", "word7Exec", "patternChecked", "word6Exec", "word5Exec", "word4Exec", "token"]}