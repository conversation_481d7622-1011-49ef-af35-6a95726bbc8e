<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Advanced Gesture Handling in DrawnUi.Maui | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Advanced Gesture Handling in DrawnUi.Maui | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="../toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/gestures.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="advanced-gesture-handling-in-drawnuimaui">Advanced Gesture Handling in DrawnUi.Maui</h1>

<p>DrawnUi.Maui provides a robust and extensible gesture system for building interactive, touch-driven UIs. This article covers how to use built-in gestures, implement custom gesture logic, and best practices for advanced scenarios.</p>
<h2 id="gesture-system-overview">Gesture System Overview</h2>
<ul>
<li><strong>Unified gesture model</strong> for tap, drag, swipe, pinch, long-press, and multi-touch</li>
<li><strong>ISkiaGestureListener</strong> interface for custom gesture handling</li>
<li><strong>SkiaHotspot</strong> and gesture listeners for declarative and code-based gestures</li>
<li><strong>Gesture locking and propagation control</strong> for complex UI hierarchies</li>
</ul>
<h2 id="basic-tap-and-click-handling">Basic Tap and Click Handling</h2>
<p>Use <code>SkiaHotspot</code> for simple tap/click detection:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaHotspot Tapped=&quot;OnTapped&quot;&gt;
    &lt;DrawUi:SkiaShape Type=&quot;Circle&quot; BackgroundColor=&quot;Blue&quot; WidthRequest=&quot;80&quot; HeightRequest=&quot;80&quot; /&gt;
&lt;/DrawUi:SkiaHotspot&gt;
</code></pre>
<pre><code class="lang-csharp">private void OnTapped(object sender, EventArgs e)
{
    // Handle tap
}
</code></pre>
<h2 id="handling-drag-swipe-and-multi-touch">Handling Drag, Swipe, and Multi-Touch</h2>
<p>Implement <code>ISkiaGestureListener</code> for advanced gestures:</p>
<pre><code class="lang-csharp">public class DraggableShape : SkiaShape, ISkiaGestureListener
{
    private float _x, _y;
    public override void OnParentChanged()
    {
        base.OnParentChanged();
        RegisterGestureListener(this);
    }
    public bool OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result)
    {
        if (type == TouchActionType.Pan)
        {
            _x = args.Location.X;
            _y = args.Location.Y;
            Invalidate();
            return true;
        }
        return false;
    }
}
</code></pre>
<h2 id="gesture-locking-and-propagation">Gesture Locking and Propagation</h2>
<p>Use the <code>LockChildrenGestures</code> property to control gesture propagation:</p>
<ul>
<li><code>LockTouch.Enabled</code>: Prevents children from receiving gestures</li>
<li><code>LockTouch.Disabled</code>: Allows all gestures to propagate</li>
<li><code>LockTouch.PassTap</code>: Only tap events pass through</li>
<li><code>LockTouch.PassTapAndLongPress</code>: Tap and long-press pass through</li>
</ul>
<p>Example:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaLayout LockChildrenGestures=&quot;PassTap&quot;&gt;
    &lt;!-- Only tap gestures reach children --&gt;
&lt;/DrawUi:SkiaLayout&gt;
</code></pre>
<h2 id="custom-gesture-handling-in-code">Custom Gesture Handling in Code</h2>
<p>Override <code>OnGestureEvent</code> for fine-grained control:</p>
<pre><code class="lang-csharp">public override ISkiaGestureListener OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result, SKPoint childOffset, SKPoint childOffsetDirect)
{
    // Custom logic for gesture routing or handling
    return base.OnGestureEvent(type, args, result, childOffset, childOffsetDirect);
}
</code></pre>
<h2 id="multi-touch-and-pinch-to-zoom">Multi-Touch and Pinch-to-Zoom</h2>
<p>Listen for pinch and multi-touch events:</p>
<pre><code class="lang-csharp">public bool OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result)
{
    if (type == TouchActionType.Pinch)
    {
        // args.PinchScale, args.Center, etc.
        // Handle zoom
        return true;
    }
    return false;
}
</code></pre>
<h2 id="gesture-utilities-and-best-practices">Gesture Utilities and Best Practices</h2>
<ul>
<li>Use <code>HadInput</code> to track which listeners have received input</li>
<li>Use <code>InputTransparent</code> to make controls ignore gestures</li>
<li>For performance, avoid deep gesture listener hierarchies</li>
<li>Use debug logging to trace gesture flow</li>
</ul>
<h2 id="example-swipe-to-delete-list-item">Example: Swipe-to-Delete List Item</h2>
<pre><code class="lang-xml">&lt;DrawUi:SkiaLayout ItemsSource=&quot;{Binding Items}&quot;&gt;
    &lt;DrawUi:SkiaLayout.ItemTemplate&gt;
        &lt;DataTemplate&gt;
            &lt;local:SwipeToDeleteItem /&gt;
        &lt;/DataTemplate&gt;
    &lt;/DrawUi:SkiaLayout.ItemTemplate&gt;
&lt;/DrawUi:SkiaLayout&gt;
</code></pre>
<pre><code class="lang-csharp">public class SwipeToDeleteItem : SkiaLayout, ISkiaGestureListener
{
    public override void OnParentChanged()
    {
        base.OnParentChanged();
        RegisterGestureListener(this);
    }
    public bool OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result)
    {
        if (type == TouchActionType.Pan &amp;&amp; result == TouchActionResult.Panning)
        {
            // Move item horizontally
            this.TranslationX = args.Location.X;
            Invalidate();
            return true;
        }
        if (type == TouchActionType.Pan &amp;&amp; result == TouchActionResult.Up)
        {
            if (Math.Abs(this.TranslationX) &gt; 100)
            {
                // Trigger delete
                // ...
            }
            this.TranslationX = 0;
            Invalidate();
            return true;
        }
        return false;
    }
}
</code></pre>
<h2 id="debugging-and-extending-gestures">Debugging and Extending Gestures</h2>
<ul>
<li>Use debug output to trace gesture events and propagation</li>
<li>Extend or compose gesture listeners for complex scenarios</li>
<li>Integrate with platform-specific gesture APIs if needed</li>
</ul>
<h2 id="summary">Summary</h2>
<p>DrawnUi.Maui’s gesture system enables rich, interactive UIs with tap, drag, swipe, pinch, and custom gestures. Use SkiaHotspot for simple cases, ISkiaGestureListener for advanced logic, and gesture locking for complex layouts.</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/gestures.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
