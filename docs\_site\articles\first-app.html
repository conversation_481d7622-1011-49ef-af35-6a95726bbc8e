<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Your First DrawnUi App | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Your First DrawnUi App | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/first-app.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="your-first-drawnui-app">Your First DrawnUi App</h1>

<p>This quickstart guide will help you create your first DrawnUi.Maui application from scratch.</p>
<h2 id="prerequisites">Prerequisites</h2>
<ul>
<li>.NET 8 or later</li>
<li>Visual Studio 2022+ (with MAUI workload) or VS Code</li>
</ul>
<h2 id="1-create-a-new-maui-project">1. Create a New MAUI Project</h2>
<pre><code class="lang-bash">dotnet new maui -n MyDrawnUiApp
cd MyDrawnUiApp
</code></pre>
<h2 id="2-add-drawnui-to-your-project">2. Add DrawnUi to Your Project</h2>
<pre><code class="lang-bash">dotnet add package DrawnUi.Maui
</code></pre>
<h2 id="3-add-a-drawnui-canvas-to-mainpage">3. Add a DrawnUi Canvas to MainPage</h2>
<p>Open <code>MainPage.xaml</code> and replace the content with:</p>
<pre><code class="lang-xml">&lt;ContentPage xmlns=&quot;http://schemas.microsoft.com/dotnet/2021/maui&quot;
             xmlns:x=&quot;http://schemas.microsoft.com/winfx/2009/xaml&quot;
             xmlns:draw=&quot;http://schemas.appomobi.com/drawnUi/2023/draw&quot;
             x:Class=&quot;MyDrawnUiApp.MainPage&quot;&gt;
    &lt;draw:Canvas HorizontalOptions=&quot;Fill&quot; VerticalOptions=&quot;Fill&quot;&gt;
        &lt;draw:SkiaLayout Type=&quot;Column&quot; Padding=&quot;32&quot; Spacing=&quot;24&quot;&gt;
            &lt;draw:SkiaLabel Text=&quot;Hello, DrawnUi!&quot; FontSize=&quot;32&quot; TextColor=&quot;Blue&quot; /&gt;
            &lt;draw:SkiaButton Text=&quot;Click Me&quot; Clicked=&quot;OnButtonClicked&quot; /&gt;
        &lt;/draw:SkiaLayout&gt;
    &lt;/draw:Canvas&gt;
&lt;/ContentPage&gt;
</code></pre>
<h2 id="4-initialize-drawnui-in-mauiprogramcs">4. Initialize DrawnUi in MauiProgram.cs</h2>
<p>Add the DrawnUi initialization to your <code>MauiProgram.cs</code>:</p>
<pre><code class="lang-csharp">public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp&lt;App&gt;()
            .UseDrawnUi() // Add this line
            .ConfigureFonts(fonts =&gt;
            {
                fonts.AddFont(&quot;OpenSans-Regular.ttf&quot;, &quot;FontText&quot;);
            });

        return builder.Build();
    }
}
</code></pre>
<h2 id="5-handle-button-click-in-code">5. Handle Button Click in Code</h2>
<p>In <code>MainPage.xaml.cs</code>:</p>
<pre><code class="lang-csharp">private void OnButtonClicked(SkiaButton sender, SkiaGesturesParameters e)
{
    // Show a message or update UI
    DisplayAlert(&quot;DrawnUi&quot;, &quot;Button clicked!&quot;, &quot;OK&quot;);
}
</code></pre>
<h2 id="6-run-your-app">6. Run Your App</h2>
<p>Build and run your app on Windows, Android, iOS, or Mac.</p>
<h2 id="next-steps">Next Steps</h2>
<ul>
<li>Explore the <a href="controls/index.html">Controls documentation</a></li>
<li>Try out <a href="samples.html">Samples</a></li>
<li>Read about <a href="advanced/index.html">Advanced features</a></li>
</ul>
<p>Welcome to the DrawnUi community!</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/first-app.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
