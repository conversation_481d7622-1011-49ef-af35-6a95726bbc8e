import{a as Z}from"./chunk-OSRY5VT3.min.js";var G=Z((k,q)=>{(function(n,r){typeof define=="function"&&define.amd?define(r):typeof k=="object"?q.exports=r():r()(n.lunr)})(k,function(){return function(n){if(typeof n>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof n.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");n.fr=function(){this.pipeline.reset(),this.pipeline.add(n.fr.trimmer,n.fr.stopWordFilter,n.fr.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(n.fr.stemmer))},n.fr.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",n.fr.trimmer=n.trimmerSupport.generateTrimmer(n.fr.wordCharacters),n.Pipeline.registerFunction(n.fr.trimmer,"trimmer-fr"),n.fr.stemmer=function(){var r=n.stemmerSupport.Among,E=n.stemmerSupport.SnowballProgram,c=new function(){var w=[new r("col",-1,-1),new r("par",-1,-1),new r("tap",-1,-1)],F=[new r("",-1,4),new r("I",0,1),new r("U",0,2),new r("Y",0,3)],A=[new r("iqU",-1,3),new r("abl",-1,3),new r("I\xE8r",-1,4),new r("i\xE8r",-1,4),new r("eus",-1,2),new r("iv",-1,1)],z=[new r("ic",-1,2),new r("abil",-1,1),new r("iv",-1,3)],x=[new r("iqUe",-1,1),new r("atrice",-1,2),new r("ance",-1,1),new r("ence",-1,5),new r("logie",-1,3),new r("able",-1,1),new r("isme",-1,1),new r("euse",-1,11),new r("iste",-1,1),new r("ive",-1,8),new r("if",-1,8),new r("usion",-1,4),new r("ation",-1,2),new r("ution",-1,4),new r("ateur",-1,2),new r("iqUes",-1,1),new r("atrices",-1,2),new r("ances",-1,1),new r("ences",-1,5),new r("logies",-1,3),new r("ables",-1,1),new r("ismes",-1,1),new r("euses",-1,11),new r("istes",-1,1),new r("ives",-1,8),new r("ifs",-1,8),new r("usions",-1,4),new r("ations",-1,2),new r("utions",-1,4),new r("ateurs",-1,2),new r("ments",-1,15),new r("ements",30,6),new r("issements",31,12),new r("it\xE9s",-1,7),new r("ment",-1,15),new r("ement",34,6),new r("issement",35,12),new r("amment",34,13),new r("emment",34,14),new r("aux",-1,10),new r("eaux",39,9),new r("eux",-1,1),new r("it\xE9",-1,7)],C=[new r("ira",-1,1),new r("ie",-1,1),new r("isse",-1,1),new r("issante",-1,1),new r("i",-1,1),new r("irai",4,1),new r("ir",-1,1),new r("iras",-1,1),new r("ies",-1,1),new r("\xEEmes",-1,1),new r("isses",-1,1),new r("issantes",-1,1),new r("\xEEtes",-1,1),new r("is",-1,1),new r("irais",13,1),new r("issais",13,1),new r("irions",-1,1),new r("issions",-1,1),new r("irons",-1,1),new r("issons",-1,1),new r("issants",-1,1),new r("it",-1,1),new r("irait",21,1),new r("issait",21,1),new r("issant",-1,1),new r("iraIent",-1,1),new r("issaIent",-1,1),new r("irent",-1,1),new r("issent",-1,1),new r("iront",-1,1),new r("\xEEt",-1,1),new r("iriez",-1,1),new r("issiez",-1,1),new r("irez",-1,1),new r("issez",-1,1)],B=[new r("a",-1,3),new r("era",0,2),new r("asse",-1,3),new r("ante",-1,3),new r("\xE9e",-1,2),new r("ai",-1,3),new r("erai",5,2),new r("er",-1,2),new r("as",-1,3),new r("eras",8,2),new r("\xE2mes",-1,3),new r("asses",-1,3),new r("antes",-1,3),new r("\xE2tes",-1,3),new r("\xE9es",-1,2),new r("ais",-1,3),new r("erais",15,2),new r("ions",-1,1),new r("erions",17,2),new r("assions",17,3),new r("erons",-1,2),new r("ants",-1,3),new r("\xE9s",-1,2),new r("ait",-1,3),new r("erait",23,2),new r("ant",-1,3),new r("aIent",-1,3),new r("eraIent",26,2),new r("\xE8rent",-1,2),new r("assent",-1,3),new r("eront",-1,2),new r("\xE2t",-1,3),new r("ez",-1,2),new r("iez",32,2),new r("eriez",33,2),new r("assiez",33,3),new r("erez",32,2),new r("\xE9",-1,2)],D=[new r("e",-1,3),new r("I\xE8re",0,2),new r("i\xE8re",0,2),new r("ion",-1,1),new r("Ier",-1,2),new r("ier",-1,2),new r("\xEB",-1,4)],y=[new r("ell",-1,-1),new r("eill",-1,-1),new r("enn",-1,-1),new r("onn",-1,-1),new r("ett",-1,-1)],a=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,128,130,103,8,5],I=[1,65,20,0,0,0,0,0,0,0,0,0,0,0,0,0,128],m,b,u,e=new E;this.setCurrent=function(i){e.setCurrent(i)},this.getCurrent=function(){return e.getCurrent()};function d(i,s,o){return e.eq_s(1,i)&&(e.ket=e.cursor,e.in_grouping(a,97,251))?(e.slice_from(s),e.cursor=o,!0):!1}function p(i,s,o){return e.eq_s(1,i)?(e.ket=e.cursor,e.slice_from(s),e.cursor=o,!0):!1}function P(){for(var i,s;;)if(i=e.cursor,!(e.in_grouping(a,97,251)&&(e.bra=e.cursor,s=e.cursor,d("u","U",i)||(e.cursor=s,d("i","I",i))||(e.cursor=s,p("y","Y",i))))&&(e.cursor=i,e.bra=i,!d("y","Y",i))){if(e.cursor=i,e.eq_s(1,"q")&&(e.bra=e.cursor,p("u","U",i)))continue;if(e.cursor=i,i>=e.limit)return;e.cursor++}}function g(){for(;!e.in_grouping(a,97,251);){if(e.cursor>=e.limit)return!0;e.cursor++}for(;!e.out_grouping(a,97,251);){if(e.cursor>=e.limit)return!0;e.cursor++}return!1}function U(){var i=e.cursor;if(u=e.limit,b=u,m=u,e.in_grouping(a,97,251)&&e.in_grouping(a,97,251)&&e.cursor<e.limit)e.cursor++;else if(e.cursor=i,!e.find_among(w,3)){e.cursor=i;do{if(e.cursor>=e.limit){e.cursor=u;break}e.cursor++}while(!e.in_grouping(a,97,251))}u=e.cursor,e.cursor=i,g()||(b=e.cursor,g()||(m=e.cursor))}function S(){for(var i,s;s=e.cursor,e.bra=s,i=e.find_among(F,4),!!i;)switch(e.ket=e.cursor,i){case 1:e.slice_from("i");break;case 2:e.slice_from("u");break;case 3:e.slice_from("y");break;case 4:if(e.cursor>=e.limit)return;e.cursor++;break}}function f(){return u<=e.cursor}function _(){return b<=e.cursor}function t(){return m<=e.cursor}function W(){var i,s;if(e.ket=e.cursor,i=e.find_among_b(x,43),i){switch(e.bra=e.cursor,i){case 1:if(!t())return!1;e.slice_del();break;case 2:if(!t())return!1;e.slice_del(),e.ket=e.cursor,e.eq_s_b(2,"ic")&&(e.bra=e.cursor,t()?e.slice_del():e.slice_from("iqU"));break;case 3:if(!t())return!1;e.slice_from("log");break;case 4:if(!t())return!1;e.slice_from("u");break;case 5:if(!t())return!1;e.slice_from("ent");break;case 6:if(!f())return!1;if(e.slice_del(),e.ket=e.cursor,i=e.find_among_b(A,6),i)switch(e.bra=e.cursor,i){case 1:t()&&(e.slice_del(),e.ket=e.cursor,e.eq_s_b(2,"at")&&(e.bra=e.cursor,t()&&e.slice_del()));break;case 2:t()?e.slice_del():_()&&e.slice_from("eux");break;case 3:t()&&e.slice_del();break;case 4:f()&&e.slice_from("i");break}break;case 7:if(!t())return!1;if(e.slice_del(),e.ket=e.cursor,i=e.find_among_b(z,3),i)switch(e.bra=e.cursor,i){case 1:t()?e.slice_del():e.slice_from("abl");break;case 2:t()?e.slice_del():e.slice_from("iqU");break;case 3:t()&&e.slice_del();break}break;case 8:if(!t())return!1;if(e.slice_del(),e.ket=e.cursor,e.eq_s_b(2,"at")&&(e.bra=e.cursor,t()&&(e.slice_del(),e.ket=e.cursor,e.eq_s_b(2,"ic")))){e.bra=e.cursor,t()?e.slice_del():e.slice_from("iqU");break}break;case 9:e.slice_from("eau");break;case 10:if(!_())return!1;e.slice_from("al");break;case 11:if(t())e.slice_del();else if(_())e.slice_from("eux");else return!1;break;case 12:if(!_()||!e.out_grouping_b(a,97,251))return!1;e.slice_del();break;case 13:return f()&&e.slice_from("ant"),!1;case 14:return f()&&e.slice_from("ent"),!1;case 15:return s=e.limit-e.cursor,e.in_grouping_b(a,97,251)&&f()&&(e.cursor=e.limit-s,e.slice_del()),!1}return!0}return!1}function L(){var i,s;if(e.cursor<u)return!1;if(s=e.limit_backward,e.limit_backward=u,e.ket=e.cursor,i=e.find_among_b(C,35),!i)return e.limit_backward=s,!1;if(e.bra=e.cursor,i==1){if(!e.out_grouping_b(a,97,251))return e.limit_backward=s,!1;e.slice_del()}return e.limit_backward=s,!0}function Y(){var i,s,o;if(e.cursor<u)return!1;if(s=e.limit_backward,e.limit_backward=u,e.ket=e.cursor,i=e.find_among_b(B,38),!i)return e.limit_backward=s,!1;switch(e.bra=e.cursor,i){case 1:if(!t())return e.limit_backward=s,!1;e.slice_del();break;case 2:e.slice_del();break;case 3:e.slice_del(),o=e.limit-e.cursor,e.ket=e.cursor,e.eq_s_b(1,"e")?(e.bra=e.cursor,e.slice_del()):e.cursor=e.limit-o;break}return e.limit_backward=s,!0}function j(){var i,s=e.limit-e.cursor,o,v,h;if(e.ket=e.cursor,e.eq_s_b(1,"s")?(e.bra=e.cursor,o=e.limit-e.cursor,e.out_grouping_b(I,97,232)?(e.cursor=e.limit-o,e.slice_del()):e.cursor=e.limit-s):e.cursor=e.limit-s,e.cursor>=u){if(v=e.limit_backward,e.limit_backward=u,e.ket=e.cursor,i=e.find_among_b(D,7),i)switch(e.bra=e.cursor,i){case 1:if(t()){if(h=e.limit-e.cursor,!e.eq_s_b(1,"s")&&(e.cursor=e.limit-h,!e.eq_s_b(1,"t")))break;e.slice_del()}break;case 2:e.slice_from("i");break;case 3:e.slice_del();break;case 4:e.eq_s_b(2,"gu")&&e.slice_del();break}e.limit_backward=v}}function R(){var i=e.limit-e.cursor;e.find_among_b(y,5)&&(e.cursor=e.limit-i,e.ket=e.cursor,e.cursor>e.limit_backward&&(e.cursor--,e.bra=e.cursor,e.slice_del()))}function V(){for(var i,s=1;e.out_grouping_b(a,97,251);)s--;if(s<=0){if(e.ket=e.cursor,i=e.limit-e.cursor,!e.eq_s_b(1,"\xE9")&&(e.cursor=e.limit-i,!e.eq_s_b(1,"\xE8")))return;e.bra=e.cursor,e.slice_from("e")}}function T(){if(!W()&&(e.cursor=e.limit,!L()&&(e.cursor=e.limit,!Y()))){e.cursor=e.limit,j();return}e.cursor=e.limit,e.ket=e.cursor,e.eq_s_b(1,"Y")?(e.bra=e.cursor,e.slice_from("i")):(e.cursor=e.limit,e.eq_s_b(1,"\xE7")&&(e.bra=e.cursor,e.slice_from("c")))}this.stem=function(){var i=e.cursor;return P(),e.cursor=i,U(),e.limit_backward=i,e.cursor=e.limit,T(),e.cursor=e.limit,R(),e.cursor=e.limit,V(),e.cursor=e.limit_backward,S(),!0}};return function(l){return typeof l.update=="function"?l.update(function(w){return c.setCurrent(w),c.stem(),c.getCurrent()}):(c.setCurrent(l),c.stem(),c.getCurrent())}}(),n.Pipeline.registerFunction(n.fr.stemmer,"stemmer-fr"),n.fr.stopWordFilter=n.generateStopWordFilter("ai aie aient aies ait as au aura aurai auraient aurais aurait auras aurez auriez aurions aurons auront aux avaient avais avait avec avez aviez avions avons ayant ayez ayons c ce ceci cel\xE0 ces cet cette d dans de des du elle en es est et eu eue eues eurent eus eusse eussent eusses eussiez eussions eut eux e\xFBmes e\xFBt e\xFBtes furent fus fusse fussent fusses fussiez fussions fut f\xFBmes f\xFBt f\xFBtes ici il ils j je l la le les leur leurs lui m ma mais me mes moi mon m\xEAme n ne nos notre nous on ont ou par pas pour qu que quel quelle quelles quels qui s sa sans se sera serai seraient serais serait seras serez seriez serions serons seront ses soi soient sois soit sommes son sont soyez soyons suis sur t ta te tes toi ton tu un une vos votre vous y \xE0 \xE9taient \xE9tais \xE9tait \xE9tant \xE9tiez \xE9tions \xE9t\xE9 \xE9t\xE9e \xE9t\xE9es \xE9t\xE9s \xEAtes".split(" ")),n.Pipeline.registerFunction(n.fr.stopWordFilter,"stopWordFilter-fr")}})});export default G();
/*! Bundled license information:

lunr-languages/lunr.fr.js:
  (*!
   * Lunr languages, `French` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.fr-H2QNBELV.min.js.map
