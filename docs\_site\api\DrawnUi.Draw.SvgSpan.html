<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SvgSpan | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SvgSpan | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SvgSpan">



  <h1 id="DrawnUi_Draw_SvgSpan" data-uid="DrawnUi.Draw.SvgSpan" class="text-break">
Class SvgSpan  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L3"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SvgSpan : TextSpan, INotifyPropertyChanged, IElementController, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IElement, IDisposable, IDrawnTextSpan</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a></div>
      <div><a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a></div>
      <div><span class="xref">SvgSpan</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller">IElementController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider">IEffectControlProvider</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement">IToolTipElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement">IContextFlyoutElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement">IElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IDrawnTextSpan.html">IDrawnTextSpan</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextProperty">TextSpan.TextProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Text">TextSpan.Text</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Default">TextSpan.Default</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_DebugString">TextSpan.DebugString</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_RenderingScale">TextSpan.RenderingScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Glyphs">TextSpan.Glyphs</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Shape">TextSpan.Shape</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextFiltered">TextSpan.TextFiltered</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CheckGlyphsCanBeRendered">TextSpan.CheckGlyphsCanBeRendered()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_SetupPaint_System_Double_SkiaSharp_SKPaint_">TextSpan.SetupPaint(double, SKPaint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetFont">TextSpan.HasSetFont</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetSize">TextSpan.HasSetSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetColor">TextSpan.HasSetColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Paint">TextSpan.Paint</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TypeFace">TextSpan.TypeFace</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_NeedShape">TextSpan.NeedShape</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasDecorations">TextSpan.HasDecorations</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Underline">TextSpan.Underline</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UnderlineWidth">TextSpan.UnderlineWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Strikeout">TextSpan.Strikeout</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_StrikeoutWidth">TextSpan.StrikeoutWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_StrikeoutColor">TextSpan.StrikeoutColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasTapHandler">TextSpan.HasTapHandler</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ForceCaptureInput">TextSpan.ForceCaptureInput</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HitIsInside_System_Single_System_Single_">TextSpan.HitIsInside(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FireTap">TextSpan.FireTap()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_DrawingOffset">TextSpan.DrawingOffset</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Tag">TextSpan.Tag</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Rects">TextSpan.Rects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CommandTapped">TextSpan.CommandTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Tapped">TextSpan.Tapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ParentControl">TextSpan.ParentControl</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_OnPropertyChanged_System_String_">TextSpan.OnPropertyChanged(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan__fontAutoSet">TextSpan._fontAutoSet</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UpdateFont">TextSpan.UpdateFont()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontFamily">TextSpan.FontFamily</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_LineSpacing">TextSpan.LineSpacing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_LineHeight">TextSpan.LineHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontWeight">TextSpan.FontWeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextColorProperty">TextSpan.TextColorProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextColor">TextSpan.TextColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_BackgroundColor">TextSpan.BackgroundColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ParagraphColor">TextSpan.ParagraphColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontSizeProperty">TextSpan.FontSizeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontSize">TextSpan.FontSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_IsItalic">TextSpan.IsItalic</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_IsBold">TextSpan.IsBold</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_AutoFindFont">TextSpan.AutoFindFont</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontDetectedWith">TextSpan.FontDetectedWith</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty">Element.AutomationIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty">Element.ClassIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild">Element.InsertLogicalChild(int, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild">Element.AddLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild">Element.RemoveLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren">Element.ClearLogicalChildren()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname">Element.FindByName(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource">Element.RemoveDynamicResource(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource">Element.SetDynamicResource(BindableProperty, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onbindingcontextchanged">Element.OnBindingContextChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildadded">Element.OnChildAdded(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildremoved">Element.OnChildRemoved(Element, int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentset">Element.OnParentSet()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging">Element.OnParentChanging(ParentChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged">Element.OnParentChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging">Element.OnHandlerChanging(HandlerChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged">Element.OnHandlerChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree">Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren">Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid">Element.AutomationId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid">Element.ClassId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects">Element.Effects</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id">Element.Id</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid">Element.StyleId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent">Element.Parent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handler">Element.Handler</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded">Element.ChildAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved">Element.ChildRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded">Element.DescendantAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved">Element.DescendantRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging">Element.ParentChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged">Element.ParentChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">Element.HandlerChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">Element.HandlerChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty">BindableObject.BindingContextProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)">BindableObject.ClearValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.ClearValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue">BindableObject.GetValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset">BindableObject.IsSet(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding">BindableObject.RemoveBinding(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding">BindableObject.SetBinding(BindableProperty, BindingBase)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings">BindableObject.ApplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging">BindableObject.OnPropertyChanging(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings">BindableObject.UnapplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)">BindableObject.SetValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)">BindableObject.SetValue(BindablePropertyKey, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)">BindableObject.CoerceValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.CoerceValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher">BindableObject.Dispatcher</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext">BindableObject.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged">BindableObject.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging">BindableObject.PropertyChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged">BindableObject.BindingContextChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_">StaticResourcesExtensions.FindParent&lt;T&gt;(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_">InternalExtensions.FindMauiContext(Element, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_">InternalExtensions.GetParentsPath(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_SvgSpan__ctor_" data-uid="DrawnUi.Draw.SvgSpan.#ctor*"></a>

  <h3 id="DrawnUi_Draw_SvgSpan__ctor" data-uid="DrawnUi.Draw.SvgSpan.#ctor">
  SvgSpan()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SvgSpan()</code></pre>
  </div>













  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_SvgSpan_Control" data-uid="DrawnUi.Draw.SvgSpan.Control">
  Control
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L96"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SkiaSvg Control</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaSvg.html">SkiaSvg</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_SvgSpan_Height_" data-uid="DrawnUi.Draw.SvgSpan.Height*"></a>

  <h3 id="DrawnUi_Draw_SvgSpan_Height" data-uid="DrawnUi.Draw.SvgSpan.Height">
  Height
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L29"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Height { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SvgSpan_Source_" data-uid="DrawnUi.Draw.SvgSpan.Source*"></a>

  <h3 id="DrawnUi_Draw_SvgSpan_Source" data-uid="DrawnUi.Draw.SvgSpan.Source">
  Source
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L80"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string Source { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SvgSpan_TintColor_" data-uid="DrawnUi.Draw.SvgSpan.TintColor*"></a>

  <h3 id="DrawnUi_Draw_SvgSpan_TintColor" data-uid="DrawnUi.Draw.SvgSpan.TintColor">
  TintColor
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L63"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color TintColor { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SvgSpan_VerticalAlignement_" data-uid="DrawnUi.Draw.SvgSpan.VerticalAlignement*"></a>

  <h3 id="DrawnUi_Draw_SvgSpan_VerticalAlignement" data-uid="DrawnUi.Draw.SvgSpan.VerticalAlignement">
  VerticalAlignement
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L12"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DrawImageAlignment VerticalAlignement { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.DrawImageAlignment.html">DrawImageAlignment</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SvgSpan_Width_" data-uid="DrawnUi.Draw.SvgSpan.Width*"></a>

  <h3 id="DrawnUi_Draw_SvgSpan_Width" data-uid="DrawnUi.Draw.SvgSpan.Width">
  Width
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L46"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Width { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_SvgSpan_Dispose_" data-uid="DrawnUi.Draw.SvgSpan.Dispose*"></a>

  <h3 id="DrawnUi_Draw_SvgSpan_Dispose" data-uid="DrawnUi.Draw.SvgSpan.Dispose">
  Dispose()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L103"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Dispose()</code></pre>
  </div>














</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L3" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
