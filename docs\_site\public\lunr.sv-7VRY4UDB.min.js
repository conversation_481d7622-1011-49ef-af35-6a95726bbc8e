import{a as k}from"./chunk-OSRY5VT3.min.js";var F=k((c,w)=>{(function(n,r){typeof define=="function"&&define.amd?define(r):typeof c=="object"?w.exports=r():r()(n.lunr)})(c,function(){return function(n){if(typeof n>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof n.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");n.sv=function(){this.pipeline.reset(),this.pipeline.add(n.sv.trimmer,n.sv.stopWordFilter,n.sv.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(n.sv.stemmer))},n.sv.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",n.sv.trimmer=n.trimmerSupport.generateTrimmer(n.sv.wordCharacters),n.Pipeline.registerFunction(n.sv.trimmer,"trimmer-sv"),n.sv.stemmer=function(){var r=n.stemmerSupport.Among,l=n.stemmerSupport.SnowballProgram,a=new function(){var o=[new r("a",-1,1),new r("arna",0,1),new r("erna",0,1),new r("heterna",2,1),new r("orna",0,1),new r("ad",-1,1),new r("e",-1,1),new r("ade",6,1),new r("ande",6,1),new r("arne",6,1),new r("are",6,1),new r("aste",6,1),new r("en",-1,1),new r("anden",12,1),new r("aren",12,1),new r("heten",12,1),new r("ern",-1,1),new r("ar",-1,1),new r("er",-1,1),new r("heter",18,1),new r("or",-1,1),new r("s",-1,2),new r("as",21,1),new r("arnas",22,1),new r("ernas",22,1),new r("ornas",22,1),new r("es",21,1),new r("ades",26,1),new r("andes",26,1),new r("ens",21,1),new r("arens",29,1),new r("hetens",29,1),new r("erns",21,1),new r("at",-1,1),new r("andet",-1,1),new r("het",-1,1),new r("ast",-1,1)],f=[new r("dd",-1,-1),new r("gd",-1,-1),new r("nn",-1,-1),new r("dt",-1,-1),new r("gt",-1,-1),new r("kt",-1,-1),new r("tt",-1,-1)],v=[new r("ig",-1,1),new r("lig",0,1),new r("els",-1,1),new r("fullt",-1,3),new r("l\xF6st",-1,2)],d=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,24,0,32],_=[119,127,149],m,t,e=new l;this.setCurrent=function(i){e.setCurrent(i)},this.getCurrent=function(){return e.getCurrent()};function p(){var i,s=e.cursor+3;if(t=e.limit,0<=s||s<=e.limit){for(m=s;;){if(i=e.cursor,e.in_grouping(d,97,246)){e.cursor=i;break}if(e.cursor=i,e.cursor>=e.limit)return;e.cursor++}for(;!e.out_grouping(d,97,246);){if(e.cursor>=e.limit)return;e.cursor++}t=e.cursor,t<m&&(t=m)}}function h(){var i,s=e.limit_backward;if(e.cursor>=t&&(e.limit_backward=t,e.cursor=e.limit,e.ket=e.cursor,i=e.find_among_b(o,37),e.limit_backward=s,i))switch(e.bra=e.cursor,i){case 1:e.slice_del();break;case 2:e.in_grouping_b(_,98,121)&&e.slice_del();break}}function g(){var i=e.limit_backward;e.cursor>=t&&(e.limit_backward=t,e.cursor=e.limit,e.find_among_b(f,7)&&(e.cursor=e.limit,e.ket=e.cursor,e.cursor>e.limit_backward&&(e.bra=--e.cursor,e.slice_del())),e.limit_backward=i)}function b(){var i,s;if(e.cursor>=t){if(s=e.limit_backward,e.limit_backward=t,e.cursor=e.limit,e.ket=e.cursor,i=e.find_among_b(v,5),i)switch(e.bra=e.cursor,i){case 1:e.slice_del();break;case 2:e.slice_from("l\xF6s");break;case 3:e.slice_from("full");break}e.limit_backward=s}}this.stem=function(){var i=e.cursor;return p(),e.limit_backward=i,e.cursor=e.limit,h(),e.cursor=e.limit,g(),e.cursor=e.limit,b(),!0}};return function(u){return typeof u.update=="function"?u.update(function(o){return a.setCurrent(o),a.stem(),a.getCurrent()}):(a.setCurrent(u),a.stem(),a.getCurrent())}}(),n.Pipeline.registerFunction(n.sv.stemmer,"stemmer-sv"),n.sv.stopWordFilter=n.generateStopWordFilter("alla allt att av blev bli blir blivit de dem den denna deras dess dessa det detta dig din dina ditt du d\xE4r d\xE5 efter ej eller en er era ert ett fr\xE5n f\xF6r ha hade han hans har henne hennes hon honom hur h\xE4r i icke ingen inom inte jag ju kan kunde man med mellan men mig min mina mitt mot mycket ni nu n\xE4r n\xE5gon n\xE5got n\xE5gra och om oss p\xE5 samma sedan sig sin sina sitta sj\xE4lv skulle som s\xE5 s\xE5dan s\xE5dana s\xE5dant till under upp ut utan vad var vara varf\xF6r varit varje vars vart vem vi vid vilka vilkas vilken vilket v\xE5r v\xE5ra v\xE5rt \xE4n \xE4r \xE5t \xF6ver".split(" ")),n.Pipeline.registerFunction(n.sv.stopWordFilter,"stopWordFilter-sv")}})});export default F();
/*! Bundled license information:

lunr-languages/lunr.sv.js:
  (*!
   * Lunr languages, `Swedish` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.sv-7VRY4UDB.min.js.map
