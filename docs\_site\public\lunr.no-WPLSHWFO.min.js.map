{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.no.js"], "sourcesContent": ["/*!\n * Lunr languages, `Norwegian` language\n * https://github.com/Mihai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.no = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.no.trimmer,\n        lunr.no.stopWordFilter,\n        lunr.no.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.no.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.no.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.no.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.no.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.no.trimmer, 'trimmer-no');\n\n    /* lunr stemmer function */\n    lunr.no.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function NorwegianStemmer() {\n          var a_0 = [new Among(\"a\", -1, 1), new Among(\"e\", -1, 1),\n              new Among(\"ede\", 1, 1), new Among(\"ande\", 1, 1),\n              new Among(\"ende\", 1, 1), new Among(\"ane\", 1, 1),\n              new Among(\"ene\", 1, 1), new Among(\"hetene\", 6, 1),\n              new Among(\"erte\", 1, 3), new Among(\"en\", -1, 1),\n              new Among(\"heten\", 9, 1), new Among(\"ar\", -1, 1),\n              new Among(\"er\", -1, 1), new Among(\"heter\", 12, 1),\n              new Among(\"s\", -1, 2), new Among(\"as\", 14, 1),\n              new Among(\"es\", 14, 1), new Among(\"edes\", 16, 1),\n              new Among(\"endes\", 16, 1), new Among(\"enes\", 16, 1),\n              new Among(\"hetenes\", 19, 1), new Among(\"ens\", 14, 1),\n              new Among(\"hetens\", 21, 1), new Among(\"ers\", 14, 1),\n              new Among(\"ets\", 14, 1), new Among(\"et\", -1, 1),\n              new Among(\"het\", 25, 1), new Among(\"ert\", -1, 3),\n              new Among(\"ast\", -1, 1)\n            ],\n            a_1 = [new Among(\"dt\", -1, -1),\n              new Among(\"vt\", -1, -1)\n            ],\n            a_2 = [new Among(\"leg\", -1, 1),\n              new Among(\"eleg\", 0, 1), new Among(\"ig\", -1, 1),\n              new Among(\"eig\", 2, 1), new Among(\"lig\", 2, 1),\n              new Among(\"elig\", 4, 1), new Among(\"els\", -1, 1),\n              new Among(\"lov\", -1, 1), new Among(\"elov\", 7, 1),\n              new Among(\"slov\", 7, 1), new Among(\"hetslov\", 9, 1)\n            ],\n            g_v = [17,\n              65, 16, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 128\n            ],\n            g_s_ending = [\n              119, 125, 149, 1\n            ],\n            I_x, I_p1, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function r_mark_regions() {\n            var v_1, c = sbp.cursor + 3;\n            I_p1 = sbp.limit;\n            if (0 <= c || c <= sbp.limit) {\n              I_x = c;\n              while (true) {\n                v_1 = sbp.cursor;\n                if (sbp.in_grouping(g_v, 97, 248)) {\n                  sbp.cursor = v_1;\n                  break;\n                }\n                if (v_1 >= sbp.limit)\n                  return;\n                sbp.cursor = v_1 + 1;\n              }\n              while (!sbp.out_grouping(g_v, 97, 248)) {\n                if (sbp.cursor >= sbp.limit)\n                  return;\n                sbp.cursor++;\n              }\n              I_p1 = sbp.cursor;\n              if (I_p1 < I_x)\n                I_p1 = I_x;\n            }\n          }\n\n          function r_main_suffix() {\n            var among_var, v_1, v_2;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_0, 29);\n              sbp.limit_backward = v_1;\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                    v_2 = sbp.limit - sbp.cursor;\n                    if (sbp.in_grouping_b(g_s_ending, 98, 122))\n                      sbp.slice_del();\n                    else {\n                      sbp.cursor = sbp.limit - v_2;\n                      if (sbp.eq_s_b(1, \"k\") &&\n                        sbp.out_grouping_b(g_v, 97, 248))\n                        sbp.slice_del();\n                    }\n                    break;\n                  case 3:\n                    sbp.slice_from(\"er\");\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_consonant_pair() {\n            var v_1 = sbp.limit - sbp.cursor,\n              v_2;\n            if (sbp.cursor >= I_p1) {\n              v_2 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              if (sbp.find_among_b(a_1, 2)) {\n                sbp.bra = sbp.cursor;\n                sbp.limit_backward = v_2;\n                sbp.cursor = sbp.limit - v_1;\n                if (sbp.cursor > sbp.limit_backward) {\n                  sbp.cursor--;\n                  sbp.bra = sbp.cursor;\n                  sbp.slice_del();\n                }\n              } else\n                sbp.limit_backward = v_2;\n            }\n          }\n\n          function r_other_suffix() {\n            var among_var, v_1;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_2, 11);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                sbp.limit_backward = v_1;\n                if (among_var == 1)\n                  sbp.slice_del();\n              } else\n                sbp.limit_backward = v_1;\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            r_main_suffix();\n            sbp.cursor = sbp.limit;\n            r_consonant_pair();\n            sbp.cursor = sbp.limit;\n            r_other_suffix();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.no.stemmer, 'stemmer-no');\n\n    lunr.no.stopWordFilter = lunr.generateStopWordFilter('alle at av bare begge ble blei bli blir blitt både båe da de deg dei deim deira deires dem den denne der dere deres det dette di din disse ditt du dykk dykkar då eg ein eit eitt eller elles en enn er et ett etter for fordi fra før ha hadde han hans har hennar henne hennes her hjå ho hoe honom hoss hossen hun hva hvem hver hvilke hvilken hvis hvor hvordan hvorfor i ikke ikkje ikkje ingen ingi inkje inn inni ja jeg kan kom korleis korso kun kunne kva kvar kvarhelst kven kvi kvifor man mange me med medan meg meget mellom men mi min mine mitt mot mykje ned no noe noen noka noko nokon nokor nokre nå når og også om opp oss over på samme seg selv si si sia sidan siden sin sine sitt sjøl skal skulle slik so som som somme somt så sånn til um upp ut uten var vart varte ved vere verte vi vil ville vore vors vort vår være være vært å'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.no.stopWordFilter, 'stopWordFilter-no');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAA4B,CACnC,IAAIC,EAAM,CAAC,IAAIH,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,IAAK,GAAI,CAAC,EAClD,IAAIA,EAAM,MAAO,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAC9C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,MAAO,EAAG,CAAC,EAC9C,IAAIA,EAAM,MAAO,EAAG,CAAC,EAAG,IAAIA,EAAM,SAAU,EAAG,CAAC,EAChD,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,QAAS,EAAG,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC/C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAChD,IAAIA,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC5C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACnD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAClD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,CACxB,EACAI,EAAM,CAAC,IAAIJ,EAAM,KAAM,GAAI,EAAE,EAC3B,IAAIA,EAAM,KAAM,GAAI,EAAE,CACxB,EACAK,EAAM,CAAC,IAAIL,EAAM,MAAO,GAAI,CAAC,EAC3B,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,MAAO,EAAG,CAAC,EAAG,IAAIA,EAAM,MAAO,EAAG,CAAC,EAC7C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAC/C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,UAAW,EAAG,CAAC,CACpD,EACAM,EAAM,CAAC,GACL,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,GACxD,EACAC,EAAa,CACX,IAAK,IAAK,IAAK,CACjB,EACAC,EAAKC,EAAMC,EAAM,IAAIT,EACvB,KAAK,WAAa,SAASU,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,GAAiB,CACxB,IAAIC,EAAKC,EAAIJ,EAAI,OAAS,EAE1B,GADAD,EAAOC,EAAI,MACP,GAAKI,GAAKA,GAAKJ,EAAI,MAAO,CAE5B,IADAF,EAAMM,IACO,CAEX,GADAD,EAAMH,EAAI,OACNA,EAAI,YAAYJ,EAAK,GAAI,GAAG,EAAG,CACjCI,EAAI,OAASG,EACb,KACF,CACA,GAAIA,GAAOH,EAAI,MACb,OACFA,EAAI,OAASG,EAAM,CACrB,CACA,KAAO,CAACH,EAAI,aAAaJ,EAAK,GAAI,GAAG,GAAG,CACtC,GAAII,EAAI,QAAUA,EAAI,MACpB,OACFA,EAAI,QACN,CACAD,EAAOC,EAAI,OACPD,EAAOD,IACTC,EAAOD,EACX,CACF,CAEA,SAASO,GAAgB,CACvB,IAAIC,EAAWH,EAAKI,EACpB,GAAIP,EAAI,QAAUD,IAChBI,EAAMH,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdM,EAAYN,EAAI,aAAaP,EAAK,EAAE,EACpCO,EAAI,eAAiBG,EACjBG,GAEF,OADAN,EAAI,IAAMA,EAAI,OACNM,EAAW,CACjB,IAAK,GACHN,EAAI,UAAU,EACd,MACF,IAAK,GACHO,EAAMP,EAAI,MAAQA,EAAI,OAClBA,EAAI,cAAcH,EAAY,GAAI,GAAG,EACvCG,EAAI,UAAU,GAEdA,EAAI,OAASA,EAAI,MAAQO,EACrBP,EAAI,OAAO,EAAG,GAAG,GACnBA,EAAI,eAAeJ,EAAK,GAAI,GAAG,GAC/BI,EAAI,UAAU,GAElB,MACF,IAAK,GACHA,EAAI,WAAW,IAAI,EACnB,KACJ,CAGN,CAEA,SAASQ,GAAmB,CAC1B,IAAIL,EAAMH,EAAI,MAAQA,EAAI,OACxBO,EACEP,EAAI,QAAUD,IAChBQ,EAAMP,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACVA,EAAI,aAAaN,EAAK,CAAC,GACzBM,EAAI,IAAMA,EAAI,OACdA,EAAI,eAAiBO,EACrBP,EAAI,OAASA,EAAI,MAAQG,EACrBH,EAAI,OAASA,EAAI,iBACnBA,EAAI,SACJA,EAAI,IAAMA,EAAI,OACdA,EAAI,UAAU,IAGhBA,EAAI,eAAiBO,EAE3B,CAEA,SAASE,GAAiB,CACxB,IAAIH,EAAWH,EACXH,EAAI,QAAUD,IAChBI,EAAMH,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdM,EAAYN,EAAI,aAAaL,EAAK,EAAE,EAChCW,GACFN,EAAI,IAAMA,EAAI,OACdA,EAAI,eAAiBG,EACjBG,GAAa,GACfN,EAAI,UAAU,GAEhBA,EAAI,eAAiBG,EAE3B,CACA,KAAK,KAAO,UAAW,CACrB,IAAIA,EAAMH,EAAI,OACd,OAAAE,EAAe,EACfF,EAAI,eAAiBG,EACrBH,EAAI,OAASA,EAAI,MACjBK,EAAc,EACdL,EAAI,OAASA,EAAI,MACjBQ,EAAiB,EACjBR,EAAI,OAASA,EAAI,MACjBS,EAAe,EACR,EACT,CACF,EAGF,OAAO,SAASC,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAAST,EAAM,CACjC,OAAAT,EAAG,WAAWS,CAAI,EAClBT,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWkB,CAAK,EACnBlB,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,u3BAAo0B,MAAM,GAAG,CAAC,EAEn4BA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_no", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "g_v", "g_s_ending", "I_x", "I_p1", "sbp", "word", "r_mark_regions", "v_1", "c", "r_main_suffix", "among_var", "v_2", "r_consonant_pair", "r_other_suffix", "token"]}