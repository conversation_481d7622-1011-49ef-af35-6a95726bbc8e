<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Enum SkiaCacheType | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Enum SkiaCacheType | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaCacheType.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaCacheType%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaCacheType">




  <h1 id="DrawnUi_Draw_SkiaCacheType" data-uid="DrawnUi.Draw.SkiaCacheType" class="text-break">
Enum SkiaCacheType  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Cache/SkiaCacheType.cs/#L4"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public enum SkiaCacheType</code></pre>
  </div>








  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>

  <h2 id="fields">Fields
</h2>
  <dl class="parameters">
    <dt id="DrawnUi_Draw_SkiaCacheType_GPU"><code>GPU = 6</code></dt>
  
  <dd><p>The cached surface will use the same graphic context as your hardware-accelerated canvas.
This kind of cache will not apply Opacity as not all platforms support transparency for hardware accelerated layer.
Will fallback to simple Image cache type if hardware acceleration is not available.</p>
</dd>
    <dt id="DrawnUi_Draw_SkiaCacheType_Image"><code>Image = 3</code></dt>
  
  <dd><p>Will use simple SKBitmap cache type, will not use hardware acceleration.
Slower but will work for sizes bigger than graphics memory if needed.</p>
</dd>
    <dt id="DrawnUi_Draw_SkiaCacheType_ImageComposite"><code>ImageComposite = 5</code></dt>
  
  <dd><p>Would receive the invalidated area rectangle, then redraw the previous cache but clipped to exclude the dirty area, then would re-create the dirty area and draw it clipped inside the dirty rectangle. This is useful for layouts with many children, like scroll content etc, but useless for non-containers.</p>
</dd>
    <dt id="DrawnUi_Draw_SkiaCacheType_ImageDoubleBuffered"><code>ImageDoubleBuffered = 4</code></dt>
  
  <dd><p>Using <code>Image</code> cache type with double buffering. Will display a previous cache while rendering the new one in background, thus not slowing scrolling etc.</p>
</dd>
    <dt id="DrawnUi_Draw_SkiaCacheType_None"><code>None = 0</code></dt>
  
  <dd><p>True and old school</p>
</dd>
    <dt id="DrawnUi_Draw_SkiaCacheType_Operations"><code>Operations = 1</code></dt>
  
  <dd><p>Create and reuse SKPicture. Try this first for labels, svg etc.
Do not use this when dropping shadows or with other effects, better use Bitmap.</p>
</dd>
    <dt id="DrawnUi_Draw_SkiaCacheType_OperationsFull"><code>OperationsFull = 2</code></dt>
  
  <dd><p>EXPERIMENTAL! May be bugged. Create and reuse SKPicture all over the canvas ignoring clipping.
Try this first for labels, svg etc.
Do not use this when dropping shadows or with other effects, better use Bitmap.</p>
</dd>
  </dl>



</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Cache/SkiaCacheType.cs/#L4" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
