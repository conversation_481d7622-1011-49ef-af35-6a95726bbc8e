<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>What&#39;s New in DrawnUi.Maui | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="What&#39;s New in DrawnUi.Maui | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/whats-new.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="whats-new-in-drawnuimaui">What's New in DrawnUi.Maui</h1>

<p>This page highlights the latest updates, features, and improvements in DrawnUi.Maui.</p>
<h2 id="latest-major-updates">Latest Major Updates</h2>
<h3 id="package-renaming-strategy">Package Renaming Strategy</h3>
<ul>
<li><strong>New Package</strong>: <code>DrawnUi.Maui</code> replaces the old package ID <code>AppoMobi.Maui.DrawnUi</code></li>
<li><strong>Backward Compatibility</strong>: Old package kept for compatibility for some time</li>
<li><strong>Migration</strong>: Simply update your package reference to use the new package name</li>
</ul>
<h3 id="documentation-improvements">Documentation Improvements</h3>
<ul>
<li><strong>New Documentation Site</strong>: First appearance of comprehensive docs in <code>/docs</code> folder</li>
<li><strong>AI Training Data</strong>: Use <code>/aidocs</code> subfolder for training language models</li>
<li><strong>Enhanced Examples</strong>: Updated example apps to align with latest changes</li>
</ul>
<h3 id="new-controls-and-features">New Controls and Features</h3>
<h4 id="skiacamera">SkiaCamera</h4>
<ul>
<li><strong>Platform Support</strong>: iOS, MacCatalyst, Windows, Android implementations</li>
<li><strong>Package</strong>: Available in <code>DrawnUi.Maui.Camera</code></li>
<li><strong>Integration</strong>: Seamlessly integrates with DrawnUI canvas rendering</li>
</ul>
<h4 id="enhanced-skiashape">Enhanced SkiaShape</h4>
<ul>
<li><strong>Multiple Children</strong>: Now can contain many <code>Children</code> instead of one <code>Content</code></li>
<li><strong>Layout Types</strong>: Can change layout type with <code>Layout</code> property</li>
<li><strong>Pixel-Perfect Rendering</strong>: Fixes for stroke and other rendering improvements</li>
</ul>
<h3 id="performance-optimizations">Performance Optimizations</h3>
<h4 id="layout-system-improvements">Layout System Improvements</h4>
<ul>
<li><strong>Fill Behavior</strong>: Stack and absolute layouts now correctly apply one-directional <code>Fill</code> of children</li>
<li><strong>Margins &amp; Padding</strong>: Now work properly everywhere (might break some legacy UIs)</li>
<li><strong>Measurement Override</strong>: Can override virtual <code>OnMeasuring</code>, while <code>Measure</code> is not virtual anymore</li>
<li><strong>Faster Initialization</strong>: Assures faster screen creation and avoids re-measurements during first-time initialization</li>
</ul>
<h4 id="rendering-performance">Rendering Performance</h4>
<ul>
<li><strong>SkiaLabel Optimizations</strong>: Important performance optimizations and fixes</li>
<li><strong>Accelerated Handlers</strong>: Performance and safety optimizations for <code>SkiaViewAccelerated:SKGLView</code> on all platforms</li>
<li><strong>Windows Sync</strong>: Windows accelerated handler now synced with display when refresh rate is &gt;=120Hz</li>
<li><strong>ImageComposite</strong>: Cache now works inside another <code>ImageComposite</code></li>
</ul>
<h3 id="gaming-enhancements">Gaming Enhancements</h3>
<ul>
<li><strong>Frame Interpolator</strong>: Adjustments for DrawnUi.Maui.Game</li>
<li><strong>Display Sync</strong>: Better synchronization with high refresh rate displays</li>
<li><strong>Performance</strong>: Optimizations for smooth gaming experiences</li>
</ul>
<h3 id="developer-experience">Developer Experience</h3>
<h4 id="fluent-c-extensions">Fluent C# Extensions</h4>
<ul>
<li><strong>Code-Behind Support</strong>: Extensive fluent extensions for constructing UI without XAML</li>
<li><strong>Binding Mimics</strong>: Ability to mimic one-way and two-way bindings without MAUI bindings</li>
<li><strong>Examples</strong>: More examples and documentation coming</li>
</ul>
<h4 id="control-styling">Control Styling</h4>
<ul>
<li><strong>Default Looks</strong>: Selectable default look for controls: SkiaButton, SkiaSwitch, SkiaCheckbox</li>
<li><strong>Platform Styles</strong>: Example: <code>&lt;draw:SkiaButton ControlStyle=&quot;Cupertino&quot; Text=&quot;Button&quot; /&gt;</code></li>
<li><strong>Consistency</strong>: Check out Sandbox project for styling examples</li>
</ul>
<h4 id="gesture-improvements">Gesture Improvements</h4>
<ul>
<li><strong>Layout Events</strong>: SkiaLayout received gesture events (<code>ChildTapped</code>, <code>Tapped</code>) for easier use without subclassing</li>
<li><strong>Shape Events</strong>: SkiaShape now has gesture events too</li>
<li><strong>False-Tap Prevention</strong>: Gestures tuned to avoid false-taps when swiping</li>
</ul>
<h3 id="breaking-changes-and-migration">Breaking Changes and Migration</h3>
<h4 id="viewsadapter-renaming">ViewsAdapter Renaming</h4>
<ul>
<li><code>GetViewForIndex</code> → <code>GetExistingViewAtIndex</code></li>
<li><code>GetViewAtIndex</code> → <code>GetOrCreateViewForIndex</code></li>
</ul>
<h4 id="rendering-changes">Rendering Changes</h4>
<ul>
<li><strong>RenderingMode</strong>: <code>Canvas</code> property <code>RenderingMode</code> replaced <code>HardwareAcceleration</code></li>
<li><strong>Custom Handlers</strong>: Retained custom handlers for all platforms</li>
</ul>
<h4 id="layout-behavior">Layout Behavior</h4>
<ul>
<li><strong>Fill Behavior</strong>: Stack and absolute layouts now correctly apply one-directional <code>Fill</code></li>
<li><strong>Margins/Padding</strong>: Proper implementation might affect legacy UIs</li>
</ul>
<h2 id="version-history">Version History</h2>
<h3 id="version-13x-current---net-9">Version 1.3.x (Current - .NET 9)</h3>
<ul>
<li>Full .NET 9 support with SkiaSharp v3</li>
<li>All latest features and optimizations</li>
<li>Recommended for new projects</li>
</ul>
<h3 id="version-12x-legacy---net-8">Version 1.2.x (Legacy - .NET 8)</h3>
<ul>
<li>.NET 8 support with SkiaSharp v2</li>
<li>No longer updated</li>
<li>Use only for existing projects that cannot migrate</li>
</ul>
<h2 id="upcoming-features">Upcoming Features</h2>
<h3 id="planned-improvements">Planned Improvements</h3>
<ul>
<li><strong>Accessibility Support</strong>: Compatible and on the roadmap</li>
<li><strong>Masonry Layout</strong>: Todo item for SkiaLayout</li>
<li><strong>Additional Platform Support</strong>: Expanding beyond current platforms</li>
</ul>
<h3 id="community-contributions">Community Contributions</h3>
<ul>
<li><strong>Effects</strong>: More visual effects and animations</li>
<li><strong>Controls</strong>: Community-contributed custom controls</li>
<li><strong>Examples</strong>: More real-world usage examples</li>
</ul>
<h2 id="migration-guide">Migration Guide</h2>
<h3 id="from-legacy-versions-12x-to-13x">From Legacy Versions (1.2.x to 1.3.x)</h3>
<ol>
<li><strong>Update .NET Version</strong>: Migrate project to .NET 9</li>
<li><strong>Update Package</strong>: Change to <code>DrawnUi.Maui</code> package</li>
<li><strong>Update Code</strong>: Address any breaking changes listed above</li>
<li><strong>Test Layouts</strong>: Verify margin/padding behavior in your layouts</li>
<li><strong>Update Handlers</strong>: Use new <code>RenderingMode</code> instead of <code>HardwareAcceleration</code></li>
</ol>
<h3 id="from-other-ui-frameworks">From Other UI Frameworks</h3>
<ul>
<li><strong>XAML Compatibility</strong>: Most XAML patterns work with minimal changes</li>
<li><strong>Control Mapping</strong>: See <a href="controls/index.html">Controls documentation</a> for equivalent controls</li>
<li><strong>Performance</strong>: Review <a href="development-notes.md#performance-tips">caching strategies</a> for optimal performance</li>
</ul>
<h2 id="getting-help-with-updates">Getting Help with Updates</h2>
<ul>
<li><strong>Migration Issues</strong>: Check <a href="faq.html">FAQ</a> for common migration problems</li>
<li><strong>Breaking Changes</strong>: See <a href="development-notes.md">Development Notes</a> for detailed technical information</li>
<li><strong>Community Support</strong>: Ask questions in <a href="https://github.com/taublast/DrawnUi/discussions">GitHub Discussions</a></li>
<li><strong>Bug Reports</strong>: Report issues on <a href="https://github.com/taublast/DrawnUi.Maui/issues">GitHub Issues</a></li>
</ul>
<hr>
<p><strong>Stay Updated</strong>: Watch the <a href="https://github.com/taublast/DrawnUi.Maui">GitHub repository</a> for the latest releases and updates!</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/whats-new.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
