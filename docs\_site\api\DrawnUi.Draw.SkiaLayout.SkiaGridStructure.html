<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaLayout.SkiaGridStructure | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaLayout.SkiaGridStructure | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLayout_SkiaGridStructure.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLayout.SkiaGridStructure%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure">



  <h1 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure" class="text-break">
Class SkiaLayout.SkiaGridStructure  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L10"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SkiaLayout.SkiaGridStructure</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">SkiaLayout.SkiaGridStructure</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure__ctor_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.#ctor*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure__ctor_DrawnUi_Draw_ISkiaGridLayout_System_Double_System_Double_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.#ctor(DrawnUi.Draw.ISkiaGridLayout,System.Double,System.Double)">
  SkiaGridStructure(ISkiaGridLayout, double, double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L52"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaGridStructure(ISkiaGridLayout parentGrid, double widthConstraint, double heightConstraint)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parentGrid</code> <a class="xref" href="DrawnUi.Draw.ISkiaGridLayout.html">ISkiaGridLayout</a></dt>
    <dd></dd>
    <dt><code>widthConstraint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
    <dt><code>heightConstraint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_ColumnDefinitions" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.ColumnDefinitions">
  ColumnDefinitions
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L45"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly IReadOnlyList&lt;IGridColumnDefinition&gt; ColumnDefinitions</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.igridcolumndefinition">IGridColumnDefinition</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_ColumnSpacing" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.ColumnSpacing">
  ColumnSpacing
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L43"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly double ColumnSpacing</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_Padding" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.Padding">
  Padding
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L41"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly Thickness Padding</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_RowDefinitions" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.RowDefinitions">
  RowDefinitions
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L44"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly IReadOnlyList&lt;IGridRowDefinition&gt; RowDefinitions</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.igridrowdefinition">IGridRowDefinition</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_RowSpacing" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.RowSpacing">
  RowSpacing
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L42"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly double RowSpacing</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_Columns_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.Columns*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_Columns" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.Columns">
  Columns
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L36"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DefinitionInfo[] Columns { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Models.DefinitionInfo.html">DefinitionInfo</a>[]</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_HeightConstraint_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.HeightConstraint*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_HeightConstraint" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.HeightConstraint">
  HeightConstraint
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L12"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double HeightConstraint { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_Rows_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.Rows*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_Rows" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.Rows">
  Rows
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L34"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DefinitionInfo[] Rows { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Models.DefinitionInfo.html">DefinitionInfo</a>[]</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_WidthConstraint_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.WidthConstraint*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_WidthConstraint" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.WidthConstraint">
  WidthConstraint
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L13"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double WidthConstraint { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_DecompressStars_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.DecompressStars*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_DecompressStars_SkiaSharp_SKSize_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.DecompressStars(SkiaSharp.SKSize)">
  DecompressStars(SKSize)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L794"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DecompressStars(SKSize targetSize)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>targetSize</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_DecompressStarsInternal_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.DecompressStarsInternal*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_DecompressStarsInternal_Microsoft_Maui_Graphics_Size_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.DecompressStarsInternal(Microsoft.Maui.Graphics.Size)">
  DecompressStarsInternal(Size)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L765"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DecompressStarsInternal(Size targetSize)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>targetSize</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.size">Size</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_GetCellBoundsFor_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.GetCellBoundsFor*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_GetCellBoundsFor_DrawnUi_Draw_ISkiaControl_System_Double_System_Double_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.GetCellBoundsFor(DrawnUi.Draw.ISkiaControl,System.Double,System.Double)">
  GetCellBoundsFor(ISkiaControl, double, double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L261"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Rect GetCellBoundsFor(ISkiaControl view, double xOffset, double yOffset)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></dt>
    <dd></dd>
    <dt><code>xOffset</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
    <dt><code>yOffset</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect">Rect</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_GridHeight_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.GridHeight*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_GridHeight" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.GridHeight">
  GridHeight()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L298"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double GridHeight()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_GridWidth_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.GridWidth*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_GridWidth" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.GridWidth">
  GridWidth()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L303"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double GridWidth()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_LeftEdgeOfColumn_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.LeftEdgeOfColumn*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_LeftEdgeOfColumn_System_Int32_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.LeftEdgeOfColumn(System.Int32)">
  LeftEdgeOfColumn(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L542"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double LeftEdgeOfColumn(int column)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>column</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_MeasuredGridHeight_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.MeasuredGridHeight*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_MeasuredGridHeight" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.MeasuredGridHeight">
  MeasuredGridHeight()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L308"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double MeasuredGridHeight()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_MeasuredGridWidth_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.MeasuredGridWidth*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_MeasuredGridWidth" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.MeasuredGridWidth">
  MeasuredGridWidth()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L325"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double MeasuredGridWidth()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_TopEdgeOfRow_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.TopEdgeOfRow*"></a>

  <h3 id="DrawnUi_Draw_SkiaLayout_SkiaGridStructure_TopEdgeOfRow_System_Int32_" data-uid="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.TopEdgeOfRow(System.Int32)">
  TopEdgeOfRow(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L555"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double TopEdgeOfRow(int row)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>row</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Structure.cs/#L10" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
