<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Interface ISkiaDrawable | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Interface ISkiaDrawable | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaDrawable.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaDrawable%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.ISkiaDrawable">



  <h1 id="DrawnUi_Draw_ISkiaDrawable" data-uid="DrawnUi.Draw.ISkiaDrawable" class="text-break">
Interface ISkiaDrawable  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L3"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface ISkiaDrawable : ISkiaSharpView, IDisposable</code></pre>
  </div>







  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_Update_System_Int64_">ISkiaSharpView.Update(long)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_SignalFrame_System_Int64_">ISkiaSharpView.SignalFrame(long)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_CanvasSize">ISkiaSharpView.CanvasSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_CreateStandaloneSurface_System_Int32_System_Int32_">ISkiaSharpView.CreateStandaloneSurface(int, int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable.dispose">IDisposable.Dispose()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_ISkiaDrawable_FPS_" data-uid="DrawnUi.Draw.ISkiaDrawable.FPS*"></a>

  <h3 id="DrawnUi_Draw_ISkiaDrawable_FPS" data-uid="DrawnUi.Draw.ISkiaDrawable.FPS">
  FPS
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L14"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">double FPS { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaDrawable_FrameTime_" data-uid="DrawnUi.Draw.ISkiaDrawable.FrameTime*"></a>

  <h3 id="DrawnUi_Draw_ISkiaDrawable_FrameTime" data-uid="DrawnUi.Draw.ISkiaDrawable.FrameTime">
  FrameTime
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L20"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">long FrameTime { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaDrawable_HasDrawn_" data-uid="DrawnUi.Draw.ISkiaDrawable.HasDrawn*"></a>

  <h3 id="DrawnUi_Draw_ISkiaDrawable_HasDrawn" data-uid="DrawnUi.Draw.ISkiaDrawable.HasDrawn">
  HasDrawn
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L18"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool HasDrawn { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaDrawable_IsDrawing_" data-uid="DrawnUi.Draw.ISkiaDrawable.IsDrawing*"></a>

  <h3 id="DrawnUi_Draw_ISkiaDrawable_IsDrawing" data-uid="DrawnUi.Draw.ISkiaDrawable.IsDrawing">
  IsDrawing
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L16"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsDrawing { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaDrawable_IsHardwareAccelerated_" data-uid="DrawnUi.Draw.ISkiaDrawable.IsHardwareAccelerated*"></a>

  <h3 id="DrawnUi_Draw_ISkiaDrawable_IsHardwareAccelerated" data-uid="DrawnUi.Draw.ISkiaDrawable.IsHardwareAccelerated">
  IsHardwareAccelerated
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L12"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsHardwareAccelerated { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaDrawable_OnDraw_" data-uid="DrawnUi.Draw.ISkiaDrawable.OnDraw*"></a>

  <h3 id="DrawnUi_Draw_ISkiaDrawable_OnDraw" data-uid="DrawnUi.Draw.ISkiaDrawable.OnDraw">
  OnDraw
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Return true if need force invalidation on next frame</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Func&lt;SKSurface, SKRect, bool&gt; OnDraw { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-3">Func</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksurface">SKSurface</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaDrawable_Surface_" data-uid="DrawnUi.Draw.ISkiaDrawable.Surface*"></a>

  <h3 id="DrawnUi_Draw_ISkiaDrawable_Surface" data-uid="DrawnUi.Draw.ISkiaDrawable.Surface">
  Surface
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L10"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">SKSurface Surface { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksurface">SKSurface</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ISkiaDrawable_Uid_" data-uid="DrawnUi.Draw.ISkiaDrawable.Uid*"></a>

  <h3 id="DrawnUi_Draw_ISkiaDrawable_Uid" data-uid="DrawnUi.Draw.ISkiaDrawable.Uid">
  Uid
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L22"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Guid Uid { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.guid">Guid</a></dt>
    <dd></dd>
  </dl>









</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaDrawable.cs/#L3" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
