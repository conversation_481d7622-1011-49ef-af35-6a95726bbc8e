<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Enum SkiaImageEffect | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Enum SkiaImageEffect | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaImageEffect.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaImageEffect%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaImageEffect">




  <h1 id="DrawnUi_Draw_SkiaImageEffect" data-uid="DrawnUi.Draw.SkiaImageEffect" class="text-break">
Enum SkiaImageEffect  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffect.cs/#L3"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public enum SkiaImageEffect</code></pre>
  </div>








  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>

  <h2 id="fields">Fields
</h2>
  <dl class="parameters">
    <dt id="DrawnUi_Draw_SkiaImageEffect_BlackAndWhite"><code>BlackAndWhite = 1</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Brightness"><code>Brightness = 11</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Contrast"><code>Contrast = 9</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Custom"><code>Custom = 15</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Darken"><code>Darken = 4</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Gamma"><code>Gamma = 12</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Grayscale"><code>Grayscale = 6</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_HSL"><code>HSL = 14</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_InvertColors"><code>InvertColors = 8</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Lighten"><code>Lighten = 5</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_None"><code>None = 0</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Pastel"><code>Pastel = 2</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Saturation"><code>Saturation = 10</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Sepia"><code>Sepia = 7</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_TSL"><code>TSL = 13</code></dt>
  
  <dd></dd>
    <dt id="DrawnUi_Draw_SkiaImageEffect_Tint"><code>Tint = 3</code></dt>
  
  <dd><p>Background color will be used to tint</p>
</dd>
  </dl>



</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffect.cs/#L3" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
