import{a as Rr}from"./chunk-OSRY5VT3.min.js";var Vr=Rr((q,H)=>{(function(t,e){typeof define=="function"&&define.amd?define(e):typeof q=="object"?H.exports=e():e()(t.lunr)})(q,function(){return function(t){if(typeof t>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof t.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");t.tr=function(){this.pipeline.reset(),this.pipeline.add(t.tr.trimmer,t.tr.stopWord<PERSON>ilter,t.tr.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(t.tr.stemmer))},t.tr.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",t.tr.trimmer=t.trimmerSupport.generateTrimmer(t.tr.wordCharacters),t.Pipeline.registerFunction(t.tr.trimmer,"trimmer-tr"),t.tr.stemmer=function(){var e=t.stemmerSupport.Among,J=t.stemmerSupport.SnowballProgram,m=new function(){var p=[new e("m",-1,-1),new e("n",-1,-1),new e("miz",-1,-1),new e("niz",-1,-1),new e("muz",-1,-1),new e("nuz",-1,-1),new e("m\xFCz",-1,-1),new e("n\xFCz",-1,-1),new e("m\u0131z",-1,-1),new e("n\u0131z",-1,-1)],K=[new e("leri",-1,-1),new e("lar\u0131",-1,-1)],M=[new e("ni",-1,-1),new e("nu",-1,-1),new e("n\xFC",-1,-1),new e("n\u0131",-1,-1)],N=[new e("in",-1,-1),new e("un",-1,-1),new e("\xFCn",-1,-1),new e("\u0131n",-1,-1)],O=[new e("a",-1,-1),new e("e",-1,-1)],Q=[new e("na",-1,-1),new e("ne",-1,-1)],R=[new e("da",-1,-1),new e("ta",-1,-1),new e("de",-1,-1),new e("te",-1,-1)],V=[new e("nda",-1,-1),new e("nde",-1,-1)],X=[new e("dan",-1,-1),new e("tan",-1,-1),new e("den",-1,-1),new e("ten",-1,-1)],Y=[new e("ndan",-1,-1),new e("nden",-1,-1)],$=[new e("la",-1,-1),new e("le",-1,-1)],rr=[new e("ca",-1,-1),new e("ce",-1,-1)],er=[new e("im",-1,-1),new e("um",-1,-1),new e("\xFCm",-1,-1),new e("\u0131m",-1,-1)],ir=[new e("sin",-1,-1),new e("sun",-1,-1),new e("s\xFCn",-1,-1),new e("s\u0131n",-1,-1)],nr=[new e("iz",-1,-1),new e("uz",-1,-1),new e("\xFCz",-1,-1),new e("\u0131z",-1,-1)],ur=[new e("siniz",-1,-1),new e("sunuz",-1,-1),new e("s\xFCn\xFCz",-1,-1),new e("s\u0131n\u0131z",-1,-1)],tr=[new e("lar",-1,-1),new e("ler",-1,-1)],sr=[new e("niz",-1,-1),new e("nuz",-1,-1),new e("n\xFCz",-1,-1),new e("n\u0131z",-1,-1)],or=[new e("dir",-1,-1),new e("tir",-1,-1),new e("dur",-1,-1),new e("tur",-1,-1),new e("d\xFCr",-1,-1),new e("t\xFCr",-1,-1),new e("d\u0131r",-1,-1),new e("t\u0131r",-1,-1)],cr=[new e("cas\u0131na",-1,-1),new e("cesine",-1,-1)],ar=[new e("di",-1,-1),new e("ti",-1,-1),new e("dik",-1,-1),new e("tik",-1,-1),new e("duk",-1,-1),new e("tuk",-1,-1),new e("d\xFCk",-1,-1),new e("t\xFCk",-1,-1),new e("d\u0131k",-1,-1),new e("t\u0131k",-1,-1),new e("dim",-1,-1),new e("tim",-1,-1),new e("dum",-1,-1),new e("tum",-1,-1),new e("d\xFCm",-1,-1),new e("t\xFCm",-1,-1),new e("d\u0131m",-1,-1),new e("t\u0131m",-1,-1),new e("din",-1,-1),new e("tin",-1,-1),new e("dun",-1,-1),new e("tun",-1,-1),new e("d\xFCn",-1,-1),new e("t\xFCn",-1,-1),new e("d\u0131n",-1,-1),new e("t\u0131n",-1,-1),new e("du",-1,-1),new e("tu",-1,-1),new e("d\xFC",-1,-1),new e("t\xFC",-1,-1),new e("d\u0131",-1,-1),new e("t\u0131",-1,-1)],lr=[new e("sa",-1,-1),new e("se",-1,-1),new e("sak",-1,-1),new e("sek",-1,-1),new e("sam",-1,-1),new e("sem",-1,-1),new e("san",-1,-1),new e("sen",-1,-1)],_r=[new e("mi\u015F",-1,-1),new e("mu\u015F",-1,-1),new e("m\xFC\u015F",-1,-1),new e("m\u0131\u015F",-1,-1)],fr=[new e("b",-1,1),new e("c",-1,2),new e("d",-1,3),new e("\u011F",-1,4)],b=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,32,8,0,0,0,0,0,0,1],v=[1,16,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8,0,0,0,0,0,0,1],mr=[1,64,16,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],dr=[17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,130],br=[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],wr=[17],x=[65],P=[65],y,E,S=[["a",mr,97,305],["e",dr,101,252],["\u0131",br,97,305],["i",wr,101,105],["o",x,111,117],["\xF6",P,246,252],["u",x,111,117]],r=new J;this.setCurrent=function(i){r.setCurrent(i)},this.getCurrent=function(){return r.getCurrent()};function h(i,n,u){for(;;){var a=r.limit-r.cursor;if(r.in_grouping_b(i,n,u)){r.cursor=r.limit-a;break}if(r.cursor=r.limit-a,r.cursor<=r.limit_backward)return!1;r.cursor--}return!0}function o(){var i,n;i=r.limit-r.cursor,h(b,97,305);for(var u=0;u<S.length;u++){n=r.limit-r.cursor;var a=S[u];if(r.eq_s_b(1,a[0])&&h(a[1],a[2],a[3]))return r.cursor=r.limit-i,!0;r.cursor=r.limit-n}return r.cursor=r.limit-n,!r.eq_s_b(1,"\xFC")||!h(P,246,252)?!1:(r.cursor=r.limit-i,!0)}function W(i,n){var u=r.limit-r.cursor,a;return i()&&(r.cursor=r.limit-u,r.cursor>r.limit_backward&&(r.cursor--,a=r.limit-r.cursor,n()))?(r.cursor=r.limit-a,!0):(r.cursor=r.limit-u,i()?(r.cursor=r.limit-u,!1):(r.cursor=r.limit-u,r.cursor<=r.limit_backward||(r.cursor--,!n())?!1:(r.cursor=r.limit-u,!0)))}function F(i){return W(i,function(){return r.in_grouping_b(b,97,305)})}function L(){return F(function(){return r.eq_s_b(1,"n")})}function kr(){return F(function(){return r.eq_s_b(1,"s")})}function l(){return F(function(){return r.eq_s_b(1,"y")})}function gr(){return W(function(){return r.in_grouping_b(v,105,305)},function(){return r.out_grouping_b(b,97,305)})}function _(){return r.find_among_b(p,10)&&gr()}function f(){return o()&&r.in_grouping_b(v,105,305)&&kr()}function d(){return r.find_among_b(K,2)}function pr(){return o()&&r.in_grouping_b(v,105,305)&&l()}function vr(){return o()&&r.find_among_b(M,4)}function I(){return o()&&r.find_among_b(N,4)&&L()}function yr(){return o()&&r.find_among_b(O,2)&&l()}function hr(){return o()&&r.find_among_b(Q,2)}function T(){return o()&&r.find_among_b(R,4)}function j(){return o()&&r.find_among_b(V,2)}function Fr(){return o()&&r.find_among_b(X,4)}function zr(){return o()&&r.find_among_b(Y,2)}function Cr(){return o()&&r.find_among_b($,2)&&l()}function Ar(){return r.eq_s_b(2,"ki")}function Dr(){return o()&&r.find_among_b(rr,2)&&L()}function z(){return o()&&r.find_among_b(er,4)&&l()}function C(){return o()&&r.find_among_b(ir,4)}function A(){return o()&&r.find_among_b(nr,4)&&l()}function D(){return r.find_among_b(ur,4)}function c(){return o()&&r.find_among_b(tr,2)}function Ur(){return o()&&r.find_among_b(sr,4)}function Z(){return o()&&r.find_among_b(or,8)}function Br(){return r.find_among_b(cr,2)}function U(){return o()&&r.find_among_b(ar,32)&&l()}function B(){return r.find_among_b(lr,8)&&l()}function w(){return o()&&r.find_among_b(_r,4)&&l()}function qr(){return r.eq_s_b(3,"ken")&&l()}function xr(){var i=r.limit-r.cursor;return!w()&&(r.cursor=r.limit-i,!U()&&(r.cursor=r.limit-i,!B()&&(r.cursor=r.limit-i,!qr())))}function Pr(){if(Br()){var i=r.limit-r.cursor;if(D()||(r.cursor=r.limit-i,c()||(r.cursor=r.limit-i,z()||(r.cursor=r.limit-i,C()||(r.cursor=r.limit-i,A()||(r.cursor=r.limit-i))))),w())return!1}return!0}function Er(){if(c()){r.bra=r.cursor,r.slice_del();var i=r.limit-r.cursor;return r.ket=r.cursor,Z()||(r.cursor=r.limit-i,U()||(r.cursor=r.limit-i,B()||(r.cursor=r.limit-i,w()||(r.cursor=r.limit-i)))),y=!1,!1}return!0}function Sr(){if(!Ur())return!0;var i=r.limit-r.cursor;return!U()&&(r.cursor=r.limit-i,!B())}function Wr(){var i=r.limit-r.cursor,n;return!D()&&(r.cursor=r.limit-i,!A()&&(r.cursor=r.limit-i,!C()&&(r.cursor=r.limit-i,!z())))?!0:(r.bra=r.cursor,r.slice_del(),n=r.limit-r.cursor,r.ket=r.cursor,w()||(r.cursor=r.limit-n),!1)}function Lr(){var i=r.limit-r.cursor,n;if(r.ket=r.cursor,y=!0,xr()&&(r.cursor=r.limit-i,Pr()&&(r.cursor=r.limit-i,Er()&&(r.cursor=r.limit-i,Sr()&&(r.cursor=r.limit-i,Wr()))))){if(r.cursor=r.limit-i,!Z())return;r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,n=r.limit-r.cursor,D()||(r.cursor=r.limit-n,c()||(r.cursor=r.limit-n,z()||(r.cursor=r.limit-n,C()||(r.cursor=r.limit-n,A()||(r.cursor=r.limit-n))))),w()||(r.cursor=r.limit-n)}r.bra=r.cursor,r.slice_del()}function s(){var i,n,u,a;if(r.ket=r.cursor,Ar()){if(i=r.limit-r.cursor,T())return r.bra=r.cursor,r.slice_del(),n=r.limit-r.cursor,r.ket=r.cursor,c()?(r.bra=r.cursor,r.slice_del(),s()):(r.cursor=r.limit-n,_()&&(r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s()))),!0;if(r.cursor=r.limit-i,I()){if(r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,u=r.limit-r.cursor,d())r.bra=r.cursor,r.slice_del();else{if(r.cursor=r.limit-u,r.ket=r.cursor,!_()&&(r.cursor=r.limit-u,!f()&&(r.cursor=r.limit-u,!s())))return!0;r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s())}return!0}if(r.cursor=r.limit-i,j()){if(a=r.limit-r.cursor,d())r.bra=r.cursor,r.slice_del();else if(r.cursor=r.limit-a,f())r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s());else if(r.cursor=r.limit-a,!s())return!1;return!0}}return!1}function Ir(i){if(r.ket=r.cursor,!j()&&(r.cursor=r.limit-i,!hr()))return!1;var n=r.limit-r.cursor;if(d())r.bra=r.cursor,r.slice_del();else if(r.cursor=r.limit-n,f())r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s());else if(r.cursor=r.limit-n,!s())return!1;return!0}function Tr(i){if(r.ket=r.cursor,!zr()&&(r.cursor=r.limit-i,!vr()))return!1;var n=r.limit-r.cursor;return!f()&&(r.cursor=r.limit-n,!d())?!1:(r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s()),!0)}function jr(){var i=r.limit-r.cursor,n;return r.ket=r.cursor,!I()&&(r.cursor=r.limit-i,!Cr())?!1:(r.bra=r.cursor,r.slice_del(),n=r.limit-r.cursor,r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s())||(r.cursor=r.limit-n,r.ket=r.cursor,!_()&&(r.cursor=r.limit-n,!f()&&(r.cursor=r.limit-n,!s())))||(r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s())),!0)}function Zr(){var i=r.limit-r.cursor,n,u;if(r.ket=r.cursor,!T()&&(r.cursor=r.limit-i,!pr()&&(r.cursor=r.limit-i,!yr())))return!1;if(r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,n=r.limit-r.cursor,_())r.bra=r.cursor,r.slice_del(),u=r.limit-r.cursor,r.ket=r.cursor,c()||(r.cursor=r.limit-u);else if(r.cursor=r.limit-n,!c())return!0;return r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,s(),!0}function Gr(){var i=r.limit-r.cursor,n,u;if(r.ket=r.cursor,c()){r.bra=r.cursor,r.slice_del(),s();return}if(r.cursor=r.limit-i,r.ket=r.cursor,Dr()){if(r.bra=r.cursor,r.slice_del(),n=r.limit-r.cursor,r.ket=r.cursor,d())r.bra=r.cursor,r.slice_del();else{if(r.cursor=r.limit-n,r.ket=r.cursor,!_()&&(r.cursor=r.limit-n,!f()&&(r.cursor=r.limit-n,r.ket=r.cursor,!c()||(r.bra=r.cursor,r.slice_del(),!s()))))return;r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s())}return}if(r.cursor=r.limit-i,!Ir(i)&&(r.cursor=r.limit-i,!Tr(i))){if(r.cursor=r.limit-i,r.ket=r.cursor,Fr()){r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,u=r.limit-r.cursor,_()?(r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s())):(r.cursor=r.limit-u,c()?(r.bra=r.cursor,r.slice_del(),s()):(r.cursor=r.limit-u,s()));return}if(r.cursor=r.limit-i,!jr()){if(r.cursor=r.limit-i,d()){r.bra=r.cursor,r.slice_del();return}r.cursor=r.limit-i,!s()&&(r.cursor=r.limit-i,!Zr()&&(r.cursor=r.limit-i,r.ket=r.cursor,!(!_()&&(r.cursor=r.limit-i,!f()))&&(r.bra=r.cursor,r.slice_del(),r.ket=r.cursor,c()&&(r.bra=r.cursor,r.slice_del(),s()))))}}}function Hr(){var i;if(r.ket=r.cursor,i=r.find_among_b(fr,4),i)switch(r.bra=r.cursor,i){case 1:r.slice_from("p");break;case 2:r.slice_from("\xE7");break;case 3:r.slice_from("t");break;case 4:r.slice_from("k");break}}function Jr(){for(;;){var i=r.limit-r.cursor;if(r.in_grouping_b(b,97,305)){r.cursor=r.limit-i;break}if(r.cursor=r.limit-i,r.cursor<=r.limit_backward)return!1;r.cursor--}return!0}function g(i,n,u){if(r.cursor=r.limit-i,Jr()){var a=r.limit-r.cursor;if(!r.eq_s_b(1,n)&&(r.cursor=r.limit-a,!r.eq_s_b(1,u)))return!0;r.cursor=r.limit-i;var Qr=r.cursor;return r.insert(r.cursor,r.cursor,u),r.cursor=Qr,!1}return!0}function Kr(){var i=r.limit-r.cursor;!r.eq_s_b(1,"d")&&(r.cursor=r.limit-i,!r.eq_s_b(1,"g"))||g(i,"a","\u0131")&&g(i,"e","i")&&g(i,"o","u")&&g(i,"\xF6","\xFC")}function Mr(){for(var i=r.cursor,n=2,u;;){for(u=r.cursor;!r.in_grouping(b,97,305);){if(r.cursor>=r.limit)return r.cursor=u,n>0?!1:(r.cursor=i,!0);r.cursor++}n--}}function G(i,n,u){for(;!r.eq_s(n,u);){if(r.cursor>=r.limit)return!0;r.cursor++}return E=n,E!=r.limit?!0:(r.cursor=i,!1)}function Nr(){var i=r.cursor;return!(G(i,2,"ad")&&(r.cursor=i,G(i,5,"soyad")))}function Or(){var i=r.cursor;return Nr()?!1:(r.limit_backward=i,r.cursor=r.limit,Kr(),r.cursor=r.limit,Hr(),!0)}this.stem=function(){return!!(Mr()&&(r.limit_backward=r.cursor,r.cursor=r.limit,Lr(),r.cursor=r.limit,y&&(Gr(),r.cursor=r.limit_backward,Or())))}};return function(k){return typeof k.update=="function"?k.update(function(p){return m.setCurrent(p),m.stem(),m.getCurrent()}):(m.setCurrent(k),m.stem(),m.getCurrent())}}(),t.Pipeline.registerFunction(t.tr.stemmer,"stemmer-tr"),t.tr.stopWordFilter=t.generateStopWordFilter("acaba altm\u0131\u015F alt\u0131 ama ancak arada asl\u0131nda ayr\u0131ca bana baz\u0131 belki ben benden beni benim beri be\u015F bile bin bir biri birka\xE7 birkez bir\xE7ok bir\u015Fey bir\u015Feyi biz bizden bize bizi bizim bu buna bunda bundan bunlar bunlar\u0131 bunlar\u0131n bunu bunun burada b\xF6yle b\xF6ylece da daha dahi de defa de\u011Fil diye di\u011Fer doksan dokuz dolay\u0131 dolay\u0131s\u0131yla d\xF6rt edecek eden ederek edilecek ediliyor edilmesi ediyor elli en etmesi etti etti\u011Fi etti\u011Fini e\u011Fer gibi g\xF6re halen hangi hatta hem hen\xFCz hep hepsi her herhangi herkesin hi\xE7 hi\xE7bir iki ile ilgili ise itibaren itibariyle i\xE7in i\u015Fte kadar kar\u015F\u0131n katrilyon kendi kendilerine kendini kendisi kendisine kendisini kez ki kim kimden kime kimi kimse k\u0131rk milyar milyon mu m\xFC m\u0131 nas\u0131l ne neden nedenle nerde nerede nereye niye ni\xE7in o olan olarak oldu olduklar\u0131n\u0131 oldu\u011Fu oldu\u011Funu olmad\u0131 olmad\u0131\u011F\u0131 olmak olmas\u0131 olmayan olmaz olsa olsun olup olur olursa oluyor on ona ondan onlar onlardan onlar\u0131 onlar\u0131n onu onun otuz oysa pek ra\u011Fmen sadece sanki sekiz seksen sen senden seni senin siz sizden sizi sizin taraf\u0131ndan trilyon t\xFCm var vard\u0131 ve veya ya yani yapacak yapmak yapt\u0131 yapt\u0131klar\u0131 yapt\u0131\u011F\u0131 yapt\u0131\u011F\u0131n\u0131 yap\u0131lan yap\u0131lmas\u0131 yap\u0131yor yedi yerine yetmi\u015F yine yirmi yoksa y\xFCz zaten \xE7ok \xE7\xFCnk\xFC \xF6yle \xFCzere \xFC\xE7 \u015Fey \u015Feyden \u015Feyi \u015Feyler \u015Fu \u015Funa \u015Funda \u015Fundan \u015Funlar\u0131 \u015Funu \u015F\xF6yle".split(" ")),t.Pipeline.registerFunction(t.tr.stopWordFilter,"stopWordFilter-tr")}})});export default Vr();
/*! Bundled license information:

lunr-languages/lunr.tr.js:
  (*!
   * Lunr languages, `Turkish` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.tr-WXUV733C.min.js.map
