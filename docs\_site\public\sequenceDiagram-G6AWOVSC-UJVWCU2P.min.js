import{a as ye}from"./chunk-I4ZXTPQC.min.js";import{a as fe,b as xe,d as Te,e as be,f as It,g as Rt}from"./chunk-C7DS3QYJ.min.js";import{a as ge,k as Kt,m as Y}from"./chunk-PYPO7LRM.min.js";import{a as Xe}from"./chunk-CM5D5KZN.min.js";import{D as Mt,H as wt,K as nt,L as yt,M as zt,N as k,O as oe,S as ce,T as Ht,U as le,V as de,W as he,X as pe,Y as ue,Z as et,h as d,ia as kt,j as X,s as ne}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import{d as Ge}from"./chunk-OSRY5VT3.min.js";var Qt=Ge(Xe(),1),Ut=function(){var t=d(function(pt,w,L,P){for(L=L||{},P=pt.length;P--;L[pt[P]]=w);return L},"o"),e=[1,2],c=[1,3],r=[1,4],s=[2,4],i=[1,9],o=[1,11],h=[1,13],p=[1,14],a=[1,16],f=[1,17],y=[1,18],g=[1,24],T=[1,25],m=[1,26],I=[1,27],A=[1,28],V=[1,29],M=[1,30],F=[1,31],C=[1,32],z=[1,33],H=[1,34],Z=[1,35],rt=[1,36],K=[1,37],U=[1,38],q=[1,39],D=[1,41],Q=[1,42],J=[1,43],j=[1,44],at=[1,45],S=[1,46],b=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],_=[4,5,16,50,52,53],$=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],it=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],N=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],te=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],ct=[68,69,70],dt=[1,122],Bt={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:d(function(w,L,P,E,R,l,vt){var u=l.length-1;switch(R){case 3:return E.apply(l[u]),l[u];break;case 4:case 9:this.$=[];break;case 5:case 10:l[u-1].push(l[u]),this.$=l[u-1];break;case 6:case 7:case 11:case 12:this.$=l[u];break;case 8:case 13:this.$=[];break;case 15:l[u].type="createParticipant",this.$=l[u];break;case 16:l[u-1].unshift({type:"boxStart",boxData:E.parseBoxData(l[u-2])}),l[u-1].push({type:"boxEnd",boxText:l[u-2]}),this.$=l[u-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(l[u-2]),sequenceIndexStep:Number(l[u-1]),sequenceVisible:!0,signalType:E.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(l[u-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:E.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:E.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:E.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:E.LINETYPE.ACTIVE_START,actor:l[u-1].actor};break;case 23:this.$={type:"activeEnd",signalType:E.LINETYPE.ACTIVE_END,actor:l[u-1].actor};break;case 29:E.setDiagramTitle(l[u].substring(6)),this.$=l[u].substring(6);break;case 30:E.setDiagramTitle(l[u].substring(7)),this.$=l[u].substring(7);break;case 31:this.$=l[u].trim(),E.setAccTitle(this.$);break;case 32:case 33:this.$=l[u].trim(),E.setAccDescription(this.$);break;case 34:l[u-1].unshift({type:"loopStart",loopText:E.parseMessage(l[u-2]),signalType:E.LINETYPE.LOOP_START}),l[u-1].push({type:"loopEnd",loopText:l[u-2],signalType:E.LINETYPE.LOOP_END}),this.$=l[u-1];break;case 35:l[u-1].unshift({type:"rectStart",color:E.parseMessage(l[u-2]),signalType:E.LINETYPE.RECT_START}),l[u-1].push({type:"rectEnd",color:E.parseMessage(l[u-2]),signalType:E.LINETYPE.RECT_END}),this.$=l[u-1];break;case 36:l[u-1].unshift({type:"optStart",optText:E.parseMessage(l[u-2]),signalType:E.LINETYPE.OPT_START}),l[u-1].push({type:"optEnd",optText:E.parseMessage(l[u-2]),signalType:E.LINETYPE.OPT_END}),this.$=l[u-1];break;case 37:l[u-1].unshift({type:"altStart",altText:E.parseMessage(l[u-2]),signalType:E.LINETYPE.ALT_START}),l[u-1].push({type:"altEnd",signalType:E.LINETYPE.ALT_END}),this.$=l[u-1];break;case 38:l[u-1].unshift({type:"parStart",parText:E.parseMessage(l[u-2]),signalType:E.LINETYPE.PAR_START}),l[u-1].push({type:"parEnd",signalType:E.LINETYPE.PAR_END}),this.$=l[u-1];break;case 39:l[u-1].unshift({type:"parStart",parText:E.parseMessage(l[u-2]),signalType:E.LINETYPE.PAR_OVER_START}),l[u-1].push({type:"parEnd",signalType:E.LINETYPE.PAR_END}),this.$=l[u-1];break;case 40:l[u-1].unshift({type:"criticalStart",criticalText:E.parseMessage(l[u-2]),signalType:E.LINETYPE.CRITICAL_START}),l[u-1].push({type:"criticalEnd",signalType:E.LINETYPE.CRITICAL_END}),this.$=l[u-1];break;case 41:l[u-1].unshift({type:"breakStart",breakText:E.parseMessage(l[u-2]),signalType:E.LINETYPE.BREAK_START}),l[u-1].push({type:"breakEnd",optText:E.parseMessage(l[u-2]),signalType:E.LINETYPE.BREAK_END}),this.$=l[u-1];break;case 43:this.$=l[u-3].concat([{type:"option",optionText:E.parseMessage(l[u-1]),signalType:E.LINETYPE.CRITICAL_OPTION},l[u]]);break;case 45:this.$=l[u-3].concat([{type:"and",parText:E.parseMessage(l[u-1]),signalType:E.LINETYPE.PAR_AND},l[u]]);break;case 47:this.$=l[u-3].concat([{type:"else",altText:E.parseMessage(l[u-1]),signalType:E.LINETYPE.ALT_ELSE},l[u]]);break;case 48:l[u-3].draw="participant",l[u-3].type="addParticipant",l[u-3].description=E.parseMessage(l[u-1]),this.$=l[u-3];break;case 49:l[u-1].draw="participant",l[u-1].type="addParticipant",this.$=l[u-1];break;case 50:l[u-3].draw="actor",l[u-3].type="addParticipant",l[u-3].description=E.parseMessage(l[u-1]),this.$=l[u-3];break;case 51:l[u-1].draw="actor",l[u-1].type="addParticipant",this.$=l[u-1];break;case 52:l[u-1].type="destroyParticipant",this.$=l[u-1];break;case 53:this.$=[l[u-1],{type:"addNote",placement:l[u-2],actor:l[u-1].actor,text:l[u]}];break;case 54:l[u-2]=[].concat(l[u-1],l[u-1]).slice(0,2),l[u-2][0]=l[u-2][0].actor,l[u-2][1]=l[u-2][1].actor,this.$=[l[u-1],{type:"addNote",placement:E.PLACEMENT.OVER,actor:l[u-2].slice(0,2),text:l[u]}];break;case 55:this.$=[l[u-1],{type:"addLinks",actor:l[u-1].actor,text:l[u]}];break;case 56:this.$=[l[u-1],{type:"addALink",actor:l[u-1].actor,text:l[u]}];break;case 57:this.$=[l[u-1],{type:"addProperties",actor:l[u-1].actor,text:l[u]}];break;case 58:this.$=[l[u-1],{type:"addDetails",actor:l[u-1].actor,text:l[u]}];break;case 61:this.$=[l[u-2],l[u]];break;case 62:this.$=l[u];break;case 63:this.$=E.PLACEMENT.LEFTOF;break;case 64:this.$=E.PLACEMENT.RIGHTOF;break;case 65:this.$=[l[u-4],l[u-1],{type:"addMessage",from:l[u-4].actor,to:l[u-1].actor,signalType:l[u-3],msg:l[u],activate:!0},{type:"activeStart",signalType:E.LINETYPE.ACTIVE_START,actor:l[u-1].actor}];break;case 66:this.$=[l[u-4],l[u-1],{type:"addMessage",from:l[u-4].actor,to:l[u-1].actor,signalType:l[u-3],msg:l[u]},{type:"activeEnd",signalType:E.LINETYPE.ACTIVE_END,actor:l[u-4].actor}];break;case 67:this.$=[l[u-3],l[u-1],{type:"addMessage",from:l[u-3].actor,to:l[u-1].actor,signalType:l[u-2],msg:l[u]}];break;case 68:this.$={type:"addParticipant",actor:l[u]};break;case 69:this.$=E.LINETYPE.SOLID_OPEN;break;case 70:this.$=E.LINETYPE.DOTTED_OPEN;break;case 71:this.$=E.LINETYPE.SOLID;break;case 72:this.$=E.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=E.LINETYPE.DOTTED;break;case 74:this.$=E.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=E.LINETYPE.SOLID_CROSS;break;case 76:this.$=E.LINETYPE.DOTTED_CROSS;break;case 77:this.$=E.LINETYPE.SOLID_POINT;break;case 78:this.$=E.LINETYPE.DOTTED_POINT;break;case 79:this.$=E.parseMessage(l[u].trim().substring(1));break}},"anonymous"),table:[{3:1,4:e,5:c,6:r},{1:[3]},{3:5,4:e,5:c,6:r},{3:6,4:e,5:c,6:r},t([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],s,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:i,5:o,8:8,9:10,12:12,13:h,14:p,17:15,18:a,21:f,22:40,23:y,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:V,37:M,38:F,39:C,41:z,43:H,44:Z,46:rt,50:K,52:U,53:q,54:D,59:Q,60:J,61:j,62:at,70:S},t(b,[2,5]),{9:47,12:12,13:h,14:p,17:15,18:a,21:f,22:40,23:y,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:V,37:M,38:F,39:C,41:z,43:H,44:Z,46:rt,50:K,52:U,53:q,54:D,59:Q,60:J,61:j,62:at,70:S},t(b,[2,7]),t(b,[2,8]),t(b,[2,14]),{12:48,50:K,52:U,53:q},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:S},{22:55,70:S},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},t(b,[2,29]),t(b,[2,30]),{32:[1,61]},{34:[1,62]},t(b,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:S},{22:72,70:S},{22:73,70:S},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:S},{22:90,70:S},{22:91,70:S},{22:92,70:S},t([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),t(b,[2,6]),t(b,[2,15]),t(_,[2,9],{10:93}),t(b,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},t(b,[2,21]),{5:[1,97]},{5:[1,98]},t(b,[2,24]),t(b,[2,25]),t(b,[2,26]),t(b,[2,27]),t(b,[2,28]),t(b,[2,31]),t(b,[2,32]),t($,s,{7:99}),t($,s,{7:100}),t($,s,{7:101}),t(it,s,{40:102,7:103}),t(N,s,{42:104,7:105}),t(N,s,{7:105,42:106}),t(te,s,{45:107,7:108}),t($,s,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:S},t(ct,[2,69]),t(ct,[2,70]),t(ct,[2,71]),t(ct,[2,72]),t(ct,[2,73]),t(ct,[2,74]),t(ct,[2,75]),t(ct,[2,76]),t(ct,[2,77]),t(ct,[2,78]),{22:118,70:S},{22:120,58:119,70:S},{70:[2,63]},{70:[2,64]},{56:121,81:dt},{56:123,81:dt},{56:124,81:dt},{56:125,81:dt},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:K,52:U,53:q},{5:[1,131]},t(b,[2,19]),t(b,[2,20]),t(b,[2,22]),t(b,[2,23]),{4:i,5:o,8:8,9:10,12:12,13:h,14:p,16:[1,132],17:15,18:a,21:f,22:40,23:y,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:V,37:M,38:F,39:C,41:z,43:H,44:Z,46:rt,50:K,52:U,53:q,54:D,59:Q,60:J,61:j,62:at,70:S},{4:i,5:o,8:8,9:10,12:12,13:h,14:p,16:[1,133],17:15,18:a,21:f,22:40,23:y,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:V,37:M,38:F,39:C,41:z,43:H,44:Z,46:rt,50:K,52:U,53:q,54:D,59:Q,60:J,61:j,62:at,70:S},{4:i,5:o,8:8,9:10,12:12,13:h,14:p,16:[1,134],17:15,18:a,21:f,22:40,23:y,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:V,37:M,38:F,39:C,41:z,43:H,44:Z,46:rt,50:K,52:U,53:q,54:D,59:Q,60:J,61:j,62:at,70:S},{16:[1,135]},{4:i,5:o,8:8,9:10,12:12,13:h,14:p,16:[2,46],17:15,18:a,21:f,22:40,23:y,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:V,37:M,38:F,39:C,41:z,43:H,44:Z,46:rt,49:[1,136],50:K,52:U,53:q,54:D,59:Q,60:J,61:j,62:at,70:S},{16:[1,137]},{4:i,5:o,8:8,9:10,12:12,13:h,14:p,16:[2,44],17:15,18:a,21:f,22:40,23:y,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:V,37:M,38:F,39:C,41:z,43:H,44:Z,46:rt,48:[1,138],50:K,52:U,53:q,54:D,59:Q,60:J,61:j,62:at,70:S},{16:[1,139]},{16:[1,140]},{4:i,5:o,8:8,9:10,12:12,13:h,14:p,16:[2,42],17:15,18:a,21:f,22:40,23:y,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:V,37:M,38:F,39:C,41:z,43:H,44:Z,46:rt,47:[1,141],50:K,52:U,53:q,54:D,59:Q,60:J,61:j,62:at,70:S},{4:i,5:o,8:8,9:10,12:12,13:h,14:p,16:[1,142],17:15,18:a,21:f,22:40,23:y,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:V,37:M,38:F,39:C,41:z,43:H,44:Z,46:rt,50:K,52:U,53:q,54:D,59:Q,60:J,61:j,62:at,70:S},{15:[1,143]},t(b,[2,49]),{15:[1,144]},t(b,[2,51]),t(b,[2,52]),{22:145,70:S},{22:146,70:S},{56:147,81:dt},{56:148,81:dt},{56:149,81:dt},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},t(b,[2,16]),t(_,[2,10]),{12:151,50:K,52:U,53:q},t(_,[2,12]),t(_,[2,13]),t(b,[2,18]),t(b,[2,34]),t(b,[2,35]),t(b,[2,36]),t(b,[2,37]),{15:[1,152]},t(b,[2,38]),{15:[1,153]},t(b,[2,39]),t(b,[2,40]),{15:[1,154]},t(b,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:dt},{56:158,81:dt},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:S},t(_,[2,11]),t(it,s,{7:103,40:160}),t(N,s,{7:105,42:161}),t(te,s,{7:108,45:162}),t(b,[2,48]),t(b,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:d(function(w,L){if(L.recoverable)this.trace(w);else{var P=new Error(w);throw P.hash=L,P}},"parseError"),parse:d(function(w){var L=this,P=[0],E=[],R=[null],l=[],vt=this.table,u="",At=0,ee=0,re=0,ze=2,ae=1,He=l.slice.call(arguments,1),W=Object.create(this.lexer),ut={yy:{}};for(var Vt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Vt)&&(ut.yy[Vt]=this.yy[Vt]);W.setInput(w,ut.yy),ut.yy.lexer=W,ut.yy.parser=this,typeof W.yylloc>"u"&&(W.yylloc={});var Yt=W.yylloc;l.push(Yt);var Ke=W.options&&W.options.ranges;typeof ut.yy.parseError=="function"?this.parseError=ut.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Ue(tt){P.length=P.length-2*tt,R.length=R.length-tt,l.length=l.length-tt}d(Ue,"popStack");function se(){var tt;return tt=E.pop()||W.lex()||ae,typeof tt!="number"&&(tt instanceof Array&&(E=tt,tt=E.pop()),tt=L.symbols_[tt]||tt),tt}d(se,"lex");for(var G,Ft,gt,st,X0,Wt,bt={},Nt,ht,ie,St;;){if(gt=P[P.length-1],this.defaultActions[gt]?st=this.defaultActions[gt]:((G===null||typeof G>"u")&&(G=se()),st=vt[gt]&&vt[gt][G]),typeof st>"u"||!st.length||!st[0]){var qt="";St=[];for(Nt in vt[gt])this.terminals_[Nt]&&Nt>ze&&St.push("'"+this.terminals_[Nt]+"'");W.showPosition?qt="Parse error on line "+(At+1)+`:
`+W.showPosition()+`
Expecting `+St.join(", ")+", got '"+(this.terminals_[G]||G)+"'":qt="Parse error on line "+(At+1)+": Unexpected "+(G==ae?"end of input":"'"+(this.terminals_[G]||G)+"'"),this.parseError(qt,{text:W.match,token:this.terminals_[G]||G,line:W.yylineno,loc:Yt,expected:St})}if(st[0]instanceof Array&&st.length>1)throw new Error("Parse Error: multiple actions possible at state: "+gt+", token: "+G);switch(st[0]){case 1:P.push(G),R.push(W.yytext),l.push(W.yylloc),P.push(st[1]),G=null,Ft?(G=Ft,Ft=null):(ee=W.yyleng,u=W.yytext,At=W.yylineno,Yt=W.yylloc,re>0&&re--);break;case 2:if(ht=this.productions_[st[1]][1],bt.$=R[R.length-ht],bt._$={first_line:l[l.length-(ht||1)].first_line,last_line:l[l.length-1].last_line,first_column:l[l.length-(ht||1)].first_column,last_column:l[l.length-1].last_column},Ke&&(bt._$.range=[l[l.length-(ht||1)].range[0],l[l.length-1].range[1]]),Wt=this.performAction.apply(bt,[u,ee,At,ut.yy,st[1],R,l].concat(He)),typeof Wt<"u")return Wt;ht&&(P=P.slice(0,-1*ht*2),R=R.slice(0,-1*ht),l=l.slice(0,-1*ht)),P.push(this.productions_[st[1]][0]),R.push(bt.$),l.push(bt._$),ie=vt[P[P.length-2]][P[P.length-1]],P.push(ie);break;case 3:return!0}}return!0},"parse")},qe=function(){var pt={EOF:1,parseError:d(function(L,P){if(this.yy.parser)this.yy.parser.parseError(L,P);else throw new Error(L)},"parseError"),setInput:d(function(w,L){return this.yy=L||this.yy||{},this._input=w,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var w=this._input[0];this.yytext+=w,this.yyleng++,this.offset++,this.match+=w,this.matched+=w;var L=w.match(/(?:\r\n?|\n).*/g);return L?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),w},"input"),unput:d(function(w){var L=w.length,P=w.split(/(?:\r\n?|\n)/g);this._input=w+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-L),this.offset-=L;var E=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),P.length-1&&(this.yylineno-=P.length-1);var R=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:P?(P.length===E.length?this.yylloc.first_column:0)+E[E.length-P.length].length-P[0].length:this.yylloc.first_column-L},this.options.ranges&&(this.yylloc.range=[R[0],R[0]+this.yyleng-L]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(w){this.unput(this.match.slice(w))},"less"),pastInput:d(function(){var w=this.matched.substr(0,this.matched.length-this.match.length);return(w.length>20?"...":"")+w.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var w=this.match;return w.length<20&&(w+=this._input.substr(0,20-w.length)),(w.substr(0,20)+(w.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var w=this.pastInput(),L=new Array(w.length+1).join("-");return w+this.upcomingInput()+`
`+L+"^"},"showPosition"),test_match:d(function(w,L){var P,E,R;if(this.options.backtrack_lexer&&(R={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(R.yylloc.range=this.yylloc.range.slice(0))),E=w[0].match(/(?:\r\n?|\n).*/g),E&&(this.yylineno+=E.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:E?E[E.length-1].length-E[E.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+w[0].length},this.yytext+=w[0],this.match+=w[0],this.matches=w,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(w[0].length),this.matched+=w[0],P=this.performAction.call(this,this.yy,this,L,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),P)return P;if(this._backtrack){for(var l in R)this[l]=R[l];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var w,L,P,E;this._more||(this.yytext="",this.match="");for(var R=this._currentRules(),l=0;l<R.length;l++)if(P=this._input.match(this.rules[R[l]]),P&&(!L||P[0].length>L[0].length)){if(L=P,E=l,this.options.backtrack_lexer){if(w=this.test_match(P,R[l]),w!==!1)return w;if(this._backtrack){L=!1;continue}else return!1}else if(!this.options.flex)break}return L?(w=this.test_match(L,R[E]),w!==!1?w:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var L=this.next();return L||this.lex()},"lex"),begin:d(function(L){this.conditionStack.push(L)},"begin"),popState:d(function(){var L=this.conditionStack.length-1;return L>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(L){return L=this.conditionStack.length-1-Math.abs(L||0),L>=0?this.conditionStack[L]:"INITIAL"},"topState"),pushState:d(function(L){this.begin(L)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:d(function(L,P,E,R){var l=R;switch(E){case 0:return 5;case 1:break;case 2:break;case 3:break;case 4:break;case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;break;case 8:return this.begin("ID"),50;break;case 9:return this.begin("ID"),52;break;case 10:return 13;case 11:return this.begin("ID"),53;break;case 12:return P.yytext=P.yytext.trim(),this.begin("ALIAS"),70;break;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;break;case 14:return this.popState(),this.popState(),5;break;case 15:return this.begin("LINE"),36;break;case 16:return this.begin("LINE"),37;break;case 17:return this.begin("LINE"),38;break;case 18:return this.begin("LINE"),39;break;case 19:return this.begin("LINE"),49;break;case 20:return this.begin("LINE"),41;break;case 21:return this.begin("LINE"),43;break;case 22:return this.begin("LINE"),48;break;case 23:return this.begin("LINE"),44;break;case 24:return this.begin("LINE"),47;break;case 25:return this.begin("LINE"),46;break;case 26:return this.popState(),15;break;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;break;case 37:return this.begin("ID"),23;break;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;break;case 41:return this.popState(),"acc_title_value";break;case 42:return this.begin("acc_descr"),33;break;case 43:return this.popState(),"acc_descr_value";break;case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 51:return 5;case 52:return P.yytext=P.yytext.trim(),70;break;case 53:return 73;case 54:return 74;case 55:return 75;case 56:return 76;case 57:return 71;case 58:return 72;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 80;case 63:return 81;case 64:return 68;case 65:return 69;case 66:return 5;case 67:return"INVALID"}},"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],inclusive:!0}}};return pt}();Bt.lexer=qe;function Pt(){this.yy={}}return d(Pt,"Parser"),Pt.prototype=Bt,Bt.Parser=Pt,new Pt}();Ut.parser=Ut;var Je=Ut,v=new ye(()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0})),Ze=d(function(t){v.records.boxes.push({name:t.text,wrap:t.wrap??xt(),fill:t.color,actorKeys:[]}),v.records.currentBox=v.records.boxes.slice(-1)[0]},"addBox"),Gt=d(function(t,e,c,r){let s=v.records.currentBox,i=v.records.actors.get(t);if(i){if(v.records.currentBox&&i.box&&v.records.currentBox!==i.box)throw new Error(`A same participant should only be defined in one Box: ${i.name} can't be in '${i.box.name}' and in '${v.records.currentBox.name}' at the same time.`);if(s=i.box?i.box:v.records.currentBox,i.box=s,i&&e===i.name&&c==null)return}if(c?.text==null&&(c={text:e,type:r}),(r==null||c.text==null)&&(c={text:e,type:r}),v.records.actors.set(t,{box:s,name:e,description:c.text,wrap:c.wrap??xt(),prevActor:v.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:r??"participant"}),v.records.prevActor){let o=v.records.actors.get(v.records.prevActor);o&&(o.nextActor=t)}v.records.currentBox&&v.records.currentBox.actorKeys.push(t),v.records.prevActor=t},"addActor"),Qe=d(t=>{let e,c=0;if(!t)return 0;for(e=0;e<v.records.messages.length;e++)v.records.messages[e].type===Lt.ACTIVE_START&&v.records.messages[e].from===t&&c++,v.records.messages[e].type===Lt.ACTIVE_END&&v.records.messages[e].from===t&&c--;return c},"activationCount"),je=d(function(t,e,c,r){v.records.messages.push({from:t,to:e,message:c.text,wrap:c.wrap??xt(),answer:r})},"addMessage"),O=d(function(t,e,c,r,s=!1){if(r===Lt.ACTIVE_END&&Qe(t??"")<1){let o=new Error("Trying to inactivate an inactive participant ("+t+")");throw o.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},o}return v.records.messages.push({from:t,to:e,message:c?.text??"",wrap:c?.wrap??xt(),type:r,activate:s}),!0},"addSignal"),$e=d(function(){return v.records.boxes.length>0},"hasAtLeastOneBox"),t0=d(function(){return v.records.boxes.some(t=>t.name)},"hasAtLeastOneBoxWithTitle"),e0=d(function(){return v.records.messages},"getMessages"),r0=d(function(){return v.records.boxes},"getBoxes"),a0=d(function(){return v.records.actors},"getActors"),s0=d(function(){return v.records.createdActors},"getCreatedActors"),i0=d(function(){return v.records.destroyedActors},"getDestroyedActors"),_t=d(function(t){return v.records.actors.get(t)},"getActor"),n0=d(function(){return[...v.records.actors.keys()]},"getActorKeys"),o0=d(function(){v.records.sequenceNumbersEnabled=!0},"enableSequenceNumbers"),c0=d(function(){v.records.sequenceNumbersEnabled=!1},"disableSequenceNumbers"),l0=d(()=>v.records.sequenceNumbersEnabled,"showSequenceNumbers"),d0=d(function(t){v.records.wrapEnabled=t},"setWrap"),we=d(t=>{if(t===void 0)return{};t=t.trim();let e=/^:?wrap:/.exec(t)!==null?!0:/^:?nowrap:/.exec(t)!==null?!1:void 0;return{cleanedText:(e===void 0?t:t.replace(/^:?(?:no)?wrap:/,"")).trim(),wrap:e}},"extractWrap"),xt=d(()=>v.records.wrapEnabled!==void 0?v.records.wrapEnabled:et().sequence?.wrap??!1,"autoWrap"),h0=d(function(){v.reset(),ce()},"clear"),p0=d(function(t){let e=t.trim(),{wrap:c,cleanedText:r}=we(e),s={text:r,wrap:c};return X.debug(`parseMessage: ${JSON.stringify(s)}`),s},"parseMessage"),u0=d(function(t){let e=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t),c=e?.[1]?e[1].trim():"transparent",r=e?.[2]?e[2].trim():void 0;if(window?.CSS)window.CSS.supports("color",c)||(c="transparent",r=t.trim());else{let o=new Option().style;o.color=c,o.color!==c&&(c="transparent",r=t.trim())}let{wrap:s,cleanedText:i}=we(r);return{text:i?wt(i,et()):void 0,color:c,wrap:s}},"parseBoxData"),Lt={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34},g0={FILLED:0,OPEN:1},f0={LEFTOF:0,RIGHTOF:1,OVER:2},ke=d(function(t,e,c){let r={actor:t,placement:e,message:c.text,wrap:c.wrap??xt()},s=[].concat(t,t);v.records.notes.push(r),v.records.messages.push({from:s[0],to:s[1],message:c.text,wrap:c.wrap??xt(),type:Lt.NOTE,placement:e})},"addNote"),Ie=d(function(t,e){let c=_t(t);try{let r=wt(e.text,et());r=r.replace(/&amp;/g,"&"),r=r.replace(/&equals;/g,"=");let s=JSON.parse(r);Ct(c,s)}catch(r){X.error("error while parsing actor link text",r)}},"addLinks"),x0=d(function(t,e){let c=_t(t);try{let r={},s=wt(e.text,et()),i=s.indexOf("@");s=s.replace(/&amp;/g,"&"),s=s.replace(/&equals;/g,"=");let o=s.slice(0,i-1).trim(),h=s.slice(i+1).trim();r[o]=h,Ct(c,r)}catch(r){X.error("error while parsing actor link text",r)}},"addALink");function Ct(t,e){if(t.links==null)t.links=e;else for(let c in e)t.links[c]=e[c]}d(Ct,"insertLinks");var Le=d(function(t,e){let c=_t(t);try{let r=wt(e.text,et()),s=JSON.parse(r);Zt(c,s)}catch(r){X.error("error while parsing actor properties text",r)}},"addProperties");function Zt(t,e){if(t.properties==null)t.properties=e;else for(let c in e)t.properties[c]=e[c]}d(Zt,"insertProperties");function _e(){v.records.currentBox=void 0}d(_e,"boxEnd");var Pe=d(function(t,e){let c=_t(t),r=document.getElementById(e.text);try{let s=r.innerHTML,i=JSON.parse(s);i.properties&&Zt(c,i.properties),i.links&&Ct(c,i.links)}catch(s){X.error("error while parsing actor details text",s)}},"addDetails"),T0=d(function(t,e){if(t?.properties!==void 0)return t.properties[e]},"getActorProperty"),Ae=d(function(t){if(Array.isArray(t))t.forEach(function(e){Ae(e)});else switch(t.type){case"sequenceIndex":v.records.messages.push({from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":Gt(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(v.records.actors.has(t.actor))throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");v.records.lastCreated=t.actor,Gt(t.actor,t.actor,t.description,t.draw),v.records.createdActors.set(t.actor,v.records.messages.length);break;case"destroyParticipant":v.records.lastDestroyed=t.actor,v.records.destroyedActors.set(t.actor,v.records.messages.length);break;case"activeStart":O(t.actor,void 0,void 0,t.signalType);break;case"activeEnd":O(t.actor,void 0,void 0,t.signalType);break;case"addNote":ke(t.actor,t.placement,t.text);break;case"addLinks":Ie(t.actor,t.text);break;case"addALink":x0(t.actor,t.text);break;case"addProperties":Le(t.actor,t.text);break;case"addDetails":Pe(t.actor,t.text);break;case"addMessage":if(v.records.lastCreated){if(t.to!==v.records.lastCreated)throw new Error("The created participant "+v.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.");v.records.lastCreated=void 0}else if(v.records.lastDestroyed){if(t.to!==v.records.lastDestroyed&&t.from!==v.records.lastDestroyed)throw new Error("The destroyed participant "+v.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");v.records.lastDestroyed=void 0}O(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":Ze(t.boxData);break;case"boxEnd":_e();break;case"loopStart":O(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":O(void 0,void 0,void 0,t.signalType);break;case"rectStart":O(void 0,void 0,t.color,t.signalType);break;case"rectEnd":O(void 0,void 0,void 0,t.signalType);break;case"optStart":O(void 0,void 0,t.optText,t.signalType);break;case"optEnd":O(void 0,void 0,void 0,t.signalType);break;case"altStart":O(void 0,void 0,t.altText,t.signalType);break;case"else":O(void 0,void 0,t.altText,t.signalType);break;case"altEnd":O(void 0,void 0,void 0,t.signalType);break;case"setAccTitle":Ht(t.text);break;case"parStart":O(void 0,void 0,t.parText,t.signalType);break;case"and":O(void 0,void 0,t.parText,t.signalType);break;case"parEnd":O(void 0,void 0,void 0,t.signalType);break;case"criticalStart":O(void 0,void 0,t.criticalText,t.signalType);break;case"option":O(void 0,void 0,t.optionText,t.signalType);break;case"criticalEnd":O(void 0,void 0,void 0,t.signalType);break;case"breakStart":O(void 0,void 0,t.breakText,t.signalType);break;case"breakEnd":O(void 0,void 0,void 0,t.signalType);break}},"apply"),Ee={addActor:Gt,addMessage:je,addSignal:O,addLinks:Ie,addDetails:Pe,addProperties:Le,autoWrap:xt,setWrap:d0,enableSequenceNumbers:o0,disableSequenceNumbers:c0,showSequenceNumbers:l0,getMessages:e0,getActors:a0,getCreatedActors:s0,getDestroyedActors:i0,getActor:_t,getActorKeys:n0,getActorProperty:T0,getAccTitle:le,getBoxes:r0,getDiagramTitle:ue,setDiagramTitle:pe,getConfig:d(()=>et().sequence,"getConfig"),clear:h0,parseMessage:p0,parseBoxData:u0,LINETYPE:Lt,ARROWTYPE:g0,PLACEMENT:f0,addNote:ke,setAccTitle:Ht,apply:Ae,setAccDescription:de,getAccDescription:he,hasAtLeastOneBox:$e,hasAtLeastOneBoxWithTitle:t0},b0=d(t=>`.actor {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }

  text.actor > tspan {
    fill: ${t.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${t.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${t.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${t.signalColor};
  }

  #arrowhead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .sequenceNumber {
    fill: ${t.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${t.signalColor};
  }

  #crosshead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .messageText {
    fill: ${t.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${t.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${t.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${t.noteBorderColor};
    fill: ${t.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${t.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation1 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation2 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${t.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
    stroke-width: 2px;
  }
`,"getStyles"),y0=b0,ft=18*2,Ne="actor-top",Se="actor-bottom",E0="actor-box",me="actor-man",jt=d(function(t,e){return fe(t,e)},"drawRect"),m0=d(function(t,e,c,r,s){if(e.links===void 0||e.links===null||Object.keys(e.links).length===0)return{height:0,width:0};let i=e.links,o=e.actorCnt,h=e.rectData;var p="none";s&&(p="block !important");let a=t.append("g");a.attr("id","actor"+o+"_popup"),a.attr("class","actorPopupMenu"),a.attr("display",p);var f="";h.class!==void 0&&(f=" "+h.class);let y=h.width>c?h.width:c,g=a.append("rect");if(g.attr("class","actorPopupMenuPanel"+f),g.attr("x",h.x),g.attr("y",h.height),g.attr("fill",h.fill),g.attr("stroke",h.stroke),g.attr("width",y),g.attr("height",h.height),g.attr("rx",h.rx),g.attr("ry",h.ry),i!=null){var T=20;for(let A in i){var m=a.append("a"),I=(0,Qt.sanitizeUrl)(i[A]);m.attr("xlink:href",I),m.attr("target","_blank"),V0(r)(A,m,h.x+10,h.height+T,y,20,{class:"actor"},r),T+=30}}return g.attr("height",T),{height:h.height+T,width:y}},"drawPopup"),v0=d(function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"},"popupMenuToggle"),Dt=d(async function(t,e,c=null){let r=t.append("foreignObject"),s=await zt(e.text,Mt()),o=r.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(s).node().getBoundingClientRect();if(r.attr("height",Math.round(o.height)).attr("width",Math.round(o.width)),e.class==="noteText"){let h=t.node().firstChild;h.setAttribute("height",o.height+2*e.textMargin);let p=h.getBBox();r.attr("x",Math.round(p.x+p.width/2-o.width/2)).attr("y",Math.round(p.y+p.height/2-o.height/2))}else if(c){let{startx:h,stopx:p,starty:a}=c;if(h>p){let f=h;h=p,p=f}r.attr("x",Math.round(h+Math.abs(h-p)/2-o.width/2)),e.class==="loopText"?r.attr("y",Math.round(a)):r.attr("y",Math.round(a-o.height))}return[r]},"drawKatex"),mt=d(function(t,e){let c=0,r=0,s=e.text.split(k.lineBreakRegex),[i,o]=Kt(e.fontSize),h=[],p=0,a=d(()=>e.y,"yfunc");if(e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0)switch(e.valign){case"top":case"start":a=d(()=>Math.round(e.y+e.textMargin),"yfunc");break;case"middle":case"center":a=d(()=>Math.round(e.y+(c+r+e.textMargin)/2),"yfunc");break;case"bottom":case"end":a=d(()=>Math.round(e.y+(c+r+2*e.textMargin)-e.textMargin),"yfunc");break}if(e.anchor!==void 0&&e.textMargin!==void 0&&e.width!==void 0)switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin),e.anchor="start",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2),e.anchor="middle",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin),e.anchor="end",e.dominantBaseline="middle",e.alignmentBaseline="middle";break}for(let[f,y]of s.entries()){e.textMargin!==void 0&&e.textMargin===0&&i!==void 0&&(p=f*i);let g=t.append("text");g.attr("x",e.x),g.attr("y",a()),e.anchor!==void 0&&g.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline),e.fontFamily!==void 0&&g.style("font-family",e.fontFamily),o!==void 0&&g.style("font-size",o),e.fontWeight!==void 0&&g.style("font-weight",e.fontWeight),e.fill!==void 0&&g.attr("fill",e.fill),e.class!==void 0&&g.attr("class",e.class),e.dy!==void 0?g.attr("dy",e.dy):p!==0&&g.attr("dy",p);let T=y||ge;if(e.tspan){let m=g.append("tspan");m.attr("x",e.x),e.fill!==void 0&&m.attr("fill",e.fill),m.text(T)}else g.text(T);e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0&&(r+=(g._groups||g)[0][0].getBBox().height,c=r),h.push(g)}return h},"drawText"),Me=d(function(t,e){function c(s,i,o,h,p){return s+","+i+" "+(s+o)+","+i+" "+(s+o)+","+(i+h-p)+" "+(s+o-p*1.2)+","+(i+h)+" "+s+","+(i+h)}d(c,"genPoints");let r=t.append("polygon");return r.attr("points",c(e.x,e.y,e.width,e.height,7)),r.attr("class","labelBox"),e.y=e.y+e.height/2,mt(t,e),r},"drawLabel"),lt=-1,Re=d((t,e,c,r)=>{t.select&&c.forEach(s=>{let i=e.get(s),o=t.select("#actor"+i.actorCnt);!r.mirrorActors&&i.stopy?o.attr("y2",i.stopy+i.height/2):r.mirrorActors&&o.attr("y2",i.stopy)})},"fixLifeLineHeights"),w0=d(function(t,e,c,r){let s=r?e.stopy:e.starty,i=e.x+e.width/2,o=s+e.height,h=t.append("g").lower();var p=h;r||(lt++,Object.keys(e.links||{}).length&&!c.forceMenus&&p.attr("onclick",v0(`actor${lt}_popup`)).attr("cursor","pointer"),p.append("line").attr("id","actor"+lt).attr("x1",i).attr("y1",o).attr("x2",i).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),p=h.append("g"),e.actorCnt=lt,e.links!=null&&p.attr("id","root-"+lt));let a=It();var f="actor";e.properties?.class?f=e.properties.class:a.fill="#eaeaea",r?f+=` ${Se}`:f+=` ${Ne}`,a.x=e.x,a.y=s,a.width=e.width,a.height=e.height,a.class=f,a.rx=3,a.ry=3,a.name=e.name;let y=jt(p,a);if(e.rectData=a,e.properties?.icon){let T=e.properties.icon.trim();T.charAt(0)==="@"?be(p,a.x+a.width-20,a.y+10,T.substr(1)):Te(p,a.x+a.width-20,a.y+10,T)}$t(c,nt(e.description))(e.description,p,a.x,a.y,a.width,a.height,{class:`actor ${E0}`},c);let g=e.height;if(y.node){let T=y.node().getBBox();e.height=T.height,g=T.height}return g},"drawActorTypeParticipant"),k0=d(function(t,e,c,r){let s=r?e.stopy:e.starty,i=e.x+e.width/2,o=s+80,h=t.append("g").lower();r||(lt++,h.append("line").attr("id","actor"+lt).attr("x1",i).attr("y1",o).attr("x2",i).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),e.actorCnt=lt);let p=t.append("g"),a=me;r?a+=` ${Se}`:a+=` ${Ne}`,p.attr("class",a),p.attr("name",e.name);let f=It();f.x=e.x,f.y=s,f.fill="#eaeaea",f.width=e.width,f.height=e.height,f.class="actor",f.rx=3,f.ry=3,p.append("line").attr("id","actor-man-torso"+lt).attr("x1",i).attr("y1",s+25).attr("x2",i).attr("y2",s+45),p.append("line").attr("id","actor-man-arms"+lt).attr("x1",i-ft/2).attr("y1",s+33).attr("x2",i+ft/2).attr("y2",s+33),p.append("line").attr("x1",i-ft/2).attr("y1",s+60).attr("x2",i).attr("y2",s+45),p.append("line").attr("x1",i).attr("y1",s+45).attr("x2",i+ft/2-2).attr("y2",s+60);let y=p.append("circle");y.attr("cx",e.x+e.width/2),y.attr("cy",s+10),y.attr("r",15),y.attr("width",e.width),y.attr("height",e.height);let g=p.node().getBBox();return e.height=g.height,$t(c,nt(e.description))(e.description,p,f.x,f.y+35,f.width,f.height,{class:`actor ${me}`},c),e.height},"drawActorTypeActor"),I0=d(async function(t,e,c,r){switch(e.type){case"actor":return await k0(t,e,c,r);case"participant":return await w0(t,e,c,r)}},"drawActor"),L0=d(function(t,e,c){let s=t.append("g");De(s,e),e.name&&$t(c)(e.name,s,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},c),s.lower()},"drawBox"),_0=d(function(t){return t.append("g")},"anchorElement"),P0=d(function(t,e,c,r,s){let i=It(),o=e.anchored;i.x=e.startx,i.y=e.starty,i.class="activation"+s%3,i.width=e.stopx-e.startx,i.height=c-e.starty,jt(o,i)},"drawActivation"),A0=d(async function(t,e,c,r){let{boxMargin:s,boxTextMargin:i,labelBoxHeight:o,labelBoxWidth:h,messageFontFamily:p,messageFontSize:a,messageFontWeight:f}=r,y=t.append("g"),g=d(function(I,A,V,M){return y.append("line").attr("x1",I).attr("y1",A).attr("x2",V).attr("y2",M).attr("class","loopLine")},"drawLoopLine");g(e.startx,e.starty,e.stopx,e.starty),g(e.stopx,e.starty,e.stopx,e.stopy),g(e.startx,e.stopy,e.stopx,e.stopy),g(e.startx,e.starty,e.startx,e.stopy),e.sections!==void 0&&e.sections.forEach(function(I){g(e.startx,I.y,e.stopx,I.y).style("stroke-dasharray","3, 3")});let T=Rt();T.text=c,T.x=e.startx,T.y=e.starty,T.fontFamily=p,T.fontSize=a,T.fontWeight=f,T.anchor="middle",T.valign="middle",T.tspan=!1,T.width=h||50,T.height=o||20,T.textMargin=i,T.class="labelText",Me(y,T),T=Ce(),T.text=e.title,T.x=e.startx+h/2+(e.stopx-e.startx)/2,T.y=e.starty+s+i,T.anchor="middle",T.valign="middle",T.textMargin=i,T.class="loopText",T.fontFamily=p,T.fontSize=a,T.fontWeight=f,T.wrap=!0;let m=nt(T.text)?await Dt(y,T,e):mt(y,T);if(e.sectionTitles!==void 0){for(let[I,A]of Object.entries(e.sectionTitles))if(A.message){T.text=A.message,T.x=e.startx+(e.stopx-e.startx)/2,T.y=e.sections[I].y+s+i,T.class="loopText",T.anchor="middle",T.valign="middle",T.tspan=!1,T.fontFamily=p,T.fontSize=a,T.fontWeight=f,T.wrap=e.wrap,nt(T.text)?(e.starty=e.sections[I].y,await Dt(y,T,e)):mt(y,T);let V=Math.round(m.map(M=>(M._groups||M)[0][0].getBBox().height).reduce((M,F)=>M+F));e.sections[I].height+=V-(s+i)}}return e.height=Math.round(e.stopy-e.starty),y},"drawLoop"),De=d(function(t,e){xe(t,e)},"drawBackgroundRect"),N0=d(function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),S0=d(function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),M0=d(function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),R0=d(function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")},"insertArrowHead"),D0=d(function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),C0=d(function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertSequenceNumber"),O0=d(function(t){t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},"insertArrowCrossHead"),Ce=d(function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}},"getTextObj"),B0=d(function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),$t=function(){function t(i,o,h,p,a,f,y){let g=o.append("text").attr("x",h+a/2).attr("y",p+f/2+5).style("text-anchor","middle").text(i);s(g,y)}d(t,"byText");function e(i,o,h,p,a,f,y,g){let{actorFontSize:T,actorFontFamily:m,actorFontWeight:I}=g,[A,V]=Kt(T),M=i.split(k.lineBreakRegex);for(let F=0;F<M.length;F++){let C=F*A-A*(M.length-1)/2,z=o.append("text").attr("x",h+a/2).attr("y",p).style("text-anchor","middle").style("font-size",V).style("font-weight",I).style("font-family",m);z.append("tspan").attr("x",h+a/2).attr("dy",C).text(M[F]),z.attr("y",p+f/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),s(z,y)}}d(e,"byTspan");function c(i,o,h,p,a,f,y,g){let T=o.append("switch"),I=T.append("foreignObject").attr("x",h).attr("y",p).attr("width",a).attr("height",f).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");I.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(i),e(i,T,h,p,a,f,y,g),s(I,y)}d(c,"byFo");async function r(i,o,h,p,a,f,y,g){let T=await yt(i,Mt()),m=o.append("switch"),A=m.append("foreignObject").attr("x",h+a/2-T.width/2).attr("y",p+f/2-T.height/2).attr("width",T.width).attr("height",T.height).append("xhtml:div").style("height","100%").style("width","100%");A.append("div").style("text-align","center").style("vertical-align","middle").html(await zt(i,Mt())),e(i,m,h,p,a,f,y,g),s(A,y)}d(r,"byKatex");function s(i,o){for(let h in o)o.hasOwnProperty(h)&&i.attr(h,o[h])}return d(s,"_setTextAttrs"),function(i,o=!1){return o?r:i.textPlacement==="fo"?c:i.textPlacement==="old"?t:e}}(),V0=function(){function t(s,i,o,h,p,a,f){let y=i.append("text").attr("x",o).attr("y",h).style("text-anchor","start").text(s);r(y,f)}d(t,"byText");function e(s,i,o,h,p,a,f,y){let{actorFontSize:g,actorFontFamily:T,actorFontWeight:m}=y,I=s.split(k.lineBreakRegex);for(let A=0;A<I.length;A++){let V=A*g-g*(I.length-1)/2,M=i.append("text").attr("x",o).attr("y",h).style("text-anchor","start").style("font-size",g).style("font-weight",m).style("font-family",T);M.append("tspan").attr("x",o).attr("dy",V).text(I[A]),M.attr("y",h+a/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),r(M,f)}}d(e,"byTspan");function c(s,i,o,h,p,a,f,y){let g=i.append("switch"),m=g.append("foreignObject").attr("x",o).attr("y",h).attr("width",p).attr("height",a).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");m.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(s),e(s,g,o,h,p,a,f,y),r(m,f)}d(c,"byFo");function r(s,i){for(let o in i)i.hasOwnProperty(o)&&s.attr(o,i[o])}return d(r,"_setTextAttrs"),function(s){return s.textPlacement==="fo"?c:s.textPlacement==="old"?t:e}}(),B={drawRect:jt,drawText:mt,drawLabel:Me,drawActor:I0,drawBox:L0,drawPopup:m0,anchorElement:_0,drawActivation:P0,drawLoop:A0,drawBackgroundRect:De,insertArrowHead:R0,insertArrowFilledHead:D0,insertSequenceNumber:C0,insertArrowCrossHead:O0,insertDatabaseIcon:N0,insertComputerIcon:S0,insertClockIcon:M0,getTextObj:Ce,getNoteRect:B0,fixLifeLineHeights:Re,sanitizeUrl:Qt.sanitizeUrl},n={},x={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:d(function(){return Math.max.apply(null,this.actors.length===0?[0]:this.actors.map(t=>t.height||0))+(this.loops.length===0?0:this.loops.map(t=>t.height||0).reduce((t,e)=>t+e))+(this.messages.length===0?0:this.messages.map(t=>t.height||0).reduce((t,e)=>t+e))+(this.notes.length===0?0:this.notes.map(t=>t.height||0).reduce((t,e)=>t+e))},"getHeight"),clear:d(function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},"clear"),addBox:d(function(t){this.boxes.push(t)},"addBox"),addActor:d(function(t){this.actors.push(t)},"addActor"),addLoop:d(function(t){this.loops.push(t)},"addLoop"),addMessage:d(function(t){this.messages.push(t)},"addMessage"),addNote:d(function(t){this.notes.push(t)},"addNote"),lastActor:d(function(){return this.actors[this.actors.length-1]},"lastActor"),lastLoop:d(function(){return this.loops[this.loops.length-1]},"lastLoop"),lastMessage:d(function(){return this.messages[this.messages.length-1]},"lastMessage"),lastNote:d(function(){return this.notes[this.notes.length-1]},"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:d(function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,Ve(et())},"init"),updateVal:d(function(t,e,c,r){t[e]===void 0?t[e]=c:t[e]=r(c,t[e])},"updateVal"),updateBounds:d(function(t,e,c,r){let s=this,i=0;function o(h){return d(function(a){i++;let f=s.sequenceItems.length-i+1;s.updateVal(a,"starty",e-f*n.boxMargin,Math.min),s.updateVal(a,"stopy",r+f*n.boxMargin,Math.max),s.updateVal(x.data,"startx",t-f*n.boxMargin,Math.min),s.updateVal(x.data,"stopx",c+f*n.boxMargin,Math.max),h!=="activation"&&(s.updateVal(a,"startx",t-f*n.boxMargin,Math.min),s.updateVal(a,"stopx",c+f*n.boxMargin,Math.max),s.updateVal(x.data,"starty",e-f*n.boxMargin,Math.min),s.updateVal(x.data,"stopy",r+f*n.boxMargin,Math.max))},"updateItemBounds")}d(o,"updateFn"),this.sequenceItems.forEach(o()),this.activations.forEach(o("activation"))},"updateBounds"),insert:d(function(t,e,c,r){let s=k.getMin(t,c),i=k.getMax(t,c),o=k.getMin(e,r),h=k.getMax(e,r);this.updateVal(x.data,"startx",s,Math.min),this.updateVal(x.data,"starty",o,Math.min),this.updateVal(x.data,"stopx",i,Math.max),this.updateVal(x.data,"stopy",h,Math.max),this.updateBounds(s,o,i,h)},"insert"),newActivation:d(function(t,e,c){let r=c.get(t.from),s=Ot(t.from).length||0,i=r.x+r.width/2+(s-1)*n.activationWidth/2;this.activations.push({startx:i,starty:this.verticalPos+2,stopx:i+n.activationWidth,stopy:void 0,actor:t.from,anchored:B.anchorElement(e)})},"newActivation"),endActivation:d(function(t){let e=this.activations.map(function(c){return c.actor}).lastIndexOf(t.from);return this.activations.splice(e,1)[0]},"endActivation"),createLoop:d(function(t={message:void 0,wrap:!1,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}},"createLoop"),newLoop:d(function(t={message:void 0,wrap:!1,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))},"newLoop"),endLoop:d(function(){return this.sequenceItems.pop()},"endLoop"),isLoopOverlap:d(function(){return this.sequenceItems.length?this.sequenceItems[this.sequenceItems.length-1].overlap:!1},"isLoopOverlap"),addSectionToLoop:d(function(t){let e=this.sequenceItems.pop();e.sections=e.sections||[],e.sectionTitles=e.sectionTitles||[],e.sections.push({y:x.getVerticalPos(),height:0}),e.sectionTitles.push(t),this.sequenceItems.push(e)},"addSectionToLoop"),saveVerticalPos:d(function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},"saveVerticalPos"),resetVerticalPos:d(function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},"resetVerticalPos"),bumpVerticalPos:d(function(t){this.verticalPos=this.verticalPos+t,this.data.stopy=k.getMax(this.data.stopy,this.verticalPos)},"bumpVerticalPos"),getVerticalPos:d(function(){return this.verticalPos},"getVerticalPos"),getBounds:d(function(){return{bounds:this.data,models:this.models}},"getBounds")},Y0=d(async function(t,e){x.bumpVerticalPos(n.boxMargin),e.height=n.boxMargin,e.starty=x.getVerticalPos();let c=It();c.x=e.startx,c.y=e.starty,c.width=e.width||n.width,c.class="note";let r=t.append("g"),s=B.drawRect(r,c),i=Rt();i.x=e.startx,i.y=e.starty,i.width=c.width,i.dy="1em",i.text=e.message,i.class="noteText",i.fontFamily=n.noteFontFamily,i.fontSize=n.noteFontSize,i.fontWeight=n.noteFontWeight,i.anchor=n.noteAlign,i.textMargin=n.noteMargin,i.valign="center";let o=nt(i.text)?await Dt(r,i):mt(r,i),h=Math.round(o.map(p=>(p._groups||p)[0][0].getBBox().height).reduce((p,a)=>p+a));s.attr("height",h+2*n.noteMargin),e.height+=h+2*n.noteMargin,x.bumpVerticalPos(h+2*n.noteMargin),e.stopy=e.starty+h+2*n.noteMargin,e.stopx=e.startx+c.width,x.insert(e.startx,e.starty,e.stopx,e.stopy),x.models.addNote(e)},"drawNote"),Tt=d(t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),"messageFont"),Et=d(t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight}),"noteFont"),Xt=d(t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight}),"actorFont");async function Oe(t,e){x.bumpVerticalPos(10);let{startx:c,stopx:r,message:s}=e,i=k.splitBreaks(s).length,o=nt(s),h=o?await yt(s,et()):Y.calculateTextDimensions(s,Tt(n));if(!o){let y=h.height/i;e.height+=y,x.bumpVerticalPos(y)}let p,a=h.height-10,f=h.width;if(c===r){p=x.getVerticalPos()+a,n.rightAngles||(a+=n.boxMargin,p=x.getVerticalPos()+a),a+=30;let y=k.getMax(f/2,n.width/2);x.insert(c-y,x.getVerticalPos()-10+a,r+y,x.getVerticalPos()+30+a)}else a+=n.boxMargin,p=x.getVerticalPos()+a,x.insert(c,p-10,r,p);return x.bumpVerticalPos(a),e.height+=a,e.stopy=e.starty+e.height,x.insert(e.fromBounds,e.starty,e.toBounds,e.stopy),p}d(Oe,"boundMessage");var F0=d(async function(t,e,c,r){let{startx:s,stopx:i,starty:o,message:h,type:p,sequenceIndex:a,sequenceVisible:f}=e,y=Y.calculateTextDimensions(h,Tt(n)),g=Rt();g.x=s,g.y=o+10,g.width=i-s,g.class="messageText",g.dy="1em",g.text=h,g.fontFamily=n.messageFontFamily,g.fontSize=n.messageFontSize,g.fontWeight=n.messageFontWeight,g.anchor=n.messageAlign,g.valign="center",g.textMargin=n.wrapPadding,g.tspan=!1,nt(g.text)?await Dt(t,g,{startx:s,stopx:i,starty:c}):mt(t,g);let T=y.width,m;s===i?n.rightAngles?m=t.append("path").attr("d",`M  ${s},${c} H ${s+k.getMax(n.width/2,T/2)} V ${c+25} H ${s}`):m=t.append("path").attr("d","M "+s+","+c+" C "+(s+60)+","+(c-10)+" "+(s+60)+","+(c+30)+" "+s+","+(c+20)):(m=t.append("line"),m.attr("x1",s),m.attr("y1",c),m.attr("x2",i),m.attr("y2",c)),p===r.db.LINETYPE.DOTTED||p===r.db.LINETYPE.DOTTED_CROSS||p===r.db.LINETYPE.DOTTED_POINT||p===r.db.LINETYPE.DOTTED_OPEN||p===r.db.LINETYPE.BIDIRECTIONAL_DOTTED?(m.style("stroke-dasharray","3, 3"),m.attr("class","messageLine1")):m.attr("class","messageLine0");let I="";n.arrowMarkerAbsolute&&(I=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,I=I.replace(/\(/g,"\\("),I=I.replace(/\)/g,"\\)")),m.attr("stroke-width",2),m.attr("stroke","none"),m.style("fill","none"),(p===r.db.LINETYPE.SOLID||p===r.db.LINETYPE.DOTTED)&&m.attr("marker-end","url("+I+"#arrowhead)"),(p===r.db.LINETYPE.BIDIRECTIONAL_SOLID||p===r.db.LINETYPE.BIDIRECTIONAL_DOTTED)&&(m.attr("marker-start","url("+I+"#arrowhead)"),m.attr("marker-end","url("+I+"#arrowhead)")),(p===r.db.LINETYPE.SOLID_POINT||p===r.db.LINETYPE.DOTTED_POINT)&&m.attr("marker-end","url("+I+"#filled-head)"),(p===r.db.LINETYPE.SOLID_CROSS||p===r.db.LINETYPE.DOTTED_CROSS)&&m.attr("marker-end","url("+I+"#crosshead)"),(f||n.showSequenceNumbers)&&(m.attr("marker-start","url("+I+"#sequencenumber)"),t.append("text").attr("x",s).attr("y",c+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(a))},"drawMessage"),W0=d(function(t,e,c,r,s,i,o){let h=0,p=0,a,f=0;for(let y of r){let g=e.get(y),T=g.box;a&&a!=T&&(o||x.models.addBox(a),p+=n.boxMargin+a.margin),T&&T!=a&&(o||(T.x=h+p,T.y=s),p+=T.margin),g.width=g.width||n.width,g.height=k.getMax(g.height||n.height,n.height),g.margin=g.margin||n.actorMargin,f=k.getMax(f,g.height),c.get(g.name)&&(p+=g.width/2),g.x=h+p,g.starty=x.getVerticalPos(),x.insert(g.x,s,g.x+g.width,g.height),h+=g.width+p,g.box&&(g.box.width=h+T.margin-g.box.x),p=g.margin,a=g.box,x.models.addActor(g)}a&&!o&&x.models.addBox(a),x.bumpVerticalPos(f)},"addActorRenderingData"),Jt=d(async function(t,e,c,r){if(r){let s=0;x.bumpVerticalPos(n.boxMargin*2);for(let i of c){let o=e.get(i);o.stopy||(o.stopy=x.getVerticalPos());let h=await B.drawActor(t,o,n,!0);s=k.getMax(s,h)}x.bumpVerticalPos(s+n.boxMargin)}else for(let s of c){let i=e.get(s);await B.drawActor(t,i,n,!1)}},"drawActors"),Be=d(function(t,e,c,r){let s=0,i=0;for(let o of c){let h=e.get(o),p=z0(h),a=B.drawPopup(t,h,p,n,n.forceMenus,r);a.height>s&&(s=a.height),a.width+h.x>i&&(i=a.width+h.x)}return{maxHeight:s,maxWidth:i}},"drawActorsPopup"),Ve=d(function(t){ne(n,t),t.fontFamily&&(n.actorFontFamily=n.noteFontFamily=n.messageFontFamily=t.fontFamily),t.fontSize&&(n.actorFontSize=n.noteFontSize=n.messageFontSize=t.fontSize),t.fontWeight&&(n.actorFontWeight=n.noteFontWeight=n.messageFontWeight=t.fontWeight)},"setConf"),Ot=d(function(t){return x.activations.filter(function(e){return e.actor===t})},"actorActivations"),ve=d(function(t,e){let c=e.get(t),r=Ot(t),s=r.reduce(function(o,h){return k.getMin(o,h.startx)},c.x+c.width/2-1),i=r.reduce(function(o,h){return k.getMax(o,h.stopx)},c.x+c.width/2+1);return[s,i]},"activationBounds");function ot(t,e,c,r,s){x.bumpVerticalPos(c);let i=r;if(e.id&&e.message&&t[e.id]){let o=t[e.id].width,h=Tt(n);e.message=Y.wrapLabel(`[${e.message}]`,o-2*n.wrapPadding,h),e.width=o,e.wrap=!0;let p=Y.calculateTextDimensions(e.message,h),a=k.getMax(p.height,n.labelBoxHeight);i=r+a,X.debug(`${a} - ${e.message}`)}s(e),x.bumpVerticalPos(i)}d(ot,"adjustLoopHeightForWrap");function Ye(t,e,c,r,s,i,o){function h(a,f){a.x<s.get(t.from).x?(x.insert(e.stopx-f,e.starty,e.startx,e.stopy+a.height/2+n.noteMargin),e.stopx=e.stopx+f):(x.insert(e.startx,e.starty,e.stopx+f,e.stopy+a.height/2+n.noteMargin),e.stopx=e.stopx-f)}d(h,"receiverAdjustment");function p(a,f){a.x<s.get(t.to).x?(x.insert(e.startx-f,e.starty,e.stopx,e.stopy+a.height/2+n.noteMargin),e.startx=e.startx+f):(x.insert(e.stopx,e.starty,e.startx+f,e.stopy+a.height/2+n.noteMargin),e.startx=e.startx-f)}if(d(p,"senderAdjustment"),i.get(t.to)==r){let a=s.get(t.to),f=a.type=="actor"?ft/2+3:a.width/2+3;h(a,f),a.starty=c-a.height/2,x.bumpVerticalPos(a.height/2)}else if(o.get(t.from)==r){let a=s.get(t.from);if(n.mirrorActors){let f=a.type=="actor"?ft/2:a.width/2;p(a,f)}a.stopy=c-a.height/2,x.bumpVerticalPos(a.height/2)}else if(o.get(t.to)==r){let a=s.get(t.to);if(n.mirrorActors){let f=a.type=="actor"?ft/2+3:a.width/2+3;h(a,f)}a.stopy=c-a.height/2,x.bumpVerticalPos(a.height/2)}}d(Ye,"adjustCreatedDestroyedData");var q0=d(async function(t,e,c,r){let{securityLevel:s,sequence:i}=et();n=i;let o;s==="sandbox"&&(o=kt("#i"+e));let h=s==="sandbox"?kt(o.nodes()[0].contentDocument.body):kt("body"),p=s==="sandbox"?o.nodes()[0].contentDocument:document;x.init(),X.debug(r.db);let a=s==="sandbox"?h.select(`[id="${e}"]`):kt(`[id="${e}"]`),f=r.db.getActors(),y=r.db.getCreatedActors(),g=r.db.getDestroyedActors(),T=r.db.getBoxes(),m=r.db.getActorKeys(),I=r.db.getMessages(),A=r.db.getDiagramTitle(),V=r.db.hasAtLeastOneBox(),M=r.db.hasAtLeastOneBoxWithTitle(),F=await Fe(f,I,r);if(n.height=await We(f,F,T),B.insertComputerIcon(a),B.insertDatabaseIcon(a),B.insertClockIcon(a),V&&(x.bumpVerticalPos(n.boxMargin),M&&x.bumpVerticalPos(T[0].textMaxHeight)),n.hideUnusedParticipants===!0){let b=new Set;I.forEach(_=>{b.add(_.from),b.add(_.to)}),m=m.filter(_=>b.has(_))}W0(a,f,y,m,0,I,!1);let C=await U0(I,f,F,r);B.insertArrowHead(a),B.insertArrowCrossHead(a),B.insertArrowFilledHead(a),B.insertSequenceNumber(a);function z(b,_){let $=x.endActivation(b);$.starty+18>_&&($.starty=_-6,_+=12),B.drawActivation(a,$,_,n,Ot(b.from).length),x.insert($.startx,_-10,$.stopx,_)}d(z,"activeEnd");let H=1,Z=1,rt=[],K=[],U=0;for(let b of I){let _,$,it;switch(b.type){case r.db.LINETYPE.NOTE:x.resetVerticalPos(),$=b.noteModel,await Y0(a,$);break;case r.db.LINETYPE.ACTIVE_START:x.newActivation(b,a,f);break;case r.db.LINETYPE.ACTIVE_END:z(b,x.getVerticalPos());break;case r.db.LINETYPE.LOOP_START:ot(C,b,n.boxMargin,n.boxMargin+n.boxTextMargin,N=>x.newLoop(N));break;case r.db.LINETYPE.LOOP_END:_=x.endLoop(),await B.drawLoop(a,_,"loop",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case r.db.LINETYPE.RECT_START:ot(C,b,n.boxMargin,n.boxMargin,N=>x.newLoop(void 0,N.message));break;case r.db.LINETYPE.RECT_END:_=x.endLoop(),K.push(_),x.models.addLoop(_),x.bumpVerticalPos(_.stopy-x.getVerticalPos());break;case r.db.LINETYPE.OPT_START:ot(C,b,n.boxMargin,n.boxMargin+n.boxTextMargin,N=>x.newLoop(N));break;case r.db.LINETYPE.OPT_END:_=x.endLoop(),await B.drawLoop(a,_,"opt",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case r.db.LINETYPE.ALT_START:ot(C,b,n.boxMargin,n.boxMargin+n.boxTextMargin,N=>x.newLoop(N));break;case r.db.LINETYPE.ALT_ELSE:ot(C,b,n.boxMargin+n.boxTextMargin,n.boxMargin,N=>x.addSectionToLoop(N));break;case r.db.LINETYPE.ALT_END:_=x.endLoop(),await B.drawLoop(a,_,"alt",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case r.db.LINETYPE.PAR_START:case r.db.LINETYPE.PAR_OVER_START:ot(C,b,n.boxMargin,n.boxMargin+n.boxTextMargin,N=>x.newLoop(N)),x.saveVerticalPos();break;case r.db.LINETYPE.PAR_AND:ot(C,b,n.boxMargin+n.boxTextMargin,n.boxMargin,N=>x.addSectionToLoop(N));break;case r.db.LINETYPE.PAR_END:_=x.endLoop(),await B.drawLoop(a,_,"par",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case r.db.LINETYPE.AUTONUMBER:H=b.message.start||H,Z=b.message.step||Z,b.message.visible?r.db.enableSequenceNumbers():r.db.disableSequenceNumbers();break;case r.db.LINETYPE.CRITICAL_START:ot(C,b,n.boxMargin,n.boxMargin+n.boxTextMargin,N=>x.newLoop(N));break;case r.db.LINETYPE.CRITICAL_OPTION:ot(C,b,n.boxMargin+n.boxTextMargin,n.boxMargin,N=>x.addSectionToLoop(N));break;case r.db.LINETYPE.CRITICAL_END:_=x.endLoop(),await B.drawLoop(a,_,"critical",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case r.db.LINETYPE.BREAK_START:ot(C,b,n.boxMargin,n.boxMargin+n.boxTextMargin,N=>x.newLoop(N));break;case r.db.LINETYPE.BREAK_END:_=x.endLoop(),await B.drawLoop(a,_,"break",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;default:try{it=b.msgModel,it.starty=x.getVerticalPos(),it.sequenceIndex=H,it.sequenceVisible=r.db.showSequenceNumbers();let N=await Oe(a,it);Ye(b,it,N,U,f,y,g),rt.push({messageModel:it,lineStartY:N}),x.models.addMessage(it)}catch(N){X.error("error while drawing message",N)}}[r.db.LINETYPE.SOLID_OPEN,r.db.LINETYPE.DOTTED_OPEN,r.db.LINETYPE.SOLID,r.db.LINETYPE.DOTTED,r.db.LINETYPE.SOLID_CROSS,r.db.LINETYPE.DOTTED_CROSS,r.db.LINETYPE.SOLID_POINT,r.db.LINETYPE.DOTTED_POINT,r.db.LINETYPE.BIDIRECTIONAL_SOLID,r.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(b.type)&&(H=H+Z),U++}X.debug("createdActors",y),X.debug("destroyedActors",g),await Jt(a,f,m,!1);for(let b of rt)await F0(a,b.messageModel,b.lineStartY,r);n.mirrorActors&&await Jt(a,f,m,!0),K.forEach(b=>B.drawBackgroundRect(a,b)),Re(a,f,m,n);for(let b of x.models.boxes)b.height=x.getVerticalPos()-b.y,x.insert(b.x,b.y,b.x+b.width,b.height),b.startx=b.x,b.starty=b.y,b.stopx=b.startx+b.width,b.stopy=b.starty+b.height,b.stroke="rgb(0,0,0, 0.5)",B.drawBox(a,b,n);V&&x.bumpVerticalPos(n.boxMargin);let q=Be(a,f,m,p),{bounds:D}=x.getBounds();D.startx===void 0&&(D.startx=0),D.starty===void 0&&(D.starty=0),D.stopx===void 0&&(D.stopx=0),D.stopy===void 0&&(D.stopy=0);let Q=D.stopy-D.starty;Q<q.maxHeight&&(Q=q.maxHeight);let J=Q+2*n.diagramMarginY;n.mirrorActors&&(J=J-n.boxMargin+n.bottomMarginAdj);let j=D.stopx-D.startx;j<q.maxWidth&&(j=q.maxWidth);let at=j+2*n.diagramMarginX;A&&a.append("text").text(A).attr("x",(D.stopx-D.startx)/2-2*n.diagramMarginX).attr("y",-25),oe(a,J,at,n.useMaxWidth);let S=A?40:0;a.attr("viewBox",D.startx-n.diagramMarginX+" -"+(n.diagramMarginY+S)+" "+at+" "+(J+S)),X.debug("models:",x.models)},"draw");async function Fe(t,e,c){let r={};for(let s of e)if(t.get(s.to)&&t.get(s.from)){let i=t.get(s.to);if(s.placement===c.db.PLACEMENT.LEFTOF&&!i.prevActor||s.placement===c.db.PLACEMENT.RIGHTOF&&!i.nextActor)continue;let o=s.placement!==void 0,h=!o,p=o?Et(n):Tt(n),a=s.wrap?Y.wrapLabel(s.message,n.width-2*n.wrapPadding,p):s.message,y=(nt(a)?await yt(s.message,et()):Y.calculateTextDimensions(a,p)).width+2*n.wrapPadding;h&&s.from===i.nextActor?r[s.to]=k.getMax(r[s.to]||0,y):h&&s.from===i.prevActor?r[s.from]=k.getMax(r[s.from]||0,y):h&&s.from===s.to?(r[s.from]=k.getMax(r[s.from]||0,y/2),r[s.to]=k.getMax(r[s.to]||0,y/2)):s.placement===c.db.PLACEMENT.RIGHTOF?r[s.from]=k.getMax(r[s.from]||0,y):s.placement===c.db.PLACEMENT.LEFTOF?r[i.prevActor]=k.getMax(r[i.prevActor]||0,y):s.placement===c.db.PLACEMENT.OVER&&(i.prevActor&&(r[i.prevActor]=k.getMax(r[i.prevActor]||0,y/2)),i.nextActor&&(r[s.from]=k.getMax(r[s.from]||0,y/2)))}return X.debug("maxMessageWidthPerActor:",r),r}d(Fe,"getMaxMessageWidthPerActor");var z0=d(function(t){let e=0,c=Xt(n);for(let r in t.links){let i=Y.calculateTextDimensions(r,c).width+2*n.wrapPadding+2*n.boxMargin;e<i&&(e=i)}return e},"getRequiredPopupWidth");async function We(t,e,c){let r=0;for(let i of t.keys()){let o=t.get(i);o.wrap&&(o.description=Y.wrapLabel(o.description,n.width-2*n.wrapPadding,Xt(n)));let h=nt(o.description)?await yt(o.description,et()):Y.calculateTextDimensions(o.description,Xt(n));o.width=o.wrap?n.width:k.getMax(n.width,h.width+2*n.wrapPadding),o.height=o.wrap?k.getMax(h.height,n.height):n.height,r=k.getMax(r,o.height)}for(let i in e){let o=t.get(i);if(!o)continue;let h=t.get(o.nextActor);if(!h){let y=e[i]+n.actorMargin-o.width/2;o.margin=k.getMax(y,n.actorMargin);continue}let a=e[i]+n.actorMargin-o.width/2-h.width/2;o.margin=k.getMax(a,n.actorMargin)}let s=0;return c.forEach(i=>{let o=Tt(n),h=i.actorKeys.reduce((f,y)=>f+=t.get(y).width+(t.get(y).margin||0),0);h-=2*n.boxTextMargin,i.wrap&&(i.name=Y.wrapLabel(i.name,h-2*n.wrapPadding,o));let p=Y.calculateTextDimensions(i.name,o);s=k.getMax(p.height,s);let a=k.getMax(h,p.width+2*n.wrapPadding);if(i.margin=n.boxTextMargin,h<a){let f=(a-h)/2;i.margin+=f}}),c.forEach(i=>i.textMaxHeight=s),k.getMax(r,n.height)}d(We,"calculateActorMargins");var H0=d(async function(t,e,c){let r=e.get(t.from),s=e.get(t.to),i=r.x,o=s.x,h=t.wrap&&t.message,p=nt(t.message)?await yt(t.message,et()):Y.calculateTextDimensions(h?Y.wrapLabel(t.message,n.width,Et(n)):t.message,Et(n)),a={width:h?n.width:k.getMax(n.width,p.width+2*n.noteMargin),height:0,startx:r.x,stopx:0,starty:0,stopy:0,message:t.message};return t.placement===c.db.PLACEMENT.RIGHTOF?(a.width=h?k.getMax(n.width,p.width):k.getMax(r.width/2+s.width/2,p.width+2*n.noteMargin),a.startx=i+(r.width+n.actorMargin)/2):t.placement===c.db.PLACEMENT.LEFTOF?(a.width=h?k.getMax(n.width,p.width+2*n.noteMargin):k.getMax(r.width/2+s.width/2,p.width+2*n.noteMargin),a.startx=i-a.width+(r.width-n.actorMargin)/2):t.to===t.from?(p=Y.calculateTextDimensions(h?Y.wrapLabel(t.message,k.getMax(n.width,r.width),Et(n)):t.message,Et(n)),a.width=h?k.getMax(n.width,r.width):k.getMax(r.width,n.width,p.width+2*n.noteMargin),a.startx=i+(r.width-a.width)/2):(a.width=Math.abs(i+r.width/2-(o+s.width/2))+n.actorMargin,a.startx=i<o?i+r.width/2-n.actorMargin/2:o+s.width/2-n.actorMargin/2),h&&(a.message=Y.wrapLabel(t.message,a.width-2*n.wrapPadding,Et(n))),X.debug(`NM:[${a.startx},${a.stopx},${a.starty},${a.stopy}:${a.width},${a.height}=${t.message}]`),a},"buildNoteModel"),K0=d(function(t,e,c){if(![c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN,c.db.LINETYPE.SOLID,c.db.LINETYPE.DOTTED,c.db.LINETYPE.SOLID_CROSS,c.db.LINETYPE.DOTTED_CROSS,c.db.LINETYPE.SOLID_POINT,c.db.LINETYPE.DOTTED_POINT,c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type))return{};let[r,s]=ve(t.from,e),[i,o]=ve(t.to,e),h=r<=i,p=h?s:r,a=h?i:o,f=Math.abs(i-o)>2,y=d(I=>h?-I:I,"adjustValue");t.from===t.to?a=p:(t.activate&&!f&&(a+=y(n.activationWidth/2-1)),[c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN].includes(t.type)||(a+=y(3)),[c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)&&(p-=y(3)));let g=[r,s,i,o],T=Math.abs(p-a);t.wrap&&t.message&&(t.message=Y.wrapLabel(t.message,k.getMax(T+2*n.wrapPadding,n.width),Tt(n)));let m=Y.calculateTextDimensions(t.message,Tt(n));return{width:k.getMax(t.wrap?0:m.width+2*n.wrapPadding,T+2*n.wrapPadding,n.width),height:0,startx:p,stopx:a,starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,g),toBounds:Math.max.apply(null,g)}},"buildMessageModel"),U0=d(async function(t,e,c,r){let s={},i=[],o,h,p;for(let a of t){switch(a.id=Y.random({length:10}),a.type){case r.db.LINETYPE.LOOP_START:case r.db.LINETYPE.ALT_START:case r.db.LINETYPE.OPT_START:case r.db.LINETYPE.PAR_START:case r.db.LINETYPE.PAR_OVER_START:case r.db.LINETYPE.CRITICAL_START:case r.db.LINETYPE.BREAK_START:i.push({id:a.id,msg:a.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case r.db.LINETYPE.ALT_ELSE:case r.db.LINETYPE.PAR_AND:case r.db.LINETYPE.CRITICAL_OPTION:a.message&&(o=i.pop(),s[o.id]=o,s[a.id]=o,i.push(o));break;case r.db.LINETYPE.LOOP_END:case r.db.LINETYPE.ALT_END:case r.db.LINETYPE.OPT_END:case r.db.LINETYPE.PAR_END:case r.db.LINETYPE.CRITICAL_END:case r.db.LINETYPE.BREAK_END:o=i.pop(),s[o.id]=o;break;case r.db.LINETYPE.ACTIVE_START:{let y=e.get(a.from?a.from:a.to.actor),g=Ot(a.from?a.from:a.to.actor).length,T=y.x+y.width/2+(g-1)*n.activationWidth/2,m={startx:T,stopx:T+n.activationWidth,actor:a.from,enabled:!0};x.activations.push(m)}break;case r.db.LINETYPE.ACTIVE_END:{let y=x.activations.map(g=>g.actor).lastIndexOf(a.from);x.activations.splice(y,1).splice(0,1)}break}a.placement!==void 0?(h=await H0(a,e,r),a.noteModel=h,i.forEach(y=>{o=y,o.from=k.getMin(o.from,h.startx),o.to=k.getMax(o.to,h.startx+h.width),o.width=k.getMax(o.width,Math.abs(o.from-o.to))-n.labelBoxWidth})):(p=K0(a,e,r),a.msgModel=p,p.startx&&p.stopx&&i.length>0&&i.forEach(y=>{if(o=y,p.startx===p.stopx){let g=e.get(a.from),T=e.get(a.to);o.from=k.getMin(g.x-p.width/2,g.x-g.width/2,o.from),o.to=k.getMax(T.x+p.width/2,T.x+g.width/2,o.to),o.width=k.getMax(o.width,Math.abs(o.to-o.from))-n.labelBoxWidth}else o.from=k.getMin(p.startx,o.from),o.to=k.getMax(p.stopx,o.to),o.width=k.getMax(o.width,p.width)-n.labelBoxWidth}))}return x.activations=[],X.debug("Loop type widths:",s),s},"calculateLoopBounds"),G0={bounds:x,drawActors:Jt,drawActorsPopup:Be,setConf:Ve,draw:q0},tr={parser:Je,db:Ee,renderer:G0,styles:y0,init:d(({wrap:t})=>{Ee.setWrap(t)},"init")};export{tr as diagram};
//# sourceMappingURL=sequenceDiagram-G6AWOVSC-UJVWCU2P.min.js.map
