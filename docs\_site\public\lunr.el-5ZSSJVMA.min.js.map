{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.el.js"], "sourcesContent": ["/*!\n * Lunr languages, `Greek` language\n * https://github.com/MihaiValentin/lunr-languages\n *\n * Copyright 2023, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.el = function() {\n      this.pipeline.reset();\n\n      if (this.searchPipeline === undefined) {\n        this.pipeline.add(\n          lunr.el.trimmer,\n          lunr.el.normilizer\n        );\n      }\n\n      this.pipeline.add(\n        lunr.el.stopWordFilter,\n        lunr.el.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(\n          lunr.el.stemmer\n        );\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.el.wordCharacters = \"A-Za-zΑαΒβΓγΔδΕεΖζΗηΘθΙιΚκΛλΜμΝνΞξΟοΠπΡρΣσςΤτΥυΦφΧχΨψΩωΆάΈέΉήΊίΌόΎύΏώΪΐΫΰΐΰ\";\n    lunr.el.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.el.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.el.trimmer, 'trimmer-el');\n\n    /* lunr stemmer function */\n    lunr.el.stemmer = (function() {\n      var stepOneExceptions = {\n        'ΦΑΓΙΑ': 'ΦΑ',\n        'ΦΑΓΙΟΥ': 'ΦΑ',\n        'ΦΑΓΙΩΝ': 'ΦΑ',\n        'ΣΚΑΓΙΑ': 'ΣΚΑ',\n        'ΣΚΑΓΙΟΥ': 'ΣΚΑ',\n        'ΣΚΑΓΙΩΝ': 'ΣΚΑ',\n        'ΣΟΓΙΟΥ': 'ΣΟ',\n        'ΣΟΓΙΑ': 'ΣΟ',\n        'ΣΟΓΙΩΝ': 'ΣΟ',\n        'ΤΑΤΟΓΙΑ': 'ΤΑΤΟ',\n        'ΤΑΤΟΓΙΟΥ': 'ΤΑΤΟ',\n        'ΤΑΤΟΓΙΩΝ': 'ΤΑΤΟ',\n        'ΚΡΕΑΣ': 'ΚΡΕ',\n        'ΚΡΕΑΤΟΣ': 'ΚΡΕ',\n        'ΚΡΕΑΤΑ': 'ΚΡΕ',\n        'ΚΡΕΑΤΩΝ': 'ΚΡΕ',\n        'ΠΕΡΑΣ': 'ΠΕΡ',\n        'ΠΕΡΑΤΟΣ': 'ΠΕΡ',\n        'ΠΕΡΑΤΑ': 'ΠΕΡ',\n        'ΠΕΡΑΤΩΝ': 'ΠΕΡ',\n        'ΤΕΡΑΣ': 'ΤΕΡ',\n        'ΤΕΡΑΤΟΣ': 'ΤΕΡ',\n        'ΤΕΡΑΤΑ': 'ΤΕΡ',\n        'ΤΕΡΑΤΩΝ': 'ΤΕΡ',\n        'ΦΩΣ': 'ΦΩ',\n        'ΦΩΤΟΣ': 'ΦΩ',\n        'ΦΩΤΑ': 'ΦΩ',\n        'ΦΩΤΩΝ': 'ΦΩ',\n        'ΚΑΘΕΣΤΩΣ': 'ΚΑΘΕΣΤ',\n        'ΚΑΘΕΣΤΩΤΟΣ': 'ΚΑΘΕΣΤ',\n        'ΚΑΘΕΣΤΩΤΑ': 'ΚΑΘΕΣΤ',\n        'ΚΑΘΕΣΤΩΤΩΝ': 'ΚΑΘΕΣΤ',\n        'ΓΕΓΟΝΟΣ': 'ΓΕΓΟΝ',\n        'ΓΕΓΟΝΟΤΟΣ': 'ΓΕΓΟΝ',\n        'ΓΕΓΟΝΟΤΑ': 'ΓΕΓΟΝ',\n        'ΓΕΓΟΝΟΤΩΝ': 'ΓΕΓΟΝ',\n        'ΕΥΑ': 'ΕΥ'\n      };\n      var protectedWords = [\n        'ΑΚΡΙΒΩΣ',\n        'ΑΛΑ',\n        'ΑΛΛΑ',\n        'ΑΛΛΙΩΣ',\n        'ΑΛΛΟΤΕ',\n        'ΑΜΑ',\n        'ΑΝΩ',\n        'ΑΝΑ',\n        'ΑΝΑΜΕΣΑ',\n        'ΑΝΑΜΕΤΑΞΥ',\n        'ΑΝΕΥ',\n        'ΑΝΤΙ',\n        'ΑΝΤΙΠΕΡΑ',\n        'ΑΝΤΙΟ',\n        'ΑΞΑΦΝΑ',\n        'ΑΠΟ',\n        'ΑΠΟΨΕ',\n        'ΑΡΑ',\n        'ΑΡΑΓΕ',\n        'ΑΥΡΙΟ',\n        'ΑΦΟΙ',\n        'ΑΦΟΥ',\n        'ΑΦΟΤΟΥ',\n        'ΒΡΕ',\n        'ΓΕΙΑ',\n        'ΓΙΑ',\n        'ΓΙΑΤΙ',\n        'ΓΡΑΜΜΑ',\n        'ΔΕΗ',\n        'ΔΕΝ',\n        'ΔΗΛΑΔΗ',\n        'ΔΙΧΩΣ',\n        'ΔΥΟ',\n        'ΕΑΝ',\n        'ΕΓΩ',\n        'ΕΔΩ',\n        'ΕΔΑ',\n        'ΕΙΘΕ',\n        'ΕΙΜΑΙ',\n        'ΕΙΜΑΣΤΕ',\n        'ΕΙΣΑΙ',\n        'ΕΙΣΑΣΤΕ',\n        'ΕΙΝΑΙ',\n        'ΕΙΣΤΕ',\n        'ΕΙΤΕ',\n        'ΕΚΕΙ',\n        'ΕΚΟ',\n        'ΕΛΑ',\n        'ΕΜΑΣ',\n        'ΕΜΕΙΣ',\n        'ΕΝΤΕΛΩΣ',\n        'ΕΝΤΟΣ',\n        'ΕΝΤΩΜΕΤΑΞΥ',\n        'ΕΝΩ',\n        'ΕΞΙ',\n        'ΕΞΙΣΟΥ',\n        'ΕΞΗΣ',\n        'ΕΞΩ',\n        'ΕΟΚ',\n        'ΕΠΑΝΩ',\n        'ΕΠΕΙΔΗ',\n        'ΕΠΕΙΤΑ',\n        'ΕΠΙ',\n        'ΕΠΙΣΗΣ',\n        'ΕΠΟΜΕΝΩΣ',\n        'ΕΠΤΑ',\n        'ΕΣΑΣ',\n        'ΕΣΕΙΣ',\n        'ΕΣΤΩ',\n        'ΕΣΥ',\n        'ΕΣΩ',\n        'ΕΤΣΙ',\n        'ΕΥΓΕ',\n        'ΕΦΕ',\n        'ΕΦΕΞΗΣ',\n        'ΕΧΤΕΣ',\n        'ΕΩΣ',\n        'ΗΔΗ',\n        'ΗΜΙ',\n        'ΗΠΑ',\n        'ΗΤΟΙ',\n        'ΘΕΣ',\n        'ΙΔΙΩΣ',\n        'ΙΔΗ',\n        'ΙΚΑ',\n        'ΙΣΩΣ',\n        'ΚΑΘΕ',\n        'ΚΑΘΕΤΙ',\n        'ΚΑΘΟΛΟΥ',\n        'ΚΑΘΩΣ',\n        'ΚΑΙ',\n        'ΚΑΝ',\n        'ΚΑΠΟΤΕ',\n        'ΚΑΠΟΥ',\n        'ΚΑΤΑ',\n        'ΚΑΤΙ',\n        'ΚΑΤΟΠΙΝ',\n        'ΚΑΤΩ',\n        'ΚΕΙ',\n        'ΚΙΧ',\n        'ΚΚΕ',\n        'ΚΟΛΑΝ',\n        'ΚΥΡΙΩΣ',\n        'ΚΩΣ',\n        'ΜΑΚΑΡΙ',\n        'ΜΑΛΙΣΤΑ',\n        'ΜΑΛΛΟΝ',\n        'ΜΑΙ',\n        'ΜΑΟ',\n        'ΜΑΟΥΣ',\n        'ΜΑΣ',\n        'ΜΕΘΑΥΡΙΟ',\n        'ΜΕΣ',\n        'ΜΕΣΑ',\n        'ΜΕΤΑ',\n        'ΜΕΤΑΞΥ',\n        'ΜΕΧΡΙ',\n        'ΜΗΔΕ',\n        'ΜΗΝ',\n        'ΜΗΠΩΣ',\n        'ΜΗΤΕ',\n        'ΜΙΑ',\n        'ΜΙΑΣ',\n        'ΜΙΣ',\n        'ΜΜΕ',\n        'ΜΟΛΟΝΟΤΙ',\n        'ΜΟΥ',\n        'ΜΠΑ',\n        'ΜΠΑΣ',\n        'ΜΠΟΥΦΑΝ',\n        'ΜΠΡΟΣ',\n        'ΝΑΙ',\n        'ΝΕΣ',\n        'ΝΤΑ',\n        'ΝΤΕ',\n        'ΞΑΝΑ',\n        'ΟΗΕ',\n        'ΟΚΤΩ',\n        'ΟΜΩΣ',\n        'ΟΝΕ',\n        'ΟΠΑ',\n        'ΟΠΟΥ',\n        'ΟΠΩΣ',\n        'ΟΣΟ',\n        'ΟΤΑΝ',\n        'ΟΤΕ',\n        'ΟΤΙ',\n        'ΟΥΤΕ',\n        'ΟΧΙ',\n        'ΠΑΛΙ',\n        'ΠΑΝ',\n        'ΠΑΝΟ',\n        'ΠΑΝΤΟΤΕ',\n        'ΠΑΝΤΟΥ',\n        'ΠΑΝΤΩΣ',\n        'ΠΑΝΩ',\n        'ΠΑΡΑ',\n        'ΠΕΡΑ',\n        'ΠΕΡΙ',\n        'ΠΕΡΙΠΟΥ',\n        'ΠΙΑ',\n        'ΠΙΟ',\n        'ΠΙΣΩ',\n        'ΠΛΑΙ',\n        'ΠΛΕΟΝ',\n        'ΠΛΗΝ',\n        'ΠΟΤΕ',\n        'ΠΟΥ',\n        'ΠΡΟ',\n        'ΠΡΟΣ',\n        'ΠΡΟΧΤΕΣ',\n        'ΠΡΟΧΘΕΣ',\n        'ΡΟΔΙ',\n        'ΠΩΣ',\n        'ΣΑΙ',\n        'ΣΑΣ',\n        'ΣΑΝ',\n        'ΣΕΙΣ',\n        'ΣΙΑ',\n        'ΣΚΙ',\n        'ΣΟΙ',\n        'ΣΟΥ',\n        'ΣΡΙ',\n        'ΣΥΝ',\n        'ΣΥΝΑΜΑ',\n        'ΣΧΕΔΟΝ',\n        'ΤΑΔΕ',\n        'ΤΑΞΙ',\n        'ΤΑΧΑ',\n        'ΤΕΙ',\n        'ΤΗΝ',\n        'ΤΗΣ',\n        'ΤΙΠΟΤΑ',\n        'ΤΙΠΟΤΕ',\n        'ΤΙΣ',\n        'ΤΟΝ',\n        'ΤΟΤΕ',\n        'ΤΟΥ',\n        'ΤΟΥΣ',\n        'ΤΣΑ',\n        'ΤΣΕ',\n        'ΤΣΙ',\n        'ΤΣΟΥ',\n        'ΤΩΝ',\n        'ΥΠΟ',\n        'ΥΠΟΨΗ',\n        'ΥΠΟΨΙΝ',\n        'ΥΣΤΕΡΑ',\n        'ΦΕΤΟΣ',\n        'ΦΙΣ',\n        'ΦΠΑ',\n        'ΧΑΦ',\n        'ΧΘΕΣ',\n        'ΧΤΕΣ',\n        'ΧΩΡΙΣ',\n        'ΩΣ',\n        'ΩΣΑΝ',\n        'ΩΣΟΤΟΥ',\n        'ΩΣΠΟΥ',\n        'ΩΣΤΕ',\n        'ΩΣΤΟΣΟ'\n      ];\n\n      var alphabet = new RegExp('^[ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ]+$');\n\n      function isGreek(word) {\n        return alphabet.test(word);\n      }\n\n      function endsInVowel(word) {\n        return /[ΑΕΗΙΟΥΩ]$/.test(word);\n      }\n\n      function endsInVowel2(word) {\n        return /[ΑΕΗΙΟΩ]$/.test(word);\n      }\n\n      function stem(word) {\n\n        var stemmedWord = word;\n\n        if (word.length < 3) {\n          return stemmedWord;\n        }\n\n        if (!isGreek(word)) {\n          return stemmedWord;\n        }\n\n        if (protectedWords.indexOf(word) >= 0) {\n          return stemmedWord;\n        }\n\n        //step 1\n        var stepOneRegExp = new RegExp('(.*)(' + Object.keys(stepOneExceptions).join('|') + ')$');\n        var match = stepOneRegExp.exec(stemmedWord);\n\n        if (match !== null) {\n          stemmedWord = match[1] + stepOneExceptions[match[2]];\n        }\n        //step 2\n        //2a\n        if ((match = /^(.+?)(ΑΔΕΣ|ΑΔΩΝ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (!/(ΟΚ|ΜΑΜ|ΜΑΝ|ΜΠΑΜΠ|ΠΑΤΕΡ|ΓΙΑΓΙ|ΝΤΑΝΤ|ΚΥΡ|ΘΕΙ|ΠΕΘΕΡ|ΜΟΥΣΑΜ|ΚΑΠΛΑΜ|ΠΑΡ|ΨΑΡ|ΤΖΟΥΡ|ΤΑΜΠΟΥΡ|ΓΑΛΑΤ|ΦΑΦΛΑΤ)$/.test(match[1])) {\n            stemmedWord += 'ΑΔ';\n          }\n        }\n\n        //2b\n        if ((match = /^(.+?)(ΕΔΕΣ|ΕΔΩΝ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/(ΟΠ|ΙΠ|ΕΜΠ|ΥΠ|ΓΗΠ|ΔΑΠ|ΚΡΑΣΠ|ΜΙΛ)$/.test(match[1])) {\n            stemmedWord += 'ΕΔ';\n          }\n        }\n\n        //2c\n        if ((match = /^(.+?)(ΟΥΔΕΣ|ΟΥΔΩΝ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/(ΑΡΚ|ΚΑΛΙΑΚ|ΠΕΤΑΛ|ΛΙΧ|ΠΛΕΞ|ΣΚ|Σ|ΦΛ|ΦΡ|ΒΕΛ|ΛΟΥΛ|ΧΝ|ΣΠ|ΤΡΑΓ|ΦΕ)$/.test(match[1])) {\n            stemmedWord += 'ΟΥΔ';\n          }\n        }\n\n        //2d\n        if ((match = /^(.+?)(ΕΩΣ|ΕΩΝ|ΕΑΣ|ΕΑ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(Θ|Δ|ΕΛ|ΓΑΛ|Ν|Π|ΙΔ|ΠΑΡ|ΣΤΕΡ|ΟΡΦ|ΑΝΔΡ|ΑΝΤΡ)$/.test(match[1])) {\n            stemmedWord += 'Ε';\n          }\n        }\n\n        //step 3\n        //3a\n        if ((match = /^(.+?)(ΕΙΟ|ΕΙΟΣ|ΕΙΟΙ|ΕΙΑ|ΕΙΑΣ|ΕΙΕΣ|ΕΙΟΥ|ΕΙΟΥΣ|ΕΙΩΝ)$/.exec(stemmedWord)) !== null && match[1].length > 4) {\n          stemmedWord = match[1];\n        }\n\n        //3b\n        if ((match = /^(.+?)(ΙΟΥΣ|ΙΑΣ|ΙΕΣ|ΙΟΣ|ΙΟΥ|ΙΟΙ|ΙΩΝ|ΙΟΝ|ΙΑ|ΙΟ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (endsInVowel(stemmedWord) || stemmedWord.length < 2 || /^(ΑΓ|ΑΓΓΕΛ|ΑΓΡ|ΑΕΡ|ΑΘΛ|ΑΚΟΥΣ|ΑΞ|ΑΣ|Β|ΒΙΒΛ|ΒΥΤ|Γ|ΓΙΑΓ|ΓΩΝ|Δ|ΔΑΝ|ΔΗΛ|ΔΗΜ|ΔΟΚΙΜ|ΕΛ|ΖΑΧΑΡ|ΗΛ|ΗΠ|ΙΔ|ΙΣΚ|ΙΣΤ|ΙΟΝ|ΙΩΝ|ΚΙΜΩΛ|ΚΟΛΟΝ|ΚΟΡ|ΚΤΗΡ|ΚΥΡ|ΛΑΓ|ΛΟΓ|ΜΑΓ|ΜΠΑΝ|ΜΠΡ|ΝΑΥΤ|ΝΟΤ|ΟΠΑΛ|ΟΞ|ΟΡ|ΟΣ|ΠΑΝΑΓ|ΠΑΤΡ|ΠΗΛ|ΠΗΝ|ΠΛΑΙΣ|ΠΟΝΤ|ΡΑΔ|ΡΟΔ|ΣΚ|ΣΚΟΡΠ|ΣΟΥΝ|ΣΠΑΝ|ΣΤΑΔ|ΣΥΡ|ΤΗΛ|ΤΙΜ|ΤΟΚ|ΤΟΠ|ΤΡΟΧ|ΦΙΛ|ΦΩΤ|Χ|ΧΙΛ|ΧΡΩΜ|ΧΩΡ)$/.test(match[1])) {\n            stemmedWord += 'Ι';\n          }\n          if (/^(ΠΑΛ)$/.test(match[1])) {\n            stemmedWord += 'ΑΙ';\n          }\n        }\n\n        //step 4\n        if ((match = /^(.+?)(ΙΚΟΣ|ΙΚΟΝ|ΙΚΕΙΣ|ΙΚΟΙ|ΙΚΕΣ|ΙΚΟΥΣ|ΙΚΗ|ΙΚΗΣ|ΙΚΟ|ΙΚΑ|ΙΚΟΥ|ΙΚΩΝ|ΙΚΩΣ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (endsInVowel(stemmedWord) || /^(ΑΔ|ΑΛ|ΑΜΑΝ|ΑΜΕΡ|ΑΜΜΟΧΑΛ|ΑΝΗΘ|ΑΝΤΙΔ|ΑΠΛ|ΑΤΤ|ΑΦΡ|ΒΑΣ|ΒΡΩΜ|ΓΕΝ|ΓΕΡ|Δ|ΔΙΚΑΝ|ΔΥΤ|ΕΙΔ|ΕΝΔ|ΕΞΩΔ|ΗΘ|ΘΕΤ|ΚΑΛΛΙΝ|ΚΑΛΠ|ΚΑΤΑΔ|ΚΟΥΖΙΝ|ΚΡ|ΚΩΔ|ΛΟΓ|Μ|ΜΕΡ|ΜΟΝΑΔ|ΜΟΥΛ|ΜΟΥΣ|ΜΠΑΓΙΑΤ|ΜΠΑΝ|ΜΠΟΛ|ΜΠΟΣ|ΜΥΣΤ|Ν|ΝΙΤ|ΞΙΚ|ΟΠΤ|ΠΑΝ|ΠΕΤΣ|ΠΙΚΑΝΤ|ΠΙΤΣ|ΠΛΑΣΤ|ΠΛΙΑΤΣ|ΠΟΝΤ|ΠΟΣΤΕΛΝ|ΠΡΩΤΟΔ|ΣΕΡΤ|ΣΗΜΑΝΤ|ΣΤΑΤ|ΣΥΝΑΔ|ΣΥΝΟΜΗΛ|ΤΕΛ|ΤΕΧΝ|ΤΡΟΠ|ΤΣΑΜ|ΥΠΟΔ|Φ|ΦΙΛΟΝ|ΦΥΛΟΔ|ΦΥΣ|ΧΑΣ)$/.test(match[1]) || /(ΦΟΙΝ)$/.test(match[1])) {\n            stemmedWord += 'ΙΚ';\n          }\n        }\n\n        //step 5\n        //5a\n        if (stemmedWord === 'ΑΓΑΜΕ') {\n          stemmedWord = 'ΑΓΑΜ';\n        }\n        if ((match = /^(.+?)(ΑΓΑΜΕ|ΗΣΑΜΕ|ΟΥΣΑΜΕ|ΗΚΑΜΕ|ΗΘΗΚΑΜΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n        }\n        if ((match = /^(.+?)(ΑΜΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(ΑΝΑΠ|ΑΠΟΘ|ΑΠΟΚ|ΑΠΟΣΤ|ΒΟΥΒ|ΞΕΘ|ΟΥΛ|ΠΕΘ|ΠΙΚΡ|ΠΟΤ|ΣΙΧ|Χ)$/.test(match[1])) {\n            stemmedWord += 'ΑΜ';\n          }\n        }\n\n        //5b\n        if ((match = /^(.+?)(ΑΓΑΝΕ|ΗΣΑΝΕ|ΟΥΣΑΝΕ|ΙΟΝΤΑΝΕ|ΙΟΤΑΝΕ|ΙΟΥΝΤΑΝΕ|ΟΝΤΑΝΕ|ΟΤΑΝΕ|ΟΥΝΤΑΝΕ|ΗΚΑΝΕ|ΗΘΗΚΑΝΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(ΤΡ|ΤΣ)$/.test(match[1])) {\n            stemmedWord += 'ΑΓΑΝ';\n          }\n        }\n        if ((match = /^(.+?)(ΑΝΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (endsInVowel2(stemmedWord) || /^(ΒΕΤΕΡ|ΒΟΥΛΚ|ΒΡΑΧΜ|Γ|ΔΡΑΔΟΥΜ|Θ|ΚΑΛΠΟΥΖ|ΚΑΣΤΕΛ|ΚΟΡΜΟΡ|ΛΑΟΠΛ|ΜΩΑΜΕΘ|Μ|ΜΟΥΣΟΥΛΜΑΝ|ΟΥΛ|Π|ΠΕΛΕΚ|ΠΛ|ΠΟΛΙΣ|ΠΟΡΤΟΛ|ΣΑΡΑΚΑΤΣ|ΣΟΥΛΤ|ΤΣΑΡΛΑΤ|ΟΡΦ|ΤΣΙΓΓ|ΤΣΟΠ|ΦΩΤΟΣΤΕΦ|Χ|ΨΥΧΟΠΛ|ΑΓ|ΟΡΦ|ΓΑΛ|ΓΕΡ|ΔΕΚ|ΔΙΠΛ|ΑΜΕΡΙΚΑΝ|ΟΥΡ|ΠΙΘ|ΠΟΥΡΙΤ|Σ|ΖΩΝΤ|ΙΚ|ΚΑΣΤ|ΚΟΠ|ΛΙΧ|ΛΟΥΘΗΡ|ΜΑΙΝΤ|ΜΕΛ|ΣΙΓ|ΣΠ|ΣΤΕΓ|ΤΡΑΓ|ΤΣΑΓ|Φ|ΕΡ|ΑΔΑΠ|ΑΘΙΓΓ|ΑΜΗΧ|ΑΝΙΚ|ΑΝΟΡΓ|ΑΠΗΓ|ΑΠΙΘ|ΑΤΣΙΓΓ|ΒΑΣ|ΒΑΣΚ|ΒΑΘΥΓΑΛ|ΒΙΟΜΗΧ|ΒΡΑΧΥΚ|ΔΙΑΤ|ΔΙΑΦ|ΕΝΟΡΓ|ΘΥΣ|ΚΑΠΝΟΒΙΟΜΗΧ|ΚΑΤΑΓΑΛ|ΚΛΙΒ|ΚΟΙΛΑΡΦ|ΛΙΒ|ΜΕΓΛΟΒΙΟΜΗΧ|ΜΙΚΡΟΒΙΟΜΗΧ|ΝΤΑΒ|ΞΗΡΟΚΛΙΒ|ΟΛΙΓΟΔΑΜ|ΟΛΟΓΑΛ|ΠΕΝΤΑΡΦ|ΠΕΡΗΦ|ΠΕΡΙΤΡ|ΠΛΑΤ|ΠΟΛΥΔΑΠ|ΠΟΛΥΜΗΧ|ΣΤΕΦ|ΤΑΒ|ΤΕΤ|ΥΠΕΡΗΦ|ΥΠΟΚΟΠ|ΧΑΜΗΛΟΔΑΠ|ΨΗΛΟΤΑΒ)$/.test(match[1])) {\n            stemmedWord += 'ΑΝ';\n          }\n        }\n\n        //5c\n        if ((match = /^(.+?)(ΗΣΕΤΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n        }\n\n        if ((match = /^(.+?)(ΕΤΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (endsInVowel2(stemmedWord) || /(ΟΔ|ΑΙΡ|ΦΟΡ|ΤΑΘ|ΔΙΑΘ|ΣΧ|ΕΝΔ|ΕΥΡ|ΤΙΘ|ΥΠΕΡΘ|ΡΑΘ|ΕΝΘ|ΡΟΘ|ΣΘ|ΠΥΡ|ΑΙΝ|ΣΥΝΔ|ΣΥΝ|ΣΥΝΘ|ΧΩΡ|ΠΟΝ|ΒΡ|ΚΑΘ|ΕΥΘ|ΕΚΘ|ΝΕΤ|ΡΟΝ|ΑΡΚ|ΒΑΡ|ΒΟΛ|ΩΦΕΛ)$/.test(match[1]) || /^(ΑΒΑΡ|ΒΕΝ|ΕΝΑΡ|ΑΒΡ|ΑΔ|ΑΘ|ΑΝ|ΑΠΛ|ΒΑΡΟΝ|ΝΤΡ|ΣΚ|ΚΟΠ|ΜΠΟΡ|ΝΙΦ|ΠΑΓ|ΠΑΡΑΚΑΛ|ΣΕΡΠ|ΣΚΕΛ|ΣΥΡΦ|ΤΟΚ|Υ|Δ|ΕΜ|ΘΑΡΡ|Θ)$/.test(match[1])) {\n            stemmedWord += 'ΕΤ';\n          }\n        }\n\n        //5d\n        if ((match = /^(.+?)(ΟΝΤΑΣ|ΩΝΤΑΣ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^ΑΡΧ$/.test(match[1])) {\n            stemmedWord += 'ΟΝΤ';\n          }\n          if (/ΚΡΕ$/.test(match[1])) {\n            stemmedWord += 'ΩΝΤ';\n          }\n        }\n\n        //5e\n        if ((match = /^(.+?)(ΟΜΑΣΤΕ|ΙΟΜΑΣΤΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^ΟΝ$/.test(match[1])) {\n            stemmedWord += 'ΟΜΑΣΤ';\n          }\n        }\n\n        //5f\n        if ((match = /^(.+?)(ΙΕΣΤΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(Π|ΑΠ|ΣΥΜΠ|ΑΣΥΜΠ|ΑΚΑΤΑΠ|ΑΜΕΤΑΜΦ)$/.test(match[1])) {\n            stemmedWord += 'ΙΕΣΤ';\n          }\n        }\n\n        if ((match = /^(.+?)(ΕΣΤΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(ΑΛ|ΑΡ|ΕΚΤΕΛ|Ζ|Μ|Ξ|ΠΑΡΑΚΑΛ|ΠΡΟ|ΝΙΣ)$/.test(match[1])) {\n            stemmedWord += 'ΕΣΤ';\n          }\n        }\n\n        //5g\n        if ((match = /^(.+?)(ΗΘΗΚΑ|ΗΘΗΚΕΣ|ΗΘΗΚΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n        }\n\n        if ((match = /^(.+?)(ΗΚΑ|ΗΚΕΣ|ΗΚΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/(ΣΚΩΛ|ΣΚΟΥΛ|ΝΑΡΘ|ΣΦ|ΟΘ|ΠΙΘ)$/.test(match[1]) || /^(ΔΙΑΘ|Θ|ΠΑΡΑΚΑΤΑΘ|ΠΡΟΣΘ|ΣΥΝΘ)$/.test(match[1])) {\n            stemmedWord += 'ΗΚ';\n          }\n        }\n\n        //5h\n        if ((match = /^(.+?)(ΟΥΣΑ|ΟΥΣΕΣ|ΟΥΣΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (endsInVowel(stemmedWord) || /^(ΦΑΡΜΑΚ|ΧΑΔ|ΑΓΚ|ΑΝΑΡΡ|ΒΡΟΜ|ΕΚΛΙΠ|ΛΑΜΠΙΔ|ΛΕΧ|Μ|ΠΑΤ|Ρ|Λ|ΜΕΔ|ΜΕΣΑΖ|ΥΠΟΤΕΙΝ|ΑΜ|ΑΙΘ|ΑΝΗΚ|ΔΕΣΠΟΖ|ΕΝΔΙΑΦΕΡ)$/.test(match[1]) || /(ΠΟΔΑΡ|ΒΛΕΠ|ΠΑΝΤΑΧ|ΦΡΥΔ|ΜΑΝΤΙΛ|ΜΑΛΛ|ΚΥΜΑΤ|ΛΑΧ|ΛΗΓ|ΦΑΓ|ΟΜ|ΠΡΩΤ)$/.test(match[1])) {\n            stemmedWord += 'ΟΥΣ';\n          }\n        }\n\n        //5i\n        if ((match = /^(.+?)(ΑΓΑ|ΑΓΕΣ|ΑΓΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(ΑΒΑΣΤ|ΠΟΛΥΦ|ΑΔΗΦ|ΠΑΜΦ|Ρ|ΑΣΠ|ΑΦ|ΑΜΑΛ|ΑΜΑΛΛΙ|ΑΝΥΣΤ|ΑΠΕΡ|ΑΣΠΑΡ|ΑΧΑΡ|ΔΕΡΒΕΝ|ΔΡΟΣΟΠ|ΞΕΦ|ΝΕΟΠ|ΝΟΜΟΤ|ΟΛΟΠ|ΟΜΟΤ|ΠΡΟΣΤ|ΠΡΟΣΩΠΟΠ|ΣΥΜΠ|ΣΥΝΤ|Τ|ΥΠΟΤ|ΧΑΡ|ΑΕΙΠ|ΑΙΜΟΣΤ|ΑΝΥΠ|ΑΠΟΤ|ΑΡΤΙΠ|ΔΙΑΤ|ΕΝ|ΕΠΙΤ|ΚΡΟΚΑΛΟΠ|ΣΙΔΗΡΟΠ|Λ|ΝΑΥ|ΟΥΛΑΜ|ΟΥΡ|Π|ΤΡ|Μ)$/.test(match[1]) || (/(ΟΦ|ΠΕΛ|ΧΟΡΤ|ΛΛ|ΣΦ|ΡΠ|ΦΡ|ΠΡ|ΛΟΧ|ΣΜΗΝ)$/.test(match[1]) && !/^(ΨΟΦ|ΝΑΥΛΟΧ)$/.test(match[1])) || /(ΚΟΛΛ)$/.test(match[1])) {\n            stemmedWord += 'ΑΓ';\n          }\n        }\n\n        //5j\n        if ((match = /^(.+?)(ΗΣΕ|ΗΣΟΥ|ΗΣΑ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(Ν|ΧΕΡΣΟΝ|ΔΩΔΕΚΑΝ|ΕΡΗΜΟΝ|ΜΕΓΑΛΟΝ|ΕΠΤΑΝ|Ι)$/.test(match[1])) {\n            stemmedWord += 'ΗΣ';\n          }\n        }\n\n        //5k\n        if ((match = /^(.+?)(ΗΣΤΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(ΑΣΒ|ΣΒ|ΑΧΡ|ΧΡ|ΑΠΛ|ΑΕΙΜΝ|ΔΥΣΧΡ|ΕΥΧΡ|ΚΟΙΝΟΧΡ|ΠΑΛΙΜΨ)$/.test(match[1])) {\n            stemmedWord += 'ΗΣΤ';\n          }\n        }\n\n        //5l\n        if ((match = /^(.+?)(ΟΥΝΕ|ΗΣΟΥΝΕ|ΗΘΟΥΝΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(Ν|Ρ|ΣΠΙ|ΣΤΡΑΒΟΜΟΥΤΣ|ΚΑΚΟΜΟΥΤΣ|ΕΞΩΝ)$/.test(match[1])) {\n            stemmedWord += 'ΟΥΝ';\n          }\n        }\n\n        //5m\n        if ((match = /^(.+?)(ΟΥΜΕ|ΗΣΟΥΜΕ|ΗΘΟΥΜΕ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1];\n          if (/^(ΠΑΡΑΣΟΥΣ|Φ|Χ|ΩΡΙΟΠΛ|ΑΖ|ΑΛΛΟΣΟΥΣ|ΑΣΟΥΣ)$/.test(match[1])) {\n            stemmedWord += 'ΟΥΜ';\n          }\n        }\n\n        //step 6\n        //6a\n        if ((match = /^(.+?)(ΜΑΤΟΙ|ΜΑΤΟΥΣ|ΜΑΤΟ|ΜΑΤΑ|ΜΑΤΩΣ|ΜΑΤΩΝ|ΜΑΤΟΣ|ΜΑΤΕΣ|ΜΑΤΗ|ΜΑΤΗΣ|ΜΑΤΟΥ)$/.exec(stemmedWord)) != null) {\n          stemmedWord = match[1] + 'Μ';\n          if (/^(ΓΡΑΜ)$/.test(match[1])) {\n            stemmedWord += 'Α';\n          } else if (/^(ΓΕ|ΣΤΑ)$/.test(match[1])) {\n            stemmedWord += 'ΑΤ';\n          }\n        }\n\n        //6b\n        if ((match = /^(.+?)(ΟΥΑ)$/.exec(stemmedWord)) !== null) {\n          stemmedWord = match[1] + 'ΟΥ';\n        }\n\n        //Handle long words\n        if (word.length === stemmedWord.length) {\n          if ((match = /^(.+?)(Α|ΑΓΑΤΕ|ΑΓΑΝ|ΑΕΙ|ΑΜΑΙ|ΑΝ|ΑΣ|ΑΣΑΙ|ΑΤΑΙ|ΑΩ|Ε|ΕΙ|ΕΙΣ|ΕΙΤΕ|ΕΣΑΙ|ΕΣ|ΕΤΑΙ|Ι|ΙΕΜΑΙ|ΙΕΜΑΣΤΕ|ΙΕΤΑΙ|ΙΕΣΑΙ|ΙΕΣΑΣΤΕ|ΙΟΜΑΣΤΑΝ|ΙΟΜΟΥΝ|ΙΟΜΟΥΝΑ|ΙΟΝΤΑΝ|ΙΟΝΤΟΥΣΑΝ|ΙΟΣΑΣΤΑΝ|ΙΟΣΑΣΤΕ|ΙΟΣΟΥΝ|ΙΟΣΟΥΝΑ|ΙΟΤΑΝ|ΙΟΥΜΑ|ΙΟΥΜΑΣΤΕ|ΙΟΥΝΤΑΙ|ΙΟΥΝΤΑΝ|Η|ΗΔΕΣ|ΗΔΩΝ|ΗΘΕΙ|ΗΘΕΙΣ|ΗΘΕΙΤΕ|ΗΘΗΚΑΤΕ|ΗΘΗΚΑΝ|ΗΘΟΥΝ|ΗΘΩ|ΗΚΑΤΕ|ΗΚΑΝ|ΗΣ|ΗΣΑΝ|ΗΣΑΤΕ|ΗΣΕΙ|ΗΣΕΣ|ΗΣΟΥΝ|ΗΣΩ|Ο|ΟΙ|ΟΜΑΙ|ΟΜΑΣΤΑΝ|ΟΜΟΥΝ|ΟΜΟΥΝΑ|ΟΝΤΑΙ|ΟΝΤΑΝ|ΟΝΤΟΥΣΑΝ|ΟΣ|ΟΣΑΣΤΑΝ|ΟΣΑΣΤΕ|ΟΣΟΥΝ|ΟΣΟΥΝΑ|ΟΤΑΝ|ΟΥ|ΟΥΜΑΙ|ΟΥΜΑΣΤΕ|ΟΥΝ|ΟΥΝΤΑΙ|ΟΥΝΤΑΝ|ΟΥΣ|ΟΥΣΑΝ|ΟΥΣΑΤΕ|Υ||ΥΑ|ΥΣ|Ω|ΩΝ|ΟΙΣ)$/.exec(stemmedWord)) !== null) {\n            stemmedWord = match[1];\n          }\n        }\n\n        //step 7\n        if ((match = /^(.+?)(ΕΣΤΕΡ|ΕΣΤΑΤ|ΟΤΕΡ|ΟΤΑΤ|ΥΤΕΡ|ΥΤΑΤ|ΩΤΕΡ|ΩΤΑΤ)$/.exec(stemmedWord)) != null) {\n          if (!/^(ΕΞ|ΕΣ|ΑΝ|ΚΑΤ|Κ|ΠΡ)$/.test(match[1])) {\n            stemmedWord = match[1];\n          }\n          if (/^(ΚΑ|Μ|ΕΛΕ|ΛΕ|ΔΕ)$/.test(match[1])) {\n            stemmedWord += 'ΥΤ';\n          }\n        }\n\n        return stemmedWord;\n      }\n\n      return function(token) {\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            return stem(word.toUpperCase()).toLowerCase();\n          });\n        } else {\n          return stem(token.toUpperCase()).toLowerCase();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.el.stemmer, 'stemmer-el');\n\n    /* lunr stopWordFilter function */\n    lunr.el.stopWordFilter = lunr.generateStopWordFilter('αλλα αν αντι απο αυτα αυτεσ αυτη αυτο αυτοι αυτοσ αυτουσ αυτων για δε δεν εαν ειμαι ειμαστε ειναι εισαι ειστε εκεινα εκεινεσ εκεινη εκεινο εκεινοι εκεινοσ εκεινουσ εκεινων ενω επι η θα ισωσ κ και κατα κι μα με μετα μη μην να ο οι ομωσ οπωσ οσο οτι παρα ποια ποιεσ ποιο ποιοι ποιοσ ποιουσ ποιων που προσ πωσ σε στη στην στο στον τα την τησ το τον τοτε του των ωσ'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.el.stopWordFilter, 'stopWordFilter-el');\n\n    /* lunr normilizer function */\n    lunr.el.normilizer = (function() {\n      var accentMap = {\n        \"Ά\": \"Α\",\n        \"ά\": \"α\",\n        \"Έ\": \"Ε\",\n        \"έ\": \"ε\",\n        \"Ή\": \"Η\",\n        \"ή\": \"η\",\n        \"Ί\": \"Ι\",\n        \"ί\": \"ι\",\n        \"Ό\": \"Ο\",\n        \"ο\": \"ο\",\n        \"Ύ\": \"Υ\",\n        \"ύ\": \"υ\",\n        \"Ώ\": \"Ω\",\n        \"ώ\": \"ω\",\n        \"Ϊ\": \"Ι\",\n        \"ϊ\": \"ι\",\n        \"Ϋ\": \"Υ\",\n        \"ϋ\": \"υ\",\n        \"ΐ\": \"ι\",\n        \"ΰ\": \"υ\"\n      };\n\n      return function(token) {\n        if (typeof token.update === \"function\") {\n          return token.update(function(term) {\n            var ret = \"\";\n            for (var i = 0; i < term.length; i++) {\n              ret += accentMap[term.charAt(i)] || term.charAt(i);\n            }\n            return ret;\n          });\n        } else {\n          var ret = \"\";\n          for (var i = 0; i < token.length; i++) {\n            ret += accentMap[token.charAt(i)] || token.charAt(i);\n          }\n          return ret;\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.el.normilizer, 'normilizer-el');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EAEhB,KAAK,iBAAmB,QAC1B,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,UACV,EAGF,KAAK,SAAS,IACZA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAClBA,EAAK,GAAG,OACV,EAEJ,EAGAA,EAAK,GAAG,eAAiB,uaACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAC5B,IAAIC,EAAoB,CACtB,+BAAS,eACT,qCAAU,eACV,qCAAU,eACV,qCAAU,qBACV,2CAAW,qBACX,2CAAW,qBACX,qCAAU,eACV,+BAAS,eACT,qCAAU,eACV,2CAAW,2BACX,iDAAY,2BACZ,iDAAY,2BACZ,+BAAS,qBACT,2CAAW,qBACX,qCAAU,qBACV,2CAAW,qBACX,+BAAS,qBACT,2CAAW,qBACX,qCAAU,qBACV,2CAAW,qBACX,+BAAS,qBACT,2CAAW,qBACX,qCAAU,qBACV,2CAAW,qBACX,mBAAO,eACP,+BAAS,eACT,yBAAQ,eACR,+BAAS,eACT,iDAAY,uCACZ,6DAAc,uCACd,uDAAa,uCACb,6DAAc,uCACd,2CAAW,iCACX,uDAAa,iCACb,iDAAY,iCACZ,uDAAa,iCACb,mBAAO,cACT,EACIC,EAAiB,CACnB,6CACA,qBACA,2BACA,uCACA,uCACA,qBACA,qBACA,qBACA,6CACA,yDACA,2BACA,2BACA,mDACA,iCACA,uCACA,qBACA,iCACA,qBACA,iCACA,iCACA,2BACA,2BACA,uCACA,qBACA,2BACA,qBACA,iCACA,uCACA,qBACA,qBACA,uCACA,iCACA,qBACA,qBACA,qBACA,qBACA,qBACA,2BACA,iCACA,6CACA,iCACA,6CACA,iCACA,iCACA,2BACA,2BACA,qBACA,qBACA,2BACA,iCACA,6CACA,iCACA,+DACA,qBACA,qBACA,uCACA,2BACA,qBACA,qBACA,iCACA,uCACA,uCACA,qBACA,uCACA,mDACA,2BACA,2BACA,iCACA,2BACA,qBACA,qBACA,2BACA,2BACA,qBACA,uCACA,iCACA,qBACA,qBACA,qBACA,qBACA,2BACA,qBACA,iCACA,qBACA,qBACA,2BACA,2BACA,uCACA,6CACA,iCACA,qBACA,qBACA,uCACA,iCACA,2BACA,2BACA,6CACA,2BACA,qBACA,qBACA,qBACA,iCACA,uCACA,qBACA,uCACA,6CACA,uCACA,qBACA,qBACA,iCACA,qBACA,mDACA,qBACA,2BACA,2BACA,uCACA,iCACA,2BACA,qBACA,iCACA,2BACA,qBACA,2BACA,qBACA,qBACA,mDACA,qBACA,qBACA,2BACA,6CACA,iCACA,qBACA,qBACA,qBACA,qBACA,2BACA,qBACA,2BACA,2BACA,qBACA,qBACA,2BACA,2BACA,qBACA,2BACA,qBACA,qBACA,2BACA,qBACA,2BACA,qBACA,2BACA,6CACA,uCACA,uCACA,2BACA,2BACA,2BACA,2BACA,6CACA,qBACA,qBACA,2BACA,2BACA,iCACA,2BACA,2BACA,qBACA,qBACA,2BACA,6CACA,6CACA,2BACA,qBACA,qBACA,qBACA,qBACA,2BACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,uCACA,uCACA,2BACA,2BACA,2BACA,qBACA,qBACA,qBACA,uCACA,uCACA,qBACA,qBACA,2BACA,qBACA,2BACA,qBACA,qBACA,qBACA,2BACA,qBACA,qBACA,iCACA,uCACA,uCACA,iCACA,qBACA,qBACA,qBACA,2BACA,2BACA,iCACA,eACA,2BACA,uCACA,iCACA,2BACA,sCACF,EAEIC,EAAW,IAAI,OAAO,uJAA+B,EAEzD,SAASC,EAAQC,EAAM,CACrB,OAAOF,EAAS,KAAKE,CAAI,CAC3B,CAEA,SAASC,EAAYD,EAAM,CACzB,MAAO,aAAa,KAAKA,CAAI,CAC/B,CAEA,SAASE,EAAaF,EAAM,CAC1B,MAAO,YAAY,KAAKA,CAAI,CAC9B,CAEA,SAASG,EAAKH,EAAM,CAElB,IAAII,EAAcJ,EAUlB,GARIA,EAAK,OAAS,GAId,CAACD,EAAQC,CAAI,GAIbH,EAAe,QAAQG,CAAI,GAAK,EAClC,OAAOI,EAIT,IAAIC,EAAgB,IAAI,OAAO,QAAU,OAAO,KAAKT,CAAiB,EAAE,KAAK,GAAG,EAAI,IAAI,EACpFU,EAAQD,EAAc,KAAKD,CAAW,EAE1C,OAAIE,IAAU,OACZF,EAAcE,EAAM,CAAC,EAAIV,EAAkBU,EAAM,CAAC,CAAC,IAIhDA,EAAQ,qBAAqB,KAAKF,CAAW,KAAO,OACvDA,EAAcE,EAAM,CAAC,EAChB,uGAAuG,KAAKA,EAAM,CAAC,CAAC,IACvHF,GAAe,kBAKdE,EAAQ,qBAAqB,KAAKF,CAAW,KAAO,OACvDA,EAAcE,EAAM,CAAC,EACjB,oCAAoC,KAAKA,EAAM,CAAC,CAAC,IACnDF,GAAe,kBAKdE,EAAQ,uBAAuB,KAAKF,CAAW,KAAO,OACzDA,EAAcE,EAAM,CAAC,EACjB,iEAAiE,KAAKA,EAAM,CAAC,CAAC,IAChFF,GAAe,wBAKdE,EAAQ,0BAA0B,KAAKF,CAAW,KAAO,OAC5DA,EAAcE,EAAM,CAAC,EACjB,+CAA+C,KAAKA,EAAM,CAAC,CAAC,IAC9DF,GAAe,YAMdE,EAAQ,uDAAuD,KAAKF,CAAW,KAAO,MAAQE,EAAM,CAAC,EAAE,OAAS,IACnHF,EAAcE,EAAM,CAAC,IAIlBA,EAAQ,kDAAkD,KAAKF,CAAW,KAAO,OACpFA,EAAcE,EAAM,CAAC,GACjBL,EAAYG,CAAW,GAAKA,EAAY,OAAS,GAAK,sSAAsS,KAAKE,EAAM,CAAC,CAAC,KAC3WF,GAAe,UAEb,UAAU,KAAKE,EAAM,CAAC,CAAC,IACzBF,GAAe,kBAKdE,EAAQ,2EAA2E,KAAKF,CAAW,KAAO,OAC7GA,EAAcE,EAAM,CAAC,GACjBL,EAAYG,CAAW,GAAK,6UAA6U,KAAKE,EAAM,CAAC,CAAC,GAAK,UAAU,KAAKA,EAAM,CAAC,CAAC,KACpZF,GAAe,iBAMfA,IAAgB,mCAClBA,EAAc,6BAEXE,EAAQ,4CAA4C,KAAKF,CAAW,KAAO,OAC9EA,EAAcE,EAAM,CAAC,IAElBA,EAAQ,eAAe,KAAKF,CAAW,KAAO,OACjDA,EAAcE,EAAM,CAAC,EACjB,2DAA2D,KAAKA,EAAM,CAAC,CAAC,IAC1EF,GAAe,kBAKdE,EAAQ,yFAAyF,KAAKF,CAAW,KAAO,OAC3HA,EAAcE,EAAM,CAAC,EACjB,YAAY,KAAKA,EAAM,CAAC,CAAC,IAC3BF,GAAe,8BAGdE,EAAQ,eAAe,KAAKF,CAAW,KAAO,OACjDA,EAAcE,EAAM,CAAC,GACjBJ,EAAaE,CAAW,GAAK,4iBAA4iB,KAAKE,EAAM,CAAC,CAAC,KACxlBF,GAAe,kBAKdE,EAAQ,iBAAiB,KAAKF,CAAW,KAAO,OACnDA,EAAcE,EAAM,CAAC,IAGlBA,EAAQ,eAAe,KAAKF,CAAW,KAAO,OACjDA,EAAcE,EAAM,CAAC,GACjBJ,EAAaE,CAAW,GAAK,mIAAmI,KAAKE,EAAM,CAAC,CAAC,GAAK,4GAA4G,KAAKA,EAAM,CAAC,CAAC,KAC7SF,GAAe,kBAKdE,EAAQ,uBAAuB,KAAKF,CAAW,KAAO,OACzDA,EAAcE,EAAM,CAAC,EACjB,QAAQ,KAAKA,EAAM,CAAC,CAAC,IACvBF,GAAe,sBAEb,OAAO,KAAKE,EAAM,CAAC,CAAC,IACtBF,GAAe,wBAKdE,EAAQ,0BAA0B,KAAKF,CAAW,KAAO,OAC5DA,EAAcE,EAAM,CAAC,EACjB,OAAO,KAAKA,EAAM,CAAC,CAAC,IACtBF,GAAe,oCAKdE,EAAQ,iBAAiB,KAAKF,CAAW,KAAO,OACnDA,EAAcE,EAAM,CAAC,EACjB,qCAAqC,KAAKA,EAAM,CAAC,CAAC,IACpDF,GAAe,8BAIdE,EAAQ,gBAAgB,KAAKF,CAAW,KAAO,OAClDA,EAAcE,EAAM,CAAC,EACjB,wCAAwC,KAAKA,EAAM,CAAC,CAAC,IACvDF,GAAe,wBAKdE,EAAQ,8BAA8B,KAAKF,CAAW,KAAO,OAChEA,EAAcE,EAAM,CAAC,IAGlBA,EAAQ,wBAAwB,KAAKF,CAAW,KAAO,OAC1DA,EAAcE,EAAM,CAAC,GACjB,+BAA+B,KAAKA,EAAM,CAAC,CAAC,GAAK,kCAAkC,KAAKA,EAAM,CAAC,CAAC,KAClGF,GAAe,kBAKdE,EAAQ,2BAA2B,KAAKF,CAAW,KAAO,OAC7DA,EAAcE,EAAM,CAAC,GACjBL,EAAYG,CAAW,GAAK,yGAAyG,KAAKE,EAAM,CAAC,CAAC,GAAK,kEAAkE,KAAKA,EAAM,CAAC,CAAC,KACxOF,GAAe,wBAKdE,EAAQ,wBAAwB,KAAKF,CAAW,KAAO,OAC1DA,EAAcE,EAAM,CAAC,GACjB,mOAAmO,KAAKA,EAAM,CAAC,CAAC,GAAM,yCAAyC,KAAKA,EAAM,CAAC,CAAC,GAAK,CAAC,iBAAiB,KAAKA,EAAM,CAAC,CAAC,GAAM,UAAU,KAAKA,EAAM,CAAC,CAAC,KAC/WF,GAAe,kBAKdE,EAAQ,wBAAwB,KAAKF,CAAW,KAAO,OAC1DA,EAAcE,EAAM,CAAC,EACjB,8CAA8C,KAAKA,EAAM,CAAC,CAAC,IAC7DF,GAAe,kBAKdE,EAAQ,gBAAgB,KAAKF,CAAW,KAAO,OAClDA,EAAcE,EAAM,CAAC,EACjB,wDAAwD,KAAKA,EAAM,CAAC,CAAC,IACvEF,GAAe,wBAKdE,EAAQ,8BAA8B,KAAKF,CAAW,KAAO,OAChEA,EAAcE,EAAM,CAAC,EACjB,yCAAyC,KAAKA,EAAM,CAAC,CAAC,IACxDF,GAAe,wBAKdE,EAAQ,8BAA8B,KAAKF,CAAW,KAAO,OAChEA,EAAcE,EAAM,CAAC,EACjB,4CAA4C,KAAKA,EAAM,CAAC,CAAC,IAC3DF,GAAe,wBAMdE,EAAQ,2EAA2E,KAAKF,CAAW,IAAM,OAC5GA,EAAcE,EAAM,CAAC,EAAI,SACrB,WAAW,KAAKA,EAAM,CAAC,CAAC,EAC1BF,GAAe,SACN,aAAa,KAAKE,EAAM,CAAC,CAAC,IACnCF,GAAe,kBAKdE,EAAQ,eAAe,KAAKF,CAAW,KAAO,OACjDA,EAAcE,EAAM,CAAC,EAAI,gBAIvBN,EAAK,SAAWI,EAAY,SACzBE,EAAQ,meAAme,KAAKF,CAAW,KAAO,OACrgBA,EAAcE,EAAM,CAAC,IAKpBA,EAAQ,qDAAqD,KAAKF,CAAW,IAAM,OACjF,wBAAwB,KAAKE,EAAM,CAAC,CAAC,IACxCF,EAAcE,EAAM,CAAC,GAEnB,qBAAqB,KAAKA,EAAM,CAAC,CAAC,IACpCF,GAAe,iBAIZA,CACT,CAEA,OAAO,SAASG,EAAO,CACrB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASP,EAAM,CACjC,OAAOG,EAAKH,EAAK,YAAY,CAAC,EAAE,YAAY,CAC9C,CAAC,EAEMG,EAAKI,EAAM,YAAY,CAAC,EAAE,YAAY,CAEjD,CACF,EAAG,EAEHZ,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,uwDAA4W,MAAM,GAAG,CAAC,EAE3aA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,EAG1EA,EAAK,GAAG,WAAc,UAAW,CAC/B,IAAIa,EAAY,CACd,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,SACL,OAAK,QACP,EAEA,OAAO,SAASD,EAAO,CACrB,GAAI,OAAOA,EAAM,QAAW,WAC1B,OAAOA,EAAM,OAAO,SAASE,EAAM,CAEjC,QADIC,EAAM,GACDC,EAAI,EAAGA,EAAIF,EAAK,OAAQE,IAC/BD,GAAOF,EAAUC,EAAK,OAAOE,CAAC,CAAC,GAAKF,EAAK,OAAOE,CAAC,EAEnD,OAAOD,CACT,CAAC,EAGD,QADIA,EAAM,GACDC,EAAI,EAAGA,EAAIJ,EAAM,OAAQI,IAChCD,GAAOF,EAAUD,EAAM,OAAOI,CAAC,CAAC,GAAKJ,EAAM,OAAOI,CAAC,EAErD,OAAOD,CAEX,CACF,EAAG,EAEHf,EAAK,SAAS,iBAAiBA,EAAK,GAAG,WAAY,eAAe,CACpE,CACF,CAAC", "names": ["require_lunr_el", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "stepOneExceptions", "protectedWords", "alphabet", "isGreek", "word", "endsInVowel", "endsInVowel2", "stem", "stemmed<PERSON><PERSON>", "stepOneRegExp", "match", "token", "accentMap", "term", "ret", "i"]}