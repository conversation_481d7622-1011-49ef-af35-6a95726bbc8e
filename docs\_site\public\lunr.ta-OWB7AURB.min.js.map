{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.ta.js"], "sourcesContent": ["/*!\n * Lunr languages, `Tamil` language\n * https://github.com/tvmani/lunr-languages\n *\n * Copyright 2021, Manikandan Venkatasubban\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.ta = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.ta.trimmer,\n        lunr.ta.stopWordFilter,\n        lunr.ta.stemmer\n      );\n\n      // change the tokenizer for japanese one\n      // if (isLunr2) { // for lunr version 2.0.0\n      //   this.tokenizer = lunr.ta.tokenizer;\n      // } else {\n      //   if (lunr.tokenizer) { // for lunr version 0.6.0\n      //     lunr.tokenizer = lunr.ta.tokenizer;\n      //   }\n      //   if (this.tokenizerFn) { // for lunr version 0.7.0 -> 1.0.0\n      //     this.tokenizerFn = lunr.ta.tokenizer;\n      //   }\n      // }\n\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.ta.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.ta.wordCharacters = \"\\u0b80-\\u0b89\\u0b8a-\\u0b8f\\u0b90-\\u0b99\\u0b9a-\\u0b9f\\u0ba0-\\u0ba9\\u0baa-\\u0baf\\u0bb0-\\u0bb9\\u0bba-\\u0bbf\\u0bc0-\\u0bc9\\u0bca-\\u0bcf\\u0bd0-\\u0bd9\\u0bda-\\u0bdf\\u0be0-\\u0be9\\u0bea-\\u0bef\\u0bf0-\\u0bf9\\u0bfa-\\u0bffa-zA-Zａ-ｚＡ-Ｚ0-9０-９\";\n\n    lunr.ta.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.ta.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.ta.trimmer, 'trimmer-ta');\n    /* lunr stop word filter */\n    lunr.ta.stopWordFilter = lunr.generateStopWordFilter(\n      'அங்கு அங்கே அது அதை அந்த அவர் அவர்கள் அவள் அவன் அவை ஆக ஆகவே ஆகையால் ஆதலால் ஆதலினால் ஆனாலும் ஆனால் இங்கு இங்கே இது இதை இந்த இப்படி இவர் இவர்கள் இவள் இவன் இவை இவ்வளவு உனக்கு உனது உன் உன்னால் எங்கு எங்கே எது எதை எந்த எப்படி எவர் எவர்கள் எவள் எவன் எவை எவ்வளவு எனக்கு எனது எனவே என் என்ன என்னால் ஏது ஏன் தனது தன்னால் தானே தான் நாங்கள் நாம் நான் நீ நீங்கள்'.split(' '));\n    /* lunr stemmer function */\n    lunr.ta.stemmer = (function() {\n\n      return function(word) {\n        // for lunr version 2\n        if (typeof word.update === \"function\") {\n          return word.update(function(word) {\n            return word;\n          })\n        } else { // for lunr version <= 1\n          return word;\n        }\n\n      }\n    })();\n\n    var segmenter = lunr.wordcut;\n    segmenter.init();\n    lunr.ta.tokenizer = function(obj) {\n      if (!arguments.length || obj == null || obj == undefined) return []\n      if (Array.isArray(obj)) return obj.map(function(t) {\n        return isLunr2 ? new lunr.Token(t.toLowerCase()) : t.toLowerCase()\n      });\n\n      var str = obj.toString().toLowerCase().replace(/^\\s+/, '');\n      return segmenter.cut(str).split('|');\n    }\n\n    lunr.Pipeline.registerFunction(lunr.ta.stemmer, 'stemmer-ta');\n    lunr.Pipeline.registerFunction(lunr.ta.stopWordFilter, 'stopWordFilter-ta');\n\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAcI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,mQAEzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAC5B,gwDAAgW,MAAM,GAAG,CAAC,EAE5WA,EAAK,GAAG,QAAW,UAAW,CAE5B,OAAO,SAASC,EAAM,CAEpB,OAAI,OAAOA,EAAK,QAAW,WAClBA,EAAK,OAAO,SAASA,EAAM,CAChC,OAAOA,CACT,CAAC,EAEMA,CAGX,CACF,EAAG,EAEH,IAAIC,EAAYF,EAAK,QACrBE,EAAU,KAAK,EACfF,EAAK,GAAG,UAAY,SAASG,EAAK,CAChC,GAAI,CAAC,UAAU,QAAUA,GAAO,MAAQA,GAAO,KAAW,MAAO,CAAC,EAClE,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOA,EAAI,IAAI,SAASC,EAAG,CACjD,OAAO,QAAU,IAAIJ,EAAK,MAAMI,EAAE,YAAY,CAAC,EAAIA,EAAE,YAAY,CACnE,CAAC,EAED,IAAIC,EAAMF,EAAI,SAAS,EAAE,YAAY,EAAE,QAAQ,OAAQ,EAAE,EACzD,OAAOD,EAAU,IAAIG,CAAG,EAAE,MAAM,GAAG,CACrC,EAEAL,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAC5DA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAE5E,CACF,CAAC", "names": ["require_lunr_ta", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "word", "segmenter", "obj", "t", "str"]}