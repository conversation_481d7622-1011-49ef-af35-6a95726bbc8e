import{a as <PERSON>,b as <PERSON>,d as No}from"./chunk-OSRY5VT3.min.js";var xo=Jo((fF,Rc)=>{(function(c,t){"use strict";typeof define=="function"&&define.amd?define([],t):typeof Rc=="object"&&Rc.exports?Rc.exports=t():(c.AnchorJS=t(),c.anchors=new c.AnchorJS)})(globalThis,function(){"use strict";function c(t){this.options=t||{},this.elements=[];function i(o){o.icon=Object.prototype.hasOwnProperty.call(o,"icon")?o.icon:"\uE9CB",o.visible=Object.prototype.hasOwnProperty.call(o,"visible")?o.visible:"hover",o.placement=Object.prototype.hasOwnProperty.call(o,"placement")?o.placement:"right",o.ariaLabel=Object.prototype.hasOwnProperty.call(o,"ariaLabel")?o.ariaLabel:"Anchor",o.class=Object.prototype.hasOwnProperty.call(o,"class")?o.class:"",o.base=Object.prototype.hasOwnProperty.call(o,"base")?o.base:"",o.truncate=Object.prototype.hasOwnProperty.call(o,"truncate")?Math.floor(o.truncate):64,o.titleText=Object.prototype.hasOwnProperty.call(o,"titleText")?o.titleText:""}i(this.options),this.add=function(o){var l,a,n,r,I,g,C,d,F,s,X,Z=[];if(i(this.options),o||(o="h2, h3, h4, h5, h6"),l=e(o),l.length===0)return this;for(b(),a=document.querySelectorAll("[id]"),n=[].map.call(a,function(B){return B.id}),I=0;I<l.length;I++){if(this.hasAnchorJSLink(l[I])){Z.push(I);continue}if(l[I].hasAttribute("id"))r=l[I].getAttribute("id");else if(l[I].hasAttribute("data-anchor-id"))r=l[I].getAttribute("data-anchor-id");else{d=this.urlify(l[I].textContent),F=d,C=0;do g!==void 0&&(F=d+"-"+C),g=n.indexOf(F),C+=1;while(g!==-1);g=void 0,n.push(F),l[I].setAttribute("id",F),r=F}s=document.createElement("a"),s.className="anchorjs-link "+this.options.class,s.setAttribute("aria-label",this.options.ariaLabel),s.setAttribute("data-anchorjs-icon",this.options.icon),this.options.titleText&&(s.title=this.options.titleText),X=document.querySelector("base")?window.location.pathname+window.location.search:"",X=this.options.base||X,s.href=X+"#"+r,this.options.visible==="always"&&(s.style.opacity="1"),this.options.icon==="\uE9CB"&&(s.style.font="1em/1 anchorjs-icons",this.options.placement==="left"&&(s.style.lineHeight="inherit")),this.options.placement==="left"?(s.style.position="absolute",s.style.marginLeft="-1.25em",s.style.paddingRight=".25em",s.style.paddingLeft=".25em",l[I].insertBefore(s,l[I].firstChild)):(s.style.marginLeft=".1875em",s.style.paddingRight=".1875em",s.style.paddingLeft=".1875em",l[I].appendChild(s))}for(I=0;I<Z.length;I++)l.splice(Z[I]-I,1);return this.elements=this.elements.concat(l),this},this.remove=function(o){for(var l,a,n=e(o),r=0;r<n.length;r++)a=n[r].querySelector(".anchorjs-link"),a&&(l=this.elements.indexOf(n[r]),l!==-1&&this.elements.splice(l,1),n[r].removeChild(a));return this},this.removeAll=function(){this.remove(this.elements)},this.urlify=function(o){var l=document.createElement("textarea");l.innerHTML=o,o=l.value;var a=/[& +$,:;=?@"#{}|^~[`%!'<>\]./()*\\\n\t\b\v\u00A0]/g;return this.options.truncate||i(this.options),o.trim().replace(/'/gi,"").replace(a,"-").replace(/-{2,}/g,"-").substring(0,this.options.truncate).replace(/^-+|-+$/gm,"").toLowerCase()},this.hasAnchorJSLink=function(o){var l=o.firstChild&&(" "+o.firstChild.className+" ").indexOf(" anchorjs-link ")>-1,a=o.lastChild&&(" "+o.lastChild.className+" ").indexOf(" anchorjs-link ")>-1;return l||a||!1};function e(o){var l;if(typeof o=="string"||o instanceof String)l=[].slice.call(document.querySelectorAll(o));else if(Array.isArray(o)||o instanceof NodeList)l=[].slice.call(o);else throw new TypeError("The selector provided to AnchorJS was invalid.");return l}function b(){if(document.head.querySelector("style.anchorjs")===null){var o=document.createElement("style"),l=".anchorjs-link{opacity:0;text-decoration:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}",a=":hover>.anchorjs-link,.anchorjs-link:focus{opacity:1}",n='@font-face{font-family:anchorjs-icons;src:url(data:n/a;base64,AAEAAAALAIAAAwAwT1MvMg8yG2cAAAE4AAAAYGNtYXDp3gC3AAABpAAAAExnYXNwAAAAEAAAA9wAAAAIZ2x5ZlQCcfwAAAH4AAABCGhlYWQHFvHyAAAAvAAAADZoaGVhBnACFwAAAPQAAAAkaG10eASAADEAAAGYAAAADGxvY2EACACEAAAB8AAAAAhtYXhwAAYAVwAAARgAAAAgbmFtZQGOH9cAAAMAAAAAunBvc3QAAwAAAAADvAAAACAAAQAAAAEAAHzE2p9fDzz1AAkEAAAAAADRecUWAAAAANQA6R8AAAAAAoACwAAAAAgAAgAAAAAAAAABAAADwP/AAAACgAAA/9MCrQABAAAAAAAAAAAAAAAAAAAAAwABAAAAAwBVAAIAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAMCQAGQAAUAAAKZAswAAACPApkCzAAAAesAMwEJAAAAAAAAAAAAAAAAAAAAARAAAAAAAAAAAAAAAAAAAAAAQAAg//0DwP/AAEADwABAAAAAAQAAAAAAAAAAAAAAIAAAAAAAAAIAAAACgAAxAAAAAwAAAAMAAAAcAAEAAwAAABwAAwABAAAAHAAEADAAAAAIAAgAAgAAACDpy//9//8AAAAg6cv//f///+EWNwADAAEAAAAAAAAAAAAAAAAACACEAAEAAAAAAAAAAAAAAAAxAAACAAQARAKAAsAAKwBUAAABIiYnJjQ3NzY2MzIWFxYUBwcGIicmNDc3NjQnJiYjIgYHBwYUFxYUBwYGIwciJicmNDc3NjIXFhQHBwYUFxYWMzI2Nzc2NCcmNDc2MhcWFAcHBgYjARQGDAUtLXoWOR8fORYtLTgKGwoKCjgaGg0gEhIgDXoaGgkJBQwHdR85Fi0tOAobCgoKOBoaDSASEiANehoaCQkKGwotLXoWOR8BMwUFLYEuehYXFxYugC44CQkKGwo4GkoaDQ0NDXoaShoKGwoFBe8XFi6ALjgJCQobCjgaShoNDQ0NehpKGgobCgoKLYEuehYXAAAADACWAAEAAAAAAAEACAAAAAEAAAAAAAIAAwAIAAEAAAAAAAMACAAAAAEAAAAAAAQACAAAAAEAAAAAAAUAAQALAAEAAAAAAAYACAAAAAMAAQQJAAEAEAAMAAMAAQQJAAIABgAcAAMAAQQJAAMAEAAMAAMAAQQJAAQAEAAMAAMAAQQJAAUAAgAiAAMAAQQJAAYAEAAMYW5jaG9yanM0MDBAAGEAbgBjAGgAbwByAGoAcwA0ADAAMABAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAH//wAP) format("truetype")}',r="[data-anchorjs-icon]::after{content:attr(data-anchorjs-icon)}",I;o.className="anchorjs",o.appendChild(document.createTextNode("")),I=document.head.querySelector('[rel="stylesheet"],style'),I===void 0?document.head.appendChild(o):document.head.insertBefore(o,I),o.sheet.insertRule(l,o.sheet.cssRules.length),o.sheet.insertRule(a,o.sheet.cssRules.length),o.sheet.insertRule(r,o.sheet.cssRules.length),o.sheet.insertRule(n,o.sheet.cssRules.length)}}}return c})});var ic={};Lo(ic,{afterMain:()=>He,afterRead:()=>ke,afterWrite:()=>Ee,applyStyles:()=>qt,arrow:()=>Di,auto:()=>Gi,basePlacements:()=>rt,beforeMain:()=>ze,beforeRead:()=>Ne,beforeWrite:()=>Se,bottom:()=>O,clippingParents:()=>Ac,computeStyles:()=>ii,createPopper:()=>Vi,createPopperBase:()=>ib,createPopperLite:()=>cb,detectOverflow:()=>S,end:()=>Xt,eventListeners:()=>ci,flip:()=>Pi,hide:()=>$i,left:()=>U,main:()=>je,modifierPhases:()=>Uc,offset:()=>qi,placements:()=>pi,popper:()=>Yt,popperGenerator:()=>zt,popperOffsets:()=>oi,preventOverflow:()=>tc,read:()=>we,reference:()=>fc,right:()=>Y,start:()=>nt,top:()=>A,variationPlacements:()=>Ei,viewport:()=>Bi,write:()=>Te});var A="top",O="bottom",Y="right",U="left",Gi="auto",rt=[A,O,Y,U],nt="start",Xt="end",Ac="clippingParents",Bi="viewport",Yt="popper",fc="reference",Ei=rt.reduce(function(c,t){return c.concat([t+"-"+nt,t+"-"+Xt])},[]),pi=[].concat(rt,[Gi]).reduce(function(c,t){return c.concat([t,t+"-"+nt,t+"-"+Xt])},[]),Ne="beforeRead",we="read",ke="afterRead",ze="beforeMain",je="main",He="afterMain",Se="beforeWrite",Te="write",Ee="afterWrite",Uc=[Ne,we,ke,ze,je,He,Se,Te,Ee];function N(c){return c?(c.nodeName||"").toLowerCase():null}function V(c){if(c==null)return window;if(c.toString()!=="[object Window]"){var t=c.ownerDocument;return t&&t.defaultView||window}return c}function _(c){var t=V(c).Element;return c instanceof t||c instanceof Element}function J(c){var t=V(c).HTMLElement;return c instanceof t||c instanceof HTMLElement}function $t(c){if(typeof ShadowRoot>"u")return!1;var t=V(c).ShadowRoot;return c instanceof t||c instanceof ShadowRoot}function wo(c){var t=c.state;Object.keys(t.elements).forEach(function(i){var e=t.styles[i]||{},b=t.attributes[i]||{},o=t.elements[i];!J(o)||!N(o)||(Object.assign(o.style,e),Object.keys(b).forEach(function(l){var a=b[l];a===!1?o.removeAttribute(l):o.setAttribute(l,a===!0?"":a)}))})}function ko(c){var t=c.state,i={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,i.popper),t.styles=i,t.elements.arrow&&Object.assign(t.elements.arrow.style,i.arrow),function(){Object.keys(t.elements).forEach(function(e){var b=t.elements[e],o=t.attributes[e]||{},l=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:i[e]),a=l.reduce(function(n,r){return n[r]="",n},{});!J(b)||!N(b)||(Object.assign(b.style,a),Object.keys(o).forEach(function(n){b.removeAttribute(n)}))})}}var qt={name:"applyStyles",enabled:!0,phase:"write",fn:wo,effect:ko,requires:["computeStyles"]};function w(c){return c.split("-")[0]}var bt=Math.max,Ot=Math.min,dt=Math.round;function ti(){var c=navigator.userAgentData;return c!=null&&c.brands&&Array.isArray(c.brands)?c.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function Wi(){return!/^((?!chrome|android).)*safari/i.test(ti())}function P(c,t,i){t===void 0&&(t=!1),i===void 0&&(i=!1);var e=c.getBoundingClientRect(),b=1,o=1;t&&J(c)&&(b=c.offsetWidth>0&&dt(e.width)/c.offsetWidth||1,o=c.offsetHeight>0&&dt(e.height)/c.offsetHeight||1);var l=_(c)?V(c):window,a=l.visualViewport,n=!Wi()&&i,r=(e.left+(n&&a?a.offsetLeft:0))/b,I=(e.top+(n&&a?a.offsetTop:0))/o,g=e.width/b,C=e.height/o;return{width:g,height:C,top:I,right:r+g,bottom:I+C,left:r,x:r,y:I}}function vt(c){var t=P(c),i=c.offsetWidth,e=c.offsetHeight;return Math.abs(t.width-i)<=1&&(i=t.width),Math.abs(t.height-e)<=1&&(e=t.height),{x:c.offsetLeft,y:c.offsetTop,width:i,height:e}}function ui(c,t){var i=t.getRootNode&&t.getRootNode();if(c.contains(t))return!0;if(i&&$t(i)){var e=t;do{if(e&&c.isSameNode(e))return!0;e=e.parentNode||e.host}while(e)}return!1}function H(c){return V(c).getComputedStyle(c)}function yc(c){return["table","td","th"].indexOf(N(c))>=0}function j(c){return((_(c)?c.ownerDocument:c.document)||window.document).documentElement}function gt(c){return N(c)==="html"?c:c.assignedSlot||c.parentNode||($t(c)?c.host:null)||j(c)}function De(c){return!J(c)||H(c).position==="fixed"?null:c.offsetParent}function zo(c){var t=/firefox/i.test(ti()),i=/Trident/i.test(ti());if(i&&J(c)){var e=H(c);if(e.position==="fixed")return null}var b=gt(c);for($t(b)&&(b=b.host);J(b)&&["html","body"].indexOf(N(b))<0;){var o=H(b);if(o.transform!=="none"||o.perspective!=="none"||o.contain==="paint"||["transform","perspective"].indexOf(o.willChange)!==-1||t&&o.willChange==="filter"||t&&o.filter&&o.filter!=="none")return b;b=b.parentNode}return null}function ot(c){for(var t=V(c),i=De(c);i&&yc(i)&&H(i).position==="static";)i=De(i);return i&&(N(i)==="html"||N(i)==="body"&&H(i).position==="static")?t:i||zo(c)||t}function Jt(c){return["top","bottom"].indexOf(c)>=0?"x":"y"}function Lt(c,t,i){return bt(c,Ot(t,i))}function Me(c,t,i){var e=Lt(c,t,i);return e>i?i:e}function Ri(){return{top:0,right:0,bottom:0,left:0}}function xi(c){return Object.assign({},Ri(),c)}function Qi(c,t){return t.reduce(function(i,e){return i[e]=c,i},{})}var jo=function(t,i){return t=typeof t=="function"?t(Object.assign({},i.rects,{placement:i.placement})):t,xi(typeof t!="number"?t:Qi(t,rt))};function Ho(c){var t,i=c.state,e=c.name,b=c.options,o=i.elements.arrow,l=i.modifiersData.popperOffsets,a=w(i.placement),n=Jt(a),r=[U,Y].indexOf(a)>=0,I=r?"height":"width";if(!(!o||!l)){var g=jo(b.padding,i),C=vt(o),d=n==="y"?A:U,F=n==="y"?O:Y,s=i.rects.reference[I]+i.rects.reference[n]-l[n]-i.rects.popper[I],X=l[n]-i.rects.reference[n],Z=ot(o),B=Z?n==="y"?Z.clientHeight||0:Z.clientWidth||0:0,G=s/2-X/2,W=g[d],u=B-C[I]-g[F],x=B/2-C[I]/2+G,Q=Lt(W,x,u),h=n;i.modifiersData[e]=(t={},t[h]=Q,t.centerOffset=Q-x,t)}}function So(c){var t=c.state,i=c.options,e=i.element,b=e===void 0?"[data-popper-arrow]":e;b!=null&&(typeof b=="string"&&(b=t.elements.popper.querySelector(b),!b)||ui(t.elements.popper,b)&&(t.elements.arrow=b))}var Di={name:"arrow",enabled:!0,phase:"main",fn:Ho,effect:So,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function $(c){return c.split("-")[1]}var To={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Eo(c,t){var i=c.x,e=c.y,b=t.devicePixelRatio||1;return{x:dt(i*b)/b||0,y:dt(e*b)/b||0}}function Ke(c){var t,i=c.popper,e=c.popperRect,b=c.placement,o=c.variation,l=c.offsets,a=c.position,n=c.gpuAcceleration,r=c.adaptive,I=c.roundOffsets,g=c.isFixed,C=l.x,d=C===void 0?0:C,F=l.y,s=F===void 0?0:F,X=typeof I=="function"?I({x:d,y:s}):{x:d,y:s};d=X.x,s=X.y;var Z=l.hasOwnProperty("x"),B=l.hasOwnProperty("y"),G=U,W=A,u=window;if(r){var x=ot(i),Q="clientHeight",h="clientWidth";if(x===V(i)&&(x=j(i),H(x).position!=="static"&&a==="absolute"&&(Q="scrollHeight",h="scrollWidth")),x=x,b===A||(b===U||b===Y)&&o===Xt){W=O;var f=g&&x===u&&u.visualViewport?u.visualViewport.height:x[Q];s-=f-e.height,s*=n?1:-1}if(b===U||(b===A||b===O)&&o===Xt){G=Y;var y=g&&x===u&&u.visualViewport?u.visualViewport.width:x[h];d-=y-e.width,d*=n?1:-1}}var v=Object.assign({position:a},r&&To),T=I===!0?Eo({x:d,y:s},V(i)):{x:d,y:s};if(d=T.x,s=T.y,n){var z;return Object.assign({},v,(z={},z[W]=B?"0":"",z[G]=Z?"0":"",z.transform=(u.devicePixelRatio||1)<=1?"translate("+d+"px, "+s+"px)":"translate3d("+d+"px, "+s+"px, 0)",z))}return Object.assign({},v,(t={},t[W]=B?s+"px":"",t[G]=Z?d+"px":"",t.transform="",t))}function Do(c){var t=c.state,i=c.options,e=i.gpuAcceleration,b=e===void 0?!0:e,o=i.adaptive,l=o===void 0?!0:o,a=i.roundOffsets,n=a===void 0?!0:a,r={placement:w(t.placement),variation:$(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:b,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Ke(Object.assign({},r,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:l,roundOffsets:n})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ke(Object.assign({},r,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:n})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var ii={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Do,data:{}};var Mi={passive:!0};function Mo(c){var t=c.state,i=c.instance,e=c.options,b=e.scroll,o=b===void 0?!0:b,l=e.resize,a=l===void 0?!0:l,n=V(t.elements.popper),r=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&r.forEach(function(I){I.addEventListener("scroll",i.update,Mi)}),a&&n.addEventListener("resize",i.update,Mi),function(){o&&r.forEach(function(I){I.removeEventListener("scroll",i.update,Mi)}),a&&n.removeEventListener("resize",i.update,Mi)}}var ci={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Mo,data:{}};var Ko={left:"right",right:"left",bottom:"top",top:"bottom"};function ei(c){return c.replace(/left|right|bottom|top/g,function(t){return Ko[t]})}var _o={start:"end",end:"start"};function Ki(c){return c.replace(/start|end/g,function(t){return _o[t]})}function Nt(c){var t=V(c),i=t.pageXOffset,e=t.pageYOffset;return{scrollLeft:i,scrollTop:e}}function wt(c){return P(j(c)).left+Nt(c).scrollLeft}function Yc(c,t){var i=V(c),e=j(c),b=i.visualViewport,o=e.clientWidth,l=e.clientHeight,a=0,n=0;if(b){o=b.width,l=b.height;var r=Wi();(r||!r&&t==="fixed")&&(a=b.offsetLeft,n=b.offsetTop)}return{width:o,height:l,x:a+wt(c),y:n}}function Oc(c){var t,i=j(c),e=Nt(c),b=(t=c.ownerDocument)==null?void 0:t.body,o=bt(i.scrollWidth,i.clientWidth,b?b.scrollWidth:0,b?b.clientWidth:0),l=bt(i.scrollHeight,i.clientHeight,b?b.scrollHeight:0,b?b.clientHeight:0),a=-e.scrollLeft+wt(c),n=-e.scrollTop;return H(b||i).direction==="rtl"&&(a+=bt(i.clientWidth,b?b.clientWidth:0)-o),{width:o,height:l,x:a,y:n}}function kt(c){var t=H(c),i=t.overflow,e=t.overflowX,b=t.overflowY;return/auto|scroll|overlay|hidden/.test(i+b+e)}function _i(c){return["html","body","#document"].indexOf(N(c))>=0?c.ownerDocument.body:J(c)&&kt(c)?c:_i(gt(c))}function Ct(c,t){var i;t===void 0&&(t=[]);var e=_i(c),b=e===((i=c.ownerDocument)==null?void 0:i.body),o=V(e),l=b?[o].concat(o.visualViewport||[],kt(e)?e:[]):e,a=t.concat(l);return b?a:a.concat(Ct(gt(l)))}function bi(c){return Object.assign({},c,{left:c.x,top:c.y,right:c.x+c.width,bottom:c.y+c.height})}function Po(c,t){var i=P(c,!1,t==="fixed");return i.top=i.top+c.clientTop,i.left=i.left+c.clientLeft,i.bottom=i.top+c.clientHeight,i.right=i.left+c.clientWidth,i.width=c.clientWidth,i.height=c.clientHeight,i.x=i.left,i.y=i.top,i}function _e(c,t,i){return t===Bi?bi(Yc(c,i)):_(t)?Po(t,i):bi(Oc(j(c)))}function $o(c){var t=Ct(gt(c)),i=["absolute","fixed"].indexOf(H(c).position)>=0,e=i&&J(c)?ot(c):c;return _(e)?t.filter(function(b){return _(b)&&ui(b,e)&&N(b)!=="body"}):[]}function vc(c,t,i,e){var b=t==="clippingParents"?$o(c):[].concat(t),o=[].concat(b,[i]),l=o[0],a=o.reduce(function(n,r){var I=_e(c,r,e);return n.top=bt(I.top,n.top),n.right=Ot(I.right,n.right),n.bottom=Ot(I.bottom,n.bottom),n.left=bt(I.left,n.left),n},_e(c,l,e));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function hi(c){var t=c.reference,i=c.element,e=c.placement,b=e?w(e):null,o=e?$(e):null,l=t.x+t.width/2-i.width/2,a=t.y+t.height/2-i.height/2,n;switch(b){case A:n={x:l,y:t.y-i.height};break;case O:n={x:l,y:t.y+t.height};break;case Y:n={x:t.x+t.width,y:a};break;case U:n={x:t.x-i.width,y:a};break;default:n={x:t.x,y:t.y}}var r=b?Jt(b):null;if(r!=null){var I=r==="y"?"height":"width";switch(o){case nt:n[r]=n[r]-(t[I]/2-i[I]/2);break;case Xt:n[r]=n[r]+(t[I]/2-i[I]/2);break;default:}}return n}function S(c,t){t===void 0&&(t={});var i=t,e=i.placement,b=e===void 0?c.placement:e,o=i.strategy,l=o===void 0?c.strategy:o,a=i.boundary,n=a===void 0?Ac:a,r=i.rootBoundary,I=r===void 0?Bi:r,g=i.elementContext,C=g===void 0?Yt:g,d=i.altBoundary,F=d===void 0?!1:d,s=i.padding,X=s===void 0?0:s,Z=xi(typeof X!="number"?X:Qi(X,rt)),B=C===Yt?fc:Yt,G=c.rects.popper,W=c.elements[F?B:C],u=vc(_(W)?W:W.contextElement||j(c.elements.popper),n,I,l),x=P(c.elements.reference),Q=hi({reference:x,element:G,strategy:"absolute",placement:b}),h=bi(Object.assign({},G,Q)),f=C===Yt?h:x,y={top:u.top-f.top+Z.top,bottom:f.bottom-u.bottom+Z.bottom,left:u.left-f.left+Z.left,right:f.right-u.right+Z.right},v=c.modifiersData.offset;if(C===Yt&&v){var T=v[b];Object.keys(y).forEach(function(z){var Vt=[Y,O].indexOf(z)>=0?1:-1,At=[A,O].indexOf(z)>=0?"y":"x";y[z]+=T[At]*Vt})}return y}function Jc(c,t){t===void 0&&(t={});var i=t,e=i.placement,b=i.boundary,o=i.rootBoundary,l=i.padding,a=i.flipVariations,n=i.allowedAutoPlacements,r=n===void 0?pi:n,I=$(e),g=I?a?Ei:Ei.filter(function(F){return $(F)===I}):rt,C=g.filter(function(F){return r.indexOf(F)>=0});C.length===0&&(C=g);var d=C.reduce(function(F,s){return F[s]=S(c,{placement:s,boundary:b,rootBoundary:o,padding:l})[w(s)],F},{});return Object.keys(d).sort(function(F,s){return d[F]-d[s]})}function qo(c){if(w(c)===Gi)return[];var t=ei(c);return[Ki(c),t,Ki(t)]}function tl(c){var t=c.state,i=c.options,e=c.name;if(!t.modifiersData[e]._skip){for(var b=i.mainAxis,o=b===void 0?!0:b,l=i.altAxis,a=l===void 0?!0:l,n=i.fallbackPlacements,r=i.padding,I=i.boundary,g=i.rootBoundary,C=i.altBoundary,d=i.flipVariations,F=d===void 0?!0:d,s=i.allowedAutoPlacements,X=t.options.placement,Z=w(X),B=Z===X,G=n||(B||!F?[ei(X)]:qo(X)),W=[X].concat(G).reduce(function(Pt,Zt){return Pt.concat(w(Zt)===Gi?Jc(t,{placement:Zt,boundary:I,rootBoundary:g,padding:r,flipVariations:F,allowedAutoPlacements:s}):Zt)},[]),u=t.rects.reference,x=t.rects.popper,Q=new Map,h=!0,f=W[0],y=0;y<W.length;y++){var v=W[y],T=w(v),z=$(v)===nt,Vt=[A,O].indexOf(T)>=0,At=Vt?"width":"height",M=S(t,{placement:v,boundary:I,rootBoundary:g,altBoundary:C,padding:r}),et=Vt?z?Y:U:z?O:A;u[At]>x[At]&&(et=ei(et));var zi=ei(et),ft=[];if(o&&ft.push(M[T]<=0),a&&ft.push(M[et]<=0,M[zi]<=0),ft.every(function(Pt){return Pt})){f=v,h=!1;break}Q.set(v,ft)}if(h)for(var ji=F?3:1,xc=function(Zt){var Ci=W.find(function(Si){var Ut=Q.get(Si);if(Ut)return Ut.slice(0,Zt).every(function(Qc){return Qc})});if(Ci)return f=Ci,"break"},Xi=ji;Xi>0;Xi--){var Hi=xc(Xi);if(Hi==="break")break}t.placement!==f&&(t.modifiersData[e]._skip=!0,t.placement=f,t.reset=!0)}}var Pi={name:"flip",enabled:!0,phase:"main",fn:tl,requiresIfExists:["offset"],data:{_skip:!1}};function Pe(c,t,i){return i===void 0&&(i={x:0,y:0}),{top:c.top-t.height-i.y,right:c.right-t.width+i.x,bottom:c.bottom-t.height+i.y,left:c.left-t.width-i.x}}function $e(c){return[A,Y,O,U].some(function(t){return c[t]>=0})}function il(c){var t=c.state,i=c.name,e=t.rects.reference,b=t.rects.popper,o=t.modifiersData.preventOverflow,l=S(t,{elementContext:"reference"}),a=S(t,{altBoundary:!0}),n=Pe(l,e),r=Pe(a,b,o),I=$e(n),g=$e(r);t.modifiersData[i]={referenceClippingOffsets:n,popperEscapeOffsets:r,isReferenceHidden:I,hasPopperEscaped:g},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":I,"data-popper-escaped":g})}var $i={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:il};function cl(c,t,i){var e=w(c),b=[U,A].indexOf(e)>=0?-1:1,o=typeof i=="function"?i(Object.assign({},t,{placement:c})):i,l=o[0],a=o[1];return l=l||0,a=(a||0)*b,[U,Y].indexOf(e)>=0?{x:a,y:l}:{x:l,y:a}}function el(c){var t=c.state,i=c.options,e=c.name,b=i.offset,o=b===void 0?[0,0]:b,l=pi.reduce(function(I,g){return I[g]=cl(g,t.rects,o),I},{}),a=l[t.placement],n=a.x,r=a.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=n,t.modifiersData.popperOffsets.y+=r),t.modifiersData[e]=l}var qi={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:el};function bl(c){var t=c.state,i=c.name;t.modifiersData[i]=hi({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}var oi={name:"popperOffsets",enabled:!0,phase:"read",fn:bl,data:{}};function Lc(c){return c==="x"?"y":"x"}function ol(c){var t=c.state,i=c.options,e=c.name,b=i.mainAxis,o=b===void 0?!0:b,l=i.altAxis,a=l===void 0?!1:l,n=i.boundary,r=i.rootBoundary,I=i.altBoundary,g=i.padding,C=i.tether,d=C===void 0?!0:C,F=i.tetherOffset,s=F===void 0?0:F,X=S(t,{boundary:n,rootBoundary:r,padding:g,altBoundary:I}),Z=w(t.placement),B=$(t.placement),G=!B,W=Jt(Z),u=Lc(W),x=t.modifiersData.popperOffsets,Q=t.rects.reference,h=t.rects.popper,f=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,y=typeof f=="number"?{mainAxis:f,altAxis:f}:Object.assign({mainAxis:0,altAxis:0},f),v=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,T={x:0,y:0};if(x){if(o){var z,Vt=W==="y"?A:U,At=W==="y"?O:Y,M=W==="y"?"height":"width",et=x[W],zi=et+X[Vt],ft=et-X[At],ji=d?-h[M]/2:0,xc=B===nt?Q[M]:h[M],Xi=B===nt?-h[M]:-Q[M],Hi=t.elements.arrow,Pt=d&&Hi?vt(Hi):{width:0,height:0},Zt=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Ri(),Ci=Zt[Vt],Si=Zt[At],Ut=Lt(0,Q[M],Pt[M]),Qc=G?Q[M]/2-ji-Ut-Ci-y.mainAxis:xc-Ut-Ci-y.mainAxis,fo=G?-Q[M]/2+ji+Ut+Si+y.mainAxis:Xi+Ut+Si+y.mainAxis,hc=t.elements.arrow&&ot(t.elements.arrow),Uo=hc?W==="y"?hc.clientTop||0:hc.clientLeft||0:0,Ae=(z=v?.[W])!=null?z:0,yo=et+Qc-Ae-Uo,Yo=et+fo-Ae,fe=Lt(d?Ot(zi,yo):zi,et,d?bt(ft,Yo):ft);x[W]=fe,T[W]=fe-et}if(a){var Ue,Oo=W==="x"?A:U,vo=W==="x"?O:Y,yt=x[u],Ti=u==="y"?"height":"width",ye=yt+X[Oo],Ye=yt-X[vo],Vc=[A,U].indexOf(Z)!==-1,Oe=(Ue=v?.[u])!=null?Ue:0,ve=Vc?ye:yt-Q[Ti]-h[Ti]-Oe+y.altAxis,Je=Vc?yt+Q[Ti]+h[Ti]-Oe-y.altAxis:Ye,Le=d&&Vc?Me(ve,yt,Je):Lt(d?ve:ye,yt,d?Je:Ye);x[u]=Le,T[u]=Le-yt}t.modifiersData[e]=T}}var tc={name:"preventOverflow",enabled:!0,phase:"main",fn:ol,requiresIfExists:["offset"]};function Nc(c){return{scrollLeft:c.scrollLeft,scrollTop:c.scrollTop}}function wc(c){return c===V(c)||!J(c)?Nt(c):Nc(c)}function ll(c){var t=c.getBoundingClientRect(),i=dt(t.width)/c.offsetWidth||1,e=dt(t.height)/c.offsetHeight||1;return i!==1||e!==1}function kc(c,t,i){i===void 0&&(i=!1);var e=J(t),b=J(t)&&ll(t),o=j(t),l=P(c,b,i),a={scrollLeft:0,scrollTop:0},n={x:0,y:0};return(e||!e&&!i)&&((N(t)!=="body"||kt(o))&&(a=wc(t)),J(t)?(n=P(t,!0),n.x+=t.clientLeft,n.y+=t.clientTop):o&&(n.x=wt(o))),{x:l.left+a.scrollLeft-n.x,y:l.top+a.scrollTop-n.y,width:l.width,height:l.height}}function nl(c){var t=new Map,i=new Set,e=[];c.forEach(function(o){t.set(o.name,o)});function b(o){i.add(o.name);var l=[].concat(o.requires||[],o.requiresIfExists||[]);l.forEach(function(a){if(!i.has(a)){var n=t.get(a);n&&b(n)}}),e.push(o)}return c.forEach(function(o){i.has(o.name)||b(o)}),e}function zc(c){var t=nl(c);return Uc.reduce(function(i,e){return i.concat(t.filter(function(b){return b.phase===e}))},[])}function jc(c){var t;return function(){return t||(t=new Promise(function(i){Promise.resolve().then(function(){t=void 0,i(c())})})),t}}function Hc(c){var t=c.reduce(function(i,e){var b=i[e.name];return i[e.name]=b?Object.assign({},b,e,{options:Object.assign({},b.options,e.options),data:Object.assign({},b.data,e.data)}):e,i},{});return Object.keys(t).map(function(i){return t[i]})}var qe={placement:"bottom",modifiers:[],strategy:"absolute"};function tb(){for(var c=arguments.length,t=new Array(c),i=0;i<c;i++)t[i]=arguments[i];return!t.some(function(e){return!(e&&typeof e.getBoundingClientRect=="function")})}function zt(c){c===void 0&&(c={});var t=c,i=t.defaultModifiers,e=i===void 0?[]:i,b=t.defaultOptions,o=b===void 0?qe:b;return function(a,n,r){r===void 0&&(r=o);var I={placement:"bottom",orderedModifiers:[],options:Object.assign({},qe,o),modifiersData:{},elements:{reference:a,popper:n},attributes:{},styles:{}},g=[],C=!1,d={state:I,setOptions:function(Z){var B=typeof Z=="function"?Z(I.options):Z;s(),I.options=Object.assign({},o,I.options,B),I.scrollParents={reference:_(a)?Ct(a):a.contextElement?Ct(a.contextElement):[],popper:Ct(n)};var G=zc(Hc([].concat(e,I.options.modifiers)));return I.orderedModifiers=G.filter(function(W){return W.enabled}),F(),d.update()},forceUpdate:function(){if(!C){var Z=I.elements,B=Z.reference,G=Z.popper;if(tb(B,G)){I.rects={reference:kc(B,ot(G),I.options.strategy==="fixed"),popper:vt(G)},I.reset=!1,I.placement=I.options.placement,I.orderedModifiers.forEach(function(y){return I.modifiersData[y.name]=Object.assign({},y.data)});for(var W=0;W<I.orderedModifiers.length;W++){if(I.reset===!0){I.reset=!1,W=-1;continue}var u=I.orderedModifiers[W],x=u.fn,Q=u.options,h=Q===void 0?{}:Q,f=u.name;typeof x=="function"&&(I=x({state:I,options:h,name:f,instance:d})||I)}}}},update:jc(function(){return new Promise(function(X){d.forceUpdate(),X(I)})}),destroy:function(){s(),C=!0}};if(!tb(a,n))return d;d.setOptions(r).then(function(X){!C&&r.onFirstUpdate&&r.onFirstUpdate(X)});function F(){I.orderedModifiers.forEach(function(X){var Z=X.name,B=X.options,G=B===void 0?{}:B,W=X.effect;if(typeof W=="function"){var u=W({state:I,name:Z,instance:d,options:G}),x=function(){};g.push(u||x)}})}function s(){g.forEach(function(X){return X()}),g=[]}return d}}var ib=zt();var al=[ci,oi,ii,qt],cb=zt({defaultModifiers:al});var Il=[ci,oi,ii,qt,qi,Pi,tc,Di,$i],Vi=zt({defaultModifiers:Il});var Gt=new Map,Sc={set(c,t,i){Gt.has(c)||Gt.set(c,new Map);let e=Gt.get(c);if(!e.has(t)&&e.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(e.keys())[0]}.`);return}e.set(t,i)},get(c,t){return Gt.has(c)&&Gt.get(c).get(t)||null},remove(c,t){if(!Gt.has(c))return;let i=Gt.get(c);i.delete(t),i.size===0&&Gt.delete(c)}},rl=1e6,dl=1e3,oe="transitionend",Ub=c=>(c&&window.CSS&&window.CSS.escape&&(c=c.replace(/#([^\s"#']+)/g,(t,i)=>`#${CSS.escape(i)}`)),c),gl=c=>c==null?`${c}`:Object.prototype.toString.call(c).match(/\s([a-z]+)/i)[1].toLowerCase(),sl=c=>{do c+=Math.floor(Math.random()*rl);while(document.getElementById(c));return c},ml=c=>{if(!c)return 0;let{transitionDuration:t,transitionDelay:i}=window.getComputedStyle(c),e=Number.parseFloat(t),b=Number.parseFloat(i);return!e&&!b?0:(t=t.split(",")[0],i=i.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(i))*dl)},yb=c=>{c.dispatchEvent(new Event(oe))},st=c=>!c||typeof c!="object"?!1:(typeof c.jquery<"u"&&(c=c[0]),typeof c.nodeType<"u"),Bt=c=>st(c)?c.jquery?c[0]:c:typeof c=="string"&&c.length>0?document.querySelector(Ub(c)):null,si=c=>{if(!st(c)||c.getClientRects().length===0)return!1;let t=getComputedStyle(c).getPropertyValue("visibility")==="visible",i=c.closest("details:not([open])");if(!i)return t;if(i!==c){let e=c.closest("summary");if(e&&e.parentNode!==i||e===null)return!1}return t},pt=c=>!c||c.nodeType!==Node.ELEMENT_NODE||c.classList.contains("disabled")?!0:typeof c.disabled<"u"?c.disabled:c.hasAttribute("disabled")&&c.getAttribute("disabled")!=="false",Yb=c=>{if(!document.documentElement.attachShadow)return null;if(typeof c.getRootNode=="function"){let t=c.getRootNode();return t instanceof ShadowRoot?t:null}return c instanceof ShadowRoot?c:c.parentNode?Yb(c.parentNode):null},rc=()=>{},Oi=c=>{c.offsetHeight},Ob=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Tc=[],Fl=c=>{document.readyState==="loading"?(Tc.length||document.addEventListener("DOMContentLoaded",()=>{for(let t of Tc)t()}),Tc.push(c)):c()},q=()=>document.documentElement.dir==="rtl",it=c=>{Fl(()=>{let t=Ob();if(t){let i=c.NAME,e=t.fn[i];t.fn[i]=c.jQueryInterface,t.fn[i].Constructor=c,t.fn[i].noConflict=()=>(t.fn[i]=e,c.jQueryInterface)}})},E=(c,t=[],i=c)=>typeof c=="function"?c(...t):i,vb=(c,t,i=!0)=>{if(!i){E(c);return}let b=ml(t)+5,o=!1,l=({target:a})=>{a===t&&(o=!0,t.removeEventListener(oe,l),E(c))};t.addEventListener(oe,l),setTimeout(()=>{o||yb(t)},b)},re=(c,t,i,e)=>{let b=c.length,o=c.indexOf(t);return o===-1?!i&&e?c[b-1]:c[0]:(o+=i?1:-1,e&&(o=(o+b)%b),c[Math.max(0,Math.min(o,b-1))])},Zl=/[^.]*(?=\..*)\.|.*/,Xl=/\..*/,Cl=/::\d+$/,Ec={},eb=1,Jb={mouseenter:"mouseover",mouseleave:"mouseout"},Gl=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Lb(c,t){return t&&`${t}::${eb++}`||c.uidEvent||eb++}function Nb(c){let t=Lb(c);return c.uidEvent=t,Ec[t]=Ec[t]||{},Ec[t]}function Bl(c,t){return function i(e){return de(e,{delegateTarget:c}),i.oneOff&&m.off(c,e.type,t),t.apply(c,[e])}}function pl(c,t,i){return function e(b){let o=c.querySelectorAll(t);for(let{target:l}=b;l&&l!==this;l=l.parentNode)for(let a of o)if(a===l)return de(b,{delegateTarget:l}),e.oneOff&&m.off(c,b.type,t,i),i.apply(l,[b])}}function wb(c,t,i=null){return Object.values(c).find(e=>e.callable===t&&e.delegationSelector===i)}function kb(c,t,i){let e=typeof t=="string",b=e?i:t||i,o=zb(c);return Gl.has(o)||(o=c),[e,b,o]}function bb(c,t,i,e,b){if(typeof t!="string"||!c)return;let[o,l,a]=kb(t,i,e);t in Jb&&(l=(F=>function(s){if(!s.relatedTarget||s.relatedTarget!==s.delegateTarget&&!s.delegateTarget.contains(s.relatedTarget))return F.call(this,s)})(l));let n=Nb(c),r=n[a]||(n[a]={}),I=wb(r,l,o?i:null);if(I){I.oneOff=I.oneOff&&b;return}let g=Lb(l,t.replace(Zl,"")),C=o?pl(c,i,l):Bl(c,l);C.delegationSelector=o?i:null,C.callable=l,C.oneOff=b,C.uidEvent=g,r[g]=C,c.addEventListener(a,C,o)}function le(c,t,i,e,b){let o=wb(t[i],e,b);o&&(c.removeEventListener(i,o,!!b),delete t[i][o.uidEvent])}function Wl(c,t,i,e){let b=t[i]||{};for(let[o,l]of Object.entries(b))o.includes(e)&&le(c,t,i,l.callable,l.delegationSelector)}function zb(c){return c=c.replace(Xl,""),Jb[c]||c}var m={on(c,t,i,e){bb(c,t,i,e,!1)},one(c,t,i,e){bb(c,t,i,e,!0)},off(c,t,i,e){if(typeof t!="string"||!c)return;let[b,o,l]=kb(t,i,e),a=l!==t,n=Nb(c),r=n[l]||{},I=t.startsWith(".");if(typeof o<"u"){if(!Object.keys(r).length)return;le(c,n,l,o,b?i:null);return}if(I)for(let g of Object.keys(n))Wl(c,n,g,t.slice(1));for(let[g,C]of Object.entries(r)){let d=g.replace(Cl,"");(!a||t.includes(d))&&le(c,n,l,C.callable,C.delegationSelector)}},trigger(c,t,i){if(typeof t!="string"||!c)return null;let e=Ob(),b=zb(t),o=t!==b,l=null,a=!0,n=!0,r=!1;o&&e&&(l=e.Event(t,i),e(c).trigger(l),a=!l.isPropagationStopped(),n=!l.isImmediatePropagationStopped(),r=l.isDefaultPrevented());let I=de(new Event(t,{bubbles:a,cancelable:!0}),i);return r&&I.preventDefault(),n&&c.dispatchEvent(I),I.defaultPrevented&&l&&l.preventDefault(),I}};function de(c,t={}){for(let[i,e]of Object.entries(t))try{c[i]=e}catch{Object.defineProperty(c,i,{configurable:!0,get(){return e}})}return c}function ob(c){if(c==="true")return!0;if(c==="false")return!1;if(c===Number(c).toString())return Number(c);if(c===""||c==="null")return null;if(typeof c!="string")return c;try{return JSON.parse(decodeURIComponent(c))}catch{return c}}function Dc(c){return c.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}var mt={setDataAttribute(c,t,i){c.setAttribute(`data-bs-${Dc(t)}`,i)},removeDataAttribute(c,t){c.removeAttribute(`data-bs-${Dc(t)}`)},getDataAttributes(c){if(!c)return{};let t={},i=Object.keys(c.dataset).filter(e=>e.startsWith("bs")&&!e.startsWith("bsConfig"));for(let e of i){let b=e.replace(/^bs/,"");b=b.charAt(0).toLowerCase()+b.slice(1,b.length),t[b]=ob(c.dataset[e])}return t},getDataAttribute(c,t){return ob(c.getAttribute(`data-bs-${Dc(t)}`))}},St=class{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,i){let e=st(i)?mt.getDataAttribute(i,"config"):{};return{...this.constructor.Default,...typeof e=="object"?e:{},...st(i)?mt.getDataAttributes(i):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,i=this.constructor.DefaultType){for(let[e,b]of Object.entries(i)){let o=t[e],l=st(o)?"element":gl(o);if(!new RegExp(b).test(l))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${e}" provided type "${l}" but expected type "${b}".`)}}},ul="5.3.3",K=class extends St{constructor(t,i){super(),t=Bt(t),t&&(this._element=t,this._config=this._getConfig(i),Sc.set(this._element,this.constructor.DATA_KEY,this))}dispose(){Sc.remove(this._element,this.constructor.DATA_KEY),m.off(this._element,this.constructor.EVENT_KEY);for(let t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,i,e=!0){vb(t,i,e)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return Sc.get(Bt(t),this.DATA_KEY)}static getOrCreateInstance(t,i={}){return this.getInstance(t)||new this(t,typeof i=="object"?i:null)}static get VERSION(){return ul}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}},Mc=c=>{let t=c.getAttribute("data-bs-target");if(!t||t==="#"){let i=c.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),t=i&&i!=="#"?i.trim():null}return t?t.split(",").map(i=>Ub(i)).join(","):null},p={find(c,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,c))},findOne(c,t=document.documentElement){return Element.prototype.querySelector.call(t,c)},children(c,t){return[].concat(...c.children).filter(i=>i.matches(t))},parents(c,t){let i=[],e=c.parentNode.closest(t);for(;e;)i.push(e),e=e.parentNode.closest(t);return i},prev(c,t){let i=c.previousElementSibling;for(;i;){if(i.matches(t))return[i];i=i.previousElementSibling}return[]},next(c,t){let i=c.nextElementSibling;for(;i;){if(i.matches(t))return[i];i=i.nextElementSibling}return[]},focusableChildren(c){let t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(i=>`${i}:not([tabindex^="-"])`).join(",");return this.find(t,c).filter(i=>!pt(i)&&si(i))},getSelectorFromElement(c){let t=Mc(c);return t&&p.findOne(t)?t:null},getElementFromSelector(c){let t=Mc(c);return t?p.findOne(t):null},getMultipleElementsFromSelector(c){let t=Mc(c);return t?p.find(t):[]}},Bc=(c,t="hide")=>{let i=`click.dismiss${c.EVENT_KEY}`,e=c.NAME;m.on(document,i,`[data-bs-dismiss="${e}"]`,function(b){if(["A","AREA"].includes(this.tagName)&&b.preventDefault(),pt(this))return;let o=p.getElementFromSelector(this)||this.closest(`.${e}`);c.getOrCreateInstance(o)[t]()})},Rl="alert",xl="bs.alert",jb=`.${xl}`,Ql=`close${jb}`,hl=`closed${jb}`,Vl="fade",Al="show",dc=class c extends K{static get NAME(){return Rl}close(){if(m.trigger(this._element,Ql).defaultPrevented)return;this._element.classList.remove(Al);let i=this._element.classList.contains(Vl);this._queueCallback(()=>this._destroyElement(),this._element,i)}_destroyElement(){this._element.remove(),m.trigger(this._element,hl),this.dispose()}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this);if(typeof t=="string"){if(i[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);i[t](this)}})}};Bc(dc,"close");it(dc);var fl="button",Ul="bs.button",yl=`.${Ul}`,Yl=".data-api",Ol="active",lb='[data-bs-toggle="button"]',vl=`click${yl}${Yl}`,gc=class c extends K{static get NAME(){return fl}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(Ol))}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this);t==="toggle"&&i[t]()})}};m.on(document,vl,lb,c=>{c.preventDefault();let t=c.target.closest(lb);gc.getOrCreateInstance(t).toggle()});it(gc);var Jl="swipe",mi=".bs.swipe",Ll=`touchstart${mi}`,Nl=`touchmove${mi}`,wl=`touchend${mi}`,kl=`pointerdown${mi}`,zl=`pointerup${mi}`,jl="touch",Hl="pen",Sl="pointer-event",Tl=40,El={endCallback:null,leftCallback:null,rightCallback:null},Dl={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"},sc=class c extends St{constructor(t,i){super(),this._element=t,!(!t||!c.isSupported())&&(this._config=this._getConfig(i),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return El}static get DefaultType(){return Dl}static get NAME(){return Jl}dispose(){m.off(this._element,mi)}_start(t){if(!this._supportPointerEvents){this._deltaX=t.touches[0].clientX;return}this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX)}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),E(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){let t=Math.abs(this._deltaX);if(t<=Tl)return;let i=t/this._deltaX;this._deltaX=0,i&&E(i>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(m.on(this._element,kl,t=>this._start(t)),m.on(this._element,zl,t=>this._end(t)),this._element.classList.add(Sl)):(m.on(this._element,Ll,t=>this._start(t)),m.on(this._element,Nl,t=>this._move(t)),m.on(this._element,wl,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===Hl||t.pointerType===jl)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}},Ml="carousel",Kl="bs.carousel",Rt=`.${Kl}`,Hb=".data-api",_l="ArrowLeft",Pl="ArrowRight",$l=500,Ai="next",li="prev",ai="left",ac="right",ql=`slide${Rt}`,Kc=`slid${Rt}`,tn=`keydown${Rt}`,cn=`mouseenter${Rt}`,en=`mouseleave${Rt}`,bn=`dragstart${Rt}`,on=`load${Rt}${Hb}`,ln=`click${Rt}${Hb}`,Sb="carousel",cc="active",nn="slide",an="carousel-item-end",In="carousel-item-start",rn="carousel-item-next",dn="carousel-item-prev",Tb=".active",Eb=".carousel-item",gn=Tb+Eb,sn=".carousel-item img",mn=".carousel-indicators",Fn="[data-bs-slide], [data-bs-slide-to]",Zn='[data-bs-ride="carousel"]',Xn={[_l]:ac,[Pl]:ai},Cn={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Gn={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"},Ui=class c extends K{constructor(t,i){super(t,i),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=p.findOne(mn,this._element),this._addEventListeners(),this._config.ride===Sb&&this.cycle()}static get Default(){return Cn}static get DefaultType(){return Gn}static get NAME(){return Ml}next(){this._slide(Ai)}nextWhenVisible(){!document.hidden&&si(this._element)&&this.next()}prev(){this._slide(li)}pause(){this._isSliding&&yb(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding){m.one(this._element,Kc,()=>this.cycle());return}this.cycle()}}to(t){let i=this._getItems();if(t>i.length-1||t<0)return;if(this._isSliding){m.one(this._element,Kc,()=>this.to(t));return}let e=this._getItemIndex(this._getActive());if(e===t)return;let b=t>e?Ai:li;this._slide(b,i[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&m.on(this._element,tn,t=>this._keydown(t)),this._config.pause==="hover"&&(m.on(this._element,cn,()=>this.pause()),m.on(this._element,en,()=>this._maybeEnableCycle())),this._config.touch&&sc.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(let e of p.find(sn,this._element))m.on(e,bn,b=>b.preventDefault());let i={leftCallback:()=>this._slide(this._directionToOrder(ai)),rightCallback:()=>this._slide(this._directionToOrder(ac)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),$l+this._config.interval))}};this._swipeHelper=new sc(this._element,i)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;let i=Xn[t.key];i&&(t.preventDefault(),this._slide(this._directionToOrder(i)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;let i=p.findOne(Tb,this._indicatorsElement);i.classList.remove(cc),i.removeAttribute("aria-current");let e=p.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);e&&(e.classList.add(cc),e.setAttribute("aria-current","true"))}_updateInterval(){let t=this._activeElement||this._getActive();if(!t)return;let i=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=i||this._config.defaultInterval}_slide(t,i=null){if(this._isSliding)return;let e=this._getActive(),b=t===Ai,o=i||re(this._getItems(),e,b,this._config.wrap);if(o===e)return;let l=this._getItemIndex(o),a=d=>m.trigger(this._element,d,{relatedTarget:o,direction:this._orderToDirection(t),from:this._getItemIndex(e),to:l});if(a(ql).defaultPrevented||!e||!o)return;let r=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(l),this._activeElement=o;let I=b?In:an,g=b?rn:dn;o.classList.add(g),Oi(o),e.classList.add(I),o.classList.add(I);let C=()=>{o.classList.remove(I,g),o.classList.add(cc),e.classList.remove(cc,g,I),this._isSliding=!1,a(Kc)};this._queueCallback(C,e,this._isAnimated()),r&&this.cycle()}_isAnimated(){return this._element.classList.contains(nn)}_getActive(){return p.findOne(gn,this._element)}_getItems(){return p.find(Eb,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return q()?t===ai?li:Ai:t===ai?Ai:li}_orderToDirection(t){return q()?t===li?ai:ac:t===li?ac:ai}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this,t);if(typeof t=="number"){i.to(t);return}if(typeof t=="string"){if(i[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);i[t]()}})}};m.on(document,ln,Fn,function(c){let t=p.getElementFromSelector(this);if(!t||!t.classList.contains(Sb))return;c.preventDefault();let i=Ui.getOrCreateInstance(t),e=this.getAttribute("data-bs-slide-to");if(e){i.to(e),i._maybeEnableCycle();return}if(mt.getDataAttribute(this,"slide")==="next"){i.next(),i._maybeEnableCycle();return}i.prev(),i._maybeEnableCycle()});m.on(window,on,()=>{let c=p.find(Zn);for(let t of c)Ui.getOrCreateInstance(t)});it(Ui);var Bn="collapse",pn="bs.collapse",vi=`.${pn}`,Wn=".data-api",un=`show${vi}`,Rn=`shown${vi}`,xn=`hide${vi}`,Qn=`hidden${vi}`,hn=`click${vi}${Wn}`,_c="show",ri="collapse",ec="collapsing",Vn="collapsed",An=`:scope .${ri} .${ri}`,fn="collapse-horizontal",Un="width",yn="height",Yn=".collapse.show, .collapse.collapsing",ne='[data-bs-toggle="collapse"]',On={parent:null,toggle:!0},vn={parent:"(null|element)",toggle:"boolean"},mc=class c extends K{constructor(t,i){super(t,i),this._isTransitioning=!1,this._triggerArray=[];let e=p.find(ne);for(let b of e){let o=p.getSelectorFromElement(b),l=p.find(o).filter(a=>a===this._element);o!==null&&l.length&&this._triggerArray.push(b)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return On}static get DefaultType(){return vn}static get NAME(){return Bn}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(Yn).filter(a=>a!==this._element).map(a=>c.getOrCreateInstance(a,{toggle:!1}))),t.length&&t[0]._isTransitioning||m.trigger(this._element,un).defaultPrevented)return;for(let a of t)a.hide();let e=this._getDimension();this._element.classList.remove(ri),this._element.classList.add(ec),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;let b=()=>{this._isTransitioning=!1,this._element.classList.remove(ec),this._element.classList.add(ri,_c),this._element.style[e]="",m.trigger(this._element,Rn)},l=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback(b,this._element,!0),this._element.style[e]=`${this._element[l]}px`}hide(){if(this._isTransitioning||!this._isShown()||m.trigger(this._element,xn).defaultPrevented)return;let i=this._getDimension();this._element.style[i]=`${this._element.getBoundingClientRect()[i]}px`,Oi(this._element),this._element.classList.add(ec),this._element.classList.remove(ri,_c);for(let b of this._triggerArray){let o=p.getElementFromSelector(b);o&&!this._isShown(o)&&this._addAriaAndCollapsedClass([b],!1)}this._isTransitioning=!0;let e=()=>{this._isTransitioning=!1,this._element.classList.remove(ec),this._element.classList.add(ri),m.trigger(this._element,Qn)};this._element.style[i]="",this._queueCallback(e,this._element,!0)}_isShown(t=this._element){return t.classList.contains(_c)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=Bt(t.parent),t}_getDimension(){return this._element.classList.contains(fn)?Un:yn}_initializeChildren(){if(!this._config.parent)return;let t=this._getFirstLevelChildren(ne);for(let i of t){let e=p.getElementFromSelector(i);e&&this._addAriaAndCollapsedClass([i],this._isShown(e))}}_getFirstLevelChildren(t){let i=p.find(An,this._config.parent);return p.find(t,this._config.parent).filter(e=>!i.includes(e))}_addAriaAndCollapsedClass(t,i){if(t.length)for(let e of t)e.classList.toggle(Vn,!i),e.setAttribute("aria-expanded",i)}static jQueryInterface(t){let i={};return typeof t=="string"&&/show|hide/.test(t)&&(i.toggle=!1),this.each(function(){let e=c.getOrCreateInstance(this,i);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}};m.on(document,hn,ne,function(c){(c.target.tagName==="A"||c.delegateTarget&&c.delegateTarget.tagName==="A")&&c.preventDefault();for(let t of p.getMultipleElementsFromSelector(this))mc.getOrCreateInstance(t,{toggle:!1}).toggle()});it(mc);var nb="dropdown",Jn="bs.dropdown",Tt=`.${Jn}`,ge=".data-api",Ln="Escape",ab="Tab",Nn="ArrowUp",Ib="ArrowDown",wn=2,kn=`hide${Tt}`,zn=`hidden${Tt}`,jn=`show${Tt}`,Hn=`shown${Tt}`,Db=`click${Tt}${ge}`,Mb=`keydown${Tt}${ge}`,Sn=`keyup${Tt}${ge}`,Ii="show",Tn="dropup",En="dropend",Dn="dropstart",Mn="dropup-center",Kn="dropdown-center",jt='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',_n=`${jt}.${Ii}`,Ic=".dropdown-menu",Pn=".navbar",$n=".navbar-nav",qn=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",ta=q()?"top-end":"top-start",ia=q()?"top-start":"top-end",ca=q()?"bottom-end":"bottom-start",ea=q()?"bottom-start":"bottom-end",ba=q()?"left-start":"right-start",oa=q()?"right-start":"left-start",la="top",na="bottom",aa={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Ia={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"},Wt=class c extends K{constructor(t,i){super(t,i),this._popper=null,this._parent=this._element.parentNode,this._menu=p.next(this._element,Ic)[0]||p.prev(this._element,Ic)[0]||p.findOne(Ic,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return aa}static get DefaultType(){return Ia}static get NAME(){return nb}toggle(){return this._isShown()?this.hide():this.show()}show(){if(pt(this._element)||this._isShown())return;let t={relatedTarget:this._element};if(!m.trigger(this._element,jn,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest($n))for(let e of[].concat(...document.body.children))m.on(e,"mouseover",rc);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Ii),this._element.classList.add(Ii),m.trigger(this._element,Hn,t)}}hide(){if(pt(this._element)||!this._isShown())return;let t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!m.trigger(this._element,kn,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))m.off(e,"mouseover",rc);this._popper&&this._popper.destroy(),this._menu.classList.remove(Ii),this._element.classList.remove(Ii),this._element.setAttribute("aria-expanded","false"),mt.removeDataAttribute(this._menu,"popper"),m.trigger(this._element,zn,t)}}_getConfig(t){if(t=super._getConfig(t),typeof t.reference=="object"&&!st(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${nb.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(typeof ic>"u")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;this._config.reference==="parent"?t=this._parent:st(this._config.reference)?t=Bt(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);let i=this._getPopperConfig();this._popper=Vi(t,this._menu,i)}_isShown(){return this._menu.classList.contains(Ii)}_getPlacement(){let t=this._parent;if(t.classList.contains(En))return ba;if(t.classList.contains(Dn))return oa;if(t.classList.contains(Mn))return la;if(t.classList.contains(Kn))return na;let i=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains(Tn)?i?ia:ta:i?ea:ca}_detectNavbar(){return this._element.closest(Pn)!==null}_getOffset(){let{offset:t}=this._config;return typeof t=="string"?t.split(",").map(i=>Number.parseInt(i,10)):typeof t=="function"?i=>t(i,this._element):t}_getPopperConfig(){let t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(mt.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...E(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:i}){let e=p.find(qn,this._menu).filter(b=>si(b));e.length&&re(e,i,t===Ib,!e.includes(i)).focus()}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof i[t]>"u")throw new TypeError(`No method named "${t}"`);i[t]()}})}static clearMenus(t){if(t.button===wn||t.type==="keyup"&&t.key!==ab)return;let i=p.find(_n);for(let e of i){let b=c.getInstance(e);if(!b||b._config.autoClose===!1)continue;let o=t.composedPath(),l=o.includes(b._menu);if(o.includes(b._element)||b._config.autoClose==="inside"&&!l||b._config.autoClose==="outside"&&l||b._menu.contains(t.target)&&(t.type==="keyup"&&t.key===ab||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;let a={relatedTarget:b._element};t.type==="click"&&(a.clickEvent=t),b._completeHide(a)}}static dataApiKeydownHandler(t){let i=/input|textarea/i.test(t.target.tagName),e=t.key===Ln,b=[Nn,Ib].includes(t.key);if(!b&&!e||i&&!e)return;t.preventDefault();let o=this.matches(jt)?this:p.prev(this,jt)[0]||p.next(this,jt)[0]||p.findOne(jt,t.delegateTarget.parentNode),l=c.getOrCreateInstance(o);if(b){t.stopPropagation(),l.show(),l._selectMenuItem(t);return}l._isShown()&&(t.stopPropagation(),l.hide(),o.focus())}};m.on(document,Mb,jt,Wt.dataApiKeydownHandler);m.on(document,Mb,Ic,Wt.dataApiKeydownHandler);m.on(document,Db,Wt.clearMenus);m.on(document,Sn,Wt.clearMenus);m.on(document,Db,jt,function(c){c.preventDefault(),Wt.getOrCreateInstance(this).toggle()});it(Wt);var Kb="backdrop",ra="fade",rb="show",db=`mousedown.bs.${Kb}`,da={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},ga={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"},Fc=class extends St{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return da}static get DefaultType(){return ga}static get NAME(){return Kb}show(t){if(!this._config.isVisible){E(t);return}this._append();let i=this._getElement();this._config.isAnimated&&Oi(i),i.classList.add(rb),this._emulateAnimation(()=>{E(t)})}hide(t){if(!this._config.isVisible){E(t);return}this._getElement().classList.remove(rb),this._emulateAnimation(()=>{this.dispose(),E(t)})}dispose(){this._isAppended&&(m.off(this._element,db),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){let t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(ra),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=Bt(t.rootElement),t}_append(){if(this._isAppended)return;let t=this._getElement();this._config.rootElement.append(t),m.on(t,db,()=>{E(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){vb(t,this._getElement(),this._config.isAnimated)}},sa="focustrap",ma="bs.focustrap",Zc=`.${ma}`,Fa=`focusin${Zc}`,Za=`keydown.tab${Zc}`,Xa="Tab",Ca="forward",gb="backward",Ga={autofocus:!0,trapElement:null},Ba={autofocus:"boolean",trapElement:"element"},Xc=class extends St{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Ga}static get DefaultType(){return Ba}static get NAME(){return sa}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),m.off(document,Zc),m.on(document,Fa,t=>this._handleFocusin(t)),m.on(document,Za,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,m.off(document,Zc))}_handleFocusin(t){let{trapElement:i}=this._config;if(t.target===document||t.target===i||i.contains(t.target))return;let e=p.focusableChildren(i);e.length===0?i.focus():this._lastTabNavDirection===gb?e[e.length-1].focus():e[0].focus()}_handleKeydown(t){t.key===Xa&&(this._lastTabNavDirection=t.shiftKey?gb:Ca)}},sb=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",mb=".sticky-top",bc="padding-right",Fb="margin-right",yi=class{constructor(){this._element=document.body}getWidth(){let t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){let t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,bc,i=>i+t),this._setElementAttributes(sb,bc,i=>i+t),this._setElementAttributes(mb,Fb,i=>i-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,bc),this._resetElementAttributes(sb,bc),this._resetElementAttributes(mb,Fb)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,i,e){let b=this.getWidth(),o=l=>{if(l!==this._element&&window.innerWidth>l.clientWidth+b)return;this._saveInitialAttribute(l,i);let a=window.getComputedStyle(l).getPropertyValue(i);l.style.setProperty(i,`${e(Number.parseFloat(a))}px`)};this._applyManipulationCallback(t,o)}_saveInitialAttribute(t,i){let e=t.style.getPropertyValue(i);e&&mt.setDataAttribute(t,i,e)}_resetElementAttributes(t,i){let e=b=>{let o=mt.getDataAttribute(b,i);if(o===null){b.style.removeProperty(i);return}mt.removeDataAttribute(b,i),b.style.setProperty(i,o)};this._applyManipulationCallback(t,e)}_applyManipulationCallback(t,i){if(st(t)){i(t);return}for(let e of p.find(t,this._element))i(e)}},pa="modal",Wa="bs.modal",tt=`.${Wa}`,ua=".data-api",Ra="Escape",xa=`hide${tt}`,Qa=`hidePrevented${tt}`,_b=`hidden${tt}`,Pb=`show${tt}`,ha=`shown${tt}`,Va=`resize${tt}`,Aa=`click.dismiss${tt}`,fa=`mousedown.dismiss${tt}`,Ua=`keydown.dismiss${tt}`,ya=`click${tt}${ua}`,Zb="modal-open",Ya="fade",Xb="show",Pc="modal-static",Oa=".modal.show",va=".modal-dialog",Ja=".modal-body",La='[data-bs-toggle="modal"]',Na={backdrop:!0,focus:!0,keyboard:!0},wa={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"},di=class c extends K{constructor(t,i){super(t,i),this._dialog=p.findOne(va,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new yi,this._addEventListeners()}static get Default(){return Na}static get DefaultType(){return wa}static get NAME(){return pa}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||m.trigger(this._element,Pb,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Zb),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||m.trigger(this._element,xa).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Xb),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){m.off(window,tt),m.off(this._dialog,tt),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Fc({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Xc({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;let i=p.findOne(Ja,this._dialog);i&&(i.scrollTop=0),Oi(this._element),this._element.classList.add(Xb);let e=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,m.trigger(this._element,ha,{relatedTarget:t})};this._queueCallback(e,this._dialog,this._isAnimated())}_addEventListeners(){m.on(this._element,Ua,t=>{if(t.key===Ra){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),m.on(window,Va,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),m.on(this._element,fa,t=>{m.one(this._element,Aa,i=>{if(!(this._element!==t.target||this._element!==i.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Zb),this._resetAdjustments(),this._scrollBar.reset(),m.trigger(this._element,_b)})}_isAnimated(){return this._element.classList.contains(Ya)}_triggerBackdropTransition(){if(m.trigger(this._element,Qa).defaultPrevented)return;let i=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;e==="hidden"||this._element.classList.contains(Pc)||(i||(this._element.style.overflowY="hidden"),this._element.classList.add(Pc),this._queueCallback(()=>{this._element.classList.remove(Pc),this._queueCallback(()=>{this._element.style.overflowY=e},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){let t=this._element.scrollHeight>document.documentElement.clientHeight,i=this._scrollBar.getWidth(),e=i>0;if(e&&!t){let b=q()?"paddingLeft":"paddingRight";this._element.style[b]=`${i}px`}if(!e&&t){let b=q()?"paddingRight":"paddingLeft";this._element.style[b]=`${i}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,i){return this.each(function(){let e=c.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t](i)}})}};m.on(document,ya,La,function(c){let t=p.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&c.preventDefault(),m.one(t,Pb,b=>{b.defaultPrevented||m.one(t,_b,()=>{si(this)&&this.focus()})});let i=p.findOne(Oa);i&&di.getInstance(i).hide(),di.getOrCreateInstance(t).toggle(this)});Bc(di);it(di);var ka="offcanvas",za="bs.offcanvas",Ft=`.${za}`,$b=".data-api",ja=`load${Ft}${$b}`,Ha="Escape",Cb="show",Gb="showing",Bb="hiding",Sa="offcanvas-backdrop",qb=".offcanvas.show",Ta=`show${Ft}`,Ea=`shown${Ft}`,Da=`hide${Ft}`,pb=`hidePrevented${Ft}`,to=`hidden${Ft}`,Ma=`resize${Ft}`,Ka=`click${Ft}${$b}`,_a=`keydown.dismiss${Ft}`,Pa='[data-bs-toggle="offcanvas"]',$a={backdrop:!0,keyboard:!0,scroll:!1},qa={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"},ut=class c extends K{constructor(t,i){super(t,i),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return $a}static get DefaultType(){return qa}static get NAME(){return ka}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||m.trigger(this._element,Ta,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new yi().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Gb);let e=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(Cb),this._element.classList.remove(Gb),m.trigger(this._element,Ea,{relatedTarget:t})};this._queueCallback(e,this._element,!0)}hide(){if(!this._isShown||m.trigger(this._element,Da).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Bb),this._backdrop.hide();let i=()=>{this._element.classList.remove(Cb,Bb),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new yi().reset(),m.trigger(this._element,to)};this._queueCallback(i,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){let t=()=>{if(this._config.backdrop==="static"){m.trigger(this._element,pb);return}this.hide()},i=!!this._config.backdrop;return new Fc({className:Sa,isVisible:i,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:i?t:null})}_initializeFocusTrap(){return new Xc({trapElement:this._element})}_addEventListeners(){m.on(this._element,_a,t=>{if(t.key===Ha){if(this._config.keyboard){this.hide();return}m.trigger(this._element,pb)}})}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this,t);if(typeof t=="string"){if(i[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);i[t](this)}})}};m.on(document,Ka,Pa,function(c){let t=p.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&c.preventDefault(),pt(this))return;m.one(t,to,()=>{si(this)&&this.focus()});let i=p.findOne(qb);i&&i!==t&&ut.getInstance(i).hide(),ut.getOrCreateInstance(t).toggle(this)});m.on(window,ja,()=>{for(let c of p.find(qb))ut.getOrCreateInstance(c).show()});m.on(window,Ma,()=>{for(let c of p.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(c).position!=="fixed"&&ut.getOrCreateInstance(c).hide()});Bc(ut);it(ut);var tI=/^aria-[\w-]*$/i,io={"*":["class","dir","id","lang","role",tI],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},iI=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),cI=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,eI=(c,t)=>{let i=c.nodeName.toLowerCase();return t.includes(i)?iI.has(i)?!!cI.test(c.nodeValue):!0:t.filter(e=>e instanceof RegExp).some(e=>e.test(i))};function bI(c,t,i){if(!c.length)return c;if(i&&typeof i=="function")return i(c);let b=new window.DOMParser().parseFromString(c,"text/html"),o=[].concat(...b.body.querySelectorAll("*"));for(let l of o){let a=l.nodeName.toLowerCase();if(!Object.keys(t).includes(a)){l.remove();continue}let n=[].concat(...l.attributes),r=[].concat(t["*"]||[],t[a]||[]);for(let I of n)eI(I,r)||l.removeAttribute(I.nodeName)}return b.body.innerHTML}var oI="TemplateFactory",lI={allowList:io,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},nI={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},aI={entry:"(string|element|function|null)",selector:"(string|element)"},ae=class extends St{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return lI}static get DefaultType(){return nI}static get NAME(){return oI}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){let t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(let[b,o]of Object.entries(this._config.content))this._setContent(t,o,b);let i=t.children[0],e=this._resolvePossibleFunction(this._config.extraClass);return e&&i.classList.add(...e.split(" ")),i}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(let[i,e]of Object.entries(t))super._typeCheckConfig({selector:i,entry:e},aI)}_setContent(t,i,e){let b=p.findOne(e,t);if(b){if(i=this._resolvePossibleFunction(i),!i){b.remove();return}if(st(i)){this._putElementInTemplate(Bt(i),b);return}if(this._config.html){b.innerHTML=this._maybeSanitize(i);return}b.textContent=i}}_maybeSanitize(t){return this._config.sanitize?bI(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return E(t,[this])}_putElementInTemplate(t,i){if(this._config.html){i.innerHTML="",i.append(t);return}i.textContent=t.textContent}},II="tooltip",rI=new Set(["sanitize","allowList","sanitizeFn"]),$c="fade",dI="modal",oc="show",gI=".tooltip-inner",Wb=`.${dI}`,ub="hide.bs.modal",fi="hover",qc="focus",sI="click",mI="manual",FI="hide",ZI="hidden",XI="show",CI="shown",GI="inserted",BI="click",pI="focusin",WI="focusout",uI="mouseenter",RI="mouseleave",xI={AUTO:"auto",TOP:"top",RIGHT:q()?"left":"right",BOTTOM:"bottom",LEFT:q()?"right":"left"},QI={allowList:io,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},hI={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"},gi=class c extends K{constructor(t,i){if(typeof ic>"u")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,i),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return QI}static get DefaultType(){return hI}static get NAME(){return II}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),m.off(this._element.closest(Wb),ub,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;let t=m.trigger(this._element,this.constructor.eventName(XI)),e=(Yb(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();let b=this._getTipElement();this._element.setAttribute("aria-describedby",b.getAttribute("id"));let{container:o}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(o.append(b),m.trigger(this._element,this.constructor.eventName(GI))),this._popper=this._createPopper(b),b.classList.add(oc),"ontouchstart"in document.documentElement)for(let a of[].concat(...document.body.children))m.on(a,"mouseover",rc);let l=()=>{m.trigger(this._element,this.constructor.eventName(CI)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(l,this.tip,this._isAnimated())}hide(){if(!this._isShown()||m.trigger(this._element,this.constructor.eventName(FI)).defaultPrevented)return;if(this._getTipElement().classList.remove(oc),"ontouchstart"in document.documentElement)for(let b of[].concat(...document.body.children))m.off(b,"mouseover",rc);this._activeTrigger[sI]=!1,this._activeTrigger[qc]=!1,this._activeTrigger[fi]=!1,this._isHovered=null;let e=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),m.trigger(this._element,this.constructor.eventName(ZI)))};this._queueCallback(e,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){let i=this._getTemplateFactory(t).toHtml();if(!i)return null;i.classList.remove($c,oc),i.classList.add(`bs-${this.constructor.NAME}-auto`);let e=sl(this.constructor.NAME).toString();return i.setAttribute("id",e),this._isAnimated()&&i.classList.add($c),i}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new ae({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[gI]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains($c)}_isShown(){return this.tip&&this.tip.classList.contains(oc)}_createPopper(t){let i=E(this._config.placement,[this,t,this._element]),e=xI[i.toUpperCase()];return Vi(this._element,t,this._getPopperConfig(e))}_getOffset(){let{offset:t}=this._config;return typeof t=="string"?t.split(",").map(i=>Number.parseInt(i,10)):typeof t=="function"?i=>t(i,this._element):t}_resolvePossibleFunction(t){return E(t,[this._element])}_getPopperConfig(t){let i={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...i,...E(this._config.popperConfig,[i])}}_setListeners(){let t=this._config.trigger.split(" ");for(let i of t)if(i==="click")m.on(this._element,this.constructor.eventName(BI),this._config.selector,e=>{this._initializeOnDelegatedTarget(e).toggle()});else if(i!==mI){let e=i===fi?this.constructor.eventName(uI):this.constructor.eventName(pI),b=i===fi?this.constructor.eventName(RI):this.constructor.eventName(WI);m.on(this._element,e,this._config.selector,o=>{let l=this._initializeOnDelegatedTarget(o);l._activeTrigger[o.type==="focusin"?qc:fi]=!0,l._enter()}),m.on(this._element,b,this._config.selector,o=>{let l=this._initializeOnDelegatedTarget(o);l._activeTrigger[o.type==="focusout"?qc:fi]=l._element.contains(o.relatedTarget),l._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},m.on(this._element.closest(Wb),ub,this._hideModalHandler)}_fixTitle(){let t=this._element.getAttribute("title");t&&(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,i){clearTimeout(this._timeout),this._timeout=setTimeout(t,i)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){let i=mt.getDataAttributes(this._element);for(let e of Object.keys(i))rI.has(e)&&delete i[e];return t={...i,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:Bt(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){let t={};for(let[i,e]of Object.entries(this._config))this.constructor.Default[i]!==e&&(t[i]=e);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof i[t]>"u")throw new TypeError(`No method named "${t}"`);i[t]()}})}};it(gi);var VI="popover",AI=".popover-header",fI=".popover-body",UI={...gi.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},yI={...gi.DefaultType,content:"(null|string|element|function)"},Ie=class c extends gi{static get Default(){return UI}static get DefaultType(){return yI}static get NAME(){return VI}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[AI]:this._getTitle(),[fI]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof i[t]>"u")throw new TypeError(`No method named "${t}"`);i[t]()}})}};it(Ie);var YI="scrollspy",OI="bs.scrollspy",se=`.${OI}`,vI=".data-api",JI=`activate${se}`,Rb=`click${se}`,LI=`load${se}${vI}`,NI="dropdown-item",ni="active",wI='[data-bs-spy="scroll"]',te="[href]",kI=".nav, .list-group",xb=".nav-link",zI=".nav-item",jI=".list-group-item",HI=`${xb}, ${zI} > ${xb}, ${jI}`,SI=".dropdown",TI=".dropdown-toggle",EI={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},DI={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"},Cc=class c extends K{constructor(t,i){super(t,i),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return EI}static get DefaultType(){return DI}static get NAME(){return YI}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(let t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=Bt(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(i=>Number.parseFloat(i))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(m.off(this._config.target,Rb),m.on(this._config.target,Rb,te,t=>{let i=this._observableSections.get(t.target.hash);if(i){t.preventDefault();let e=this._rootElement||window,b=i.offsetTop-this._element.offsetTop;if(e.scrollTo){e.scrollTo({top:b,behavior:"smooth"});return}e.scrollTop=b}}))}_getNewObserver(){let t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(i=>this._observerCallback(i),t)}_observerCallback(t){let i=l=>this._targetLinks.get(`#${l.target.id}`),e=l=>{this._previousScrollData.visibleEntryTop=l.target.offsetTop,this._process(i(l))},b=(this._rootElement||document.documentElement).scrollTop,o=b>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=b;for(let l of t){if(!l.isIntersecting){this._activeTarget=null,this._clearActiveClass(i(l));continue}let a=l.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(o&&a){if(e(l),!b)return;continue}!o&&!a&&e(l)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;let t=p.find(te,this._config.target);for(let i of t){if(!i.hash||pt(i))continue;let e=p.findOne(decodeURI(i.hash),this._element);si(e)&&(this._targetLinks.set(decodeURI(i.hash),i),this._observableSections.set(i.hash,e))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(ni),this._activateParents(t),m.trigger(this._element,JI,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(NI)){p.findOne(TI,t.closest(SI)).classList.add(ni);return}for(let i of p.parents(t,kI))for(let e of p.prev(i,HI))e.classList.add(ni)}_clearActiveClass(t){t.classList.remove(ni);let i=p.find(`${te}.${ni}`,t);for(let e of i)e.classList.remove(ni)}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this,t);if(typeof t=="string"){if(i[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);i[t]()}})}};m.on(window,LI,()=>{for(let c of p.find(wI))Cc.getOrCreateInstance(c)});it(Cc);var MI="tab",KI="bs.tab",Et=`.${KI}`,_I=`hide${Et}`,PI=`hidden${Et}`,$I=`show${Et}`,qI=`shown${Et}`,tr=`click${Et}`,ir=`keydown${Et}`,cr=`load${Et}`,er="ArrowLeft",Qb="ArrowRight",br="ArrowUp",hb="ArrowDown",ie="Home",Vb="End",Ht="active",Ab="fade",ce="show",or="dropdown",co=".dropdown-toggle",lr=".dropdown-menu",ee=`:not(${co})`,nr='.list-group, .nav, [role="tablist"]',ar=".nav-item, .list-group-item",Ir=`.nav-link${ee}, .list-group-item${ee}, [role="tab"]${ee}`,eo='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',be=`${Ir}, ${eo}`,rr=`.${Ht}[data-bs-toggle="tab"], .${Ht}[data-bs-toggle="pill"], .${Ht}[data-bs-toggle="list"]`,Yi=class c extends K{constructor(t){super(t),this._parent=this._element.closest(nr),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),m.on(this._element,ir,i=>this._keydown(i)))}static get NAME(){return MI}show(){let t=this._element;if(this._elemIsActive(t))return;let i=this._getActiveElem(),e=i?m.trigger(i,_I,{relatedTarget:t}):null;m.trigger(t,$I,{relatedTarget:i}).defaultPrevented||e&&e.defaultPrevented||(this._deactivate(i,t),this._activate(t,i))}_activate(t,i){if(!t)return;t.classList.add(Ht),this._activate(p.getElementFromSelector(t));let e=()=>{if(t.getAttribute("role")!=="tab"){t.classList.add(ce);return}t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),m.trigger(t,qI,{relatedTarget:i})};this._queueCallback(e,t,t.classList.contains(Ab))}_deactivate(t,i){if(!t)return;t.classList.remove(Ht),t.blur(),this._deactivate(p.getElementFromSelector(t));let e=()=>{if(t.getAttribute("role")!=="tab"){t.classList.remove(ce);return}t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),m.trigger(t,PI,{relatedTarget:i})};this._queueCallback(e,t,t.classList.contains(Ab))}_keydown(t){if(![er,Qb,br,hb,ie,Vb].includes(t.key))return;t.stopPropagation(),t.preventDefault();let i=this._getChildren().filter(b=>!pt(b)),e;if([ie,Vb].includes(t.key))e=i[t.key===ie?0:i.length-1];else{let b=[Qb,hb].includes(t.key);e=re(i,t.target,b,!0)}e&&(e.focus({preventScroll:!0}),c.getOrCreateInstance(e).show())}_getChildren(){return p.find(be,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,i){this._setAttributeIfNotExists(t,"role","tablist");for(let e of i)this._setInitialAttributesOnChild(e)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);let i=this._elemIsActive(t),e=this._getOuterElement(t);t.setAttribute("aria-selected",i),e!==t&&this._setAttributeIfNotExists(e,"role","presentation"),i||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){let i=p.getElementFromSelector(t);i&&(this._setAttributeIfNotExists(i,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(i,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,i){let e=this._getOuterElement(t);if(!e.classList.contains(or))return;let b=(o,l)=>{let a=p.findOne(o,e);a&&a.classList.toggle(l,i)};b(co,Ht),b(lr,ce),e.setAttribute("aria-expanded",i)}_setAttributeIfNotExists(t,i,e){t.hasAttribute(i)||t.setAttribute(i,e)}_elemIsActive(t){return t.classList.contains(Ht)}_getInnerElement(t){return t.matches(be)?t:p.findOne(be,t)}_getOuterElement(t){return t.closest(ar)||t}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this);if(typeof t=="string"){if(i[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);i[t]()}})}};m.on(document,tr,eo,function(c){["A","AREA"].includes(this.tagName)&&c.preventDefault(),!pt(this)&&Yi.getOrCreateInstance(this).show()});m.on(window,cr,()=>{for(let c of p.find(rr))Yi.getOrCreateInstance(c)});it(Yi);var dr="toast",gr="bs.toast",xt=`.${gr}`,sr=`mouseover${xt}`,mr=`mouseout${xt}`,Fr=`focusin${xt}`,Zr=`focusout${xt}`,Xr=`hide${xt}`,Cr=`hidden${xt}`,Gr=`show${xt}`,Br=`shown${xt}`,pr="fade",fb="hide",lc="show",nc="showing",Wr={animation:"boolean",autohide:"boolean",delay:"number"},ur={animation:!0,autohide:!0,delay:5e3},Gc=class c extends K{constructor(t,i){super(t,i),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return ur}static get DefaultType(){return Wr}static get NAME(){return dr}show(){if(m.trigger(this._element,Gr).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(pr);let i=()=>{this._element.classList.remove(nc),m.trigger(this._element,Br),this._maybeScheduleHide()};this._element.classList.remove(fb),Oi(this._element),this._element.classList.add(lc,nc),this._queueCallback(i,this._element,this._config.animation)}hide(){if(!this.isShown()||m.trigger(this._element,Xr).defaultPrevented)return;let i=()=>{this._element.classList.add(fb),this._element.classList.remove(nc,lc),m.trigger(this._element,Cr)};this._element.classList.add(nc),this._queueCallback(i,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(lc),super.dispose()}isShown(){return this._element.classList.contains(lc)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,i){switch(t.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=i;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=i;break}}if(i){this._clearTimeout();return}let e=t.relatedTarget;this._element===e||this._element.contains(e)||this._maybeScheduleHide()}_setListeners(){m.on(this._element,sr,t=>this._onInteraction(t,!0)),m.on(this._element,mr,t=>this._onInteraction(t,!1)),m.on(this._element,Fr,t=>this._onInteraction(t,!0)),m.on(this._element,Zr,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){let i=c.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof i[t]>"u")throw new TypeError(`No method named "${t}"`);i[t](this)}})}};Bc(Gc);it(Gc);var Be=globalThis,pc=Be.trustedTypes,bo=pc?pc.createPolicy("lit-html",{createHTML:c=>c}):void 0,ro="$lit$",Qt=`lit$${Math.random().toFixed(9).slice(2)}$`,go="?"+Qt,Rr=`<${go}>`,Kt=document,Li=()=>Kt.createComment(""),Ni=c=>c===null||typeof c!="object"&&typeof c!="function",pe=Array.isArray,xr=c=>pe(c)||typeof c?.[Symbol.iterator]=="function",me=`[ 	
\f\r]`,Ji=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,oo=/-->/g,lo=/>/g,Dt=RegExp(`>|${me}(?:([^\\s"'>=/]+)(${me}*=${me}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`,"g"),no=/'/g,ao=/"/g,so=/^(?:script|style|textarea|title)$/i,We=c=>(t,...i)=>({_$litType$:c,strings:t,values:i}),R=We(1),tF=We(2),iF=We(3),_t=Symbol.for("lit-noChange"),k=Symbol.for("lit-nothing"),Io=new WeakMap,Mt=Kt.createTreeWalker(Kt,129);function mo(c,t){if(!pe(c)||!c.hasOwnProperty("raw"))throw Error("invalid template strings array");return bo!==void 0?bo.createHTML(t):t}var Qr=(c,t)=>{let i=c.length-1,e=[],b,o=t===2?"<svg>":t===3?"<math>":"",l=Ji;for(let a=0;a<i;a++){let n=c[a],r,I,g=-1,C=0;for(;C<n.length&&(l.lastIndex=C,I=l.exec(n),I!==null);)C=l.lastIndex,l===Ji?I[1]==="!--"?l=oo:I[1]!==void 0?l=lo:I[2]!==void 0?(so.test(I[2])&&(b=RegExp("</"+I[2],"g")),l=Dt):I[3]!==void 0&&(l=Dt):l===Dt?I[0]===">"?(l=b??Ji,g=-1):I[1]===void 0?g=-2:(g=l.lastIndex-I[2].length,r=I[1],l=I[3]===void 0?Dt:I[3]==='"'?ao:no):l===ao||l===no?l=Dt:l===oo||l===lo?l=Ji:(l=Dt,b=void 0);let d=l===Dt&&c[a+1].startsWith("/>")?" ":"";o+=l===Ji?n+Rr:g>=0?(e.push(r),n.slice(0,g)+ro+n.slice(g)+Qt+d):n+Qt+(g===-2?a:d)}return[mo(c,o+(c[i]||"<?>")+(t===2?"</svg>":t===3?"</math>":"")),e]},wi=class c{constructor({strings:t,_$litType$:i},e){let b;this.parts=[];let o=0,l=0,a=t.length-1,n=this.parts,[r,I]=Qr(t,i);if(this.el=c.createElement(r,e),Mt.currentNode=this.el.content,i===2||i===3){let g=this.el.content.firstChild;g.replaceWith(...g.childNodes)}for(;(b=Mt.nextNode())!==null&&n.length<a;){if(b.nodeType===1){if(b.hasAttributes())for(let g of b.getAttributeNames())if(g.endsWith(ro)){let C=I[l++],d=b.getAttribute(g).split(Qt),F=/([.?@])?(.*)/.exec(C);n.push({type:1,index:o,name:F[2],strings:d,ctor:F[1]==="."?Ze:F[1]==="?"?Xe:F[1]==="@"?Ce:Zi}),b.removeAttribute(g)}else g.startsWith(Qt)&&(n.push({type:6,index:o}),b.removeAttribute(g));if(so.test(b.tagName)){let g=b.textContent.split(Qt),C=g.length-1;if(C>0){b.textContent=pc?pc.emptyScript:"";for(let d=0;d<C;d++)b.append(g[d],Li()),Mt.nextNode(),n.push({type:2,index:++o});b.append(g[C],Li())}}}else if(b.nodeType===8)if(b.data===go)n.push({type:2,index:o});else{let g=-1;for(;(g=b.data.indexOf(Qt,g+1))!==-1;)n.push({type:7,index:o}),g+=Qt.length-1}o++}}static createElement(t,i){let e=Kt.createElement("template");return e.innerHTML=t,e}};function Fi(c,t,i=c,e){if(t===_t)return t;let b=e!==void 0?i._$Co?.[e]:i._$Cl,o=Ni(t)?void 0:t._$litDirective$;return b?.constructor!==o&&(b?._$AO?.(!1),o===void 0?b=void 0:(b=new o(c),b._$AT(c,i,e)),e!==void 0?(i._$Co??=[])[e]=b:i._$Cl=b),b!==void 0&&(t=Fi(c,b._$AS(c,t.values),b,e)),t}var Fe=class{constructor(t,i){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=i}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){let{el:{content:i},parts:e}=this._$AD,b=(t?.creationScope??Kt).importNode(i,!0);Mt.currentNode=b;let o=Mt.nextNode(),l=0,a=0,n=e[0];for(;n!==void 0;){if(l===n.index){let r;n.type===2?r=new ki(o,o.nextSibling,this,t):n.type===1?r=new n.ctor(o,n.name,n.strings,this,t):n.type===6&&(r=new Ge(o,this,t)),this._$AV.push(r),n=e[++a]}l!==n?.index&&(o=Mt.nextNode(),l++)}return Mt.currentNode=Kt,b}p(t){let i=0;for(let e of this._$AV)e!==void 0&&(e.strings!==void 0?(e._$AI(t,e,i),i+=e.strings.length-2):e._$AI(t[i])),i++}},ki=class c{get _$AU(){return this._$AM?._$AU??this._$Cv}constructor(t,i,e,b){this.type=2,this._$AH=k,this._$AN=void 0,this._$AA=t,this._$AB=i,this._$AM=e,this.options=b,this._$Cv=b?.isConnected??!0}get parentNode(){let t=this._$AA.parentNode,i=this._$AM;return i!==void 0&&t?.nodeType===11&&(t=i.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,i=this){t=Fi(this,t,i),Ni(t)?t===k||t==null||t===""?(this._$AH!==k&&this._$AR(),this._$AH=k):t!==this._$AH&&t!==_t&&this._(t):t._$litType$!==void 0?this.$(t):t.nodeType!==void 0?this.T(t):xr(t)?this.k(t):this._(t)}O(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}T(t){this._$AH!==t&&(this._$AR(),this._$AH=this.O(t))}_(t){this._$AH!==k&&Ni(this._$AH)?this._$AA.nextSibling.data=t:this.T(Kt.createTextNode(t)),this._$AH=t}$(t){let{values:i,_$litType$:e}=t,b=typeof e=="number"?this._$AC(t):(e.el===void 0&&(e.el=wi.createElement(mo(e.h,e.h[0]),this.options)),e);if(this._$AH?._$AD===b)this._$AH.p(i);else{let o=new Fe(b,this),l=o.u(this.options);o.p(i),this.T(l),this._$AH=o}}_$AC(t){let i=Io.get(t.strings);return i===void 0&&Io.set(t.strings,i=new wi(t)),i}k(t){pe(this._$AH)||(this._$AH=[],this._$AR());let i=this._$AH,e,b=0;for(let o of t)b===i.length?i.push(e=new c(this.O(Li()),this.O(Li()),this,this.options)):e=i[b],e._$AI(o),b++;b<i.length&&(this._$AR(e&&e._$AB.nextSibling,b),i.length=b)}_$AR(t=this._$AA.nextSibling,i){for(this._$AP?.(!1,!0,i);t&&t!==this._$AB;){let e=t.nextSibling;t.remove(),t=e}}setConnected(t){this._$AM===void 0&&(this._$Cv=t,this._$AP?.(t))}},Zi=class{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(t,i,e,b,o){this.type=1,this._$AH=k,this._$AN=void 0,this.element=t,this.name=i,this._$AM=b,this.options=o,e.length>2||e[0]!==""||e[1]!==""?(this._$AH=Array(e.length-1).fill(new String),this.strings=e):this._$AH=k}_$AI(t,i=this,e,b){let o=this.strings,l=!1;if(o===void 0)t=Fi(this,t,i,0),l=!Ni(t)||t!==this._$AH&&t!==_t,l&&(this._$AH=t);else{let a=t,n,r;for(t=o[0],n=0;n<o.length-1;n++)r=Fi(this,a[e+n],i,n),r===_t&&(r=this._$AH[n]),l||=!Ni(r)||r!==this._$AH[n],r===k?t=k:t!==k&&(t+=(r??"")+o[n+1]),this._$AH[n]=r}l&&!b&&this.j(t)}j(t){t===k?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,t??"")}},Ze=class extends Zi{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===k?void 0:t}},Xe=class extends Zi{constructor(){super(...arguments),this.type=4}j(t){this.element.toggleAttribute(this.name,!!t&&t!==k)}},Ce=class extends Zi{constructor(t,i,e,b,o){super(t,i,e,b,o),this.type=5}_$AI(t,i=this){if((t=Fi(this,t,i,0)??k)===_t)return;let e=this._$AH,b=t===k&&e!==k||t.capture!==e.capture||t.once!==e.once||t.passive!==e.passive,o=t!==k&&(e===k||b);b&&this.element.removeEventListener(this.name,this,e),o&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){typeof this._$AH=="function"?this._$AH.call(this.options?.host??this.element,t):this._$AH.handleEvent(t)}},Ge=class{constructor(t,i,e){this.element=t,this.type=6,this._$AN=void 0,this._$AM=i,this.options=e}get _$AU(){return this._$AM._$AU}_$AI(t){Fi(this,t)}};var hr=Be.litHtmlPolyfillSupport;hr?.(wi,ki),(Be.litHtmlVersions??=[]).push("3.2.1");var lt=(c,t,i)=>{let e=i?.renderBefore??t,b=e._$litPart$;if(b===void 0){let o=i?.renderBefore??null;e._$litPart$=b=new ki(t.insertBefore(Li(),o),o,void 0,i??{})}return b._$AI(c),b};async function D(){return await import("./main.js").then(c=>c.default)}function at(c){return document.querySelector(`meta[name="${c}"]`)?.content}function L(c,t){let i=at(`loc:${c}`)||c;if(t)for(let e in t)i=i.replace(`{${e}}`,t[e]);return i}function ue(c){if(!c)return[];let t=/([a-z0-9])([A-Z]+[a-z])|([a-zA-Z0-9][.,/<>_])/g,i=[],e=0;for(;;){let b=t.exec(c);if(!b)break;let o=b.index+(b[1]||b[3]).length;i.push(c.slice(e,o)),e=o}return e<c.length&&i.push(c.slice(e)),i}function ct(c){let t=[];return ue(c).forEach(i=>{t.length>0&&t.push(R`<wbr>`),t.push(R`${i}`)}),R`${t}`}function Wc(c){return c.hostname!==window.location.hostname||c.protocol!==window.location.protocol}function Fo(c,t){return i(c)===i(t);function i(e){return e.pathname.replace(/\/index\.html$/gi,"/").replace(/\.html$/gi,"").replace(/\/$/gi,"").toLowerCase()}}async function Re(){if(document.querySelectorAll("pre code").length<=0)return;let{default:t}=await import("./es-4I4X6RME.min.js"),{configureHljs:i}=await D();i?.(t),document.querySelectorAll("pre code").forEach(e=>{t.highlightElement(e)}),document.querySelectorAll("pre code[highlight-lines]").forEach(e=>{if(e.innerHTML==="")return;let b=e.getAttribute("highlight-lines");if(!b)return;let o=e.innerHTML.split(`
`),l=b.split(",");for(let a of l){let n=0,r=0,I=a.match(/^(\d+)-(\d+)?$/);if(I)n=+I[1],r=+I[2],(isNaN(r)||r>o.length)&&(r=o.length);else{if(isNaN(Number(a)))continue;n=+a,r=n}n<=0||r<=0||n>r||n>o.length||(o[n-1]='<span class="line-highlight">'+o[n-1],o[r-1]=o[r-1]+"</span>")}e.innerHTML=o.join(`
`)})}function Zo(c){localStorage.setItem("theme",c),c==="auto"?document.documentElement.setAttribute("data-bs-theme",window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"):document.documentElement.setAttribute("data-bs-theme",c)}async function Xo(){return localStorage.getItem("theme")||(await D()).defaultTheme||"auto"}async function Co(){Zo(await Xo())}function Go(c){return new MutationObserver(()=>c(xe())).observe(document.documentElement,{attributes:!0,attributeFilter:["data-bs-theme"]})}function xe(){return document.documentElement.getAttribute("data-bs-theme")}async function Bo(c){let t=await Xo(),i=t==="light"?"sun":t==="dark"?"moon":"circle-half";return R`
    <div class='dropdown'>
      <a title='${L("changeTheme")}' class='btn border-0 dropdown-toggle' data-bs-toggle='dropdown' aria-expanded='false'>
        <i class='bi bi-${i}'></i>
      </a>
      <ul class='dropdown-menu dropdown-menu-end'>
        <li><a class='dropdown-item' href='#' @click=${b=>e(b,"light")}><i class='bi bi-sun'></i> ${L("themeLight")}</a></li>
        <li><a class='dropdown-item' href='#' @click=${b=>e(b,"dark")}><i class='bi bi-moon'></i> ${L("themeDark")}</a></li>
        <li><a class='dropdown-item' href='#' @click=${b=>e(b,"auto")}><i class='bi bi-circle-half'></i> ${L("themeAuto")}</a></li>
      </ul>
    </div>`;function e(b,o){b.preventDefault(),Zo(o),c()}}async function he(){Ar(),Ur(),yr(),Yr(),vr(),Or(),await Promise.all([fr(),Vr(),Qe()]),Go(Qe)}async function Vr(){document.querySelectorAll(".math").length>0&&await import("./tex-svg-full-SL33OL2J.min.js")}async function Qe(){let c=document.querySelectorAll("pre code.lang-mermaid"),t=document.querySelectorAll("pre.mermaid[data-mermaid]");if(c.length<=0&&t.length<=0)return;let{default:i}=await import("./mermaid.core-QWHI4VJR.min.js"),e=xe()==="dark"?"dark":"default",{mermaid:b}=await D();i.initialize(Object.assign({startOnLoad:!1,theme:e},b));let o=[];c.forEach(l=>{if(l.offsetParent){o.push(l.parentElement);let a=l.innerHTML;l.parentElement.classList.add("mermaid"),l.parentElement.setAttribute("data-mermaid",a),l.parentElement.innerHTML=a}}),t.forEach(l=>{l.offsetParent&&(l.removeAttribute("data-processed"),l.innerHTML=l.getAttribute("data-mermaid"),o.push(l))}),await i.run({nodes:o})}function Ar(){document.querySelectorAll("article h1,h2,h3,h4,h5,h6,.xref,.text-break").forEach(c=>{if(c.innerHTML?.trim()===c.innerText?.trim()){let t=[];for(let i of ue(c.innerText))t.length>0&&t.push(document.createElement("wbr")),t.push(i);c.replaceChildren(...t)}})}async function fr(){let{showLightbox:c}=await D(),t=200,i=Array.from(document.querySelectorAll("article a img[src]"));document.querySelectorAll("article img[src]").forEach(e=>{o()?b():e.addEventListener("load",()=>{o()&&b()});function b(){let l=document.createElement("a");l.target="_blank",l.rel="noopener noreferrer nofollow",e.parentElement.tagName==="PICTURE"?e.parentElement.addEventListener("click",()=>{l.href=e.currentSrc,l.click()}):(l.href=e.src,e.replaceWith(l),l.appendChild(e))}function o(){return c?c(e):e.naturalWidth>t&&e.naturalHeight>t&&!i.includes(e)}})}function Ur(){document.querySelectorAll("table").forEach(c=>{c.classList.add("table","table-bordered","table-condensed");let t=document.createElement("div");t.className="table-responsive",c.parentElement.insertBefore(t,c),t.appendChild(c)})}function yr(){document.querySelectorAll(".NOTE, .TIP").forEach(c=>c.classList.add("alert","alert-info")),document.querySelectorAll(".WARNING").forEach(c=>c.classList.add("alert","alert-warning")),document.querySelectorAll(".IMPORTANT, .CAUTION").forEach(c=>c.classList.add("alert","alert-danger"))}function Yr(){at("docfx:disablenewtab")!=="true"&&document.querySelectorAll("article a[href]").forEach(c=>{c.hostname!==window.location.hostname&&c.innerText.trim()!==""&&(c.target="_blank",c.rel="noopener noreferrer nofollow",c.classList.add("external"))})}function Or(){document.querySelectorAll("pre>code").forEach(c=>{if(c.textContent.trim().length===0)return;let t=!1;i();function i(){let e=t?R`<a class='btn border-0 link-success code-action'><i class='bi bi-check-lg'></i></a>`:R`<a class='btn border-0 code-action' title='${L("copy")}' href='#' @click=${b}><i class='bi bi-clipboard'></i></a>`;lt(e,c.parentElement);async function b(o){o.preventDefault(),await navigator.clipboard.writeText(c.innerText),t=!0,i(),setTimeout(()=>{t=!1,i()},1e3)}}})}function vr(){g();let c={id:"data-bi-id",name:"data-bi-name",type:"data-bi-type"},t=function(){function d(F,s,X){this.li=F,this.a=s,this.section=X}return Object.defineProperty(d.prototype,"tabIds",{get:function(){return this.a.getAttribute("data-tab").split(" ")},enumerable:!0,configurable:!0}),Object.defineProperty(d.prototype,"condition",{get:function(){return this.a.getAttribute("data-condition")},enumerable:!0,configurable:!0}),Object.defineProperty(d.prototype,"visible",{get:function(){return!this.li.hasAttribute("hidden")},set:function(F){F?(this.li.removeAttribute("hidden"),this.li.removeAttribute("aria-hidden")):(this.li.setAttribute("hidden","hidden"),this.li.setAttribute("aria-hidden","true"))},enumerable:!0,configurable:!0}),Object.defineProperty(d.prototype,"selected",{get:function(){return!this.section.hasAttribute("hidden")},set:function(F){F?(this.a.setAttribute("aria-selected","true"),this.a.classList.add("active"),this.a.tabIndex=0,this.section.removeAttribute("hidden"),this.section.removeAttribute("aria-hidden")):(this.a.setAttribute("aria-selected","false"),this.a.classList.remove("active"),this.a.tabIndex=-1,this.section.setAttribute("hidden","hidden"),this.section.setAttribute("aria-hidden","true"))},enumerable:!0,configurable:!0}),d.prototype.focus=function(){this.a.focus()},d}();i(document.body);function i(d){let F=n(),s=d.querySelectorAll(".tabGroup"),X={groups:[],selectedTabs:[]};for(let Z=0;Z<s.length;Z++){let B=e(s.item(Z));B.independent||(b(B,X),X.groups.push(B))}return d.addEventListener("click",function(Z){return l(Z,X)}),X.groups.length===0||(a(F),r(X)),X}function e(d){let F={independent:d.hasAttribute("data-tab-group-independent"),tabs:[]},s=d.firstElementChild.firstElementChild;for(;s;){let X=s.firstElementChild;X.setAttribute(c.name,"tab");let Z=X.getAttribute("data-tab").replace(/\+/g," ");X.setAttribute("data-tab",Z);let B=d.querySelector('[id="'+X.getAttribute("aria-controls")+'"]'),G=new t(s,X,B);F.tabs.push(G),s=s.nextElementSibling}return d.setAttribute(c.name,"tab-group"),d.tabGroup=F,F}function b(d,F){let s=!1,X;for(let Z=0,B=d.tabs;Z<B.length;Z++){let G=B[Z];G.visible=G.condition===null||F.selectedTabs.indexOf(G.condition)!==-1,G.visible&&(X||(X=G)),G.selected=G.visible&&I(F.selectedTabs,G.tabIds),s=s||G.selected}if(!s){for(let B=0,G=d.tabs;B<G.length;B++){let W=G[B].tabIds;for(let u=0,x=W;u<x.length;u++){let Q=x[u],h=F.selectedTabs.indexOf(Q);h!==-1&&F.selectedTabs.splice(h,1)}}let Z=X;Z.selected=!0,F.selectedTabs.push(Z.tabIds[0])}}function o(d){if(!(d.target instanceof HTMLElement))return null;let F=d.target.closest("a[data-tab]");if(F===null)return null;let s=F.getAttribute("data-tab").split(" "),X=F.parentElement.parentElement.parentElement.tabGroup;return X===void 0?null:{tabIds:s,group:X,anchor:F}}function l(d,F){let s=o(d);if(s===null)return;d.preventDefault(),s.anchor.href="javascript:",setTimeout(function(){s.anchor.href="#"+s.anchor.getAttribute("aria-controls")});let X=s.tabIds,Z=s.group,B=s.anchor.getBoundingClientRect().top;if(Z.independent)for(let W=0,u=Z.tabs;W<u.length;W++){let x=u[W];x.selected=I(x.tabIds,X)}else{if(I(F.selectedTabs,X))return;let W=Z.tabs.filter(function(u){return u.selected})[0].tabIds[0];F.selectedTabs.splice(F.selectedTabs.indexOf(W),1,X[0]);for(let u=0,x=F.groups;u<x.length;u++){let Q=x[u];b(Q,F)}r(F)}C();let G=s.anchor.getBoundingClientRect().top;G!==B&&d instanceof MouseEvent&&window.scrollTo(0,window.pageYOffset+G-B)}function a(d){for(let F=0,s=d;F<s.length;F++){let X=s[F],Z=document.querySelector('.tabGroup > ul > li > a[data-tab="'+X+'"]:not([hidden])');if(Z===null)return;Z.dispatchEvent(new CustomEvent("click",{bubbles:!0}))}}function n(){let F=new URLSearchParams(window.location.search).get("tabs");return F?F.split(","):[]}function r(d){let F=new URLSearchParams(window.location.search);F.set("tabs",d.selectedTabs.join());let s=location.protocol+"//"+location.host+location.pathname+"?"+F.toString()+location.hash;location.href!==s&&history.replaceState({},document.title,s)}function I(d,F){for(let s=0,X=d;s<X.length;s++){let Z=X[s];for(let B=0,G=F;B<G.length;B++){let W=G[B];if(Z===W)return!0}}return!1}function g(){document.querySelectorAll("div.tabGroup>ul").forEach(d=>d.classList.add("nav","nav-tabs")),document.querySelectorAll("div.tabGroup>ul>li").forEach(d=>d.classList.add("nav-item")),document.querySelectorAll("div.tabGroup>ul>li>a").forEach(d=>d.classList.add("nav-link")),document.querySelectorAll("div.tabGroup>section").forEach(d=>d.classList.add("card"))}function C(){Qe()}}var po={ATTRIBUTE:1,CHILD:2,PROPERTY:3,BOOLEAN_ATTRIBUTE:4,EVENT:5,ELEMENT:6},Wo=c=>(...t)=>({_$litDirective$:c,values:t}),uc=class{constructor(t){}get _$AU(){return this._$AM._$AU}_$AT(t,i,e){this._$Ct=t,this._$AM=i,this._$Ci=e}_$AS(t,i){return this.update(t,i)}update(t,i){return this.render(...i)}};var ht=Wo(class extends uc{constructor(c){if(super(c),c.type!==po.ATTRIBUTE||c.name!=="class"||c.strings?.length>2)throw Error("`classMap()` can only be used in the `class` attribute and must be the only part in the attribute.")}render(c){return" "+Object.keys(c).filter(t=>c[t]).join(" ")+" "}update(c,[t]){if(this.st===void 0){this.st=new Set,c.strings!==void 0&&(this.nt=new Set(c.strings.join(" ").split(/\s/).filter(e=>e!=="")));for(let e in t)t[e]&&!this.nt?.has(e)&&this.st.add(e);return this.render(t)}let i=c.element.classList;for(let e of this.st)e in t||(i.remove(e),this.st.delete(e));for(let e in t){let b=!!t[e];b===this.st.has(e)||this.nt?.has(e)||(b?(i.add(e),this.st.add(e)):(i.remove(e),this.st.delete(e)))}return _t}});var It;async function uo(){let c=document.getElementById("search-query");if(!c||!window.Worker)return;let t=at("docfx:rel")||"",i=new Worker(t+"public/search-worker.min.js",{type:"module"});i.onerror=n=>{console.error("Error occurred at search-worker. message: "+n.message)},i.onmessage=function(n){switch(n.data.e){case"index-ready":c.disabled=!1,c.addEventListener("input",b),c.addEventListener("keypress",function(r){r.key==="Enter"&&event.preventDefault()}),window.docfx.searchReady=!0;break;case"query-ready":document.body.setAttribute("data-search","true"),a(n.data.d,0),window.docfx.searchResultReady=!0,c.value===""&&document.body.removeAttribute("data-search");break}};let{lunrLanguages:e}=await D();i.postMessage({init:{lunrLanguages:e}});function b(){if(It=c.value,It==="")document.body.removeAttribute("data-search");else{let n=It.replace(/\s+/g," ").split(" ").map(r=>"+"+r).join(" ");i.postMessage({q:n})}}function o(n,r){let I=n.split(/\/+/),g=r.split(/\/+/),C=I.length-1,d=[];for(let F=0;F<g.length;F++)g[F]===".."?C--:g[F]!=="."&&d.push(g[F]);return I.slice(0,C).concat(d).join("/")}function l(n){let I=It.split(/\s+/g),g=n.indexOf(I[0]);if(g>512)return"..."+n.slice(g-512,g+512)+"...";if(g<=512)return n.slice(0,g+512)+"..."}function a(n,r){let g=Math.ceil(n.length/10);lt(C(r),document.getElementById("search-results"));function C(F){if(n.length===0)return R`<div class="search-list">${L("searchNoResults",{query:It})}</div>`;let s=F*10,X=n.slice(s,s+10),Z=R`
        <div class="search-list">${L("searchResultsCount",{count:n.length.toString(),query:It})}</div>
        <div class="sr-items">${X.map(B=>{let G=window.location.href,W=o(G,t+B.href),u=t+B.href+"?q="+It,x=B.summary?l(B.summary):"";return R`
            <div class="sr-item">
              <div class="item-title"><a href="${u}" target="_blank" rel="noopener noreferrer">${Ve(B.title,It)}</a></div>
              <div class="item-href">${Ve(W,It)}</div>
              <div class="item-brief">${Ve(x,It)}</div>
            </div>`})}
        </div>`;return R`${Z} ${d()}`}function d(){let s=Math.max(0,Math.min(r-2,g-5)),X=Math.min(g,s+5),Z=Array.from(new Array(X-s).keys()).map(G=>G+s);if(Z.length<=1)return null;return R`
        <nav>
          <ul class="pagination">
            <li class="page-item">
              <a class="page-link ${ht({disabled:r<=0})}" href="#" aria-label="Previous"
                @click="${()=>B(r-1)}">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
            ${Z.map(G=>R`
              <li class="page-item">
                <a class="page-link ${ht({active:r===G})}" href="#"
                  @click="${()=>B(G)}">${G+1}</a></li>`)}
            <li class="page-item">
              <a class="page-link ${ht({disabled:r>=g-1})}" href="#" aria-label="Next"
                @click="${()=>B(r+1)}">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
          </ul>
        </nav>`;function B(G){G>=0&&G<g&&a(n,G)}}}}function Ve(c,t){let e=t.split(/\s+/g).map(a=>a.toLowerCase()),b=c.toLowerCase(),o=[],l=0;for(let a=0;a<e.length;a++){let n=e[a],r=b.indexOf(n,l);r>=0&&(o.push(R`${c.slice(l,r)}`),o.push(R`<b>${c.slice(r,r+n.length)}</b>`),l=r+n.length)}return o.push(R`${c.slice(l)}`),R`${o}`}async function Ro(){let c=at("docfx:tocrel");if(!c)return[];let t=at("docfx:disabletocfilter")==="true",i=new URL(c.replace(/.html$/gi,".json"),window.location.href),{items:e,pdf:b,pdfFileName:o}=await(await fetch(i)).json(),l=t?"":localStorage?.getItem("tocFilterUrl")||"",a=t?"":localStorage?.getItem("tocFilter")||"";(l===""||l!==i.toString())&&(a="",t||(localStorage?.setItem("tocFilter",""),localStorage?.setItem("tocFilterUrl",i.toString())));let n=[],r=[];e.forEach(g);let I=document.getElementById("toc");if(I){C();let Z=I.querySelectorAll("li.active"),B=Z[Z.length-1];B&&(B.scrollIntoView({block:"nearest"}),location.hash&&(location.href=location.href))}return r.length>0&&Jr(e,r[0]),n.slice(0,-1);function g(Z){let B;if(Z.href){let G=new URL(Z.href,i);Z.href=G.href,B=Wc(G)?!1:Fo(G,window.location),B&&(Z.items&&(Z.expanded=!0),r.push(Z))}if(Z.items)for(let G of Z.items)g(G)&&(B=!0,Z.expanded=!0);return B?(n.unshift(Z),!0):!1}function C(){lt(R`
      ${F()}
      <div class="flex-fill overflow-y-auto">${d(e)||s()}</div>
      ${X()}`,I)}function d(Z){let B=Z.map(G=>{let{href:W,name:u,items:x,expanded:Q}=G;if(!u)return null;let h=!x||x.length<=0,f=h?null:d(x);if(a!==""&&!f&&!u.toLowerCase().includes(a.toLowerCase()))return null;let y=W?R`<a class='${ht({"nav-link":!n.includes(G)})}' href=${W}>${ct(u)}</a>`:h?R`<span class='text-body-tertiary name-only'>${ct(u)}</a>`:R`<a class='${ht({"nav-link":!n.includes(G)})}' href='#' @click=${T}>${ct(u)}</a>`,v=a!==""&&Q!==!1&&f!=null||Q===!0;return R`
        <li class=${ht({expander:!h,expanded:v,active:n.includes(G)})}>
          ${h?null:R`<span class='expand-stub' @click=${T}></span>`}
          ${y}
          ${f}
        </li>`;function T(z){z.preventDefault(),G.expanded=!v,C()}}).filter(G=>G);return B.length>0?R`<ul>${B}</ul>`:null}function F(){return t?null:R`
      <form class='filter'>
        <i class='bi bi-filter'></i>
        <input class='form-control' @input=${Z} value='${a}' type='search' placeholder='${L("tocFilter")}' autocomplete='off' aria-label='${L("tocFilter")}'>
      </form>`;function Z(B){a=B.target.value.trim(),localStorage?.setItem("tocFilter",a),C()}}function s(){return a===""?null:R`<div class='no-result'>${L("searchNoResults",{query:a})}</div>`}function X(){return b?R`<div class="py-2 mb-md-4"><a class="pdf-link" href="${new URL(o||"toc.pdf",i)}">${L("downloadPdf")}</a></div>`:null}}function Jr(c,t){let i=document.getElementById("nextArticle");if(!i)return;let e=r(c),b=e.findIndex(I=>I===t),o=e[b-1],l=e[b+1];if(!o&&!l)return;let a=o?R`<div class="prev"><span><i class='bi bi-chevron-left'></i> ${L("prevArticle")}</span> <a href="${o.href}" rel="prev">${ct(o.name)}</a></div>`:null,n=l?R`<div class="next"><span>${L("nextArticle")} <i class='bi bi-chevron-right'></i></span> <a href="${l.href}" rel="next">${ct(l.name)}</a></div>`:null;lt(R`${a} ${n}`,i);function r(I){let g=[];for(let C of I)C.href&&g.push(C),C.items&&g.push(...r(C.items));return g}}var Qo=No(xo());async function ho(){let c=document.getElementById("navbar");if(!c)return[];let{iconLinks:t}=await D(),i=await a(),e=wr(i),b=n=>R`<li class='nav-item'><a class='nav-link ${n===e?"active":null}' aria-current=${n===e?"page":!1} href=${n.href}>${ct(n.name)}</a></li>`,o=R`
    <ul class='navbar-nav'>${i.map(n=>{if("items"in n){let r=n.items.some(I=>I===e)?"active":null;return R`
            <li class='nav-item dropdown'>
              <a class='nav-link dropdown-toggle ${r}' href='#' role='button' data-bs-toggle='dropdown' aria-expanded='false'>
                ${ct(n.name)}
              </a>
              <ul class='dropdown-menu'>${n.items.map(b)}</ul>
            </li>`}else return b(n)})}</ul>`;async function l(){let n=R`
      <form class="icons">
        ${t?.map(r=>R`<a href="${r.href}" title="${r.title}" class="btn border-0"><i class="bi bi-${r.icon}"></i></a>`)}
        ${await Bo(l)}
      </form>`;lt(R`${o} ${n}`,c)}return await l(),e?[e]:[];async function a(){let n=at("docfx:navrel");if(!n)return[];let r=new URL(n.replace(/.html$/gi,".json"),window.location.href),{items:I}=await fetch(r).then(g=>g.json());return I.map(g=>"items"in g?{name:g.name,items:g.items.map(C=>({name:C.name,href:new URL(C.href,r)}))}:{name:g.name,href:new URL(g.href,r)})}}function Vo(c){let t=document.getElementById("breadcrumb");t&&lt(R`
        <ol class="breadcrumb">
          ${c.map(i=>R`<li class="breadcrumb-item"><a href="${i.href}">${ct(i.name)}</a></li>`)}
        </ol>`,t)}async function Ao(){await Lr();let c=document.getElementById("affix");c&&lt(Nr(),c)}async function Lr(){let c=new Qo.default,{anchors:t}=await D();c.options=Object.assign({visible:"hover",icon:"#"},t),c.add("article h2:not(.no-anchor), article h3:not(.no-anchor), article h4:not(.no-anchor)"),location.hash&&(location.href=location.href)}function Nr(){let c=Array.from(document.querySelectorAll("article h2, article h3"));if(c.length>0)return R`
      <h5 class="border-bottom">${L("inThisArticle")}</h5>
      <ul>${c.map(t=>t.tagName==="H2"?R`<li><a class="link-body-emphasis" href="#${t.id}">${ct(t.innerText)}</a></li>`:R`<li><a class="link-secondary" href="#${t.id}">${ct(t.innerText)}</a></li>`)}</ul>`}function wr(c){let t=new URL(window.location.href),i,e=0;for(let b of c.map(o=>"items"in o?o.items:o).flat()){if(Wc(b.href))continue;let o=kr(t,b.href);o===e?i=void 0:o>e&&(e=o,i=b)}return i}function kr(c,t){let i=c.pathname.split("/"),e=t.pathname.split("/"),b=0;for(;b<i.length&&b<e.length&&i[b]===e[b];)b++;return b}async function zr(){window.docfx=window.docfx||{};let{start:c}=await D();c?.(),navigator.userAgent.indexOf("docfx/pdf")>=0?await Promise.all([he(),Re()]):await Promise.all([Co(),uo(),Ao(),he(),i(),Re()]),window.docfx.ready=!0;async function i(){let[e,b]=await Promise.all([ho(),Ro()]);Vo([...e,...b])}}zr().catch(console.error);
/*! Bundled license information:

bootstrap/dist/js/bootstrap.esm.js:
  (*!
    * Bootstrap v5.3.3 (https://getbootstrap.com/)
    * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
    * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
    *)

lit-html/lit-html.js:
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

lit-html/directive.js:
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

lit-html/directives/class-map.js:
  (**
   * @license
   * Copyright 2018 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)
*/
//# sourceMappingURL=docfx.min.js.map
