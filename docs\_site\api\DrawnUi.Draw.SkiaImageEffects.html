<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaImageEffects | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaImageEffects | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaImageEffects.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaImageEffects%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaImageEffects">



  <h1 id="DrawnUi_Draw_SkiaImageEffects" data-uid="DrawnUi.Draw.SkiaImageEffects" class="text-break">
Class SkiaImageEffects  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L3"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class SkiaImageEffects</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">SkiaImageEffects</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_SkiaImageEffects_Brightness_" data-uid="DrawnUi.Draw.SkiaImageEffects.Brightness*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Brightness_System_Single_" data-uid="DrawnUi.Draw.SkiaImageEffects.Brightness(System.Single)">
  Brightness(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L229"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This effect increases the brightness of an image. amount is between 0 (no change) and 1 (white).</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Brightness(float amount)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>amount</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Contrast_" data-uid="DrawnUi.Draw.SkiaImageEffects.Contrast*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Contrast_System_Single_" data-uid="DrawnUi.Draw.SkiaImageEffects.Contrast(System.Single)">
  Contrast(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L187"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This effect adjusts the contrast of an image. amount is the adjustment level. Negative values decrease contrast, positive values increase contrast, and 0 means no change.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Contrast(float amount)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>amount</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Darken_" data-uid="DrawnUi.Draw.SkiaImageEffects.Darken*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Darken_System_Single_" data-uid="DrawnUi.Draw.SkiaImageEffects.Darken(System.Single)">
  Darken(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L73"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Darken(float amount)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>amount</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Gamma_" data-uid="DrawnUi.Draw.SkiaImageEffects.Gamma*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Gamma_System_Single_" data-uid="DrawnUi.Draw.SkiaImageEffects.Gamma(System.Single)">
  Gamma(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L263"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This effect applies gamma correction to an image. gamma must be greater than 0. A .</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Gamma(float gamma)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>gamma</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>








  <h4 class="section">Exceptions</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.argumentoutofrangeexception">ArgumentOutOfRangeException</a></dt>
    <dd></dd>
  </dl>



  <a id="DrawnUi_Draw_SkiaImageEffects_Grayscale_" data-uid="DrawnUi.Draw.SkiaImageEffects.Grayscale*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Grayscale" data-uid="DrawnUi.Draw.SkiaImageEffects.Grayscale">
  Grayscale()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L103"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This effect turns an image to grayscale. This particular version uses the NTSC/PAL/SECAM standard luminance value weights: 0.2989 for red, 0.587 for green, and 0.114 for blue.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Grayscale()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Grayscale2_" data-uid="DrawnUi.Draw.SkiaImageEffects.Grayscale2*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Grayscale2" data-uid="DrawnUi.Draw.SkiaImageEffects.Grayscale2">
  Grayscale2()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L124"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This effect turns an image to grayscale.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Grayscale2()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_HSL_" data-uid="DrawnUi.Draw.SkiaImageEffects.HSL*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_HSL_System_Single_System_Single_System_Single_SkiaSharp_SKBlendMode_" data-uid="DrawnUi.Draw.SkiaImageEffects.HSL(System.Single,System.Single,System.Single,SkiaSharp.SKBlendMode)">
  HSL(float, float, float, SKBlendMode)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L47"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter HSL(float hue, float saturation, float lightness, SKBlendMode mode)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>hue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>saturation</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>lightness</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>mode</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skblendmode">SKBlendMode</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_InvertColors_" data-uid="DrawnUi.Draw.SkiaImageEffects.InvertColors*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_InvertColors" data-uid="DrawnUi.Draw.SkiaImageEffects.InvertColors">
  InvertColors()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L170"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This effect inverts the colors in an image. NOT WORKING!</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter InvertColors()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Lighten_" data-uid="DrawnUi.Draw.SkiaImageEffects.Lighten*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Lighten_System_Single_" data-uid="DrawnUi.Draw.SkiaImageEffects.Lighten(System.Single)">
  Lighten(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L86"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Lighten(float amount)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>amount</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Lightness_" data-uid="DrawnUi.Draw.SkiaImageEffects.Lightness*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Lightness_System_Single_" data-uid="DrawnUi.Draw.SkiaImageEffects.Lightness(System.Single)">
  Lightness(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L245"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adjusts the brightness of an image:</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Lightness(float amount)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>amount</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Pastel_" data-uid="DrawnUi.Draw.SkiaImageEffects.Pastel*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Pastel" data-uid="DrawnUi.Draw.SkiaImageEffects.Pastel">
  Pastel()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L136"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Pastel()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Saturation_" data-uid="DrawnUi.Draw.SkiaImageEffects.Saturation*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Saturation_System_Single_" data-uid="DrawnUi.Draw.SkiaImageEffects.Saturation(System.Single)">
  Saturation(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L206"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This effect adjusts the saturation of an image. amount is the adjustment level. Negative values desaturate the image, positive values increase saturation, and 0 means no change.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Saturation(float amount)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>amount</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Sepia_" data-uid="DrawnUi.Draw.SkiaImageEffects.Sepia*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Sepia" data-uid="DrawnUi.Draw.SkiaImageEffects.Sepia">
  Sepia()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L153"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The sepia effect can give your photos a warm, brownish tone that mimics the look of an older photo.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Sepia()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_Tint_" data-uid="DrawnUi.Draw.SkiaImageEffects.Tint*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_Tint_Microsoft_Maui_Graphics_Color_SkiaSharp_SKBlendMode_" data-uid="DrawnUi.Draw.SkiaImageEffects.Tint(Microsoft.Maui.Graphics.Color,SkiaSharp.SKBlendMode)">
  Tint(Color, SKBlendMode)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L25"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>If you want to Tint: SKBlendMode.SrcATop + ColorTint with alpha below 1</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter Tint(Color color, SKBlendMode mode)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>color</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
    <dt><code>mode</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skblendmode">SKBlendMode</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageEffects_TintSL_" data-uid="DrawnUi.Draw.SkiaImageEffects.TintSL*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageEffects_TintSL_Microsoft_Maui_Graphics_Color_System_Single_System_Single_SkiaSharp_SKBlendMode_" data-uid="DrawnUi.Draw.SkiaImageEffects.TintSL(Microsoft.Maui.Graphics.Color,System.Single,System.Single,SkiaSharp.SKBlendMode)">
  TintSL(Color, float, float, SKBlendMode)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L32"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKColorFilter TintSL(Color tint, float saturation, float lightness, SKBlendMode mode)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>tint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
    <dt><code>saturation</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>lightness</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>mode</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skblendmode">SKBlendMode</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter">SKColorFilter</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Enums/SkiaImageEffects.cs/#L3" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
