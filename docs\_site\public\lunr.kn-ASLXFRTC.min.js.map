{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.kn.js"], "sourcesContent": ["/*!\n * Lunr languages, `Kannada` language\n * https://github.com/MiKr13/lunr-languages\n *\n * Copyright 2023, India\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.kn = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.kn.trimmer,\n        lunr.kn.stopWordFilter,\n        lunr.kn.stemmer\n      );\n\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.kn.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.kn.wordCharacters = \"\\u0C80-\\u0C84\\u0C85-\\u0C94\\u0C95-\\u0CB9\\u0CBE-\\u0CCC\\u0CBC-\\u0CBD\\u0CD5-\\u0CD6\\u0CDD-\\u0CDE\\u0CE0-\\u0CE1\\u0CE2-\\u0CE3\\u0CE4\\u0CE5\\u0CE6-\\u0CEF\\u0CF1-\\u0CF3\";\n    lunr.kn.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.kn.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.kn.trimmer, 'trimmer-kn');\n    /* lunr stop word filter */\n    lunr.kn.stopWordFilter = lunr.generateStopWordFilter(\n      'ಮತ್ತು ಈ ಒಂದು ರಲ್ಲಿ ಹಾಗೂ ಎಂದು ಅಥವಾ ಇದು ರ ಅವರು ಎಂಬ ಮೇಲೆ ಅವರ ತನ್ನ ಆದರೆ ತಮ್ಮ ನಂತರ ಮೂಲಕ ಹೆಚ್ಚು ನ ಆ ಕೆಲವು ಅನೇಕ ಎರಡು ಹಾಗು ಪ್ರಮುಖ ಇದನ್ನು ಇದರ ಸುಮಾರು ಅದರ ಅದು ಮೊದಲ ಬಗ್ಗೆ ನಲ್ಲಿ ರಂದು ಇತರ ಅತ್ಯಂತ ಹೆಚ್ಚಿನ ಸಹ ಸಾಮಾನ್ಯವಾಗಿ ನೇ ಹಲವಾರು ಹೊಸ ದಿ ಕಡಿಮೆ ಯಾವುದೇ ಹೊಂದಿದೆ ದೊಡ್ಡ ಅನ್ನು ಇವರು ಪ್ರಕಾರ ಇದೆ ಮಾತ್ರ ಕೂಡ ಇಲ್ಲಿ ಎಲ್ಲಾ ವಿವಿಧ ಅದನ್ನು ಹಲವು ರಿಂದ ಕೇವಲ ದ ದಕ್ಷಿಣ ಗೆ ಅವನ ಅತಿ ನೆಯ ಬಹಳ ಕೆಲಸ ಎಲ್ಲ ಪ್ರತಿ ಇತ್ಯಾದಿ ಇವು ಬೇರೆ ಹೀಗೆ ನಡುವೆ ಇದಕ್ಕೆ ಎಸ್ ಇವರ ಮೊದಲು ಶ್ರೀ ಮಾಡುವ ಇದರಲ್ಲಿ ರೀತಿಯ ಮಾಡಿದ ಕಾಲ ಅಲ್ಲಿ ಮಾಡಲು ಅದೇ ಈಗ ಅವು ಗಳು ಎ ಎಂಬುದು ಅವನು ಅಂದರೆ ಅವರಿಗೆ ಇರುವ ವಿಶೇಷ ಮುಂದೆ ಅವುಗಳ ಮುಂತಾದ ಮೂಲ ಬಿ ಮೀ ಒಂದೇ ಇನ್ನೂ ಹೆಚ್ಚಾಗಿ ಮಾಡಿ ಅವರನ್ನು ಇದೇ ಯ ರೀತಿಯಲ್ಲಿ ಜೊತೆ ಅದರಲ್ಲಿ ಮಾಡಿದರು ನಡೆದ ಆಗ ಮತ್ತೆ ಪೂರ್ವ ಆತ ಬಂದ ಯಾವ ಒಟ್ಟು ಇತರೆ ಹಿಂದೆ ಪ್ರಮಾಣದ ಗಳನ್ನು ಕುರಿತು ಯು ಆದ್ದರಿಂದ ಅಲ್ಲದೆ ನಗರದ ಮೇಲಿನ ಏಕೆಂದರೆ ರಷ್ಟು ಎಂಬುದನ್ನು ಬಾರಿ ಎಂದರೆ ಹಿಂದಿನ ಆದರೂ ಆದ ಸಂಬಂಧಿಸಿದ ಮತ್ತೊಂದು ಸಿ ಆತನ '.split(' '));\n    /* lunr stemmer function */\n    lunr.kn.stemmer = (function() {\n\n      return function(word) {\n        // for lunr version 2\n        if (typeof word.update === \"function\") {\n          return word.update(function(word) {\n            return word;\n          })\n        } else { // for lunr version <= 1\n          return word;\n        }\n\n      }\n    })();\n\n    var segmenter = lunr.wordcut;\n    segmenter.init();\n    lunr.kn.tokenizer = function(obj) {\n      if (!arguments.length || obj == null || obj == undefined) return []\n      if (Array.isArray(obj)) return obj.map(function(t) {\n        return isLunr2 ? new lunr.Token(t.toLowerCase()) : t.toLowerCase()\n      });\n\n      var str = obj.toString().toLowerCase().replace(/^\\s+/, '');\n      return segmenter.cut(str).split('|');\n    }\n\n    lunr.Pipeline.registerFunction(lunr.kn.stemmer, 'stemmer-kn');\n    lunr.Pipeline.registerFunction(lunr.kn.stopWordFilter, 'stopWordFilter-kn');\n\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAEI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,8JACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAC5B,i6HAAuxB,MAAM,GAAG,CAAC,EAEnyBA,EAAK,GAAG,QAAW,UAAW,CAE5B,OAAO,SAASC,EAAM,CAEpB,OAAI,OAAOA,EAAK,QAAW,WAClBA,EAAK,OAAO,SAASA,EAAM,CAChC,OAAOA,CACT,CAAC,EAEMA,CAGX,CACF,EAAG,EAEH,IAAIC,EAAYF,EAAK,QACrBE,EAAU,KAAK,EACfF,EAAK,GAAG,UAAY,SAASG,EAAK,CAChC,GAAI,CAAC,UAAU,QAAUA,GAAO,MAAQA,GAAO,KAAW,MAAO,CAAC,EAClE,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOA,EAAI,IAAI,SAASC,EAAG,CACjD,OAAO,QAAU,IAAIJ,EAAK,MAAMI,EAAE,YAAY,CAAC,EAAIA,EAAE,YAAY,CACnE,CAAC,EAED,IAAIC,EAAMF,EAAI,SAAS,EAAE,YAAY,EAAE,QAAQ,OAAQ,EAAE,EACzD,OAAOD,EAAU,IAAIG,CAAG,EAAE,MAAM,GAAG,CACrC,EAEAL,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAC5DA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAE5E,CACF,CAAC", "names": ["require_lunr_kn", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "word", "segmenter", "obj", "t", "str"]}