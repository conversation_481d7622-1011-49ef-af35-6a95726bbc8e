{"version": 3, "sources": ["../../node_modules/d3-sankey/node_modules/d3-array/src/max.js", "../../node_modules/d3-sankey/node_modules/d3-array/src/min.js", "../../node_modules/d3-sankey/node_modules/d3-array/src/sum.js", "../../node_modules/d3-sankey/src/align.js", "../../node_modules/d3-sankey/src/constant.js", "../../node_modules/d3-sankey/src/sankey.js", "../../node_modules/d3-sankey/node_modules/d3-path/src/path.js", "../../node_modules/d3-sankey/node_modules/d3-shape/src/constant.js", "../../node_modules/d3-sankey/node_modules/d3-shape/src/point.js", "../../node_modules/d3-sankey/node_modules/d3-shape/src/array.js", "../../node_modules/d3-sankey/node_modules/d3-shape/src/link/index.js", "../../node_modules/d3-sankey/src/sankeyLinkHorizontal.js", "../../node_modules/mermaid/dist/chunks/mermaid.core/sankeyDiagram-Y46BX6SQ.mjs"], "sourcesContent": ["export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import {min} from \"d3-array\";\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n", "export default function constant(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {max, min, sum} from \"d3-array\";\nimport {justify} from \"./align.js\";\nimport constant from \"./constant.js\";\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport default function Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n", "var pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction Path() {\n  this._x0 = this._y0 = // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\n\nfunction path() {\n  return new Path;\n}\n\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function(x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function(x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function(x1, y1, x, y) {\n    this._ += \"Q\" + (+x1) + \",\" + (+y1) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + (+x1) + \",\" + (+y1) + \",\" + (+x2) + \",\" + (+y2) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + (+(y01 * x20 > x01 * y20)) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + (+(da >= pi)) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function(x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + (+w) + \"v\" + (+h) + \"h\" + (-w) + \"Z\";\n  },\n  toString: function() {\n    return this._;\n  }\n};\n\nexport default path;\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "export var slice = Array.prototype.slice;\n", "import {path} from \"d3-path\";\nimport {slice} from \"../array.js\";\nimport constant from \"../constant.js\";\nimport {x as pointX, y as pointY} from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nexport function linkVertical() {\n  return link(curveVertical);\n}\n\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "import {linkHorizontal} from \"d3-shape\";\n\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\n\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\n\nexport default function() {\n  return linkHorizontal()\n      .source(horizontalSource)\n      .target(horizontalTarget);\n}\n", "import {\n  __name,\n  clear,\n  common_default,\n  defaultConfig2 as defaultConfig,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/diagrams/sankey/parser/sankey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 9], $V1 = [1, 10], $V2 = [1, 5, 10, 12];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SANKEY\": 4, \"NEWLINE\": 5, \"csv\": 6, \"opt_eof\": 7, \"record\": 8, \"csv_tail\": 9, \"EOF\": 10, \"field[source]\": 11, \"COMMA\": 12, \"field[target]\": 13, \"field[value]\": 14, \"field\": 15, \"escaped\": 16, \"non_escaped\": 17, \"DQUOTE\": 18, \"ESCAPED_TEXT\": 19, \"NON_ESCAPED_TEXT\": 20, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SANKEY\", 5: \"NEWLINE\", 10: \"EOF\", 11: \"field[source]\", 12: \"COMMA\", 13: \"field[target]\", 14: \"field[value]\", 18: \"DQUOTE\", 19: \"ESCAPED_TEXT\", 20: \"NON_ESCAPED_TEXT\" },\n    productions_: [0, [3, 4], [6, 2], [9, 2], [9, 0], [7, 1], [7, 0], [8, 5], [15, 1], [15, 1], [16, 3], [17, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 7:\n          const source = yy.findOrCreateNode($$[$0 - 4].trim().replaceAll('\"\"', '\"'));\n          const target = yy.findOrCreateNode($$[$0 - 2].trim().replaceAll('\"\"', '\"'));\n          const value = parseFloat($$[$0].trim());\n          yy.addLink(source, target, value);\n          break;\n        case 8:\n        case 9:\n        case 11:\n          this.$ = $$[$0];\n          break;\n        case 10:\n          this.$ = $$[$0 - 1];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, { 5: [1, 3] }, { 6: 4, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 1: [2, 6], 7: 11, 10: [1, 12] }, o($V1, [2, 4], { 9: 13, 5: [1, 14] }), { 12: [1, 15] }, o($V2, [2, 8]), o($V2, [2, 9]), { 19: [1, 16] }, o($V2, [2, 11]), { 1: [2, 1] }, { 1: [2, 5] }, o($V1, [2, 2]), { 6: 17, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 15: 18, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 18: [1, 19] }, o($V1, [2, 3]), { 12: [1, 20] }, o($V2, [2, 10]), { 15: 21, 16: 7, 17: 8, 18: $V0, 20: $V1 }, o([1, 5, 10], [2, 7])],\n    defaultActions: { 11: [2, 1], 12: [2, 5] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"csv\");\n            return 4;\n            break;\n          case 1:\n            return 10;\n            break;\n          case 2:\n            return 5;\n            break;\n          case 3:\n            return 12;\n            break;\n          case 4:\n            this.pushState(\"escaped_text\");\n            return 18;\n            break;\n          case 5:\n            return 20;\n            break;\n          case 6:\n            this.popState(\"escaped_text\");\n            return 18;\n            break;\n          case 7:\n            return 19;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:sankey-beta\\b)/i, /^(?:$)/i, /^(?:((\\u000D\\u000A)|(\\u000A)))/i, /^(?:(\\u002C))/i, /^(?:(\\u0022))/i, /^(?:([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])*)/i, /^(?:(\\u0022)(?!(\\u0022)))/i, /^(?:(([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])|(\\u002C)|(\\u000D)|(\\u000A)|(\\u0022)(\\u0022))*)/i],\n      conditions: { \"csv\": { \"rules\": [1, 2, 3, 4, 5, 6, 7], \"inclusive\": false }, \"escaped_text\": { \"rules\": [6, 7], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sankey_default = parser;\n\n// src/diagrams/sankey/sankeyDB.ts\nvar links = [];\nvar nodes = [];\nvar nodesMap = /* @__PURE__ */ new Map();\nvar clear2 = /* @__PURE__ */ __name(() => {\n  links = [];\n  nodes = [];\n  nodesMap = /* @__PURE__ */ new Map();\n  clear();\n}, \"clear\");\nvar SankeyLink = class {\n  constructor(source, target, value = 0) {\n    this.source = source;\n    this.target = target;\n    this.value = value;\n  }\n  static {\n    __name(this, \"SankeyLink\");\n  }\n};\nvar addLink = /* @__PURE__ */ __name((source, target, value) => {\n  links.push(new SankeyLink(source, target, value));\n}, \"addLink\");\nvar SankeyNode = class {\n  constructor(ID) {\n    this.ID = ID;\n  }\n  static {\n    __name(this, \"SankeyNode\");\n  }\n};\nvar findOrCreateNode = /* @__PURE__ */ __name((ID) => {\n  ID = common_default.sanitizeText(ID, getConfig());\n  let node = nodesMap.get(ID);\n  if (node === void 0) {\n    node = new SankeyNode(ID);\n    nodesMap.set(ID, node);\n    nodes.push(node);\n  }\n  return node;\n}, \"findOrCreateNode\");\nvar getNodes = /* @__PURE__ */ __name(() => nodes, \"getNodes\");\nvar getLinks = /* @__PURE__ */ __name(() => links, \"getLinks\");\nvar getGraph = /* @__PURE__ */ __name(() => ({\n  nodes: nodes.map((node) => ({ id: node.ID })),\n  links: links.map((link) => ({\n    source: link.source.ID,\n    target: link.target.ID,\n    value: link.value\n  }))\n}), \"getGraph\");\nvar sankeyDB_default = {\n  nodesMap,\n  getConfig: /* @__PURE__ */ __name(() => getConfig().sankey, \"getConfig\"),\n  getNodes,\n  getLinks,\n  getGraph,\n  addLink,\n  findOrCreateNode,\n  getAccTitle,\n  setAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getDiagramTitle,\n  setDiagramTitle,\n  clear: clear2\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nimport {\n  select as d3select,\n  scaleOrdinal as d3scaleOrdinal,\n  schemeTableau10 as d3schemeTableau10\n} from \"d3\";\nimport {\n  sankey as d3Sankey,\n  sankeyLinkHorizontal as d3SankeyLinkHorizontal,\n  sankeyLeft as d3SankeyLeft,\n  sankeyRight as d3SankeyRight,\n  sankeyCenter as d3SankeyCenter,\n  sankeyJustify as d3SankeyJustify\n} from \"d3-sankey\";\n\n// src/rendering-util/uid.ts\nvar Uid = class _Uid {\n  static {\n    __name(this, \"Uid\");\n  }\n  static {\n    this.count = 0;\n  }\n  static next(name) {\n    return new _Uid(name + ++_Uid.count);\n  }\n  constructor(id) {\n    this.id = id;\n    this.href = `#${id}`;\n  }\n  toString() {\n    return \"url(\" + this.href + \")\";\n  }\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nvar alignmentsMap = {\n  left: d3SankeyLeft,\n  right: d3SankeyRight,\n  center: d3SankeyCenter,\n  justify: d3SankeyJustify\n};\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  const { securityLevel, sankey: conf } = getConfig();\n  const defaultSankeyConfig = defaultConfig.sankey;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = d3select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? d3select(sandboxElement.nodes()[0].contentDocument.body) : d3select(\"body\");\n  const svg = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n  const width = conf?.width ?? defaultSankeyConfig.width;\n  const height = conf?.height ?? defaultSankeyConfig.width;\n  const useMaxWidth = conf?.useMaxWidth ?? defaultSankeyConfig.useMaxWidth;\n  const nodeAlignment = conf?.nodeAlignment ?? defaultSankeyConfig.nodeAlignment;\n  const prefix = conf?.prefix ?? defaultSankeyConfig.prefix;\n  const suffix = conf?.suffix ?? defaultSankeyConfig.suffix;\n  const showValues = conf?.showValues ?? defaultSankeyConfig.showValues;\n  const graph = diagObj.db.getGraph();\n  const nodeAlign = alignmentsMap[nodeAlignment];\n  const nodeWidth = 10;\n  const sankey = d3Sankey().nodeId((d) => d.id).nodeWidth(nodeWidth).nodePadding(10 + (showValues ? 15 : 0)).nodeAlign(nodeAlign).extent([\n    [0, 0],\n    [width, height]\n  ]);\n  sankey(graph);\n  const colorScheme = d3scaleOrdinal(d3schemeTableau10);\n  svg.append(\"g\").attr(\"class\", \"nodes\").selectAll(\".node\").data(graph.nodes).join(\"g\").attr(\"class\", \"node\").attr(\"id\", (d) => (d.uid = Uid.next(\"node-\")).id).attr(\"transform\", function(d) {\n    return \"translate(\" + d.x0 + \",\" + d.y0 + \")\";\n  }).attr(\"x\", (d) => d.x0).attr(\"y\", (d) => d.y0).append(\"rect\").attr(\"height\", (d) => {\n    return d.y1 - d.y0;\n  }).attr(\"width\", (d) => d.x1 - d.x0).attr(\"fill\", (d) => colorScheme(d.id));\n  const getText = /* @__PURE__ */ __name(({ id: id2, value }) => {\n    if (!showValues) {\n      return id2;\n    }\n    return `${id2}\n${prefix}${Math.round(value * 100) / 100}${suffix}`;\n  }, \"getText\");\n  svg.append(\"g\").attr(\"class\", \"node-labels\").attr(\"font-family\", \"sans-serif\").attr(\"font-size\", 14).selectAll(\"text\").data(graph.nodes).join(\"text\").attr(\"x\", (d) => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6).attr(\"y\", (d) => (d.y1 + d.y0) / 2).attr(\"dy\", `${showValues ? \"0\" : \"0.35\"}em`).attr(\"text-anchor\", (d) => d.x0 < width / 2 ? \"start\" : \"end\").text(getText);\n  const link = svg.append(\"g\").attr(\"class\", \"links\").attr(\"fill\", \"none\").attr(\"stroke-opacity\", 0.5).selectAll(\".link\").data(graph.links).join(\"g\").attr(\"class\", \"link\").style(\"mix-blend-mode\", \"multiply\");\n  const linkColor = conf?.linkColor ?? \"gradient\";\n  if (linkColor === \"gradient\") {\n    const gradient = link.append(\"linearGradient\").attr(\"id\", (d) => (d.uid = Uid.next(\"linearGradient-\")).id).attr(\"gradientUnits\", \"userSpaceOnUse\").attr(\"x1\", (d) => d.source.x1).attr(\"x2\", (d) => d.target.x0);\n    gradient.append(\"stop\").attr(\"offset\", \"0%\").attr(\"stop-color\", (d) => colorScheme(d.source.id));\n    gradient.append(\"stop\").attr(\"offset\", \"100%\").attr(\"stop-color\", (d) => colorScheme(d.target.id));\n  }\n  let coloring;\n  switch (linkColor) {\n    case \"gradient\":\n      coloring = /* @__PURE__ */ __name((d) => d.uid, \"coloring\");\n      break;\n    case \"source\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.source.id), \"coloring\");\n      break;\n    case \"target\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.target.id), \"coloring\");\n      break;\n    default:\n      coloring = linkColor;\n  }\n  link.append(\"path\").attr(\"d\", d3SankeyLinkHorizontal()).attr(\"stroke\", coloring).attr(\"stroke-width\", (d) => Math.max(1, d.width));\n  setupGraphViewbox(void 0, svg, 0, useMaxWidth);\n}, \"draw\");\nvar sankeyRenderer_default = {\n  draw\n};\n\n// src/diagrams/sankey/sankeyUtils.ts\nvar prepareTextForParsing = /* @__PURE__ */ __name((text) => {\n  const textToParse = text.replaceAll(/^[^\\S\\n\\r]+|[^\\S\\n\\r]+$/g, \"\").replaceAll(/([\\n\\r])+/g, \"\\n\").trim();\n  return textToParse;\n}, \"prepareTextForParsing\");\n\n// src/diagrams/sankey/sankeyDiagram.ts\nvar originalParse = sankey_default.parse.bind(sankey_default);\nsankey_default.parse = (text) => originalParse(prepareTextForParsing(text));\nvar diagram = {\n  parser: sankey_default,\n  db: sankeyDB_default,\n  renderer: sankeyRenderer_default\n};\nexport {\n  diagram\n};\n"], "mappings": "6LAAe,SAARA,EAAqBC,EAAQC,EAAS,CAC3C,IAAIF,EACJ,GAAIE,IAAY,OACd,QAAWC,KAASF,EACdE,GAAS,OACLH,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzCD,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,EAGZ,CACA,OAAOH,CACT,CCnBe,SAARK,EAAqBC,EAAQC,EAAS,CAC3C,IAAIF,EACJ,GAAIE,IAAY,OACd,QAAWC,KAASF,EACdE,GAAS,OACLH,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzCD,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,EAGZ,CACA,OAAOH,CACT,CCnBe,SAARK,EAAqBC,EAAQC,EAAS,CAC3C,IAAIF,EAAM,EACV,GAAIE,IAAY,OACd,QAASC,KAASF,GACZE,EAAQ,CAACA,KACXH,GAAOG,OAGN,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACZE,EAAQ,CAACD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,KACzCD,GAAOG,EAGb,CACA,OAAOH,CACT,CCfA,SAASK,GAAYC,EAAG,CACtB,OAAOA,EAAE,OAAO,KAClB,CAEO,SAASC,GAAKC,EAAM,CACzB,OAAOA,EAAK,KACd,CAEO,SAASC,GAAMD,EAAM,EAAG,CAC7B,OAAO,EAAI,EAAIA,EAAK,MACtB,CAEO,SAASE,EAAQF,EAAM,EAAG,CAC/B,OAAOA,EAAK,YAAY,OAASA,EAAK,MAAQ,EAAI,CACpD,CAEO,SAASG,GAAOH,EAAM,CAC3B,OAAOA,EAAK,YAAY,OAASA,EAAK,MAChCA,EAAK,YAAY,OAASI,EAAIJ,EAAK,YAAaH,EAAW,EAAI,EAC/D,CACR,CCtBe,SAARQ,EAA0BC,EAAG,CAClC,OAAO,UAAW,CAChB,OAAOA,CACT,CACF,CCAA,SAASC,GAAuBC,EAAGC,EAAG,CACpC,OAAOC,EAAiBF,EAAE,OAAQC,EAAE,MAAM,GAAKD,EAAE,MAAQC,EAAE,KAC7D,CAEA,SAASE,GAAuBH,EAAGC,EAAG,CACpC,OAAOC,EAAiBF,EAAE,OAAQC,EAAE,MAAM,GAAKD,EAAE,MAAQC,EAAE,KAC7D,CAEA,SAASC,EAAiBF,EAAGC,EAAG,CAC9B,OAAOD,EAAE,GAAKC,EAAE,EAClB,CAEA,SAASG,GAAMC,EAAG,CAChB,OAAOA,EAAE,KACX,CAEA,SAASC,GAAUD,EAAG,CACpB,OAAOA,EAAE,KACX,CAEA,SAASE,GAAaC,EAAO,CAC3B,OAAOA,EAAM,KACf,CAEA,SAASC,GAAaD,EAAO,CAC3B,OAAOA,EAAM,KACf,CAEA,SAASE,GAAKC,EAAUC,EAAI,CAC1B,IAAMC,EAAOF,EAAS,IAAIC,CAAE,EAC5B,GAAI,CAACC,EAAM,MAAM,IAAI,MAAM,YAAcD,CAAE,EAC3C,OAAOC,CACT,CAEA,SAASC,GAAoB,CAAC,MAAAC,CAAK,EAAG,CACpC,QAAWF,KAAQE,EAAO,CACxB,IAAIC,EAAKH,EAAK,GACVI,EAAKD,EACT,QAAWE,KAAQL,EAAK,YACtBK,EAAK,GAAKF,EAAKE,EAAK,MAAQ,EAC5BF,GAAME,EAAK,MAEb,QAAWA,KAAQL,EAAK,YACtBK,EAAK,GAAKD,EAAKC,EAAK,MAAQ,EAC5BD,GAAMC,EAAK,KAEf,CACF,CAEe,SAARC,GAA0B,CAC/B,IAAIC,EAAK,EAAGJ,EAAK,EAAGK,EAAK,EAAGJ,EAAK,EAC7BK,EAAK,GACLC,EAAK,EAAGC,EACRZ,EAAKN,GACLmB,EAAQC,EACRC,EACAC,EACAb,EAAQR,GACRsB,EAAQpB,GACRqB,EAAa,EAEjB,SAASC,GAAS,CAChB,IAAMvB,EAAQ,CAAC,MAAOO,EAAM,MAAM,KAAM,SAAS,EAAG,MAAOc,EAAM,MAAM,KAAM,SAAS,CAAC,EACvF,OAAAG,EAAiBxB,CAAK,EACtByB,EAAkBzB,CAAK,EACvB0B,EAAkB1B,CAAK,EACvB2B,EAAmB3B,CAAK,EACxB4B,EAAoB5B,CAAK,EACzBM,GAAoBN,CAAK,EAClBA,CACT,CAEAuB,EAAO,OAAS,SAASvB,EAAO,CAC9B,OAAAM,GAAoBN,CAAK,EAClBA,CACT,EAEAuB,EAAO,OAAS,SAASM,EAAG,CAC1B,OAAO,UAAU,QAAUzB,EAAK,OAAOyB,GAAM,WAAaA,EAAIC,EAASD,CAAC,EAAGN,GAAUnB,CACvF,EAEAmB,EAAO,UAAY,SAASM,EAAG,CAC7B,OAAO,UAAU,QAAUZ,EAAQ,OAAOY,GAAM,WAAaA,EAAIC,EAASD,CAAC,EAAGN,GAAUN,CAC1F,EAEAM,EAAO,SAAW,SAASM,EAAG,CAC5B,OAAO,UAAU,QAAUV,EAAOU,EAAGN,GAAUJ,CACjD,EAEAI,EAAO,UAAY,SAASM,EAAG,CAC7B,OAAO,UAAU,QAAUf,EAAK,CAACe,EAAGN,GAAUT,CAChD,EAEAS,EAAO,YAAc,SAASM,EAAG,CAC/B,OAAO,UAAU,QAAUd,EAAKC,EAAK,CAACa,EAAGN,GAAUR,CACrD,EAEAQ,EAAO,MAAQ,SAASM,EAAG,CACzB,OAAO,UAAU,QAAUtB,EAAQ,OAAOsB,GAAM,WAAaA,EAAIC,EAASD,CAAC,EAAGN,GAAUhB,CAC1F,EAEAgB,EAAO,MAAQ,SAASM,EAAG,CACzB,OAAO,UAAU,QAAUR,EAAQ,OAAOQ,GAAM,WAAaA,EAAIC,EAASD,CAAC,EAAGN,GAAUF,CAC1F,EAEAE,EAAO,SAAW,SAASM,EAAG,CAC5B,OAAO,UAAU,QAAUT,EAAWS,EAAGN,GAAUH,CACrD,EAEAG,EAAO,KAAO,SAASM,EAAG,CACxB,OAAO,UAAU,QAAUjB,EAAKJ,EAAK,EAAGK,EAAK,CAACgB,EAAE,CAAC,EAAGpB,EAAK,CAACoB,EAAE,CAAC,EAAGN,GAAU,CAACV,EAAKD,EAAIH,EAAKD,CAAE,CAC7F,EAEAe,EAAO,OAAS,SAASM,EAAG,CAC1B,OAAO,UAAU,QAAUjB,EAAK,CAACiB,EAAE,CAAC,EAAE,CAAC,EAAGhB,EAAK,CAACgB,EAAE,CAAC,EAAE,CAAC,EAAGrB,EAAK,CAACqB,EAAE,CAAC,EAAE,CAAC,EAAGpB,EAAK,CAACoB,EAAE,CAAC,EAAE,CAAC,EAAGN,GAAU,CAAC,CAACX,EAAIJ,CAAE,EAAG,CAACK,EAAIJ,CAAE,CAAC,CACtH,EAEAc,EAAO,WAAa,SAASM,EAAG,CAC9B,OAAO,UAAU,QAAUP,EAAa,CAACO,EAAGN,GAAUD,CACxD,EAEA,SAASE,EAAiB,CAAC,MAAAjB,EAAO,MAAAc,CAAK,EAAG,CACxC,OAAW,CAACU,EAAG1B,CAAI,IAAKE,EAAM,QAAQ,EACpCF,EAAK,MAAQ0B,EACb1B,EAAK,YAAc,CAAC,EACpBA,EAAK,YAAc,CAAC,EAEtB,IAAMF,EAAW,IAAI,IAAII,EAAM,IAAI,CAACV,EAAGkC,IAAM,CAAC3B,EAAGP,EAAGkC,EAAGxB,CAAK,EAAGV,CAAC,CAAC,CAAC,EAClE,OAAW,CAACkC,EAAGrB,CAAI,IAAKW,EAAM,QAAQ,EAAG,CACvCX,EAAK,MAAQqB,EACb,GAAI,CAAC,OAAAC,EAAQ,OAAAC,CAAM,EAAIvB,EACnB,OAAOsB,GAAW,WAAUA,EAAStB,EAAK,OAASR,GAAKC,EAAU6B,CAAM,GACxE,OAAOC,GAAW,WAAUA,EAASvB,EAAK,OAASR,GAAKC,EAAU8B,CAAM,GAC5ED,EAAO,YAAY,KAAKtB,CAAI,EAC5BuB,EAAO,YAAY,KAAKvB,CAAI,CAC9B,CACA,GAAIU,GAAY,KACd,OAAW,CAAC,YAAAc,EAAa,YAAAC,CAAW,IAAK5B,EACvC2B,EAAY,KAAKd,CAAQ,EACzBe,EAAY,KAAKf,CAAQ,CAG/B,CAEA,SAASK,EAAkB,CAAC,MAAAlB,CAAK,EAAG,CAClC,QAAWF,KAAQE,EACjBF,EAAK,MAAQA,EAAK,aAAe,OAC3B,KAAK,IAAI+B,EAAI/B,EAAK,YAAaT,EAAK,EAAGwC,EAAI/B,EAAK,YAAaT,EAAK,CAAC,EACnES,EAAK,UAEf,CAEA,SAASqB,EAAkB,CAAC,MAAAnB,CAAK,EAAG,CAClC,IAAM8B,EAAI9B,EAAM,OACZ+B,EAAU,IAAI,IAAI/B,CAAK,EACvBgC,EAAO,IAAI,IACXC,EAAI,EACR,KAAOF,EAAQ,MAAM,CACnB,QAAWjC,KAAQiC,EAAS,CAC1BjC,EAAK,MAAQmC,EACb,OAAW,CAAC,OAAAP,CAAM,IAAK5B,EAAK,YAC1BkC,EAAK,IAAIN,CAAM,CAEnB,CACA,GAAI,EAAEO,EAAIH,EAAG,MAAM,IAAI,MAAM,eAAe,EAC5CC,EAAUC,EACVA,EAAO,IAAI,GACb,CACF,CAEA,SAASZ,EAAmB,CAAC,MAAApB,CAAK,EAAG,CACnC,IAAM8B,EAAI9B,EAAM,OACZ+B,EAAU,IAAI,IAAI/B,CAAK,EACvBgC,EAAO,IAAI,IACXC,EAAI,EACR,KAAOF,EAAQ,MAAM,CACnB,QAAWjC,KAAQiC,EAAS,CAC1BjC,EAAK,OAASmC,EACd,OAAW,CAAC,OAAAR,CAAM,IAAK3B,EAAK,YAC1BkC,EAAK,IAAIP,CAAM,CAEnB,CACA,GAAI,EAAEQ,EAAIH,EAAG,MAAM,IAAI,MAAM,eAAe,EAC5CC,EAAUC,EACVA,EAAO,IAAI,GACb,CACF,CAEA,SAASE,EAAkB,CAAC,MAAAlC,CAAK,EAAG,CAClC,IAAMiC,EAAIE,EAAInC,EAAOV,GAAKA,EAAE,KAAK,EAAI,EAC/B8C,GAAM9B,EAAKD,EAAKE,IAAO0B,EAAI,GAC3BI,EAAU,IAAI,MAAMJ,CAAC,EAC3B,QAAWnC,KAAQE,EAAO,CACxB,IAAMwB,EAAI,KAAK,IAAI,EAAG,KAAK,IAAIS,EAAI,EAAG,KAAK,MAAMvB,EAAM,KAAK,KAAMZ,EAAMmC,CAAC,CAAC,CAAC,CAAC,EAC5EnC,EAAK,MAAQ0B,EACb1B,EAAK,GAAKO,EAAKmB,EAAIY,EACnBtC,EAAK,GAAKA,EAAK,GAAKS,EAChB8B,EAAQb,CAAC,EAAGa,EAAQb,CAAC,EAAE,KAAK1B,CAAI,EAC/BuC,EAAQb,CAAC,EAAI,CAAC1B,CAAI,CACzB,CACA,GAAIc,EAAM,QAAW0B,KAAUD,EAC7BC,EAAO,KAAK1B,CAAI,EAElB,OAAOyB,CACT,CAEA,SAASE,EAAuBF,EAAS,CACvC,IAAMG,EAAKC,EAAIJ,EAAS,IAAMnC,EAAKD,GAAM,EAAE,OAAS,GAAKQ,GAAMoB,EAAI,EAAGxC,EAAK,CAAC,EAC5E,QAAWW,KAASqC,EAAS,CAC3B,IAAIK,EAAIzC,EACR,QAAWH,KAAQE,EAAO,CACxBF,EAAK,GAAK4C,EACV5C,EAAK,GAAK4C,EAAI5C,EAAK,MAAQ0C,EAC3BE,EAAI5C,EAAK,GAAKW,EACd,QAAWN,KAAQL,EAAK,YACtBK,EAAK,MAAQA,EAAK,MAAQqC,CAE9B,CACAE,GAAKxC,EAAKwC,EAAIjC,IAAOT,EAAM,OAAS,GACpC,QAASwB,EAAI,EAAGA,EAAIxB,EAAM,OAAQ,EAAEwB,EAAG,CACrC,IAAM1B,EAAOE,EAAMwB,CAAC,EACpB1B,EAAK,IAAM4C,GAAKlB,EAAI,GACpB1B,EAAK,IAAM4C,GAAKlB,EAAI,EACtB,CACAmB,EAAa3C,CAAK,CACpB,CACF,CAEA,SAASqB,EAAoB5B,EAAO,CAClC,IAAM4C,EAAUH,EAAkBzC,CAAK,EACvCgB,EAAK,KAAK,IAAID,GAAKN,EAAKD,IAAOkC,EAAIE,EAAS,GAAK,EAAE,MAAM,EAAI,EAAE,EAC/DE,EAAuBF,CAAO,EAC9B,QAASb,EAAI,EAAGA,EAAIT,EAAY,EAAES,EAAG,CACnC,IAAMoB,EAAQ,KAAK,IAAI,IAAMpB,CAAC,EACxBqB,EAAO,KAAK,IAAI,EAAID,GAAQpB,EAAI,GAAKT,CAAU,EACrD+B,EAAiBT,EAASO,EAAOC,CAAI,EACrCE,EAAiBV,EAASO,EAAOC,CAAI,CACvC,CACF,CAGA,SAASE,EAAiBV,EAASO,EAAOC,EAAM,CAC9C,QAASrB,EAAI,EAAGM,EAAIO,EAAQ,OAAQb,EAAIM,EAAG,EAAEN,EAAG,CAC9C,IAAMc,EAASD,EAAQb,CAAC,EACxB,QAAWE,KAAUY,EAAQ,CAC3B,IAAII,EAAI,EACJM,EAAI,EACR,OAAW,CAAC,OAAAvB,EAAQ,MAAApC,EAAK,IAAKqC,EAAO,YAAa,CAChD,IAAIuB,EAAI5D,IAASqC,EAAO,MAAQD,EAAO,OACvCiB,GAAKQ,EAAUzB,EAAQC,CAAM,EAAIuB,EACjCD,GAAKC,CACP,CACA,GAAI,EAAED,EAAI,GAAI,SACd,IAAIxC,GAAMkC,EAAIM,EAAItB,EAAO,IAAMkB,EAC/BlB,EAAO,IAAMlB,EACbkB,EAAO,IAAMlB,EACb2C,EAAiBzB,CAAM,CACzB,CACId,IAAS,QAAW0B,EAAO,KAAKnD,CAAgB,EACpDiE,EAAkBd,EAAQO,CAAI,CAChC,CACF,CAGA,SAASC,EAAiBT,EAASO,EAAOC,EAAM,CAC9C,QAASf,EAAIO,EAAQ,OAAQb,EAAIM,EAAI,EAAGN,GAAK,EAAG,EAAEA,EAAG,CACnD,IAAMc,EAASD,EAAQb,CAAC,EACxB,QAAWC,KAAUa,EAAQ,CAC3B,IAAII,EAAI,EACJM,EAAI,EACR,OAAW,CAAC,OAAAtB,EAAQ,MAAArC,EAAK,IAAKoC,EAAO,YAAa,CAChD,IAAIwB,EAAI5D,IAASqC,EAAO,MAAQD,EAAO,OACvCiB,GAAKW,EAAU5B,EAAQC,CAAM,EAAIuB,EACjCD,GAAKC,CACP,CACA,GAAI,EAAED,EAAI,GAAI,SACd,IAAIxC,GAAMkC,EAAIM,EAAIvB,EAAO,IAAMmB,EAC/BnB,EAAO,IAAMjB,EACbiB,EAAO,IAAMjB,EACb2C,EAAiB1B,CAAM,CACzB,CACIb,IAAS,QAAW0B,EAAO,KAAKnD,CAAgB,EACpDiE,EAAkBd,EAAQO,CAAI,CAChC,CACF,CAEA,SAASO,EAAkBpD,EAAO4C,EAAO,CACvC,IAAMpB,EAAIxB,EAAM,QAAU,EACpBsD,EAAUtD,EAAMwB,CAAC,EACvB+B,EAA6BvD,EAAOsD,EAAQ,GAAK7C,EAAIe,EAAI,EAAGoB,CAAK,EACjEY,EAA6BxD,EAAOsD,EAAQ,GAAK7C,EAAIe,EAAI,EAAGoB,CAAK,EACjEW,EAA6BvD,EAAOE,EAAIF,EAAM,OAAS,EAAG4C,CAAK,EAC/DY,EAA6BxD,EAAOC,EAAI,EAAG2C,CAAK,CAClD,CAGA,SAASY,EAA6BxD,EAAO0C,EAAGlB,EAAGoB,EAAO,CACxD,KAAOpB,EAAIxB,EAAM,OAAQ,EAAEwB,EAAG,CAC5B,IAAM1B,EAAOE,EAAMwB,CAAC,EACdhB,GAAMkC,EAAI5C,EAAK,IAAM8C,EACvBpC,EAAK,OAAMV,EAAK,IAAMU,EAAIV,EAAK,IAAMU,GACzCkC,EAAI5C,EAAK,GAAKW,CAChB,CACF,CAGA,SAAS8C,EAA6BvD,EAAO0C,EAAGlB,EAAGoB,EAAO,CACxD,KAAOpB,GAAK,EAAG,EAAEA,EAAG,CAClB,IAAM1B,EAAOE,EAAMwB,CAAC,EACdhB,GAAMV,EAAK,GAAK4C,GAAKE,EACvBpC,EAAK,OAAMV,EAAK,IAAMU,EAAIV,EAAK,IAAMU,GACzCkC,EAAI5C,EAAK,GAAKW,CAChB,CACF,CAEA,SAAS0C,EAAiB,CAAC,YAAAxB,EAAa,YAAAC,CAAW,EAAG,CACpD,GAAIf,IAAa,OAAW,CAC1B,OAAW,CAAC,OAAQ,CAAC,YAAAc,CAAW,CAAC,IAAKC,EACpCD,EAAY,KAAKvC,EAAsB,EAEzC,OAAW,CAAC,OAAQ,CAAC,YAAAwC,CAAW,CAAC,IAAKD,EACpCC,EAAY,KAAK5C,EAAsB,CAE3C,CACF,CAEA,SAAS2D,EAAa3C,EAAO,CAC3B,GAAIa,IAAa,OACf,OAAW,CAAC,YAAAc,EAAa,YAAAC,CAAW,IAAK5B,EACvC2B,EAAY,KAAKvC,EAAsB,EACvCwC,EAAY,KAAK5C,EAAsB,CAG7C,CAGA,SAASkE,EAAUzB,EAAQC,EAAQ,CACjC,IAAIgB,EAAIjB,EAAO,IAAMA,EAAO,YAAY,OAAS,GAAKhB,EAAK,EAC3D,OAAW,CAAC,OAAQX,EAAM,MAAA2D,CAAK,IAAKhC,EAAO,YAAa,CACtD,GAAI3B,IAAS4B,EAAQ,MACrBgB,GAAKe,EAAQhD,CACf,CACA,OAAW,CAAC,OAAQX,EAAM,MAAA2D,CAAK,IAAK/B,EAAO,YAAa,CACtD,GAAI5B,IAAS2B,EAAQ,MACrBiB,GAAKe,CACP,CACA,OAAOf,CACT,CAGA,SAASW,EAAU5B,EAAQC,EAAQ,CACjC,IAAIgB,EAAIhB,EAAO,IAAMA,EAAO,YAAY,OAAS,GAAKjB,EAAK,EAC3D,OAAW,CAAC,OAAQX,EAAM,MAAA2D,CAAK,IAAK/B,EAAO,YAAa,CACtD,GAAI5B,IAAS2B,EAAQ,MACrBiB,GAAKe,EAAQhD,CACf,CACA,OAAW,CAAC,OAAQX,EAAM,MAAA2D,CAAK,IAAKhC,EAAO,YAAa,CACtD,GAAI3B,IAAS4B,EAAQ,MACrBgB,GAAKe,CACP,CACA,OAAOf,CACT,CAEA,OAAO1B,CACT,CChXA,IAAI0C,GAAK,KAAK,GACVC,GAAM,EAAID,GACVE,EAAU,KACVC,GAAaF,GAAMC,EAEvB,SAASE,IAAO,CACd,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,KACtB,KAAK,EAAI,EACX,CAEA,SAASC,IAAO,CACd,OAAO,IAAID,EACb,CAEAA,GAAK,UAAYC,GAAK,UAAY,CAChC,YAAaD,GACb,OAAQ,SAASE,EAAGC,EAAG,CACrB,KAAK,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAACD,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAACC,EAC7E,EACA,UAAW,UAAW,CAChB,KAAK,MAAQ,OACf,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IACrC,KAAK,GAAK,IAEd,EACA,OAAQ,SAASD,EAAGC,EAAG,CACrB,KAAK,GAAK,KAAO,KAAK,IAAM,CAACD,GAAK,KAAO,KAAK,IAAM,CAACC,EACvD,EACA,iBAAkB,SAASC,EAAIC,EAAIH,EAAGC,EAAG,CACvC,KAAK,GAAK,KAAO,CAACC,EAAM,KAAO,CAACC,EAAM,KAAO,KAAK,IAAM,CAACH,GAAK,KAAO,KAAK,IAAM,CAACC,EACnF,EACA,cAAe,SAASC,EAAIC,EAAIC,EAAIC,EAAIL,EAAGC,EAAG,CAC5C,KAAK,GAAK,KAAO,CAACC,EAAM,KAAO,CAACC,EAAM,KAAO,CAACC,EAAM,KAAO,CAACC,EAAM,KAAO,KAAK,IAAM,CAACL,GAAK,KAAO,KAAK,IAAM,CAACC,EAC/G,EACA,MAAO,SAASC,EAAIC,EAAIC,EAAIC,EAAIC,EAAG,CACjCJ,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAI,CAACA,EAC7C,IAAIC,EAAK,KAAK,IACVC,EAAK,KAAK,IACVC,EAAML,EAAKF,EACXQ,EAAML,EAAKF,EACXQ,EAAMJ,EAAKL,EACXU,EAAMJ,EAAKL,EACXU,EAAQF,EAAMA,EAAMC,EAAMA,EAG9B,GAAIN,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAsBA,CAAC,EAGlD,GAAI,KAAK,MAAQ,KACf,KAAK,GAAK,KAAO,KAAK,IAAMJ,GAAM,KAAO,KAAK,IAAMC,WAI3CU,EAAQjB,EAKd,GAAI,EAAE,KAAK,IAAIgB,EAAMH,EAAMC,EAAMC,CAAG,EAAIf,IAAY,CAACU,EACxD,KAAK,GAAK,KAAO,KAAK,IAAMJ,GAAM,KAAO,KAAK,IAAMC,OAIjD,CACH,IAAIW,EAAMV,EAAKG,EACXQ,EAAMV,EAAKG,EACXQ,EAAQP,EAAMA,EAAMC,EAAMA,EAC1BO,EAAQH,EAAMA,EAAMC,EAAMA,EAC1BG,EAAM,KAAK,KAAKF,CAAK,EACrBG,EAAM,KAAK,KAAKN,CAAK,EACrBO,EAAId,EAAI,KAAK,KAAKZ,GAAK,KAAK,MAAMsB,EAAQH,EAAQI,IAAU,EAAIC,EAAMC,EAAI,GAAK,CAAC,EAChFE,EAAMD,EAAID,EACVG,EAAMF,EAAIF,EAGV,KAAK,IAAIG,EAAM,CAAC,EAAIzB,IACtB,KAAK,GAAK,KAAOM,EAAKmB,EAAMV,GAAO,KAAOR,EAAKkB,EAAMT,IAGvD,KAAK,GAAK,IAAMN,EAAI,IAAMA,EAAI,SAAW,EAAEM,EAAME,EAAMH,EAAMI,GAAQ,KAAO,KAAK,IAAMb,EAAKoB,EAAMb,GAAO,KAAO,KAAK,IAAMN,EAAKmB,EAAMZ,EACxI,CACF,EACA,IAAK,SAASV,EAAGC,EAAGK,EAAGiB,EAAIC,EAAIC,EAAK,CAClCzB,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAGK,EAAI,CAACA,EAAGmB,EAAM,CAAC,CAACA,EAChC,IAAIC,EAAKpB,EAAI,KAAK,IAAIiB,CAAE,EACpBI,EAAKrB,EAAI,KAAK,IAAIiB,CAAE,EACpBhB,EAAKP,EAAI0B,EACTlB,EAAKP,EAAI0B,EACTC,EAAK,EAAIH,EACTI,EAAKJ,EAAMF,EAAKC,EAAKA,EAAKD,EAG9B,GAAIjB,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAsBA,CAAC,EAG9C,KAAK,MAAQ,KACf,KAAK,GAAK,IAAMC,EAAK,IAAMC,GAIpB,KAAK,IAAI,KAAK,IAAMD,CAAE,EAAIX,GAAW,KAAK,IAAI,KAAK,IAAMY,CAAE,EAAIZ,KACtE,KAAK,GAAK,IAAMW,EAAK,IAAMC,GAIxBF,IAGDuB,EAAK,IAAGA,EAAKA,EAAKlC,GAAMA,IAGxBkC,EAAKhC,GACP,KAAK,GAAK,IAAMS,EAAI,IAAMA,EAAI,QAAUsB,EAAK,KAAO5B,EAAI0B,GAAM,KAAOzB,EAAI0B,GAAM,IAAMrB,EAAI,IAAMA,EAAI,QAAUsB,EAAK,KAAO,KAAK,IAAMrB,GAAM,KAAO,KAAK,IAAMC,GAIrJqB,EAAKjC,IACZ,KAAK,GAAK,IAAMU,EAAI,IAAMA,EAAI,OAAS,EAAEuB,GAAMnC,IAAO,IAAMkC,EAAK,KAAO,KAAK,IAAM5B,EAAIM,EAAI,KAAK,IAAIkB,CAAE,GAAK,KAAO,KAAK,IAAMvB,EAAIK,EAAI,KAAK,IAAIkB,CAAE,IAEpJ,EACA,KAAM,SAASxB,EAAGC,EAAG6B,EAAGC,EAAG,CACzB,KAAK,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAAC/B,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAACC,GAAK,KAAO,CAAC6B,EAAK,KAAO,CAACC,EAAK,IAAO,CAACD,EAAK,GACzH,EACA,SAAU,UAAW,CACnB,OAAO,KAAK,CACd,CACF,EAEA,IAAOE,GAAQjC,GCjIA,SAARkC,GAAiBC,EAAG,CACzB,OAAO,UAAoB,CACzB,OAAOA,CACT,CACF,CCJO,SAASC,GAAEC,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CAEO,SAASC,GAAED,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CCNO,IAAIE,GAAQ,MAAM,UAAU,MCMnC,SAASC,GAAWC,EAAG,CACrB,OAAOA,EAAE,MACX,CAEA,SAASC,GAAWD,EAAG,CACrB,OAAOA,EAAE,MACX,CAEA,SAASE,GAAKC,EAAO,CACnB,IAAIC,EAASL,GACTM,EAASJ,GACTK,EAAIA,GACJC,EAAIA,GACJC,EAAU,KAEd,SAASN,GAAO,CACd,IAAIO,EAAQC,EAAOC,GAAM,KAAK,SAAS,EAAGC,EAAIR,EAAO,MAAM,KAAMM,CAAI,EAAGG,EAAIR,EAAO,MAAM,KAAMK,CAAI,EAGnG,GAFKF,IAASA,EAAUC,EAASK,GAAK,GACtCX,EAAMK,EAAS,CAACF,EAAE,MAAM,MAAOI,EAAK,CAAC,EAAIE,EAAGF,EAAK,EAAG,CAACH,EAAE,MAAM,KAAMG,CAAI,EAAG,CAACJ,EAAE,MAAM,MAAOI,EAAK,CAAC,EAAIG,EAAGH,EAAK,EAAG,CAACH,EAAE,MAAM,KAAMG,CAAI,CAAC,EAC/HD,EAAQ,OAAOD,EAAU,KAAMC,EAAS,IAAM,IACpD,CAEA,OAAAP,EAAK,OAAS,SAASa,EAAG,CACxB,OAAO,UAAU,QAAUX,EAASW,EAAGb,GAAQE,CACjD,EAEAF,EAAK,OAAS,SAASa,EAAG,CACxB,OAAO,UAAU,QAAUV,EAASU,EAAGb,GAAQG,CACjD,EAEAH,EAAK,EAAI,SAASa,EAAG,CACnB,OAAO,UAAU,QAAUT,EAAI,OAAOS,GAAM,WAAaA,EAAIC,GAAS,CAACD,CAAC,EAAGb,GAAQI,CACrF,EAEAJ,EAAK,EAAI,SAASa,EAAG,CACnB,OAAO,UAAU,QAAUR,EAAI,OAAOQ,GAAM,WAAaA,EAAIC,GAAS,CAACD,CAAC,EAAGb,GAAQK,CACrF,EAEAL,EAAK,QAAU,SAASa,EAAG,CACzB,OAAO,UAAU,QAAWP,EAAUO,GAAY,KAAWb,GAAQM,CACvE,EAEON,CACT,CAEA,SAASe,GAAgBT,EAASU,EAAIC,EAAIC,EAAIC,EAAI,CAChDb,EAAQ,OAAOU,EAAIC,CAAE,EACrBX,EAAQ,cAAcU,GAAMA,EAAKE,GAAM,EAAGD,EAAID,EAAIG,EAAID,EAAIC,CAAE,CAC9D,CAgBO,SAASC,IAAiB,CAC/B,OAAOC,GAAKC,EAAe,CAC7B,CCtEA,SAASC,GAAiBC,EAAG,CAC3B,MAAO,CAACA,EAAE,OAAO,GAAIA,EAAE,EAAE,CAC3B,CAEA,SAASC,GAAiBD,EAAG,CAC3B,MAAO,CAACA,EAAE,OAAO,GAAIA,EAAE,EAAE,CAC3B,CAEe,SAARE,IAAmB,CACxB,OAAOC,GAAe,EACjB,OAAOJ,EAAgB,EACvB,OAAOE,EAAgB,CAC9B,CCEA,IAAIG,GAAS,UAAW,CACtB,IAAIC,EAAoBC,EAAO,SAASC,EAAGC,EAAGC,EAAIC,EAAG,CACnD,IAAKD,EAAKA,GAAM,CAAC,EAAGC,EAAIH,EAAE,OAAQG,IAAKD,EAAGF,EAAEG,CAAC,CAAC,EAAIF,EAAG,CACrD,OAAOC,CACT,EAAG,GAAG,EAAGE,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAG,GAAI,EAAE,EACrDC,EAAU,CACZ,MAAuBR,EAAO,UAAiB,CAC/C,EAAG,OAAO,EACV,GAAI,CAAC,EACL,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,OAAU,EAAG,QAAW,EAAG,IAAO,EAAG,QAAW,EAAG,OAAU,EAAG,SAAY,EAAG,IAAO,GAAI,gBAAiB,GAAI,MAAS,GAAI,gBAAiB,GAAI,eAAgB,GAAI,MAAS,GAAI,QAAW,GAAI,YAAe,GAAI,OAAU,GAAI,aAAgB,GAAI,iBAAoB,GAAI,QAAW,EAAG,KAAQ,CAAE,EAC1U,WAAY,CAAE,EAAG,QAAS,EAAG,SAAU,EAAG,UAAW,GAAI,MAAO,GAAI,gBAAiB,GAAI,QAAS,GAAI,gBAAiB,GAAI,eAAgB,GAAI,SAAU,GAAI,eAAgB,GAAI,kBAAmB,EACpM,aAAc,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,CAAC,EAC5G,cAA+BA,EAAO,SAAmBS,EAAQC,EAAQC,EAAUC,EAAIC,EAASC,EAAIC,EAAI,CACtG,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACf,IAAK,GACH,IAAMI,EAASL,EAAG,iBAAiBE,EAAGE,EAAK,CAAC,EAAE,KAAK,EAAE,WAAW,KAAM,GAAG,CAAC,EACpEE,EAASN,EAAG,iBAAiBE,EAAGE,EAAK,CAAC,EAAE,KAAK,EAAE,WAAW,KAAM,GAAG,CAAC,EACpEG,EAAQ,WAAWL,EAAGE,CAAE,EAAE,KAAK,CAAC,EACtCJ,EAAG,QAAQK,EAAQC,EAAQC,CAAK,EAChC,MACF,IAAK,GACL,IAAK,GACL,IAAK,IACH,KAAK,EAAIL,EAAGE,CAAE,EACd,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAClB,KACJ,CACF,EAAG,WAAW,EACd,MAAO,CAAC,CAAE,EAAG,EAAG,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAIX,EAAK,GAAIC,CAAI,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,GAAI,GAAI,CAAC,EAAG,EAAE,CAAE,EAAGP,EAAEO,EAAK,CAAC,EAAG,CAAC,EAAG,CAAE,EAAG,GAAI,EAAG,CAAC,EAAG,EAAE,CAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAGP,EAAEQ,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGR,EAAEQ,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAGR,EAAEQ,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,EAAGR,EAAEO,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG,CAAE,EAAG,GAAI,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAID,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAID,EAAK,GAAIC,CAAI,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAGP,EAAEO,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAGP,EAAEQ,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAIF,EAAK,GAAIC,CAAI,EAAGP,EAAE,CAAC,EAAG,EAAG,EAAE,EAAG,CAAC,EAAG,CAAC,CAAC,CAAC,EACliB,eAAgB,CAAE,GAAI,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,CAAC,CAAE,EACzC,WAA4BC,EAAO,SAAoBoB,EAAKC,EAAM,CAChE,GAAIA,EAAK,YACP,KAAK,MAAMD,CAAG,MACT,CACL,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACR,CACF,EAAG,YAAY,EACf,MAAuBtB,EAAO,SAAeuB,EAAO,CAClD,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,EAAQ,KAAK,MAAOpB,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAGoB,EAAa,EAAGC,EAAS,EAAGC,EAAM,EAClKC,EAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAS,OAAO,OAAO,KAAK,KAAK,EACjCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAASlC,KAAK,KAAK,GACb,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,CAAC,IACjDkC,EAAY,GAAGlC,CAAC,EAAI,KAAK,GAAGA,CAAC,GAGjCiC,EAAO,SAASX,EAAOY,EAAY,EAAE,EACrCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAO,OAAU,MAC1BA,EAAO,OAAS,CAAC,GAEnB,IAAIE,EAAQF,EAAO,OACnBN,EAAO,KAAKQ,CAAK,EACjB,IAAIC,EAASH,EAAO,SAAWA,EAAO,QAAQ,OAC1C,OAAOC,EAAY,GAAG,YAAe,WACvC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAEhD,SAASG,EAASC,EAAG,CACnBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CAClC,CACAvC,EAAOsC,EAAU,UAAU,EAC3B,SAASE,GAAM,CACb,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAO,IAAI,GAAKF,EACpC,OAAOS,GAAU,WACfA,aAAiB,QACnBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAErBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE3BA,CACT,CACAzC,EAAOwC,EAAK,KAAK,EAEjB,QADIE,EAAQC,EAAgBC,EAAOC,EAAQC,EAAGC,EAAGC,EAAQ,CAAC,EAAGC,EAAGC,EAAKC,EAAUC,IAClE,CAUX,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EAC3BC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACtCA,EAASF,EAAI,GAEfK,EAAShB,EAAMe,CAAK,GAAKf,EAAMe,CAAK,EAAEF,CAAM,GAE1C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CACjE,IAAIQ,EAAS,GACbD,EAAW,CAAC,EACZ,IAAKH,KAAKpB,EAAMe,CAAK,EACf,KAAK,WAAWK,CAAC,GAAKA,EAAIlB,GAC5BqB,EAAS,KAAK,IAAM,KAAK,WAAWH,CAAC,EAAI,GAAG,EAG5Cf,EAAO,aACTmB,EAAS,wBAA0B1C,EAAW,GAAK;AAAA,EAAQuB,EAAO,aAAa,EAAI;AAAA,YAAiBkB,EAAS,KAAK,IAAI,EAAI,WAAa,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,EAAS,wBAA0B1C,EAAW,GAAK,iBAAmB+B,GAAUV,EAAM,eAAiB,KAAO,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAErJ,KAAK,WAAWW,EAAQ,CACtB,KAAMnB,EAAO,MACb,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAO,SACb,IAAKE,EACL,SAAAgB,CACF,CAAC,CACH,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAChD,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEpG,OAAQG,EAAO,CAAC,EAAG,CACjB,IAAK,GACHpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAO,MAAM,EACzBN,EAAO,KAAKM,EAAO,MAAM,EACzBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,GASHD,EAASC,EACTA,EAAiB,OATjBjC,EAASwB,EAAO,OAChBzB,EAASyB,EAAO,OAChBvB,EAAWuB,EAAO,SAClBE,EAAQF,EAAO,OACXJ,EAAa,GACfA,KAMJ,MACF,IAAK,GAwBH,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,EAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,EAAM,GAAK,CACT,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WACzC,EACIS,IACFW,EAAM,GAAG,MAAQ,CACfpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACnC,GAEFmB,EAAI,KAAK,cAAc,MAAMC,EAAO,CAClCvC,EACAC,EACAC,EACAwB,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACF,EAAE,OAAOK,CAAI,CAAC,EACV,OAAOc,EAAM,IACf,OAAOA,EAELG,IACFzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAEnCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,EAAM,CAAC,EACnBpB,EAAO,KAAKoB,EAAM,EAAE,EACpBG,EAAWtB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,CAAQ,EACnB,MACF,IAAK,GACH,MAAO,EACX,CACF,CACA,MAAO,EACT,EAAG,OAAO,CACZ,EACIG,EAAwB,UAAW,CACrC,IAAIpB,EAAS,CACX,IAAK,EACL,WAA4BlC,EAAO,SAAoBoB,EAAKC,EAAM,CAChE,GAAI,KAAK,GAAG,OACV,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAEvB,EAAG,YAAY,EAEf,SAA0BpB,EAAO,SAASuB,EAAOX,EAAI,CACnD,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASW,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACZ,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACf,EACI,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,EAAG,CAAC,GAE3B,KAAK,OAAS,EACP,IACT,EAAG,UAAU,EAEb,MAAuBvB,EAAO,UAAW,CACvC,IAAIuD,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACF,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEV,KAAK,QAAQ,QACf,KAAK,OAAO,MAAM,CAAC,IAErB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACT,EAAG,OAAO,EAEV,MAAuBvD,EAAO,SAASuD,EAAI,CACzC,IAAIL,EAAMK,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EACpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASL,CAAG,EAC5D,KAAK,QAAUA,EACf,IAAIO,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EACzDD,EAAM,OAAS,IACjB,KAAK,UAAYA,EAAM,OAAS,GAElC,IAAIT,EAAI,KAAK,OAAO,MACpB,YAAK,OAAS,CACZ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaS,GAASA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAAKA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAAS,KAAK,OAAO,aAAeN,CAC1L,EACI,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAErD,KAAK,OAAS,KAAK,OAAO,OACnB,IACT,EAAG,OAAO,EAEV,KAAsBlD,EAAO,UAAW,CACtC,YAAK,MAAQ,GACN,IACT,EAAG,MAAM,EAET,OAAwBA,EAAO,UAAW,CACxC,GAAI,KAAK,QAAQ,gBACf,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAChO,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACb,CAAC,EAEH,OAAO,IACT,EAAG,QAAQ,EAEX,KAAsBA,EAAO,SAASuC,EAAG,CACvC,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAChC,EAAG,MAAM,EAET,UAA2BvC,EAAO,UAAW,CAC3C,IAAI0D,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAQ,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAAG,WAAW,EAEd,cAA+B1D,EAAO,UAAW,CAC/C,IAAI2D,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KAChBA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAKA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAG,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CACjF,EAAG,eAAe,EAElB,aAA8B3D,EAAO,UAAW,CAC9C,IAAI4D,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACjD,EAAG,cAAc,EAEjB,WAA4B7D,EAAO,SAAS8D,EAAOC,EAAc,CAC/D,IAAItB,EAAOe,EAAOQ,EAmDlB,GAlDI,KAAK,QAAQ,kBACfA,EAAS,CACP,SAAU,KAAK,SACf,OAAQ,CACN,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC3B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACb,EACI,KAAK,QAAQ,SACfA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAGnDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACF,KAAK,UAAYA,EAAM,QAEzB,KAAK,OAAS,CACZ,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EAAQA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAAS,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MAC/I,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAE9D,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBrB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMsB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SACpB,KAAK,KAAO,IAEVtB,EACF,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1B,QAASxC,KAAK+D,EACZ,KAAK/D,CAAC,EAAI+D,EAAO/D,CAAC,EAEpB,MAAO,EACT,CACA,MAAO,EACT,EAAG,YAAY,EAEf,KAAsBD,EAAO,UAAW,CACtC,GAAI,KAAK,KACP,OAAO,KAAK,IAET,KAAK,SACR,KAAK,KAAO,IAEd,IAAIyC,EAAOqB,EAAOG,EAAWC,EACxB,KAAK,QACR,KAAK,OAAS,GACd,KAAK,MAAQ,IAGf,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAEhC,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGzD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAEhC,GADA3B,EAAQ,KAAK,WAAWwB,EAAWE,EAAMC,CAAC,CAAC,EACvC3B,IAAU,GACZ,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1BqB,EAAQ,GACR,QACF,KACE,OAAO,EAEX,SAAW,CAAC,KAAK,QAAQ,KACvB,MAIN,OAAIA,GACFrB,EAAQ,KAAK,WAAWqB,EAAOK,EAAMD,CAAK,CAAC,EACvCzB,IAAU,GACLA,EAEF,IAEL,KAAK,SAAW,GACX,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACtH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACb,CAAC,CAEL,EAAG,MAAM,EAET,IAAqBzC,EAAO,UAAe,CACzC,IAAI+C,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGK,KAAK,IAAI,CAEpB,EAAG,KAAK,EAER,MAAuB/C,EAAO,SAAeqE,EAAW,CACtD,KAAK,eAAe,KAAKA,CAAS,CACpC,EAAG,OAAO,EAEV,SAA0BrE,EAAO,UAAoB,CACnD,IAAIuC,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACC,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEhC,EAAG,UAAU,EAEb,cAA+BvC,EAAO,UAAyB,CAC7D,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAC3E,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAEtC,EAAG,eAAe,EAElB,SAA0BA,EAAO,SAAkBuC,EAAG,CAEpD,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACA,KAAK,eAAeA,CAAC,EAErB,SAEX,EAAG,UAAU,EAEb,UAA2BvC,EAAO,SAAmBqE,EAAW,CAC9D,KAAK,MAAMA,CAAS,CACtB,EAAG,WAAW,EAEd,eAAgCrE,EAAO,UAA0B,CAC/D,OAAO,KAAK,eAAe,MAC7B,EAAG,gBAAgB,EACnB,QAAS,CAAE,mBAAoB,EAAK,EACpC,cAA+BA,EAAO,SAAmBY,EAAI0D,EAAKC,EAA2BC,EAAU,CACrG,IAAIC,EAAUD,EACd,OAAQD,EAA2B,CACjC,IAAK,GACH,YAAK,UAAU,KAAK,EACb,EACP,MACF,IAAK,GACH,MAAO,IAET,IAAK,GACH,MAAO,GAET,IAAK,GACH,MAAO,IAET,IAAK,GACH,YAAK,UAAU,cAAc,EACtB,GACP,MACF,IAAK,GACH,MAAO,IAET,IAAK,GACH,YAAK,SAAS,cAAc,EACrB,GACP,MACF,IAAK,GACH,MAAO,GAEX,CACF,EAAG,WAAW,EACd,MAAO,CAAC,sBAAuB,UAAW,kCAAmC,iBAAkB,iBAAkB,qDAAsD,6BAA8B,kGAAkG,EACvS,WAAY,CAAE,IAAO,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAAG,UAAa,EAAM,EAAG,aAAgB,CAAE,MAAS,CAAC,EAAG,CAAC,EAAG,UAAa,EAAM,EAAG,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAAG,UAAa,EAAK,CAAE,CAC5M,EACA,OAAOrC,CACT,EAAE,EACF1B,EAAQ,MAAQ8C,EAChB,SAASoB,GAAS,CAChB,KAAK,GAAK,CAAC,CACb,CACA,OAAA1E,EAAO0E,EAAQ,QAAQ,EACvBA,EAAO,UAAYlE,EACnBA,EAAQ,OAASkE,EACV,IAAIA,CACb,EAAE,EACF5E,GAAO,OAASA,GAChB,IAAI6E,GAAiB7E,GAGjB8E,GAAQ,CAAC,EACTC,GAAQ,CAAC,EACTC,GAA2B,IAAI,IAC/BC,GAAyB/E,EAAO,IAAM,CACxC4E,GAAQ,CAAC,EACTC,GAAQ,CAAC,EACTC,GAA2B,IAAI,IAC/BE,GAAM,CACR,EAAG,OAAO,EACNC,GAAa,KAAM,CACrB,YAAYhE,EAAQC,EAAQC,EAAQ,EAAG,CACrC,KAAK,OAASF,EACd,KAAK,OAASC,EACd,KAAK,MAAQC,CACf,CACA,MAAO,CACLnB,EAAO,KAAM,YAAY,CAC3B,CACF,EACIkF,GAA0BlF,EAAO,CAACiB,EAAQC,EAAQC,IAAU,CAC9DyD,GAAM,KAAK,IAAIK,GAAWhE,EAAQC,EAAQC,CAAK,CAAC,CAClD,EAAG,SAAS,EACRgE,GAAa,KAAM,CACrB,YAAYC,EAAI,CACd,KAAK,GAAKA,CACZ,CACA,MAAO,CACLpF,EAAO,KAAM,YAAY,CAC3B,CACF,EACIqF,GAAmCrF,EAAQoF,GAAO,CACpDA,EAAKE,GAAe,aAAaF,EAAIG,EAAU,CAAC,EAChD,IAAIC,EAAOV,GAAS,IAAIM,CAAE,EAC1B,OAAII,IAAS,SACXA,EAAO,IAAIL,GAAWC,CAAE,EACxBN,GAAS,IAAIM,EAAII,CAAI,EACrBX,GAAM,KAAKW,CAAI,GAEVA,CACT,EAAG,kBAAkB,EACjBC,GAA2BzF,EAAO,IAAM6E,GAAO,UAAU,EACzDa,GAA2B1F,EAAO,IAAM4E,GAAO,UAAU,EACzDe,GAA2B3F,EAAO,KAAO,CAC3C,MAAO6E,GAAM,IAAKW,IAAU,CAAE,GAAIA,EAAK,EAAG,EAAE,EAC5C,MAAOZ,GAAM,IAAKgB,IAAU,CAC1B,OAAQA,EAAK,OAAO,GACpB,OAAQA,EAAK,OAAO,GACpB,MAAOA,EAAK,KACd,EAAE,CACJ,GAAI,UAAU,EACVC,GAAmB,CACrB,SAAAf,GACA,UAA2B9E,EAAO,IAAMuF,EAAU,EAAE,OAAQ,WAAW,EACvE,SAAAE,GACA,SAAAC,GACA,SAAAC,GACA,QAAAT,GACA,iBAAAG,GACA,YAAAS,GACA,YAAAC,GACA,kBAAAC,GACA,kBAAAC,GACA,gBAAAC,GACA,gBAAAC,GACA,MAAOpB,EACT,EAkBIqB,GAAM,MAAMC,EAAK,CACnB,MAAO,CACLrG,EAAO,KAAM,KAAK,CACpB,CACA,MAAO,CACL,KAAK,MAAQ,CACf,CACA,OAAO,KAAKsG,EAAM,CAChB,OAAO,IAAID,GAAKC,GAAO,EAAED,GAAK,KAAK,CACrC,CACA,YAAYE,EAAI,CACd,KAAK,GAAKA,EACV,KAAK,KAAO,IAAIA,CAAE,EACpB,CACA,UAAW,CACT,MAAO,OAAS,KAAK,KAAO,GAC9B,CACF,EAGIC,GAAgB,CAClB,KAAMC,GACN,MAAOC,GACP,OAAQC,GACR,QAASC,CACX,EACIC,GAAuB7G,EAAO,SAAS8G,EAAMP,EAAIQ,EAAUC,EAAS,CACtE,GAAM,CAAE,cAAAC,EAAe,OAAQC,CAAK,EAAI3B,EAAU,EAC5C4B,EAAsBC,GAAc,OACtCC,EACAJ,IAAkB,YACpBI,EAAiBC,EAAS,KAAOf,CAAE,GAErC,IAAMgB,EAAON,IAAkB,UAAYK,EAASD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EAAIC,EAAS,MAAM,EAC/GE,EAAMP,IAAkB,UAAYM,EAAK,OAAO,QAAQhB,CAAE,IAAI,EAAIe,EAAS,QAAQf,CAAE,IAAI,EACzFkB,EAAQP,GAAM,OAASC,EAAoB,MAC3CO,EAASR,GAAM,QAAUC,EAAoB,MAC7CQ,EAAcT,GAAM,aAAeC,EAAoB,YACvDS,EAAgBV,GAAM,eAAiBC,EAAoB,cAC3DU,EAASX,GAAM,QAAUC,EAAoB,OAC7CW,EAASZ,GAAM,QAAUC,EAAoB,OAC7CY,EAAab,GAAM,YAAcC,EAAoB,WACrDa,EAAQhB,EAAQ,GAAG,SAAS,EAC5BiB,EAAYzB,GAAcoB,CAAa,EAE9BM,EAAS,EAAE,OAAQC,GAAMA,EAAE,EAAE,EAAE,UAD5B,EAC+C,EAAE,YAAY,IAAMJ,EAAa,GAAK,EAAE,EAAE,UAAUE,CAAS,EAAE,OAAO,CACrI,CAAC,EAAG,CAAC,EACL,CAACR,EAAOC,CAAM,CAChB,CAAC,EACMM,CAAK,EACZ,IAAMI,EAAcC,GAAeC,EAAiB,EACpDd,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAAE,UAAU,OAAO,EAAE,KAAKQ,EAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,QAAS,MAAM,EAAE,KAAK,KAAOG,IAAOA,EAAE,IAAM/B,GAAI,KAAK,OAAO,GAAG,EAAE,EAAE,KAAK,YAAa,SAAS+B,EAAG,CAC1L,MAAO,aAAeA,EAAE,GAAK,IAAMA,EAAE,GAAK,GAC5C,CAAC,EAAE,KAAK,IAAMA,GAAMA,EAAE,EAAE,EAAE,KAAK,IAAMA,GAAMA,EAAE,EAAE,EAAE,OAAO,MAAM,EAAE,KAAK,SAAWA,GACvEA,EAAE,GAAKA,EAAE,EACjB,EAAE,KAAK,QAAUA,GAAMA,EAAE,GAAKA,EAAE,EAAE,EAAE,KAAK,OAASA,GAAMC,EAAYD,EAAE,EAAE,CAAC,EAC1E,IAAMI,EAA0BvI,EAAO,CAAC,CAAE,GAAIwI,EAAK,MAAArH,CAAM,IAClD4G,EAGE,GAAGS,CAAG;AAAA,EACfX,CAAM,GAAG,KAAK,MAAM1G,EAAQ,GAAG,EAAI,GAAG,GAAG2G,CAAM,GAHpCU,EAIR,SAAS,EACZhB,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,aAAa,EAAE,KAAK,cAAe,YAAY,EAAE,KAAK,YAAa,EAAE,EAAE,UAAU,MAAM,EAAE,KAAKQ,EAAM,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,IAAMG,GAAMA,EAAE,GAAKV,EAAQ,EAAIU,EAAE,GAAK,EAAIA,EAAE,GAAK,CAAC,EAAE,KAAK,IAAMA,IAAOA,EAAE,GAAKA,EAAE,IAAM,CAAC,EAAE,KAAK,KAAM,GAAGJ,EAAa,IAAM,MAAM,IAAI,EAAE,KAAK,cAAgBI,GAAMA,EAAE,GAAKV,EAAQ,EAAI,QAAU,KAAK,EAAE,KAAKc,CAAO,EAC3W,IAAM3C,EAAO4B,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,OAAQ,MAAM,EAAE,KAAK,iBAAkB,EAAG,EAAE,UAAU,OAAO,EAAE,KAAKQ,EAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,QAAS,MAAM,EAAE,MAAM,iBAAkB,UAAU,EACtMS,EAAYvB,GAAM,WAAa,WACrC,GAAIuB,IAAc,WAAY,CAC5B,IAAMC,EAAW9C,EAAK,OAAO,gBAAgB,EAAE,KAAK,KAAOuC,IAAOA,EAAE,IAAM/B,GAAI,KAAK,iBAAiB,GAAG,EAAE,EAAE,KAAK,gBAAiB,gBAAgB,EAAE,KAAK,KAAO+B,GAAMA,EAAE,OAAO,EAAE,EAAE,KAAK,KAAOA,GAAMA,EAAE,OAAO,EAAE,EAC/MO,EAAS,OAAO,MAAM,EAAE,KAAK,SAAU,IAAI,EAAE,KAAK,aAAeP,GAAMC,EAAYD,EAAE,OAAO,EAAE,CAAC,EAC/FO,EAAS,OAAO,MAAM,EAAE,KAAK,SAAU,MAAM,EAAE,KAAK,aAAeP,GAAMC,EAAYD,EAAE,OAAO,EAAE,CAAC,CACnG,CACA,IAAIQ,EACJ,OAAQF,EAAW,CACjB,IAAK,WACHE,EAA2B3I,EAAQmI,GAAMA,EAAE,IAAK,UAAU,EAC1D,MACF,IAAK,SACHQ,EAA2B3I,EAAQmI,GAAMC,EAAYD,EAAE,OAAO,EAAE,EAAG,UAAU,EAC7E,MACF,IAAK,SACHQ,EAA2B3I,EAAQmI,GAAMC,EAAYD,EAAE,OAAO,EAAE,EAAG,UAAU,EAC7E,MACF,QACEQ,EAAWF,CACf,CACA7C,EAAK,OAAO,MAAM,EAAE,KAAK,IAAKgD,GAAuB,CAAC,EAAE,KAAK,SAAUD,CAAQ,EAAE,KAAK,eAAiBR,GAAM,KAAK,IAAI,EAAGA,EAAE,KAAK,CAAC,EACjIU,GAAkB,OAAQrB,EAAK,EAAGG,CAAW,CAC/C,EAAG,MAAM,EACLmB,GAAyB,CAC3B,KAAAjC,EACF,EAGIkC,GAAwC/I,EAAQ8G,GAC9BA,EAAK,WAAW,2BAA4B,EAAE,EAAE,WAAW,aAAc;AAAA,CAAI,EAAE,KAAK,EAEvG,uBAAuB,EAGtBkC,GAAgBrE,GAAe,MAAM,KAAKA,EAAc,EAC5DA,GAAe,MAASmC,GAASkC,GAAcD,GAAsBjC,CAAI,CAAC,EAC1E,IAAImC,GAAU,CACZ,OAAQtE,GACR,GAAIkB,GACJ,SAAUiD,EACZ", "names": ["max", "values", "valueof", "value", "index", "min", "values", "valueof", "value", "index", "sum", "values", "valueof", "value", "index", "targetDepth", "d", "left", "node", "right", "justify", "center", "min", "constant", "x", "ascendingSourceBreadth", "a", "b", "ascendingBreadth", "ascending<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "d", "defaultId", "defaultNodes", "graph", "defaultLinks", "find", "nodeById", "id", "node", "computeLinkBreadths", "nodes", "y0", "y1", "link", "<PERSON><PERSON>", "x0", "x1", "dx", "dy", "py", "align", "justify", "sort", "linkSort", "links", "iterations", "sankey", "computeNodeLinks", "computeNodeValues", "computeNodeDepths", "computeNodeHeights", "computeNodeBreadths", "_", "constant", "i", "source", "target", "sourceLinks", "targetLinks", "sum", "n", "current", "next", "x", "computeNodeLayers", "max", "kx", "columns", "column", "initializeNodeBreadths", "ky", "min", "y", "reorderLinks", "alpha", "beta", "relaxRightToLeft", "relaxLeftToRight", "w", "v", "targetTop", "reorderNodeLinks", "resolveCollisions", "sourceTop", "subject", "resolveCollisionsBottomToTop", "resolveCollisionsTopToBottom", "width", "pi", "tau", "epsilon", "tauEpsilon", "Path", "path", "x", "y", "x1", "y1", "x2", "y2", "r", "x0", "y0", "x21", "y21", "x01", "y01", "l01_2", "x20", "y20", "l21_2", "l20_2", "l21", "l01", "l", "t01", "t21", "a0", "a1", "ccw", "dx", "dy", "cw", "da", "w", "h", "path_default", "constant_default", "x", "x", "p", "y", "slice", "linkSource", "d", "linkTarget", "link", "curve", "source", "target", "x", "y", "context", "buffer", "argv", "slice", "s", "t", "path_default", "_", "constant_default", "curveHorizontal", "x0", "y0", "x1", "y1", "linkHorizontal", "link", "curveHorizontal", "horizontalSource", "d", "horizontalTarget", "sankeyLinkHorizontal_default", "linkHorizontal", "parser", "o", "__name", "k", "v", "o2", "l", "$V0", "$V1", "$V2", "parser2", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "source", "target", "value", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer2", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "lexer", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "sankey_default", "links", "nodes", "nodesMap", "clear2", "clear", "SankeyLink", "addLink", "SankeyNode", "ID", "findOrCreateNode", "common_default", "getConfig2", "node", "getNodes", "getLinks", "getGraph", "link", "sankeyDB_default", "getAccTitle", "setAccTitle", "getAccDescription", "setAccDescription", "getDiagramTitle", "setDiagramTitle", "<PERSON><PERSON>", "_Uid", "name", "id", "alignmentsMap", "left", "right", "center", "justify", "draw", "text", "_version", "diagObj", "securityLevel", "conf", "defaultSankeyConfig", "defaultConfig2", "sandboxElement", "select_default", "root", "svg", "width", "height", "useMaxWidth", "nodeAlignment", "prefix", "suffix", "showValues", "graph", "nodeAlign", "<PERSON><PERSON>", "d", "colorScheme", "ordinal", "Tableau10_default", "getText", "id2", "linkColor", "gradient", "coloring", "sankeyLinkHorizontal_default", "setupGraphViewbox", "sankeyRenderer_default", "prepareTextForParsing", "originalParse", "diagram"]}