import{a as U,c as E,d as F}from"./chunk-6YMKSKZH.min.js";import{a as P}from"./chunk-2YMHYP32.min.js";import{a as G}from"./chunk-3Z74ZUXG.min.js";import"./chunk-U4DUTLYF.min.js";import"./chunk-IQQ46AC6.min.js";import"./chunk-ISDTAGDN.min.js";import"./chunk-JL3VILNY.min.js";import"./chunk-TLYS76Q7.min.js";import"./chunk-CLIYZZ5Y.min.js";import"./chunk-N6ME3NZU.min.js";import"./chunk-V55NTXQN.min.js";import"./chunk-BD4P4Z7J.min.js";import"./chunk-AUO2PXKS.min.js";import{m as C}from"./chunk-PYPO7LRM.min.js";import"./chunk-CM5D5KZN.min.js";import{Fa as R,Ja as W,N as T,O as A,Z as t,h as u,ia as z,j as S}from"./chunk-U3SD26FK.min.js";import"./chunk-CXRPJJJE.min.js";import"./chunk-OSRY5VT3.min.js";var _={},Y=u((e,i)=>{_[e]=i},"set"),I=u(e=>_[e],"get"),X=u(()=>Object.keys(_),"keys"),$=u(()=>X().length,"size"),q={get:I,set:Y,keys:X,size:$},Z=u(e=>e.append("circle").attr("class","start-state").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit).attr("cy",t().state.padding+t().state.sizeUnit),"drawStartState"),j=u(e=>e.append("line").style("stroke","grey").style("stroke-dasharray","3").attr("x1",t().state.textHeight).attr("class","divider").attr("x2",t().state.textHeight*2).attr("y1",0).attr("y2",0),"drawDivider"),K=u((e,i)=>{let s=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+2*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.id),o=s.node().getBBox();return e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",o.width+2*t().state.padding).attr("height",o.height+2*t().state.padding).attr("rx",t().state.radius),s},"drawSimpleState"),Q=u((e,i)=>{let s=u(function(l,y,m){let k=l.append("tspan").attr("x",2*t().state.padding).text(y);m||k.attr("dy",t().state.textHeight)},"addTspan"),n=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+1.3*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.descriptions[0]).node().getBBox(),h=n.height,p=e.append("text").attr("x",t().state.padding).attr("y",h+t().state.padding*.4+t().state.dividerMargin+t().state.textHeight).attr("class","state-description"),a=!0,d=!0;i.descriptions.forEach(function(l){a||(s(p,l,d),d=!1),a=!1});let w=e.append("line").attr("x1",t().state.padding).attr("y1",t().state.padding+h+t().state.dividerMargin/2).attr("y2",t().state.padding+h+t().state.dividerMargin/2).attr("class","descr-divider"),x=p.node().getBBox(),c=Math.max(x.width,n.width);return w.attr("x2",c+3*t().state.padding),e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",c+2*t().state.padding).attr("height",x.height+h+2*t().state.padding).attr("rx",t().state.radius),e},"drawDescrState"),V=u((e,i,s)=>{let o=t().state.padding,n=2*t().state.padding,h=e.node().getBBox(),p=h.width,a=h.x,d=e.append("text").attr("x",0).attr("y",t().state.titleShift).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.id),x=d.node().getBBox().width+n,c=Math.max(x,p);c===p&&(c=c+n);let l,y=e.node().getBBox();i.doc,l=a-o,x>p&&(l=(p-c)/2+o),Math.abs(a-y.x)<o&&x>p&&(l=a-(x-p)/2);let m=1-t().state.textHeight;return e.insert("rect",":first-child").attr("x",l).attr("y",m).attr("class",s?"alt-composit":"composit").attr("width",c).attr("height",y.height+t().state.textHeight+t().state.titleShift+1).attr("rx","0"),d.attr("x",l+o),x<=p&&d.attr("x",a+(c-n)/2-x/2+o),e.insert("rect",":first-child").attr("x",l).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",c).attr("height",t().state.textHeight*3).attr("rx",t().state.radius),e.insert("rect",":first-child").attr("x",l).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",c).attr("height",y.height+3+2*t().state.textHeight).attr("rx",t().state.radius),e},"addTitleAndBox"),tt=u(e=>(e.append("circle").attr("class","end-state-outer").attr("r",t().state.sizeUnit+t().state.miniPadding).attr("cx",t().state.padding+t().state.sizeUnit+t().state.miniPadding).attr("cy",t().state.padding+t().state.sizeUnit+t().state.miniPadding),e.append("circle").attr("class","end-state-inner").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit+2).attr("cy",t().state.padding+t().state.sizeUnit+2)),"drawEndState"),et=u((e,i)=>{let s=t().state.forkWidth,o=t().state.forkHeight;if(i.parentId){let n=s;s=o,o=n}return e.append("rect").style("stroke","black").style("fill","black").attr("width",s).attr("height",o).attr("x",t().state.padding).attr("y",t().state.padding)},"drawForkJoinState"),at=u((e,i,s,o)=>{let n=0,h=o.append("text");h.style("text-anchor","start"),h.attr("class","noteText");let p=e.replace(/\r\n/g,"<br/>");p=p.replace(/\n/g,"<br/>");let a=p.split(T.lineBreakRegex),d=1.25*t().state.noteMargin;for(let w of a){let x=w.trim();if(x.length>0){let c=h.append("tspan");if(c.text(x),d===0){let l=c.node().getBBox();d+=l.height}n+=d,c.attr("x",i+t().state.noteMargin),c.attr("y",s+n+1.25*t().state.noteMargin)}}return{textWidth:h.node().getBBox().width,textHeight:n}},"_drawLongText"),it=u((e,i)=>{i.attr("class","state-note");let s=i.append("rect").attr("x",0).attr("y",t().state.padding),o=i.append("g"),{textWidth:n,textHeight:h}=at(e,0,0,o);return s.attr("height",h+2*t().state.noteMargin),s.attr("width",n+t().state.noteMargin*2),s},"drawNote"),O=u(function(e,i){let s=i.id,o={id:s,label:i.id,width:0,height:0},n=e.append("g").attr("id",s).attr("class","stateGroup");i.type==="start"&&Z(n),i.type==="end"&&tt(n),(i.type==="fork"||i.type==="join")&&et(n,i),i.type==="note"&&it(i.note.text,n),i.type==="divider"&&j(n),i.type==="default"&&i.descriptions.length===0&&K(n,i),i.type==="default"&&i.descriptions.length>0&&Q(n,i);let h=n.node().getBBox();return o.width=h.width+2*t().state.padding,o.height=h.height+2*t().state.padding,q.set(s,o),o},"drawState"),J=0,rt=u(function(e,i,s){let o=u(function(d){switch(d){case E.relationType.AGGREGATION:return"aggregation";case E.relationType.EXTENSION:return"extension";case E.relationType.COMPOSITION:return"composition";case E.relationType.DEPENDENCY:return"dependency"}},"getRelationType");i.points=i.points.filter(d=>!Number.isNaN(d.y));let n=i.points,h=R().x(function(d){return d.x}).y(function(d){return d.y}).curve(W),p=e.append("path").attr("d",h(n)).attr("id","edge"+J).attr("class","transition"),a="";if(t().state.arrowMarkerAbsolute&&(a=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,a=a.replace(/\(/g,"\\("),a=a.replace(/\)/g,"\\)")),p.attr("marker-end","url("+a+"#"+o(E.relationType.DEPENDENCY)+"End)"),s.title!==void 0){let d=e.append("g").attr("class","stateLabel"),{x:w,y:x}=C.calcLabelPosition(i.points),c=T.getRows(s.title),l=0,y=[],m=0,k=0;for(let r=0;r<=c.length;r++){let f=d.append("text").attr("text-anchor","middle").text(c[r]).attr("x",w).attr("y",x+l),g=f.node().getBBox();m=Math.max(m,g.width),k=Math.min(k,g.x),S.info(g.x,w,x+l),l===0&&(l=f.node().getBBox().height,S.info("Title height",l,x)),y.push(f)}let N=l*c.length;if(c.length>1){let r=(c.length-1)*l*.5;y.forEach((f,g)=>f.attr("y",x+g*l-r)),N=l*c.length}let M=d.node().getBBox();d.insert("rect",":first-child").attr("class","box").attr("x",w-m/2-t().state.padding/2).attr("y",x-N/2-t().state.padding/2-3.5).attr("width",m+t().state.padding).attr("height",N+t().state.padding),S.info(M)}J++},"drawEdge"),b,L={},nt=u(function(){},"setConf"),dt=u(function(e){e.append("defs").append("marker").attr("id","dependencyEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"insertMarkers"),st=u(function(e,i,s,o){b=t().state;let n=t().securityLevel,h;n==="sandbox"&&(h=z("#i"+i));let p=n==="sandbox"?z(h.nodes()[0].contentDocument.body):z("body"),a=n==="sandbox"?h.nodes()[0].contentDocument:document;S.debug("Rendering diagram "+e);let d=p.select(`[id='${i}']`);dt(d);let w=o.db.getRootDoc();D(w,d,void 0,!1,p,a,o);let x=b.padding,c=d.node().getBBox(),l=c.width+x*2,y=c.height+x*2,m=l*1.75;A(d,y,m,b.useMaxWidth),d.attr("viewBox",`${c.x-b.padding}  ${c.y-b.padding} `+l+" "+y)},"draw"),ot=u(e=>e?e.length*b.fontSizeFactor:1,"getLabelWidth"),D=u((e,i,s,o,n,h,p)=>{let a=new G({compound:!0,multigraph:!0}),d,w=!0;for(d=0;d<e.length;d++)if(e[d].stmt==="relation"){w=!1;break}s?a.setGraph({rankdir:"LR",multigraph:!0,compound:!0,ranker:"tight-tree",ranksep:w?1:b.edgeLengthFactor,nodeSep:w?1:50,isMultiGraph:!0}):a.setGraph({rankdir:"TB",multigraph:!0,compound:!0,ranksep:w?1:b.edgeLengthFactor,nodeSep:w?1:50,ranker:"tight-tree",isMultiGraph:!0}),a.setDefaultEdgeLabel(function(){return{}}),p.db.extract(e);let x=p.db.getStates(),c=p.db.getRelations(),l=Object.keys(x),y=!0;for(let r of l){let f=x[r];s&&(f.parentId=s);let g;if(f.doc){let B=i.append("g").attr("id",f.id).attr("class","stateGroup");if(g=D(f.doc,B,f.id,!o,n,h,p),y){B=V(B,f,o);let v=B.node().getBBox();g.width=v.width,g.height=v.height+b.padding/2,L[f.id]={y:b.compositTitleSize}}else{let v=B.node().getBBox();g.width=v.width,g.height=v.height}}else g=O(i,f,a);if(f.note){let B={descriptions:[],id:f.id+"-note",note:f.note,type:"note"},v=O(i,B,a);f.note.position==="left of"?(a.setNode(g.id+"-note",v),a.setNode(g.id,g)):(a.setNode(g.id,g),a.setNode(g.id+"-note",v)),a.setParent(g.id,g.id+"-group"),a.setParent(g.id+"-note",g.id+"-group")}else a.setNode(g.id,g)}S.debug("Count=",a.nodeCount(),a);let m=0;c.forEach(function(r){m++,S.debug("Setting edge",r),a.setEdge(r.id1,r.id2,{relation:r,width:ot(r.title),height:b.labelHeight*T.getRows(r.title).length,labelpos:"c"},"id"+m)}),P(a),S.debug("Graph after layout",a.nodes());let k=i.node();a.nodes().forEach(function(r){r!==void 0&&a.node(r)!==void 0?(S.warn("Node "+r+": "+JSON.stringify(a.node(r))),n.select("#"+k.id+" #"+r).attr("transform","translate("+(a.node(r).x-a.node(r).width/2)+","+(a.node(r).y+(L[r]?L[r].y:0)-a.node(r).height/2)+" )"),n.select("#"+k.id+" #"+r).attr("data-x-shift",a.node(r).x-a.node(r).width/2),h.querySelectorAll("#"+k.id+" #"+r+" .divider").forEach(g=>{let B=g.parentElement,v=0,H=0;B&&(B.parentElement&&(v=B.parentElement.getBBox().width),H=parseInt(B.getAttribute("data-x-shift"),10),Number.isNaN(H)&&(H=0)),g.setAttribute("x1",0-H+8),g.setAttribute("x2",v-H-8)})):S.debug("No Node "+r+": "+JSON.stringify(a.node(r)))});let N=k.getBBox();a.edges().forEach(function(r){r!==void 0&&a.edge(r)!==void 0&&(S.debug("Edge "+r.v+" -> "+r.w+": "+JSON.stringify(a.edge(r))),rt(i,a.edge(r),a.edge(r).relation))}),N=k.getBBox();let M={id:s||"root",label:s||"root",width:0,height:0};return M.width=N.width+2*b.padding,M.height=N.height+2*b.padding,S.debug("Doc rendered",M,a),M},"renderDoc"),ct={setConf:nt,draw:st},Et={parser:U,db:E,renderer:ct,styles:F,init:u(e=>{e.state||(e.state={}),e.state.arrowMarkerAbsolute=e.arrowMarkerAbsolute,E.clear()},"init")};export{Et as diagram};
//# sourceMappingURL=stateDiagram-MAYHULR4-UPNPJ5ZA.min.js.map
