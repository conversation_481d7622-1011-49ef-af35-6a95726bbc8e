{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.fr.js"], "sourcesContent": ["/*!\n * Lunr languages, `French` language\n * https://github.com/Mihai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.fr = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.fr.trimmer,\n        lunr.fr.stopWordFilter,\n        lunr.fr.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.fr.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.fr.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.fr.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.fr.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.fr.trimmer, 'trimmer-fr');\n\n    /* lunr stemmer function */\n    lunr.fr.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function FrenchStemmer() {\n          var a_0 = [new Among(\"col\", -1, -1), new Among(\"par\", -1, -1),\n              new Among(\"tap\", -1, -1)\n            ],\n            a_1 = [new Among(\"\", -1, 4),\n              new Among(\"I\", 0, 1), new Among(\"U\", 0, 2), new Among(\"Y\", 0, 3)\n            ],\n            a_2 = [\n              new Among(\"iqU\", -1, 3), new Among(\"abl\", -1, 3),\n              new Among(\"I\\u00E8r\", -1, 4), new Among(\"i\\u00E8r\", -1, 4),\n              new Among(\"eus\", -1, 2), new Among(\"iv\", -1, 1)\n            ],\n            a_3 = [\n              new Among(\"ic\", -1, 2), new Among(\"abil\", -1, 1),\n              new Among(\"iv\", -1, 3)\n            ],\n            a_4 = [new Among(\"iqUe\", -1, 1),\n              new Among(\"atrice\", -1, 2), new Among(\"ance\", -1, 1),\n              new Among(\"ence\", -1, 5), new Among(\"logie\", -1, 3),\n              new Among(\"able\", -1, 1), new Among(\"isme\", -1, 1),\n              new Among(\"euse\", -1, 11), new Among(\"iste\", -1, 1),\n              new Among(\"ive\", -1, 8), new Among(\"if\", -1, 8),\n              new Among(\"usion\", -1, 4), new Among(\"ation\", -1, 2),\n              new Among(\"ution\", -1, 4), new Among(\"ateur\", -1, 2),\n              new Among(\"iqUes\", -1, 1), new Among(\"atrices\", -1, 2),\n              new Among(\"ances\", -1, 1), new Among(\"ences\", -1, 5),\n              new Among(\"logies\", -1, 3), new Among(\"ables\", -1, 1),\n              new Among(\"ismes\", -1, 1), new Among(\"euses\", -1, 11),\n              new Among(\"istes\", -1, 1), new Among(\"ives\", -1, 8),\n              new Among(\"ifs\", -1, 8), new Among(\"usions\", -1, 4),\n              new Among(\"ations\", -1, 2), new Among(\"utions\", -1, 4),\n              new Among(\"ateurs\", -1, 2), new Among(\"ments\", -1, 15),\n              new Among(\"ements\", 30, 6), new Among(\"issements\", 31, 12),\n              new Among(\"it\\u00E9s\", -1, 7), new Among(\"ment\", -1, 15),\n              new Among(\"ement\", 34, 6), new Among(\"issement\", 35, 12),\n              new Among(\"amment\", 34, 13), new Among(\"emment\", 34, 14),\n              new Among(\"aux\", -1, 10), new Among(\"eaux\", 39, 9),\n              new Among(\"eux\", -1, 1), new Among(\"it\\u00E9\", -1, 7)\n            ],\n            a_5 = [\n              new Among(\"ira\", -1, 1), new Among(\"ie\", -1, 1),\n              new Among(\"isse\", -1, 1), new Among(\"issante\", -1, 1),\n              new Among(\"i\", -1, 1), new Among(\"irai\", 4, 1),\n              new Among(\"ir\", -1, 1), new Among(\"iras\", -1, 1),\n              new Among(\"ies\", -1, 1), new Among(\"\\u00EEmes\", -1, 1),\n              new Among(\"isses\", -1, 1), new Among(\"issantes\", -1, 1),\n              new Among(\"\\u00EEtes\", -1, 1), new Among(\"is\", -1, 1),\n              new Among(\"irais\", 13, 1), new Among(\"issais\", 13, 1),\n              new Among(\"irions\", -1, 1), new Among(\"issions\", -1, 1),\n              new Among(\"irons\", -1, 1), new Among(\"issons\", -1, 1),\n              new Among(\"issants\", -1, 1), new Among(\"it\", -1, 1),\n              new Among(\"irait\", 21, 1), new Among(\"issait\", 21, 1),\n              new Among(\"issant\", -1, 1), new Among(\"iraIent\", -1, 1),\n              new Among(\"issaIent\", -1, 1), new Among(\"irent\", -1, 1),\n              new Among(\"issent\", -1, 1), new Among(\"iront\", -1, 1),\n              new Among(\"\\u00EEt\", -1, 1), new Among(\"iriez\", -1, 1),\n              new Among(\"issiez\", -1, 1), new Among(\"irez\", -1, 1),\n              new Among(\"issez\", -1, 1)\n            ],\n            a_6 = [new Among(\"a\", -1, 3),\n              new Among(\"era\", 0, 2), new Among(\"asse\", -1, 3),\n              new Among(\"ante\", -1, 3), new Among(\"\\u00E9e\", -1, 2),\n              new Among(\"ai\", -1, 3), new Among(\"erai\", 5, 2),\n              new Among(\"er\", -1, 2), new Among(\"as\", -1, 3),\n              new Among(\"eras\", 8, 2), new Among(\"\\u00E2mes\", -1, 3),\n              new Among(\"asses\", -1, 3), new Among(\"antes\", -1, 3),\n              new Among(\"\\u00E2tes\", -1, 3), new Among(\"\\u00E9es\", -1, 2),\n              new Among(\"ais\", -1, 3), new Among(\"erais\", 15, 2),\n              new Among(\"ions\", -1, 1), new Among(\"erions\", 17, 2),\n              new Among(\"assions\", 17, 3), new Among(\"erons\", -1, 2),\n              new Among(\"ants\", -1, 3), new Among(\"\\u00E9s\", -1, 2),\n              new Among(\"ait\", -1, 3), new Among(\"erait\", 23, 2),\n              new Among(\"ant\", -1, 3), new Among(\"aIent\", -1, 3),\n              new Among(\"eraIent\", 26, 2), new Among(\"\\u00E8rent\", -1, 2),\n              new Among(\"assent\", -1, 3), new Among(\"eront\", -1, 2),\n              new Among(\"\\u00E2t\", -1, 3), new Among(\"ez\", -1, 2),\n              new Among(\"iez\", 32, 2), new Among(\"eriez\", 33, 2),\n              new Among(\"assiez\", 33, 3), new Among(\"erez\", 32, 2),\n              new Among(\"\\u00E9\", -1, 2)\n            ],\n            a_7 = [new Among(\"e\", -1, 3),\n              new Among(\"I\\u00E8re\", 0, 2), new Among(\"i\\u00E8re\", 0, 2),\n              new Among(\"ion\", -1, 1), new Among(\"Ier\", -1, 2),\n              new Among(\"ier\", -1, 2), new Among(\"\\u00EB\", -1, 4)\n            ],\n            a_8 = [\n              new Among(\"ell\", -1, -1), new Among(\"eill\", -1, -1),\n              new Among(\"enn\", -1, -1), new Among(\"onn\", -1, -1),\n              new Among(\"ett\", -1, -1)\n            ],\n            g_v = [17, 65, 16, 1, 0, 0, 0, 0, 0, 0,\n              0, 0, 0, 0, 0, 128, 130, 103, 8, 5\n            ],\n            g_keep_with_s = [1, 65, 20, 0,\n              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128\n            ],\n            I_p2, I_p1, I_pV, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function habr1(c1, c2, v_1) {\n            if (sbp.eq_s(1, c1)) {\n              sbp.ket = sbp.cursor;\n              if (sbp.in_grouping(g_v, 97, 251)) {\n                sbp.slice_from(c2);\n                sbp.cursor = v_1;\n                return true;\n              }\n            }\n            return false;\n          }\n\n          function habr2(c1, c2, v_1) {\n            if (sbp.eq_s(1, c1)) {\n              sbp.ket = sbp.cursor;\n              sbp.slice_from(c2);\n              sbp.cursor = v_1;\n              return true;\n            }\n            return false;\n          }\n\n          function r_prelude() {\n            var v_1, v_2;\n            while (true) {\n              v_1 = sbp.cursor;\n              if (sbp.in_grouping(g_v, 97, 251)) {\n                sbp.bra = sbp.cursor;\n                v_2 = sbp.cursor;\n                if (habr1(\"u\", \"U\", v_1))\n                  continue;\n                sbp.cursor = v_2;\n                if (habr1(\"i\", \"I\", v_1))\n                  continue;\n                sbp.cursor = v_2;\n                if (habr2(\"y\", \"Y\", v_1))\n                  continue;\n              }\n              sbp.cursor = v_1;\n              sbp.bra = v_1;\n              if (!habr1(\"y\", \"Y\", v_1)) {\n                sbp.cursor = v_1;\n                if (sbp.eq_s(1, \"q\")) {\n                  sbp.bra = sbp.cursor;\n                  if (habr2(\"u\", \"U\", v_1))\n                    continue;\n                }\n                sbp.cursor = v_1;\n                if (v_1 >= sbp.limit)\n                  return;\n                sbp.cursor++;\n              }\n            }\n          }\n\n          function habr3() {\n            while (!sbp.in_grouping(g_v, 97, 251)) {\n              if (sbp.cursor >= sbp.limit)\n                return true;\n              sbp.cursor++;\n            }\n            while (!sbp.out_grouping(g_v, 97, 251)) {\n              if (sbp.cursor >= sbp.limit)\n                return true;\n              sbp.cursor++;\n            }\n            return false;\n          }\n\n          function r_mark_regions() {\n            var v_1 = sbp.cursor;\n            I_pV = sbp.limit;\n            I_p1 = I_pV;\n            I_p2 = I_pV;\n            if (sbp.in_grouping(g_v, 97, 251) && sbp.in_grouping(g_v, 97, 251) &&\n              sbp.cursor < sbp.limit)\n              sbp.cursor++;\n            else {\n              sbp.cursor = v_1;\n              if (!sbp.find_among(a_0, 3)) {\n                sbp.cursor = v_1;\n                do {\n                  if (sbp.cursor >= sbp.limit) {\n                    sbp.cursor = I_pV;\n                    break;\n                  }\n                  sbp.cursor++;\n                } while (!sbp.in_grouping(g_v, 97, 251));\n              }\n            }\n            I_pV = sbp.cursor;\n            sbp.cursor = v_1;\n            if (!habr3()) {\n              I_p1 = sbp.cursor;\n              if (!habr3())\n                I_p2 = sbp.cursor;\n            }\n          }\n\n          function r_postlude() {\n            var among_var, v_1;\n            while (true) {\n              v_1 = sbp.cursor;\n              sbp.bra = v_1;\n              among_var = sbp.find_among(a_1, 4);\n              if (!among_var)\n                break;\n              sbp.ket = sbp.cursor;\n              switch (among_var) {\n                case 1:\n                  sbp.slice_from(\"i\");\n                  break;\n                case 2:\n                  sbp.slice_from(\"u\");\n                  break;\n                case 3:\n                  sbp.slice_from(\"y\");\n                  break;\n                case 4:\n                  if (sbp.cursor >= sbp.limit)\n                    return;\n                  sbp.cursor++;\n                  break;\n              }\n            }\n          }\n\n          function r_RV() {\n            return I_pV <= sbp.cursor;\n          }\n\n          function r_R1() {\n            return I_p1 <= sbp.cursor;\n          }\n\n          function r_R2() {\n            return I_p2 <= sbp.cursor;\n          }\n\n          function r_standard_suffix() {\n            var among_var, v_1;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_4, 43);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              switch (among_var) {\n                case 1:\n                  if (!r_R2())\n                    return false;\n                  sbp.slice_del();\n                  break;\n                case 2:\n                  if (!r_R2())\n                    return false;\n                  sbp.slice_del();\n                  sbp.ket = sbp.cursor;\n                  if (sbp.eq_s_b(2, \"ic\")) {\n                    sbp.bra = sbp.cursor;\n                    if (!r_R2())\n                      sbp.slice_from(\"iqU\");\n                    else\n                      sbp.slice_del();\n                  }\n                  break;\n                case 3:\n                  if (!r_R2())\n                    return false;\n                  sbp.slice_from(\"log\");\n                  break;\n                case 4:\n                  if (!r_R2())\n                    return false;\n                  sbp.slice_from(\"u\");\n                  break;\n                case 5:\n                  if (!r_R2())\n                    return false;\n                  sbp.slice_from(\"ent\");\n                  break;\n                case 6:\n                  if (!r_RV())\n                    return false;\n                  sbp.slice_del();\n                  sbp.ket = sbp.cursor;\n                  among_var = sbp.find_among_b(a_2, 6);\n                  if (among_var) {\n                    sbp.bra = sbp.cursor;\n                    switch (among_var) {\n                      case 1:\n                        if (r_R2()) {\n                          sbp.slice_del();\n                          sbp.ket = sbp.cursor;\n                          if (sbp.eq_s_b(2, \"at\")) {\n                            sbp.bra = sbp.cursor;\n                            if (r_R2())\n                              sbp.slice_del();\n                          }\n                        }\n                        break;\n                      case 2:\n                        if (r_R2())\n                          sbp.slice_del();\n                        else if (r_R1())\n                          sbp.slice_from(\"eux\");\n                        break;\n                      case 3:\n                        if (r_R2())\n                          sbp.slice_del();\n                        break;\n                      case 4:\n                        if (r_RV())\n                          sbp.slice_from(\"i\");\n                        break;\n                    }\n                  }\n                  break;\n                case 7:\n                  if (!r_R2())\n                    return false;\n                  sbp.slice_del();\n                  sbp.ket = sbp.cursor;\n                  among_var = sbp.find_among_b(a_3, 3);\n                  if (among_var) {\n                    sbp.bra = sbp.cursor;\n                    switch (among_var) {\n                      case 1:\n                        if (r_R2())\n                          sbp.slice_del();\n                        else\n                          sbp.slice_from(\"abl\");\n                        break;\n                      case 2:\n                        if (r_R2())\n                          sbp.slice_del();\n                        else\n                          sbp.slice_from(\"iqU\");\n                        break;\n                      case 3:\n                        if (r_R2())\n                          sbp.slice_del();\n                        break;\n                    }\n                  }\n                  break;\n                case 8:\n                  if (!r_R2())\n                    return false;\n                  sbp.slice_del();\n                  sbp.ket = sbp.cursor;\n                  if (sbp.eq_s_b(2, \"at\")) {\n                    sbp.bra = sbp.cursor;\n                    if (r_R2()) {\n                      sbp.slice_del();\n                      sbp.ket = sbp.cursor;\n                      if (sbp.eq_s_b(2, \"ic\")) {\n                        sbp.bra = sbp.cursor;\n                        if (r_R2())\n                          sbp.slice_del();\n                        else\n                          sbp.slice_from(\"iqU\");\n                        break;\n                      }\n                    }\n                  }\n                  break;\n                case 9:\n                  sbp.slice_from(\"eau\");\n                  break;\n                case 10:\n                  if (!r_R1())\n                    return false;\n                  sbp.slice_from(\"al\");\n                  break;\n                case 11:\n                  if (r_R2())\n                    sbp.slice_del();\n                  else if (!r_R1())\n                    return false;\n                  else\n                    sbp.slice_from(\"eux\");\n                  break;\n                case 12:\n                  if (!r_R1() || !sbp.out_grouping_b(g_v, 97, 251))\n                    return false;\n                  sbp.slice_del();\n                  break;\n                case 13:\n                  if (r_RV())\n                    sbp.slice_from(\"ant\");\n                  return false;\n                case 14:\n                  if (r_RV())\n                    sbp.slice_from(\"ent\");\n                  return false;\n                case 15:\n                  v_1 = sbp.limit - sbp.cursor;\n                  if (sbp.in_grouping_b(g_v, 97, 251) && r_RV()) {\n                    sbp.cursor = sbp.limit - v_1;\n                    sbp.slice_del();\n                  }\n                  return false;\n              }\n              return true;\n            }\n            return false;\n          }\n\n          function r_i_verb_suffix() {\n            var among_var, v_1;\n            if (sbp.cursor < I_pV)\n              return false;\n            v_1 = sbp.limit_backward;\n            sbp.limit_backward = I_pV;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_5, 35);\n            if (!among_var) {\n              sbp.limit_backward = v_1;\n              return false;\n            }\n            sbp.bra = sbp.cursor;\n            if (among_var == 1) {\n              if (!sbp.out_grouping_b(g_v, 97, 251)) {\n                sbp.limit_backward = v_1;\n                return false;\n              }\n              sbp.slice_del();\n            }\n            sbp.limit_backward = v_1;\n            return true;\n          }\n\n          function r_verb_suffix() {\n            var among_var, v_2, v_3;\n            if (sbp.cursor < I_pV)\n              return false;\n            v_2 = sbp.limit_backward;\n            sbp.limit_backward = I_pV;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_6, 38);\n            if (!among_var) {\n              sbp.limit_backward = v_2;\n              return false;\n            }\n            sbp.bra = sbp.cursor;\n            switch (among_var) {\n              case 1:\n                if (!r_R2()) {\n                  sbp.limit_backward = v_2;\n                  return false;\n                }\n                sbp.slice_del();\n                break;\n              case 2:\n                sbp.slice_del();\n                break;\n              case 3:\n                sbp.slice_del();\n                v_3 = sbp.limit - sbp.cursor;\n                sbp.ket = sbp.cursor;\n                if (sbp.eq_s_b(1, \"e\")) {\n                  sbp.bra = sbp.cursor;\n                  sbp.slice_del();\n                } else\n                  sbp.cursor = sbp.limit - v_3;\n                break;\n            }\n            sbp.limit_backward = v_2;\n            return true;\n          }\n\n          function r_residual_suffix() {\n            var among_var, v_1 = sbp.limit - sbp.cursor,\n              v_2, v_4, v_5;\n            sbp.ket = sbp.cursor;\n            if (sbp.eq_s_b(1, \"s\")) {\n              sbp.bra = sbp.cursor;\n              v_2 = sbp.limit - sbp.cursor;\n              if (sbp.out_grouping_b(g_keep_with_s, 97, 232)) {\n                sbp.cursor = sbp.limit - v_2;\n                sbp.slice_del();\n              } else\n                sbp.cursor = sbp.limit - v_1;\n            } else\n              sbp.cursor = sbp.limit - v_1;\n            if (sbp.cursor >= I_pV) {\n              v_4 = sbp.limit_backward;\n              sbp.limit_backward = I_pV;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_7, 7);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    if (r_R2()) {\n                      v_5 = sbp.limit - sbp.cursor;\n                      if (!sbp.eq_s_b(1, \"s\")) {\n                        sbp.cursor = sbp.limit - v_5;\n                        if (!sbp.eq_s_b(1, \"t\"))\n                          break;\n                      }\n                      sbp.slice_del();\n                    }\n                    break;\n                  case 2:\n                    sbp.slice_from(\"i\");\n                    break;\n                  case 3:\n                    sbp.slice_del();\n                    break;\n                  case 4:\n                    if (sbp.eq_s_b(2, \"gu\"))\n                      sbp.slice_del();\n                    break;\n                }\n              }\n              sbp.limit_backward = v_4;\n            }\n          }\n\n          function r_un_double() {\n            var v_1 = sbp.limit - sbp.cursor;\n            if (sbp.find_among_b(a_8, 5)) {\n              sbp.cursor = sbp.limit - v_1;\n              sbp.ket = sbp.cursor;\n              if (sbp.cursor > sbp.limit_backward) {\n                sbp.cursor--;\n                sbp.bra = sbp.cursor;\n                sbp.slice_del();\n              }\n            }\n          }\n\n          function r_un_accent() {\n            var v_1, v_2 = 1;\n            while (sbp.out_grouping_b(g_v, 97, 251))\n              v_2--;\n            if (v_2 <= 0) {\n              sbp.ket = sbp.cursor;\n              v_1 = sbp.limit - sbp.cursor;\n              if (!sbp.eq_s_b(1, \"\\u00E9\")) {\n                sbp.cursor = sbp.limit - v_1;\n                if (!sbp.eq_s_b(1, \"\\u00E8\"))\n                  return;\n              }\n              sbp.bra = sbp.cursor;\n              sbp.slice_from(\"e\");\n            }\n          }\n\n          function habr5() {\n            if (!r_standard_suffix()) {\n              sbp.cursor = sbp.limit;\n              if (!r_i_verb_suffix()) {\n                sbp.cursor = sbp.limit;\n                if (!r_verb_suffix()) {\n                  sbp.cursor = sbp.limit;\n                  r_residual_suffix();\n                  return;\n                }\n              }\n            }\n            sbp.cursor = sbp.limit;\n            sbp.ket = sbp.cursor;\n            if (sbp.eq_s_b(1, \"Y\")) {\n              sbp.bra = sbp.cursor;\n              sbp.slice_from(\"i\");\n            } else {\n              sbp.cursor = sbp.limit;\n              if (sbp.eq_s_b(1, \"\\u00E7\")) {\n                sbp.bra = sbp.cursor;\n                sbp.slice_from(\"c\");\n              }\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_prelude();\n            sbp.cursor = v_1;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            habr5();\n            sbp.cursor = sbp.limit;\n            r_un_double();\n            sbp.cursor = sbp.limit;\n            r_un_accent();\n            sbp.cursor = sbp.limit_backward;\n            r_postlude();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.fr.stemmer, 'stemmer-fr');\n\n    lunr.fr.stopWordFilter = lunr.generateStopWordFilter('ai aie aient aies ait as au aura aurai auraient aurais aurait auras aurez auriez aurions aurons auront aux avaient avais avait avec avez aviez avions avons ayant ayez ayons c ce ceci celà ces cet cette d dans de des du elle en es est et eu eue eues eurent eus eusse eussent eusses eussiez eussions eut eux eûmes eût eûtes furent fus fusse fussent fusses fussiez fussions fut fûmes fût fûtes ici il ils j je l la le les leur leurs lui m ma mais me mes moi mon même n ne nos notre nous on ont ou par pas pour qu que quel quelle quelles quels qui s sa sans se sera serai seraient serais serait seras serez seriez serions serons seront ses soi soient sois soit sommes son sont soyez soyons suis sur t ta te tes toi ton tu un une vos votre vous y à étaient étais était étant étiez étions été étée étées étés êtes'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.fr.stopWordFilter, 'stopWordFilter-fr');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAAyB,CAChC,IAAIC,EAAM,CAAC,IAAIH,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACxD,IAAIA,EAAM,MAAO,GAAI,EAAE,CACzB,EACAI,EAAM,CAAC,IAAIJ,EAAM,GAAI,GAAI,CAAC,EACxB,IAAIA,EAAM,IAAK,EAAG,CAAC,EAAG,IAAIA,EAAM,IAAK,EAAG,CAAC,EAAG,IAAIA,EAAM,IAAK,EAAG,CAAC,CACjE,EACAK,EAAM,CACJ,IAAIL,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EACzD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,CAChD,EACAM,EAAM,CACJ,IAAIN,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,KAAM,GAAI,CAAC,CACvB,EACAO,EAAM,CAAC,IAAIP,EAAM,OAAQ,GAAI,CAAC,EAC5B,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACnD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAClD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAW,GAAI,CAAC,EACrD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACpD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,EAAE,EACpD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EAClD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACrD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,EAAE,EACrD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,EAAE,EACzD,IAAIA,EAAM,UAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EACvD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,EAAE,EACvD,IAAIA,EAAM,SAAU,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAU,GAAI,EAAE,EACvD,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,CACtD,EACAQ,EAAM,CACJ,IAAIR,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAW,GAAI,CAAC,EACpD,IAAIA,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAC7C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EACrD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,CAAC,EACtD,IAAIA,EAAM,UAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EACpD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACpD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAW,GAAI,CAAC,EACtD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACpD,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAClD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACpD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAW,GAAI,CAAC,EACtD,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACtD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACpD,IAAIA,EAAM,QAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACrD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,CAC1B,EACAS,EAAM,CAAC,IAAIT,EAAM,IAAK,GAAI,CAAC,EACzB,IAAIA,EAAM,MAAO,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAW,GAAI,CAAC,EACpD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAC9C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC7C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EACrD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,UAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EAC1D,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACnD,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACrD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAW,GAAI,CAAC,EACpD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAc,GAAI,CAAC,EAC1D,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACpD,IAAIA,EAAM,QAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAClD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACnD,IAAIA,EAAM,OAAU,GAAI,CAAC,CAC3B,EACAU,EAAM,CAAC,IAAIV,EAAM,IAAK,GAAI,CAAC,EACzB,IAAIA,EAAM,UAAa,EAAG,CAAC,EAAG,IAAIA,EAAM,UAAa,EAAG,CAAC,EACzD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,GAAI,CAAC,CACpD,EACAW,EAAM,CACJ,IAAIX,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAClD,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACjD,IAAIA,EAAM,MAAO,GAAI,EAAE,CACzB,EACAY,EAAM,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EACnC,EAAG,EAAG,EAAG,EAAG,EAAG,IAAK,IAAK,IAAK,EAAG,CACnC,EACAC,EAAgB,CAAC,EAAG,GAAI,GAAI,EAC1B,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GACtC,EACAC,EAAMC,EAAMC,EAAMC,EAAM,IAAIhB,EAC9B,KAAK,WAAa,SAASiB,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,EAAMC,EAAIC,EAAIC,EAAK,CAC1B,OAAIL,EAAI,KAAK,EAAGG,CAAE,IAChBH,EAAI,IAAMA,EAAI,OACVA,EAAI,YAAYL,EAAK,GAAI,GAAG,IAC9BK,EAAI,WAAWI,CAAE,EACjBJ,EAAI,OAASK,EACN,IAGJ,EACT,CAEA,SAASC,EAAMH,EAAIC,EAAIC,EAAK,CAC1B,OAAIL,EAAI,KAAK,EAAGG,CAAE,GAChBH,EAAI,IAAMA,EAAI,OACdA,EAAI,WAAWI,CAAE,EACjBJ,EAAI,OAASK,EACN,IAEF,EACT,CAEA,SAASE,GAAY,CAEnB,QADIF,EAAKG,IAGP,GADAH,EAAML,EAAI,OACN,EAAAA,EAAI,YAAYL,EAAK,GAAI,GAAG,IAC9BK,EAAI,IAAMA,EAAI,OACdQ,EAAMR,EAAI,OACNE,EAAM,IAAK,IAAKG,CAAG,IAEvBL,EAAI,OAASQ,EACTN,EAAM,IAAK,IAAKG,CAAG,KAEvBL,EAAI,OAASQ,EACTF,EAAM,IAAK,IAAKD,CAAG,OAGzBL,EAAI,OAASK,EACbL,EAAI,IAAMK,EACN,CAACH,EAAM,IAAK,IAAKG,CAAG,GAAG,CAEzB,GADAL,EAAI,OAASK,EACTL,EAAI,KAAK,EAAG,GAAG,IACjBA,EAAI,IAAMA,EAAI,OACVM,EAAM,IAAK,IAAKD,CAAG,GACrB,SAGJ,GADAL,EAAI,OAASK,EACTA,GAAOL,EAAI,MACb,OACFA,EAAI,QACN,CAEJ,CAEA,SAASS,GAAQ,CACf,KAAO,CAACT,EAAI,YAAYL,EAAK,GAAI,GAAG,GAAG,CACrC,GAAIK,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,KAAO,CAACA,EAAI,aAAaL,EAAK,GAAI,GAAG,GAAG,CACtC,GAAIK,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASU,GAAiB,CACxB,IAAIL,EAAML,EAAI,OAId,GAHAD,EAAOC,EAAI,MACXF,EAAOC,EACPF,EAAOE,EACHC,EAAI,YAAYL,EAAK,GAAI,GAAG,GAAKK,EAAI,YAAYL,EAAK,GAAI,GAAG,GAC/DK,EAAI,OAASA,EAAI,MACjBA,EAAI,iBAEJA,EAAI,OAASK,EACT,CAACL,EAAI,WAAWd,EAAK,CAAC,EAAG,CAC3Bc,EAAI,OAASK,EACb,EAAG,CACD,GAAIL,EAAI,QAAUA,EAAI,MAAO,CAC3BA,EAAI,OAASD,EACb,KACF,CACAC,EAAI,QACN,OAAS,CAACA,EAAI,YAAYL,EAAK,GAAI,GAAG,EACxC,CAEFI,EAAOC,EAAI,OACXA,EAAI,OAASK,EACRI,EAAM,IACTX,EAAOE,EAAI,OACNS,EAAM,IACTZ,EAAOG,EAAI,QAEjB,CAEA,SAASW,GAAa,CAEpB,QADIC,EAAWP,EAEbA,EAAML,EAAI,OACVA,EAAI,IAAMK,EACVO,EAAYZ,EAAI,WAAWb,EAAK,CAAC,EAC7B,EAACyB,GAGL,OADAZ,EAAI,IAAMA,EAAI,OACNY,EAAW,CACjB,IAAK,GACHZ,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACH,GAAIA,EAAI,QAAUA,EAAI,MACpB,OACFA,EAAI,SACJ,KACJ,CAEJ,CAEA,SAASa,GAAO,CACd,OAAOd,GAAQC,EAAI,MACrB,CAEA,SAASc,GAAO,CACd,OAAOhB,GAAQE,EAAI,MACrB,CAEA,SAASe,GAAO,CACd,OAAOlB,GAAQG,EAAI,MACrB,CAEA,SAASgB,GAAoB,CAC3B,IAAIJ,EAAWP,EAGf,GAFAL,EAAI,IAAMA,EAAI,OACdY,EAAYZ,EAAI,aAAaV,EAAK,EAAE,EAChCsB,EAAW,CAEb,OADAZ,EAAI,IAAMA,EAAI,OACNY,EAAW,CACjB,IAAK,GACH,GAAI,CAACG,EAAK,EACR,MAAO,GACTf,EAAI,UAAU,EACd,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACTe,EAAK,EAGRf,EAAI,UAAU,EAFdA,EAAI,WAAW,KAAK,GAIxB,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,WAAW,KAAK,EACpB,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GACTf,EAAI,WAAW,KAAK,EACpB,MACF,IAAK,GACH,GAAI,CAACa,EAAK,EACR,MAAO,GAIT,GAHAb,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACdY,EAAYZ,EAAI,aAAaZ,EAAK,CAAC,EAC/BwB,EAEF,OADAZ,EAAI,IAAMA,EAAI,OACNY,EAAW,CACjB,IAAK,GACCG,EAAK,IACPf,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVe,EAAK,GACPf,EAAI,UAAU,IAGpB,MACF,IAAK,GACCe,EAAK,EACPf,EAAI,UAAU,EACPc,EAAK,GACZd,EAAI,WAAW,KAAK,EACtB,MACF,IAAK,GACCe,EAAK,GACPf,EAAI,UAAU,EAChB,MACF,IAAK,GACCa,EAAK,GACPb,EAAI,WAAW,GAAG,EACpB,KACJ,CAEF,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GAIT,GAHAf,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACdY,EAAYZ,EAAI,aAAaX,EAAK,CAAC,EAC/BuB,EAEF,OADAZ,EAAI,IAAMA,EAAI,OACNY,EAAW,CACjB,IAAK,GACCG,EAAK,EACPf,EAAI,UAAU,EAEdA,EAAI,WAAW,KAAK,EACtB,MACF,IAAK,GACCe,EAAK,EACPf,EAAI,UAAU,EAEdA,EAAI,WAAW,KAAK,EACtB,MACF,IAAK,GACCe,EAAK,GACPf,EAAI,UAAU,EAChB,KACJ,CAEF,MACF,IAAK,GACH,GAAI,CAACe,EAAK,EACR,MAAO,GAGT,GAFAf,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVe,EAAK,IACPf,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IAAG,CACvBA,EAAI,IAAMA,EAAI,OACVe,EAAK,EACPf,EAAI,UAAU,EAEdA,EAAI,WAAW,KAAK,EACtB,KACF,CAGJ,MACF,IAAK,GACHA,EAAI,WAAW,KAAK,EACpB,MACF,IAAK,IACH,GAAI,CAACc,EAAK,EACR,MAAO,GACTd,EAAI,WAAW,IAAI,EACnB,MACF,IAAK,IACH,GAAIe,EAAK,EACPf,EAAI,UAAU,UACNc,EAAK,EAGbd,EAAI,WAAW,KAAK,MAFpB,OAAO,GAGT,MACF,IAAK,IACH,GAAI,CAACc,EAAK,GAAK,CAACd,EAAI,eAAeL,EAAK,GAAI,GAAG,EAC7C,MAAO,GACTK,EAAI,UAAU,EACd,MACF,IAAK,IACH,OAAIa,EAAK,GACPb,EAAI,WAAW,KAAK,EACf,GACT,IAAK,IACH,OAAIa,EAAK,GACPb,EAAI,WAAW,KAAK,EACf,GACT,IAAK,IACH,OAAAK,EAAML,EAAI,MAAQA,EAAI,OAClBA,EAAI,cAAcL,EAAK,GAAI,GAAG,GAAKkB,EAAK,IAC1Cb,EAAI,OAASA,EAAI,MAAQK,EACzBL,EAAI,UAAU,GAET,EACX,CACA,MAAO,EACT,CACA,MAAO,EACT,CAEA,SAASiB,GAAkB,CACzB,IAAIL,EAAWP,EACf,GAAIL,EAAI,OAASD,EACf,MAAO,GAKT,GAJAM,EAAML,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdY,EAAYZ,EAAI,aAAaT,EAAK,EAAE,EAChC,CAACqB,EACH,OAAAZ,EAAI,eAAiBK,EACd,GAGT,GADAL,EAAI,IAAMA,EAAI,OACVY,GAAa,EAAG,CAClB,GAAI,CAACZ,EAAI,eAAeL,EAAK,GAAI,GAAG,EAClC,OAAAK,EAAI,eAAiBK,EACd,GAETL,EAAI,UAAU,CAChB,CACA,OAAAA,EAAI,eAAiBK,EACd,EACT,CAEA,SAASa,GAAgB,CACvB,IAAIN,EAAWJ,EAAKW,EACpB,GAAInB,EAAI,OAASD,EACf,MAAO,GAKT,GAJAS,EAAMR,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdY,EAAYZ,EAAI,aAAaR,EAAK,EAAE,EAChC,CAACoB,EACH,OAAAZ,EAAI,eAAiBQ,EACd,GAGT,OADAR,EAAI,IAAMA,EAAI,OACNY,EAAW,CACjB,IAAK,GACH,GAAI,CAACG,EAAK,EACR,OAAAf,EAAI,eAAiBQ,EACd,GAETR,EAAI,UAAU,EACd,MACF,IAAK,GACHA,EAAI,UAAU,EACd,MACF,IAAK,GACHA,EAAI,UAAU,EACdmB,EAAMnB,EAAI,MAAQA,EAAI,OACtBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,GACnBA,EAAI,IAAMA,EAAI,OACdA,EAAI,UAAU,GAEdA,EAAI,OAASA,EAAI,MAAQmB,EAC3B,KACJ,CACA,OAAAnB,EAAI,eAAiBQ,EACd,EACT,CAEA,SAASY,GAAoB,CAC3B,IAAIR,EAAWP,EAAML,EAAI,MAAQA,EAAI,OACnCQ,EAAKa,EAAKC,EAYZ,GAXAtB,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,GACnBA,EAAI,IAAMA,EAAI,OACdQ,EAAMR,EAAI,MAAQA,EAAI,OAClBA,EAAI,eAAeJ,EAAe,GAAI,GAAG,GAC3CI,EAAI,OAASA,EAAI,MAAQQ,EACzBR,EAAI,UAAU,GAEdA,EAAI,OAASA,EAAI,MAAQK,GAE3BL,EAAI,OAASA,EAAI,MAAQK,EACvBL,EAAI,QAAUD,EAAM,CAKtB,GAJAsB,EAAMrB,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdY,EAAYZ,EAAI,aAAaP,EAAK,CAAC,EAC/BmB,EAEF,OADAZ,EAAI,IAAMA,EAAI,OACNY,EAAW,CACjB,IAAK,GACH,GAAIG,EAAK,EAAG,CAEV,GADAO,EAAMtB,EAAI,MAAQA,EAAI,OAClB,CAACA,EAAI,OAAO,EAAG,GAAG,IACpBA,EAAI,OAASA,EAAI,MAAQsB,EACrB,CAACtB,EAAI,OAAO,EAAG,GAAG,GACpB,MAEJA,EAAI,UAAU,CAChB,CACA,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,UAAU,EACd,MACF,IAAK,GACCA,EAAI,OAAO,EAAG,IAAI,GACpBA,EAAI,UAAU,EAChB,KACJ,CAEFA,EAAI,eAAiBqB,CACvB,CACF,CAEA,SAASE,GAAc,CACrB,IAAIlB,EAAML,EAAI,MAAQA,EAAI,OACtBA,EAAI,aAAaN,EAAK,CAAC,IACzBM,EAAI,OAASA,EAAI,MAAQK,EACzBL,EAAI,IAAMA,EAAI,OACVA,EAAI,OAASA,EAAI,iBACnBA,EAAI,SACJA,EAAI,IAAMA,EAAI,OACdA,EAAI,UAAU,GAGpB,CAEA,SAASwB,GAAc,CAErB,QADInB,EAAKG,EAAM,EACRR,EAAI,eAAeL,EAAK,GAAI,GAAG,GACpCa,IACF,GAAIA,GAAO,EAAG,CAGZ,GAFAR,EAAI,IAAMA,EAAI,OACdK,EAAML,EAAI,MAAQA,EAAI,OAClB,CAACA,EAAI,OAAO,EAAG,MAAQ,IACzBA,EAAI,OAASA,EAAI,MAAQK,EACrB,CAACL,EAAI,OAAO,EAAG,MAAQ,GACzB,OAEJA,EAAI,IAAMA,EAAI,OACdA,EAAI,WAAW,GAAG,CACpB,CACF,CAEA,SAASyB,GAAQ,CACf,GAAI,CAACT,EAAkB,IACrBhB,EAAI,OAASA,EAAI,MACb,CAACiB,EAAgB,IACnBjB,EAAI,OAASA,EAAI,MACb,CAACkB,EAAc,IAAG,CACpBlB,EAAI,OAASA,EAAI,MACjBoB,EAAkB,EAClB,MACF,CAGJpB,EAAI,OAASA,EAAI,MACjBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,GACnBA,EAAI,IAAMA,EAAI,OACdA,EAAI,WAAW,GAAG,IAElBA,EAAI,OAASA,EAAI,MACbA,EAAI,OAAO,EAAG,MAAQ,IACxBA,EAAI,IAAMA,EAAI,OACdA,EAAI,WAAW,GAAG,GAGxB,CACA,KAAK,KAAO,UAAW,CACrB,IAAIK,EAAML,EAAI,OACd,OAAAO,EAAU,EACVP,EAAI,OAASK,EACbK,EAAe,EACfV,EAAI,eAAiBK,EACrBL,EAAI,OAASA,EAAI,MACjByB,EAAM,EACNzB,EAAI,OAASA,EAAI,MACjBuB,EAAY,EACZvB,EAAI,OAASA,EAAI,MACjBwB,EAAY,EACZxB,EAAI,OAASA,EAAI,eACjBW,EAAW,EACJ,EACT,CACF,EAGF,OAAO,SAASe,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASzB,EAAM,CACjC,OAAAhB,EAAG,WAAWgB,CAAI,EAClBhB,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWyC,CAAK,EACnBzC,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,k3BAA0yB,MAAM,GAAG,CAAC,EAEz2BA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_fr", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6", "a_7", "a_8", "g_v", "g_keep_with_s", "I_p2", "I_p1", "I_pV", "sbp", "word", "habr1", "c1", "c2", "v_1", "habr2", "r_prelude", "v_2", "habr3", "r_mark_regions", "r_postlude", "among_var", "r_RV", "r_R1", "r_R2", "r_standard_suffix", "r_i_verb_suffix", "r_verb_suffix", "v_3", "r_residual_suffix", "v_4", "v_5", "r_un_double", "r_un_accent", "habr5", "token"]}