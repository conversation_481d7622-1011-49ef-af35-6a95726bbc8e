import{a as u}from"./chunk-OSRY5VT3.min.js";var f=u((n,o)=>{(function(e,t){typeof define=="function"&&define.amd?define(t):typeof n=="object"?o.exports=t():t()(e.lunr)})(n,function(){return function(e){if(typeof e>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof e.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");e.hi=function(){this.pipeline.reset(),this.pipeline.add(e.hi.trimmer,e.hi.stopWordFilter,e.hi.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(e.hi.stemmer))},e.hi.wordCharacters="\u0900-\u0903\u0904-\u090F\u0910-\u091F\u0920-\u092F\u0930-\u093F\u0940-\u094F\u0950-\u095F\u0960-\u096F\u0970-\u097Fa-zA-Z\uFF41-\uFF5A\uFF21-\uFF3A0-9\uFF10-\uFF19",e.hi.trimmer=e.trimmerSupport.generateTrimmer(e.hi.wordCharacters),e.Pipeline.registerFunction(e.hi.trimmer,"trimmer-hi"),e.hi.stopWordFilter=e.generateStopWordFilter("\u0905\u0924 \u0905\u092A\u0928\u093E \u0905\u092A\u0928\u0940 \u0905\u092A\u0928\u0947 \u0905\u092D\u0940 \u0905\u0902\u0926\u0930 \u0906\u0926\u093F \u0906\u092A \u0907\u0924\u094D\u092F\u093E\u0926\u093F \u0907\u0928 \u0907\u0928\u0915\u093E \u0907\u0928\u094D\u0939\u0940\u0902 \u0907\u0928\u094D\u0939\u0947\u0902 \u0907\u0928\u094D\u0939\u094B\u0902 \u0907\u0938 \u0907\u0938\u0915\u093E \u0907\u0938\u0915\u0940 \u0907\u0938\u0915\u0947 \u0907\u0938\u092E\u0947\u0902 \u0907\u0938\u0940 \u0907\u0938\u0947 \u0909\u0928 \u0909\u0928\u0915\u093E \u0909\u0928\u0915\u0940 \u0909\u0928\u0915\u0947 \u0909\u0928\u0915\u094B \u0909\u0928\u094D\u0939\u0940\u0902 \u0909\u0928\u094D\u0939\u0947\u0902 \u0909\u0928\u094D\u0939\u094B\u0902 \u0909\u0938 \u0909\u0938\u0915\u0947 \u0909\u0938\u0940 \u0909\u0938\u0947 \u090F\u0915 \u090F\u0935\u0902 \u090F\u0938 \u0910\u0938\u0947 \u0914\u0930 \u0915\u0908 \u0915\u0930 \u0915\u0930\u0924\u093E \u0915\u0930\u0924\u0947 \u0915\u0930\u0928\u093E \u0915\u0930\u0928\u0947 \u0915\u0930\u0947\u0902 \u0915\u0939\u0924\u0947 \u0915\u0939\u093E \u0915\u093E \u0915\u093E\u095E\u0940 \u0915\u093F \u0915\u093F\u0924\u0928\u093E \u0915\u093F\u0928\u094D\u0939\u0947\u0902 \u0915\u093F\u0928\u094D\u0939\u094B\u0902 \u0915\u093F\u092F\u093E \u0915\u093F\u0930 \u0915\u093F\u0938 \u0915\u093F\u0938\u0940 \u0915\u093F\u0938\u0947 \u0915\u0940 \u0915\u0941\u091B \u0915\u0941\u0932 \u0915\u0947 \u0915\u094B \u0915\u094B\u0908 \u0915\u094C\u0928 \u0915\u094C\u0928\u0938\u093E \u0917\u092F\u093E \u0918\u0930 \u091C\u092C \u091C\u0939\u093E\u0901 \u091C\u093E \u091C\u093F\u0924\u0928\u093E \u091C\u093F\u0928 \u091C\u093F\u0928\u094D\u0939\u0947\u0902 \u091C\u093F\u0928\u094D\u0939\u094B\u0902 \u091C\u093F\u0938 \u091C\u093F\u0938\u0947 \u091C\u0940\u0927\u0930 \u091C\u0948\u0938\u093E \u091C\u0948\u0938\u0947 \u091C\u094B \u0924\u0915 \u0924\u092C \u0924\u0930\u0939 \u0924\u093F\u0928 \u0924\u093F\u0928\u094D\u0939\u0947\u0902 \u0924\u093F\u0928\u094D\u0939\u094B\u0902 \u0924\u093F\u0938 \u0924\u093F\u0938\u0947 \u0924\u094B \u0925\u093E \u0925\u0940 \u0925\u0947 \u0926\u092C\u093E\u0930\u093E \u0926\u093F\u092F\u093E \u0926\u0941\u0938\u0930\u093E \u0926\u0942\u0938\u0930\u0947 \u0926\u094B \u0926\u094D\u0935\u093E\u0930\u093E \u0928 \u0928\u0915\u0947 \u0928\u0939\u0940\u0902 \u0928\u093E \u0928\u093F\u0939\u093E\u092F\u0924 \u0928\u0940\u091A\u0947 \u0928\u0947 \u092A\u0930 \u092A\u0939\u0932\u0947 \u092A\u0942\u0930\u093E \u092A\u0947 \u092B\u093F\u0930 \u092C\u0928\u0940 \u092C\u0939\u0940 \u092C\u0939\u0941\u0924 \u092C\u093E\u0926 \u092C\u093E\u0932\u093E \u092C\u093F\u0932\u0915\u0941\u0932 \u092D\u0940 \u092D\u0940\u0924\u0930 \u092E\u0917\u0930 \u092E\u093E\u0928\u094B \u092E\u0947 \u092E\u0947\u0902 \u092F\u0926\u093F \u092F\u0939 \u092F\u0939\u093E\u0901 \u092F\u0939\u0940 \u092F\u093E \u092F\u093F\u0939 \u092F\u0947 \u0930\u0916\u0947\u0902 \u0930\u0939\u093E \u0930\u0939\u0947 \u0931\u094D\u0935\u093E\u0938\u093E \u0932\u093F\u090F \u0932\u093F\u092F\u0947 \u0932\u0947\u0915\u093F\u0928 \u0935 \u0935\u095A\u0948\u0930\u0939 \u0935\u0930\u094D\u0917 \u0935\u0939 \u0935\u0939\u093E\u0901 \u0935\u0939\u0940\u0902 \u0935\u093E\u0932\u0947 \u0935\u0941\u0939 \u0935\u0947 \u0935\u094B \u0938\u0915\u0924\u093E \u0938\u0915\u0924\u0947 \u0938\u092C\u0938\u0947 \u0938\u092D\u0940 \u0938\u093E\u0925 \u0938\u093E\u092C\u0941\u0924 \u0938\u093E\u092D \u0938\u093E\u0930\u093E \u0938\u0947 \u0938\u094B \u0938\u0902\u0917 \u0939\u0940 \u0939\u0941\u0906 \u0939\u0941\u0908 \u0939\u0941\u090F \u0939\u0948 \u0939\u0948\u0902 \u0939\u094B \u0939\u094B\u0924\u093E \u0939\u094B\u0924\u0940 \u0939\u094B\u0924\u0947 \u0939\u094B\u0928\u093E \u0939\u094B\u0928\u0947".split(" ")),e.hi.stemmer=function(){return function(i){return typeof i.update=="function"?i.update(function(r){return r}):i}}();var t=e.wordcut;t.init(),e.hi.tokenizer=function(i){if(!arguments.length||i==null||i==null)return[];if(Array.isArray(i))return i.map(function(s){return isLunr2?new e.Token(s.toLowerCase()):s.toLowerCase()});var r=i.toString().toLowerCase().replace(/^\s+/,"");return t.cut(r).split("|")},e.Pipeline.registerFunction(e.hi.stemmer,"stemmer-hi"),e.Pipeline.registerFunction(e.hi.stopWordFilter,"stopWordFilter-hi")}})});export default f();
/*! Bundled license information:

lunr-languages/lunr.hi.js:
  (*!
   * Lunr languages, `Hindi` language
   * https://github.com/MiKr13/lunr-languages
   *
   * Copyright 2020, Mihir Kumar
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.hi-PWWMAGLU.min.js.map
