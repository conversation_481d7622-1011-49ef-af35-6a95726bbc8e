<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaImageManager | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaImageManager | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaImageManager.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaImageManager%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaImageManager">



  <h1 id="DrawnUi_Draw_SkiaImageManager" data-uid="DrawnUi.Draw.SkiaImageManager" class="text-break">
Class SkiaImageManager  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SkiaImageManager : IDisposable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">SkiaImageManager</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_SkiaImageManager__ctor_" data-uid="DrawnUi.Draw.SkiaImageManager.#ctor*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager__ctor" data-uid="DrawnUi.Draw.SkiaImageManager.#ctor">
  SkiaImageManager()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L226"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaImageManager()</code></pre>
  </div>













  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_SkiaImageManager_CacheLongevitySecs" data-uid="DrawnUi.Draw.SkiaImageManager.CacheLongevitySecs">
  CacheLongevitySecs
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L185"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Caching provider setting</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int CacheLongevitySecs</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaImageManager_LoadLocalAsync" data-uid="DrawnUi.Draw.SkiaImageManager.LoadLocalAsync">
  LoadLocalAsync
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L175"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Normally we load local images in a synchronous manner, and remote in async one. Set this to true if you want to load load images async too.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool LoadLocalAsync</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaImageManager_LogEnabled" data-uid="DrawnUi.Draw.SkiaImageManager.LogEnabled">
  LogEnabled
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L196"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool LogEnabled</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaImageManager_NativeFilePrefix" data-uid="DrawnUi.Draw.SkiaImageManager.NativeFilePrefix">
  NativeFilePrefix
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L190"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Convention for local files saved in native platform. Shared resources from Resources/Raw/ do not need this prefix.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string NativeFilePrefix</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaImageManager_ReuseBitmaps" data-uid="DrawnUi.Draw.SkiaImageManager.ReuseBitmaps">
  ReuseBitmaps
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L180"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>If set to true will not return clones for same sources, but will just return the existing cached SKBitmap reference. Useful if you have a lot on images reusing same sources, but you have to be carefull not to dispose the shared image. SkiaImage is aware of this setting and will keep a cached SKBitmap from being disposed.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool ReuseBitmaps</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_SkiaImageManager_Instance_" data-uid="DrawnUi.Draw.SkiaImageManager.Instance*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_Instance" data-uid="DrawnUi.Draw.SkiaImageManager.Instance">
  Instance
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L215"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaImageManager Instance { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaImageManager.html">SkiaImageManager</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaImageManager_IsDisposed_" data-uid="DrawnUi.Draw.SkiaImageManager.IsDisposed*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_IsDisposed" data-uid="DrawnUi.Draw.SkiaImageManager.IsDisposed">
  IsDisposed
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L594"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsDisposed { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaImageManager_IsLoadingLocked_" data-uid="DrawnUi.Draw.SkiaImageManager.IsLoadingLocked*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_IsLoadingLocked" data-uid="DrawnUi.Draw.SkiaImageManager.IsLoadingLocked">
  IsLoadingLocked
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L250"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsLoadingLocked { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaImageManager_IsOffline_" data-uid="DrawnUi.Draw.SkiaImageManager.IsOffline*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_IsOffline" data-uid="DrawnUi.Draw.SkiaImageManager.IsOffline">
  IsOffline
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L785"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsOffline { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_SkiaImageManager_AddToCache_" data-uid="DrawnUi.Draw.SkiaImageManager.AddToCache*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_AddToCache_System_String_SkiaSharp_SKBitmap_System_Int32_" data-uid="DrawnUi.Draw.SkiaImageManager.AddToCache(System.String,SkiaSharp.SKBitmap,System.Int32)">
  AddToCache(string, SKBitmap, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L683"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Returns false if key already exists</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool AddToCache(string uri, SKBitmap bitmap, int cacheLongevitySecs)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>uri</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>bitmap</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a></dt>
    <dd></dd>
    <dt><code>cacheLongevitySecs</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_CancelAll_" data-uid="DrawnUi.Draw.SkiaImageManager.CancelAll*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_CancelAll" data-uid="DrawnUi.Draw.SkiaImageManager.CancelAll">
  CancelAll()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L263"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void CancelAll()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaImageManager_Dispose_" data-uid="DrawnUi.Draw.SkiaImageManager.Dispose*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_Dispose" data-uid="DrawnUi.Draw.SkiaImageManager.Dispose">
  Dispose()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L776"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Dispose()</code></pre>
  </div>













  <a id="DrawnUi_Draw_SkiaImageManager_GetFromCache_" data-uid="DrawnUi.Draw.SkiaImageManager.GetFromCache*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_GetFromCache_System_String_" data-uid="DrawnUi.Draw.SkiaImageManager.GetFromCache(System.String)">
  GetFromCache(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L697"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Return bitmap from cache if existing, respects the <code>ReuseBitmaps</code> flag.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKBitmap GetFromCache(string url)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>url</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_GetFromCacheInternal_" data-uid="DrawnUi.Draw.SkiaImageManager.GetFromCacheInternal*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_GetFromCacheInternal_System_String_" data-uid="DrawnUi.Draw.SkiaImageManager.GetFromCacheInternal(System.String)">
  GetFromCacheInternal(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L710"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Used my manager for cache organization. You should use <code>GetFromCache</code> for custom controls instead.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKBitmap GetFromCacheInternal(string url)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>url</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_GetUriFromImageSource_" data-uid="DrawnUi.Draw.SkiaImageManager.GetUriFromImageSource*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_GetUriFromImageSource_Microsoft_Maui_Controls_ImageSource_" data-uid="DrawnUi.Draw.SkiaImageManager.GetUriFromImageSource(Microsoft.Maui.Controls.ImageSource)">
  GetUriFromImageSource(ImageSource)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L755"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetUriFromImageSource(ImageSource source)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>source</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.imagesource">ImageSource</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_LoadFromFile_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadFromFile*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_LoadFromFile_System_String_System_Threading_CancellationToken_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadFromFile(System.String,System.Threading.CancellationToken)">
  LoadFromFile(string, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L799"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Task&lt;SKBitmap&gt; LoadFromFile(string filename, CancellationToken cancel)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>filename</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>cancel</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_LoadImageAsync_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadImageAsync*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_LoadImageAsync_Microsoft_Maui_Controls_ImageSource_System_Threading_CancellationToken_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadImageAsync(Microsoft.Maui.Controls.ImageSource,System.Threading.CancellationToken)">
  LoadImageAsync(ImageSource, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L327"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Direct load, without any queue or manager cache, for internal use. Please use LoadImageManagedAsync instead.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task&lt;SKBitmap&gt; LoadImageAsync(ImageSource source, CancellationToken token)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>source</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.imagesource">ImageSource</a></dt>
    <dd></dd>
    <dt><code>token</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_LoadImageFromInternetAsync_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadImageFromInternetAsync*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_LoadImageFromInternetAsync_Microsoft_Maui_Controls_UriImageSource_System_Threading_CancellationToken_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadImageFromInternetAsync(Microsoft.Maui.Controls.UriImageSource,System.Threading.CancellationToken)">
  LoadImageFromInternetAsync(UriImageSource, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L864"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Task&lt;SKBitmap&gt; LoadImageFromInternetAsync(UriImageSource uriSource, CancellationToken cancel)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>uriSource</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.uriimagesource">UriImageSource</a></dt>
    <dd></dd>
    <dt><code>cancel</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_LoadImageManagedAsync_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadImageManagedAsync*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_LoadImageManagedAsync_Microsoft_Maui_Controls_ImageSource_System_Threading_CancellationTokenSource_DrawnUi_Draw_LoadPriority_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadImageManagedAsync(Microsoft.Maui.Controls.ImageSource,System.Threading.CancellationTokenSource,DrawnUi.Draw.LoadPriority)">
  LoadImageManagedAsync(ImageSource, CancellationTokenSource, LoadPriority)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L338"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Uses queue and manager cache</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task&lt;SKBitmap&gt; LoadImageManagedAsync(ImageSource source, CancellationTokenSource token, LoadPriority priority = LoadPriority.Normal)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>source</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.imagesource">ImageSource</a></dt>
    <dd></dd>
    <dt><code>token</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource">CancellationTokenSource</a></dt>
    <dd></dd>
    <dt><code>priority</code> <a class="xref" href="DrawnUi.Draw.LoadPriority.html">LoadPriority</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_LoadImageOnPlatformAsync_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadImageOnPlatformAsync*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_LoadImageOnPlatformAsync_Microsoft_Maui_Controls_ImageSource_System_Threading_CancellationToken_" data-uid="DrawnUi.Draw.SkiaImageManager.LoadImageOnPlatformAsync(Microsoft.Maui.Controls.ImageSource,System.Threading.CancellationToken)">
  LoadImageOnPlatformAsync(ImageSource, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L447"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Task&lt;SKBitmap&gt; LoadImageOnPlatformAsync(ImageSource source, CancellationToken cancel)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>source</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.imagesource">ImageSource</a></dt>
    <dd></dd>
    <dt><code>cancel</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_Preload_" data-uid="DrawnUi.Draw.SkiaImageManager.Preload*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_Preload_Microsoft_Maui_Controls_ImageSource_System_Threading_CancellationTokenSource_" data-uid="DrawnUi.Draw.SkiaImageManager.Preload(Microsoft.Maui.Controls.ImageSource,System.Threading.CancellationTokenSource)">
  Preload(ImageSource, CancellationTokenSource)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L715"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task Preload(ImageSource source, CancellationTokenSource cts)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>source</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.imagesource">ImageSource</a></dt>
    <dd></dd>
    <dt><code>cts</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource">CancellationTokenSource</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_PreloadBanners_" data-uid="DrawnUi.Draw.SkiaImageManager.PreloadBanners*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_PreloadBanners__1_System_Collections_Generic_IList___0__System_Threading_CancellationTokenSource_" data-uid="DrawnUi.Draw.SkiaImageManager.PreloadBanners``1(System.Collections.Generic.IList{``0},System.Threading.CancellationTokenSource)">
  PreloadBanners&lt;T&gt;(IList&lt;T&gt;, CancellationTokenSource)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L119"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task PreloadBanners&lt;T&gt;(IList&lt;T&gt; list, CancellationTokenSource cancel = null) where T : IHasBanner</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>list</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;T&gt;</dt>
    <dd></dd>
    <dt><code>cancel</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource">CancellationTokenSource</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Type Parameters</h4>
  <dl class="parameters">
    <dt><code>T</code></dt>
    <dd></dd>
  </dl>










  <a id="DrawnUi_Draw_SkiaImageManager_PreloadImage_" data-uid="DrawnUi.Draw.SkiaImageManager.PreloadImage*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_PreloadImage_Microsoft_Maui_Controls_ImageSource_System_Threading_CancellationTokenSource_" data-uid="DrawnUi.Draw.SkiaImageManager.PreloadImage(Microsoft.Maui.Controls.ImageSource,System.Threading.CancellationTokenSource)">
  PreloadImage(ImageSource, CancellationTokenSource)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L17"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Preloads an image from the given source.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task PreloadImage(ImageSource source, CancellationTokenSource cancel = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>source</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.imagesource">ImageSource</a></dt>
    <dd><p>The image source to preload</p>
</dd>
    <dt><code>cancel</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource">CancellationTokenSource</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_PreloadImage_" data-uid="DrawnUi.Draw.SkiaImageManager.PreloadImage*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_PreloadImage_System_String_System_Threading_CancellationTokenSource_" data-uid="DrawnUi.Draw.SkiaImageManager.PreloadImage(System.String,System.Threading.CancellationTokenSource)">
  PreloadImage(string, CancellationTokenSource)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L43"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task PreloadImage(string source, CancellationTokenSource cancel = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>source</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>cancel</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource">CancellationTokenSource</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_PreloadImages_" data-uid="DrawnUi.Draw.SkiaImageManager.PreloadImages*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_PreloadImages_System_Collections_Generic_IList_System_String__System_Threading_CancellationTokenSource_" data-uid="DrawnUi.Draw.SkiaImageManager.PreloadImages(System.Collections.Generic.IList{System.String},System.Threading.CancellationTokenSource)">
  PreloadImages(IList&lt;string&gt;, CancellationTokenSource)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L69"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task PreloadImages(IList&lt;string&gt; list, CancellationTokenSource cancel = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>list</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</dt>
    <dd></dd>
    <dt><code>cancel</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource">CancellationTokenSource</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaImageManager_TraceLog_" data-uid="DrawnUi.Draw.SkiaImageManager.TraceLog*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_TraceLog_System_String_" data-uid="DrawnUi.Draw.SkiaImageManager.TraceLog(System.String)">
  TraceLog(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L198"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TraceLog(string message)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>message</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaImageManager_UpdateInCache_" data-uid="DrawnUi.Draw.SkiaImageManager.UpdateInCache*"></a>

  <h3 id="DrawnUi_Draw_SkiaImageManager_UpdateInCache_System_String_SkiaSharp_SKBitmap_System_Int32_" data-uid="DrawnUi.Draw.SkiaImageManager.UpdateInCache(System.String,SkiaSharp.SKBitmap,System.Int32)">
  UpdateInCache(string, SKBitmap, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L671"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UpdateInCache(string uri, SKBitmap bitmap, int cacheLongevityMinutes)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>uri</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>bitmap</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a></dt>
    <dd></dd>
    <dt><code>cacheLongevityMinutes</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="events">Events
</h2>



  <h3 id="DrawnUi_Draw_SkiaImageManager_CanReload" data-uid="DrawnUi.Draw.SkiaImageManager.CanReload">
  CanReload
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L192"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler CanReload</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>








</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs/#L8" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
