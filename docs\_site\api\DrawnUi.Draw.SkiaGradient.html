<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaGradient | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaGradient | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaGradient.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaGradient%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaGradient">



  <h1 id="DrawnUi_Draw_SkiaGradient" data-uid="DrawnUi.Draw.SkiaGradient" class="text-break">
Class SkiaGradient  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SkiaGradient : BindableObject, INotifyPropertyChanged, ICloneable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></div>
      <div><span class="xref">SkiaGradient</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.icloneable">ICloneable</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty">BindableObject.BindingContextProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)">BindableObject.ClearValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.ClearValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue">BindableObject.GetValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset">BindableObject.IsSet(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding">BindableObject.RemoveBinding(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding">BindableObject.SetBinding(BindableProperty, BindingBase)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings">BindableObject.ApplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onbindingcontextchanged">BindableObject.OnBindingContextChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanged">BindableObject.OnPropertyChanged(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging">BindableObject.OnPropertyChanging(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings">BindableObject.UnapplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)">BindableObject.SetValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)">BindableObject.SetValue(BindablePropertyKey, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)">BindableObject.CoerceValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.CoerceValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher">BindableObject.Dispatcher</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext">BindableObject.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged">BindableObject.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging">BindableObject.PropertyChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged">BindableObject.BindingContextChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_SkiaGradient_BlendModeProperty" data-uid="DrawnUi.Draw.SkiaGradient.BlendModeProperty">
  BlendModeProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L77"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty BlendModeProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_ColorPositionsProperty" data-uid="DrawnUi.Draw.SkiaGradient.ColorPositionsProperty">
  ColorPositionsProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L212"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ColorPositionsProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_ColorsProperty" data-uid="DrawnUi.Draw.SkiaGradient.ColorsProperty">
  ColorsProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L150"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ColorsProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_EndXRatioProperty" data-uid="DrawnUi.Draw.SkiaGradient.EndXRatioProperty">
  EndXRatioProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L131"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty EndXRatioProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_EndYRatioProperty" data-uid="DrawnUi.Draw.SkiaGradient.EndYRatioProperty">
  EndYRatioProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L139"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty EndYRatioProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_LightProperty" data-uid="DrawnUi.Draw.SkiaGradient.LightProperty">
  LightProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L97"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty LightProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_OpacityProperty" data-uid="DrawnUi.Draw.SkiaGradient.OpacityProperty">
  OpacityProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L105"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty OpacityProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_StartXRatioProperty" data-uid="DrawnUi.Draw.SkiaGradient.StartXRatioProperty">
  StartXRatioProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L115"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty StartXRatioProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_StartYRatioProperty" data-uid="DrawnUi.Draw.SkiaGradient.StartYRatioProperty">
  StartYRatioProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L123"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty StartYRatioProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_TileModeProperty" data-uid="DrawnUi.Draw.SkiaGradient.TileModeProperty">
  TileModeProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L87"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty TileModeProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_SkiaGradient_TypeProperty" data-uid="DrawnUi.Draw.SkiaGradient.TypeProperty">
  TypeProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L68"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty TypeProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_SkiaGradient_BlendMode_" data-uid="DrawnUi.Draw.SkiaGradient.BlendMode*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_BlendMode" data-uid="DrawnUi.Draw.SkiaGradient.BlendMode">
  BlendMode
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L81"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKBlendMode BlendMode { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skblendmode">SKBlendMode</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_ColorPositions_" data-uid="DrawnUi.Draw.SkiaGradient.ColorPositions*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_ColorPositions" data-uid="DrawnUi.Draw.SkiaGradient.ColorPositions">
  ColorPositions
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L227"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IList&lt;double&gt; ColorPositions { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_Colors_" data-uid="DrawnUi.Draw.SkiaGradient.Colors*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_Colors" data-uid="DrawnUi.Draw.SkiaGradient.Colors">
  Colors
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L165"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IList&lt;Color&gt; Colors { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_EndXRatio_" data-uid="DrawnUi.Draw.SkiaGradient.EndXRatio*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_EndXRatio" data-uid="DrawnUi.Draw.SkiaGradient.EndXRatio">
  EndXRatio
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L133"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float EndXRatio { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_EndYRatio_" data-uid="DrawnUi.Draw.SkiaGradient.EndYRatio*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_EndYRatio" data-uid="DrawnUi.Draw.SkiaGradient.EndYRatio">
  EndYRatio
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L141"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float EndYRatio { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_Light_" data-uid="DrawnUi.Draw.SkiaGradient.Light*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_Light" data-uid="DrawnUi.Draw.SkiaGradient.Light">
  Light
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L99"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Light { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_Opacity_" data-uid="DrawnUi.Draw.SkiaGradient.Opacity*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_Opacity" data-uid="DrawnUi.Draw.SkiaGradient.Opacity">
  Opacity
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L109"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Opacity { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_Parent_" data-uid="DrawnUi.Draw.SkiaGradient.Parent*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_Parent" data-uid="DrawnUi.Draw.SkiaGradient.Parent">
  Parent
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L9"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ISkiaControl Parent { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_StartXRatio_" data-uid="DrawnUi.Draw.SkiaGradient.StartXRatio*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_StartXRatio" data-uid="DrawnUi.Draw.SkiaGradient.StartXRatio">
  StartXRatio
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L117"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float StartXRatio { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_StartYRatio_" data-uid="DrawnUi.Draw.SkiaGradient.StartYRatio*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_StartYRatio" data-uid="DrawnUi.Draw.SkiaGradient.StartYRatio">
  StartYRatio
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L125"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float StartYRatio { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_TileMode_" data-uid="DrawnUi.Draw.SkiaGradient.TileMode*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_TileMode" data-uid="DrawnUi.Draw.SkiaGradient.TileMode">
  TileMode
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L90"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKShaderTileMode TileMode { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skshadertilemode">SKShaderTileMode</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_SkiaGradient_Type_" data-uid="DrawnUi.Draw.SkiaGradient.Type*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_Type" data-uid="DrawnUi.Draw.SkiaGradient.Type">
  Type
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L71"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public GradientType Type { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.GradientType.html">GradientType</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_SkiaGradient_Clone_" data-uid="DrawnUi.Draw.SkiaGradient.Clone*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_Clone" data-uid="DrawnUi.Draw.SkiaGradient.Clone">
  Clone()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Creates a new object that is a copy of the current instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public object Clone()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd><p>A new object that is a copy of this instance.</p>
</dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaGradient_FromBrush_" data-uid="DrawnUi.Draw.SkiaGradient.FromBrush*"></a>

  <h3 id="DrawnUi_Draw_SkiaGradient_FromBrush_Microsoft_Maui_Controls_GradientBrush_" data-uid="DrawnUi.Draw.SkiaGradient.FromBrush(Microsoft.Maui.Controls.GradientBrush)">
  FromBrush(GradientBrush)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L28"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaGradient FromBrush(GradientBrush gradientBrush)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>gradientBrush</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.gradientbrush">GradientBrush</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaGradient.html">SkiaGradient</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/SkiaGradient.cs/#L7" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
