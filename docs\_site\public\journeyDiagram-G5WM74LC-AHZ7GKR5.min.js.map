{"version": 3, "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-G5WM74LC.mjs"], "sourcesContent": ["import {\n  drawBackgroundRect,\n  drawRect,\n  drawText,\n  getNoteRect\n} from \"./chunk-ASOPGD6M.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/diagrams/user-journey/parser/journey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 11, 12, 14, 16, 17, 18], $V1 = [1, 9], $V2 = [1, 10], $V3 = [1, 11], $V4 = [1, 12], $V5 = [1, 13], $V6 = [1, 14];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"journey\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"title\": 11, \"acc_title\": 12, \"acc_title_value\": 13, \"acc_descr\": 14, \"acc_descr_value\": 15, \"acc_descr_multiline_value\": 16, \"section\": 17, \"taskName\": 18, \"taskData\": 19, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"journey\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 11: \"title\", 12: \"acc_title\", 13: \"acc_title_value\", 14: \"acc_descr\", 15: \"acc_descr_value\", 16: \"acc_descr_multiline_value\", 17: \"section\", 18: \"taskName\", 19: \"taskData\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 2]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 10:\n        case 11:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 12:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 13:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 15, 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 8]), { 13: [1, 16] }, { 15: [1, 17] }, o($V0, [2, 11]), o($V0, [2, 12]), { 19: [1, 18] }, o($V0, [2, 4]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 13])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 10;\n            break;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            return 4;\n            break;\n          case 6:\n            return 11;\n            break;\n          case 7:\n            this.begin(\"acc_title\");\n            return 12;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.begin(\"acc_descr\");\n            return 14;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 17;\n            break;\n          case 15:\n            return 18;\n            break;\n          case 16:\n            return 19;\n            break;\n          case 17:\n            return \":\";\n            break;\n          case 18:\n            return 6;\n            break;\n          case 19:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:journey\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:section\\s[^#:\\n;]+)/i, /^(?:[^#:\\n;]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar journey_default = parser;\n\n// src/diagrams/user-journey/journeyDb.js\nvar currentSection = \"\";\nvar sections = [];\nvar tasks = [];\nvar rawTasks = [];\nvar clear2 = /* @__PURE__ */ __name(function() {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = \"\";\n  rawTasks.length = 0;\n  clear();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ __name(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ __name(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ __name(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks.push(...rawTasks);\n  return tasks;\n}, \"getTasks\");\nvar updateActors = /* @__PURE__ */ __name(function() {\n  const tempActors = [];\n  tasks.forEach((task) => {\n    if (task.people) {\n      tempActors.push(...task.people);\n    }\n  });\n  const unique = new Set(tempActors);\n  return [...unique].sort();\n}, \"updateActors\");\nvar addTask = /* @__PURE__ */ __name(function(descr, taskData) {\n  const pieces = taskData.substr(1).split(\":\");\n  let score = 0;\n  let peeps = [];\n  if (pieces.length === 1) {\n    score = Number(pieces[0]);\n    peeps = [];\n  } else {\n    score = Number(pieces[0]);\n    peeps = pieces[1].split(\",\");\n  }\n  const peopleList = peeps.map((s) => s.trim());\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    people: peopleList,\n    task: descr,\n    score\n  };\n  rawTasks.push(rawTask);\n}, \"addTask\");\nvar addTaskOrg = /* @__PURE__ */ __name(function(descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ __name(function() {\n  const compileTask = /* @__PURE__ */ __name(function(pos) {\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar getActors = /* @__PURE__ */ __name(function() {\n  return updateActors();\n}, \"getActors\");\nvar journeyDb_default = {\n  getConfig: /* @__PURE__ */ __name(() => getConfig().journey, \"getConfig\"),\n  clear: clear2,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  getActors\n};\n\n// src/diagrams/user-journey/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.label {\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n    color: ${options.textColor};\n  }\n  .mouth {\n    stroke: #666;\n  }\n\n  line {\n    stroke: ${options.textColor}\n  }\n\n  .legend {\n    fill: ${options.textColor};\n  }\n\n  .label text {\n    fill: #333;\n  }\n  .label {\n    color: ${options.textColor}\n  }\n\n  .face {\n    ${options.faceColor ? `fill: ${options.faceColor}` : \"fill: #FFF8DC\"};\n    stroke: #999;\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 1.5px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n    }\n    text-align: center;\n  }\n\n  .cluster rect {\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .task-type-0, .section-type-0  {\n    ${options.fillType0 ? `fill: ${options.fillType0}` : \"\"};\n  }\n  .task-type-1, .section-type-1  {\n    ${options.fillType0 ? `fill: ${options.fillType1}` : \"\"};\n  }\n  .task-type-2, .section-type-2  {\n    ${options.fillType0 ? `fill: ${options.fillType2}` : \"\"};\n  }\n  .task-type-3, .section-type-3  {\n    ${options.fillType0 ? `fill: ${options.fillType3}` : \"\"};\n  }\n  .task-type-4, .section-type-4  {\n    ${options.fillType0 ? `fill: ${options.fillType4}` : \"\"};\n  }\n  .task-type-5, .section-type-5  {\n    ${options.fillType0 ? `fill: ${options.fillType5}` : \"\"};\n  }\n  .task-type-6, .section-type-6  {\n    ${options.fillType0 ? `fill: ${options.fillType6}` : \"\"};\n  }\n  .task-type-7, .section-type-7  {\n    ${options.fillType0 ? `fill: ${options.fillType7}` : \"\"};\n  }\n\n  .actor-0 {\n    ${options.actor0 ? `fill: ${options.actor0}` : \"\"};\n  }\n  .actor-1 {\n    ${options.actor1 ? `fill: ${options.actor1}` : \"\"};\n  }\n  .actor-2 {\n    ${options.actor2 ? `fill: ${options.actor2}` : \"\"};\n  }\n  .actor-3 {\n    ${options.actor3 ? `fill: ${options.actor3}` : \"\"};\n  }\n  .actor-4 {\n    ${options.actor4 ? `fill: ${options.actor4}` : \"\"};\n  }\n  .actor-5 {\n    ${options.actor5 ? `fill: ${options.actor5}` : \"\"};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/user-journey/journeyRenderer.ts\nimport { select } from \"d3\";\n\n// src/diagrams/user-journey/svgDraw.js\nimport { arc as d3arc } from \"d3\";\nvar drawRect2 = /* @__PURE__ */ __name(function(elem, rectData) {\n  return drawRect(elem, rectData);\n}, \"drawRect\");\nvar drawFace = /* @__PURE__ */ __name(function(element, faceData) {\n  const radius = 15;\n  const circleElement = element.append(\"circle\").attr(\"cx\", faceData.cx).attr(\"cy\", faceData.cy).attr(\"class\", \"face\").attr(\"r\", radius).attr(\"stroke-width\", 2).attr(\"overflow\", \"visible\");\n  const face = element.append(\"g\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx - radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx + radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  function smile(face2) {\n    const arc = d3arc().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 2) + \")\");\n  }\n  __name(smile, \"smile\");\n  function sad(face2) {\n    const arc = d3arc().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 7) + \")\");\n  }\n  __name(sad, \"sad\");\n  function ambivalent(face2) {\n    face2.append(\"line\").attr(\"class\", \"mouth\").attr(\"stroke\", 2).attr(\"x1\", faceData.cx - 5).attr(\"y1\", faceData.cy + 7).attr(\"x2\", faceData.cx + 5).attr(\"y2\", faceData.cy + 7).attr(\"class\", \"mouth\").attr(\"stroke-width\", \"1px\").attr(\"stroke\", \"#666\");\n  }\n  __name(ambivalent, \"ambivalent\");\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n  return circleElement;\n}, \"drawFace\");\nvar drawCircle = /* @__PURE__ */ __name(function(element, circleData) {\n  const circleElement = element.append(\"circle\");\n  circleElement.attr(\"cx\", circleData.cx);\n  circleElement.attr(\"cy\", circleData.cy);\n  circleElement.attr(\"class\", \"actor-\" + circleData.pos);\n  circleElement.attr(\"fill\", circleData.fill);\n  circleElement.attr(\"stroke\", circleData.stroke);\n  circleElement.attr(\"r\", circleData.r);\n  if (circleElement.class !== void 0) {\n    circleElement.attr(\"class\", circleElement.class);\n  }\n  if (circleData.title !== void 0) {\n    circleElement.append(\"title\").text(circleData.title);\n  }\n  return circleElement;\n}, \"drawCircle\");\nvar drawText2 = /* @__PURE__ */ __name(function(elem, textData) {\n  return drawText(elem, textData);\n}, \"drawText\");\nvar drawLabel = /* @__PURE__ */ __name(function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  __name(genPoints, \"genPoints\");\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText2(elem, txtObject);\n}, \"drawLabel\");\nvar drawSection = /* @__PURE__ */ __name(function(elem, section, conf2) {\n  const g = elem.append(\"g\");\n  const rect = getNoteRect();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf2.width * section.taskCount + // width of the tasks\n  conf2.diagramMarginX * (section.taskCount - 1);\n  rect.height = conf2.height;\n  rect.class = \"journey-section section-type-\" + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect2(g, rect);\n  _drawTextCandidateFunc(conf2)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"journey-section section-type-\" + section.num },\n    conf2,\n    section.colour\n  );\n}, \"drawSection\");\nvar taskCount = -1;\nvar drawTask = /* @__PURE__ */ __name(function(elem, task, conf2) {\n  const center = task.x + conf2.width / 2;\n  const g = elem.append(\"g\");\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append(\"line\").attr(\"id\", \"task\" + taskCount).attr(\"x1\", center).attr(\"y1\", task.y).attr(\"x2\", center).attr(\"y2\", maxHeight).attr(\"class\", \"task-line\").attr(\"stroke-width\", \"1px\").attr(\"stroke-dasharray\", \"4 2\").attr(\"stroke\", \"#666\");\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score\n  });\n  const rect = getNoteRect();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf2.width;\n  rect.height = conf2.height;\n  rect.class = \"task task-type-\" + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect2(g, rect);\n  let xPos = task.x + 14;\n  task.people.forEach((person) => {\n    const colour = task.actors[person].color;\n    const circle = {\n      cx: xPos,\n      cy: task.y,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      title: person,\n      pos: task.actors[person].position\n    };\n    drawCircle(g, circle);\n    xPos += 10;\n  });\n  _drawTextCandidateFunc(conf2)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"task\" },\n    conf2,\n    task.colour\n  );\n}, \"drawTask\");\nvar drawBackgroundRect2 = /* @__PURE__ */ __name(function(elem, bounds2) {\n  drawBackgroundRect(elem, bounds2);\n}, \"drawBackgroundRect\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"font-color\", colour).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2, colour) {\n    const { taskFontSize, taskFontFamily } = conf2;\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).attr(\"fill\", colour).style(\"text-anchor\", \"middle\").style(\"font-size\", taskFontSize).style(\"font-family\", taskFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  __name(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const body = g.append(\"switch\");\n    const f = body.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"position\", \"fixed\");\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").attr(\"class\", \"label\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, body, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  __name(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar initGraphics = /* @__PURE__ */ __name(function(graphics) {\n  graphics.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 5).attr(\"refY\", 2).attr(\"markerWidth\", 6).attr(\"markerHeight\", 4).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0,0 V 4 L6,2 Z\");\n}, \"initGraphics\");\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawCircle,\n  drawSection,\n  drawText: drawText2,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect: drawBackgroundRect2,\n  initGraphics\n};\n\n// src/diagrams/user-journey/journeyRenderer.ts\nvar setConf = /* @__PURE__ */ __name(function(cnf) {\n  const keys = Object.keys(cnf);\n  keys.forEach(function(key) {\n    conf[key] = cnf[key];\n  });\n}, \"setConf\");\nvar actors = {};\nfunction drawActorLegend(diagram2) {\n  const conf2 = getConfig().journey;\n  let yPos = 60;\n  Object.keys(actors).forEach((person) => {\n    const colour = actors[person].color;\n    const circleData = {\n      cx: 20,\n      cy: yPos,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      pos: actors[person].position\n    };\n    svgDraw_default.drawCircle(diagram2, circleData);\n    const labelData = {\n      x: 40,\n      y: yPos + 7,\n      fill: \"#666\",\n      text: person,\n      textMargin: conf2.boxTextMargin | 5\n    };\n    svgDraw_default.drawText(diagram2, labelData);\n    yPos += 20;\n  });\n}\n__name(drawActorLegend, \"drawActorLegend\");\nvar conf = getConfig().journey;\nvar LEFT_MARGIN = conf.leftMargin;\nvar draw = /* @__PURE__ */ __name(function(text, id, version, diagObj) {\n  const conf2 = getConfig().journey;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  bounds.init();\n  const diagram2 = root.select(\"#\" + id);\n  svgDraw_default.initGraphics(diagram2);\n  const tasks2 = diagObj.db.getTasks();\n  const title = diagObj.db.getDiagramTitle();\n  const actorNames = diagObj.db.getActors();\n  for (const member in actors) {\n    delete actors[member];\n  }\n  let actorPos = 0;\n  actorNames.forEach((actorName) => {\n    actors[actorName] = {\n      color: conf2.actorColours[actorPos % conf2.actorColours.length],\n      position: actorPos\n    };\n    actorPos++;\n  });\n  drawActorLegend(diagram2);\n  bounds.insert(0, 0, LEFT_MARGIN, Object.keys(actors).length * 50);\n  drawTasks(diagram2, tasks2, 0);\n  const box = bounds.getBounds();\n  if (title) {\n    diagram2.append(\"text\").text(title).attr(\"x\", LEFT_MARGIN).attr(\"font-size\", \"4ex\").attr(\"font-weight\", \"bold\").attr(\"y\", 25);\n  }\n  const height = box.stopy - box.starty + 2 * conf2.diagramMarginY;\n  const width = LEFT_MARGIN + box.stopx + 2 * conf2.diagramMarginX;\n  configureSvgSize(diagram2, height, width, conf2.useMaxWidth);\n  diagram2.append(\"line\").attr(\"x1\", LEFT_MARGIN).attr(\"y1\", conf2.height * 4).attr(\"x2\", width - LEFT_MARGIN - 4).attr(\"y2\", conf2.height * 4).attr(\"stroke-width\", 4).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\");\n  const extraVertForTitle = title ? 70 : 0;\n  diagram2.attr(\"viewBox\", `${box.startx} -25 ${width} ${height + extraVertForTitle}`);\n  diagram2.attr(\"preserveAspectRatio\", \"xMinYMin meet\");\n  diagram2.attr(\"height\", height + extraVertForTitle + 25);\n}, \"draw\");\nvar bounds = {\n  data: {\n    startx: void 0,\n    stopx: void 0,\n    starty: void 0,\n    stopy: void 0\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  init: /* @__PURE__ */ __name(function() {\n    this.sequenceItems = [];\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0\n    };\n    this.verticalPos = 0;\n  }, \"init\"),\n  updateVal: /* @__PURE__ */ __name(function(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }, \"updateVal\"),\n  updateBounds: /* @__PURE__ */ __name(function(startx, starty, stopx, stopy) {\n    const conf2 = getConfig().journey;\n    const _self = this;\n    let cnt = 0;\n    function updateFn(type) {\n      return /* @__PURE__ */ __name(function updateItemBounds(item) {\n        cnt++;\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, \"starty\", starty - n * conf2.boxMargin, Math.min);\n        _self.updateVal(item, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        _self.updateVal(bounds.data, \"startx\", startx - n * conf2.boxMargin, Math.min);\n        _self.updateVal(bounds.data, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n        if (!(type === \"activation\")) {\n          _self.updateVal(item, \"startx\", startx - n * conf2.boxMargin, Math.min);\n          _self.updateVal(item, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n          _self.updateVal(bounds.data, \"starty\", starty - n * conf2.boxMargin, Math.min);\n          _self.updateVal(bounds.data, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        }\n      }, \"updateItemBounds\");\n    }\n    __name(updateFn, \"updateFn\");\n    this.sequenceItems.forEach(updateFn());\n  }, \"updateBounds\"),\n  insert: /* @__PURE__ */ __name(function(startx, starty, stopx, stopy) {\n    const _startx = Math.min(startx, stopx);\n    const _stopx = Math.max(startx, stopx);\n    const _starty = Math.min(starty, stopy);\n    const _stopy = Math.max(starty, stopy);\n    this.updateVal(bounds.data, \"startx\", _startx, Math.min);\n    this.updateVal(bounds.data, \"starty\", _starty, Math.min);\n    this.updateVal(bounds.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(bounds.data, \"stopy\", _stopy, Math.max);\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  }, \"insert\"),\n  bumpVerticalPos: /* @__PURE__ */ __name(function(bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = this.verticalPos;\n  }, \"bumpVerticalPos\"),\n  getVerticalPos: /* @__PURE__ */ __name(function() {\n    return this.verticalPos;\n  }, \"getVerticalPos\"),\n  getBounds: /* @__PURE__ */ __name(function() {\n    return this.data;\n  }, \"getBounds\")\n};\nvar fills = conf.sectionFills;\nvar textColours = conf.sectionColours;\nvar drawTasks = /* @__PURE__ */ __name(function(diagram2, tasks2, verticalPos) {\n  const conf2 = getConfig().journey;\n  let lastSection = \"\";\n  const sectionVHeight = conf2.height * 2 + conf2.diagramMarginY;\n  const taskPos = verticalPos + sectionVHeight;\n  let sectionNumber = 0;\n  let fill = \"#CCC\";\n  let colour = \"black\";\n  let num = 0;\n  for (const [i, task] of tasks2.entries()) {\n    if (lastSection !== task.section) {\n      fill = fills[sectionNumber % fills.length];\n      num = sectionNumber % fills.length;\n      colour = textColours[sectionNumber % textColours.length];\n      let taskInSectionCount = 0;\n      const currentSection2 = task.section;\n      for (let taskIndex = i; taskIndex < tasks2.length; taskIndex++) {\n        if (tasks2[taskIndex].section == currentSection2) {\n          taskInSectionCount = taskInSectionCount + 1;\n        } else {\n          break;\n        }\n      }\n      const section = {\n        x: i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN,\n        y: 50,\n        text: task.section,\n        fill,\n        num,\n        colour,\n        taskCount: taskInSectionCount\n      };\n      svgDraw_default.drawSection(diagram2, section, conf2);\n      lastSection = task.section;\n      sectionNumber++;\n    }\n    const taskActors = task.people.reduce((acc, actorName) => {\n      if (actors[actorName]) {\n        acc[actorName] = actors[actorName];\n      }\n      return acc;\n    }, {});\n    task.x = i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN;\n    task.y = taskPos;\n    task.width = conf2.diagramMarginX;\n    task.height = conf2.diagramMarginY;\n    task.colour = colour;\n    task.fill = fill;\n    task.num = num;\n    task.actors = taskActors;\n    svgDraw_default.drawTask(diagram2, task, conf2);\n    bounds.insert(task.x, task.y, task.x + task.width + conf2.taskMargin, 300 + 5 * 30);\n  }\n}, \"drawTasks\");\nvar journeyRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/user-journey/journeyDiagram.ts\nvar diagram = {\n  parser: journey_default,\n  db: journeyDb_default,\n  renderer: journeyRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    journeyRenderer_default.setConf(cnf.journey);\n    journeyDb_default.clear();\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "mappings": "uQAoBA,IAAIA,EAAS,UAAW,CACtB,IAAIC,EAAoBC,EAAO,SAASC,EAAGC,EAAGC,EAAI,EAAG,CACnD,IAAKA,EAAKA,GAAM,CAAC,EAAG,EAAIF,EAAE,OAAQ,IAAKE,EAAGF,EAAE,CAAC,CAAC,EAAIC,EAAG,CACrD,OAAOC,CACT,EAAG,GAAG,EAAGC,EAAM,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EACrIC,EAAU,CACZ,MAAuBX,EAAO,UAAiB,CAC/C,EAAG,OAAO,EACV,GAAI,CAAC,EACL,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,QAAW,EAAG,SAAY,EAAG,IAAO,EAAG,KAAQ,EAAG,MAAS,EAAG,UAAa,EAAG,QAAW,GAAI,MAAS,GAAI,UAAa,GAAI,gBAAmB,GAAI,UAAa,GAAI,gBAAmB,GAAI,0BAA6B,GAAI,QAAW,GAAI,SAAY,GAAI,SAAY,GAAI,QAAW,EAAG,KAAQ,CAAE,EACtU,WAAY,CAAE,EAAG,QAAS,EAAG,UAAW,EAAG,MAAO,EAAG,QAAS,GAAI,UAAW,GAAI,QAAS,GAAI,YAAa,GAAI,kBAAmB,GAAI,YAAa,GAAI,kBAAmB,GAAI,4BAA6B,GAAI,UAAW,GAAI,WAAY,GAAI,UAAW,EACzP,aAAc,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,CAAC,EACxH,cAA+BA,EAAO,SAAmBY,EAAQC,EAAQC,EAAUC,EAAIC,EAASC,EAAIC,EAAI,CACtG,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACf,IAAK,GACH,OAAOC,EAAGE,EAAK,CAAC,EAElB,IAAK,GACH,KAAK,EAAI,CAAC,EACV,MACF,IAAK,GACHF,EAAGE,EAAK,CAAC,EAAE,KAAKF,EAAGE,CAAE,CAAC,EACtB,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAClB,MACF,IAAK,GACL,IAAK,GACH,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,GACL,IAAK,GACH,KAAK,EAAI,CAAC,EACV,MACF,IAAK,GACHJ,EAAG,gBAAgBE,EAAGE,CAAE,EAAE,OAAO,CAAC,CAAC,EACnC,KAAK,EAAIF,EAAGE,CAAE,EAAE,OAAO,CAAC,EACxB,MACF,IAAK,GACH,KAAK,EAAIF,EAAGE,CAAE,EAAE,KAAK,EACrBJ,EAAG,YAAY,KAAK,CAAC,EACrB,MACF,IAAK,IACL,IAAK,IACH,KAAK,EAAIE,EAAGE,CAAE,EAAE,KAAK,EACrBJ,EAAG,kBAAkB,KAAK,CAAC,EAC3B,MACF,IAAK,IACHA,EAAG,WAAWE,EAAGE,CAAE,EAAE,OAAO,CAAC,CAAC,EAC9B,KAAK,EAAIF,EAAGE,CAAE,EAAE,OAAO,CAAC,EACxB,MACF,IAAK,IACHJ,EAAG,QAAQE,EAAGE,EAAK,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAC7B,KAAK,EAAI,OACT,KACJ,CACF,EAAG,WAAW,EACd,MAAO,CAAC,CAAE,EAAG,EAAG,EAAG,CAAC,EAAG,CAAC,CAAE,EAAG,CAAE,EAAG,CAAC,CAAC,CAAE,EAAGpB,EAAEK,EAAK,CAAC,EAAG,CAAC,EAAG,CAAE,EAAG,CAAE,CAAC,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,EAAG,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,EAAG,GAAI,CAAC,EAAG,CAAC,EAAG,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAGX,EAAEK,EAAK,CAAC,EAAG,CAAC,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAE,CAAC,EAAGL,EAAEK,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG,CAAE,EAAG,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAI,EAAGX,EAAEK,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGL,EAAEK,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGL,EAAEK,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAGL,EAAEK,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGL,EAAEK,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAE,EAAGL,EAAEK,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGL,EAAEK,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGL,EAAEK,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGL,EAAEK,EAAK,CAAC,EAAG,EAAE,CAAC,CAAC,EAChe,eAAgB,CAAC,EACjB,WAA4BJ,EAAO,SAAoBoB,EAAKC,EAAM,CAChE,GAAIA,EAAK,YACP,KAAK,MAAMD,CAAG,MACT,CACL,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACR,CACF,EAAG,YAAY,EACf,MAAuBtB,EAAO,SAAeuB,EAAO,CAClD,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,EAAQ,KAAK,MAAOjB,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAGiB,GAAa,EAAGC,GAAS,EAAGC,GAAM,EAClKC,GAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAS,OAAO,OAAO,KAAK,KAAK,EACjCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAASlC,KAAK,KAAK,GACb,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,CAAC,IACjDkC,EAAY,GAAGlC,CAAC,EAAI,KAAK,GAAGA,CAAC,GAGjCiC,EAAO,SAASX,EAAOY,EAAY,EAAE,EACrCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAO,OAAU,MAC1BA,EAAO,OAAS,CAAC,GAEnB,IAAIE,EAAQF,EAAO,OACnBN,EAAO,KAAKQ,CAAK,EACjB,IAAIC,GAASH,EAAO,SAAWA,EAAO,QAAQ,OAC1C,OAAOC,EAAY,GAAG,YAAe,WACvC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAEhD,SAASG,GAASC,EAAG,CACnBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CAClC,CACAvC,EAAOsC,GAAU,UAAU,EAC3B,SAASE,IAAM,CACb,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAO,IAAI,GAAKF,GACpC,OAAOS,GAAU,WACfA,aAAiB,QACnBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAErBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE3BA,CACT,CACAzC,EAAOwC,GAAK,KAAK,EAEjB,QADIE,EAAQC,EAAgBC,EAAOC,EAAQC,GAAGC,EAAGC,EAAQ,CAAC,EAAGC,EAAGC,EAAKC,GAAUC,IAClE,CAUX,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EAC3BC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACtCA,EAASF,GAAI,GAEfK,EAAShB,EAAMe,CAAK,GAAKf,EAAMe,CAAK,EAAEF,CAAM,GAE1C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CACjE,IAAIQ,EAAS,GACbD,EAAW,CAAC,EACZ,IAAKH,KAAKpB,EAAMe,CAAK,EACf,KAAK,WAAWK,CAAC,GAAKA,EAAIlB,IAC5BqB,EAAS,KAAK,IAAM,KAAK,WAAWH,CAAC,EAAI,GAAG,EAG5Cf,EAAO,aACTmB,EAAS,wBAA0BvC,EAAW,GAAK;AAAA,EAAQoB,EAAO,aAAa,EAAI;AAAA,YAAiBkB,EAAS,KAAK,IAAI,EAAI,WAAa,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,EAAS,wBAA0BvC,EAAW,GAAK,iBAAmB4B,GAAUV,GAAM,eAAiB,KAAO,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAErJ,KAAK,WAAWW,EAAQ,CACtB,KAAMnB,EAAO,MACb,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAO,SACb,IAAKE,EACL,SAAAgB,CACF,CAAC,CACH,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAChD,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEpG,OAAQG,EAAO,CAAC,EAAG,CACjB,IAAK,GACHpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAO,MAAM,EACzBN,EAAO,KAAKM,EAAO,MAAM,EACzBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,GASHD,EAASC,EACTA,EAAiB,OATjB9B,EAASqB,EAAO,OAChBtB,EAASsB,EAAO,OAChBpB,EAAWoB,EAAO,SAClBE,EAAQF,EAAO,OACXJ,GAAa,GACfA,MAMJ,MACF,IAAK,GAwBH,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,EAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,EAAM,GAAK,CACT,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WACzC,EACIS,KACFW,EAAM,GAAG,MAAQ,CACfpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACnC,GAEFmB,EAAI,KAAK,cAAc,MAAMC,EAAO,CAClCpC,EACAC,EACAC,EACAqB,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACF,EAAE,OAAOK,EAAI,CAAC,EACV,OAAOc,EAAM,IACf,OAAOA,EAELG,IACFzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAEnCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,EAAM,CAAC,EACnBpB,EAAO,KAAKoB,EAAM,EAAE,EACpBG,GAAWtB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACF,IAAK,GACH,MAAO,EACX,CACF,CACA,MAAO,EACT,EAAG,OAAO,CACZ,EACIG,EAAwB,UAAW,CACrC,IAAIpB,EAAS,CACX,IAAK,EACL,WAA4BlC,EAAO,SAAoBoB,EAAKC,EAAM,CAChE,GAAI,KAAK,GAAG,OACV,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAEvB,EAAG,YAAY,EAEf,SAA0BpB,EAAO,SAASuB,EAAOR,EAAI,CACnD,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACZ,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACf,EACI,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,EAAG,CAAC,GAE3B,KAAK,OAAS,EACP,IACT,EAAG,UAAU,EAEb,MAAuBvB,EAAO,UAAW,CACvC,IAAIuD,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACF,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEV,KAAK,QAAQ,QACf,KAAK,OAAO,MAAM,CAAC,IAErB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACT,EAAG,OAAO,EAEV,MAAuBvD,EAAO,SAASuD,EAAI,CACzC,IAAIL,EAAMK,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EACpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASL,CAAG,EAC5D,KAAK,QAAUA,EACf,IAAIO,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EACzDD,EAAM,OAAS,IACjB,KAAK,UAAYA,EAAM,OAAS,GAElC,IAAIT,EAAI,KAAK,OAAO,MACpB,YAAK,OAAS,CACZ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaS,GAASA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAAKA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAAS,KAAK,OAAO,aAAeN,CAC1L,EACI,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAErD,KAAK,OAAS,KAAK,OAAO,OACnB,IACT,EAAG,OAAO,EAEV,KAAsBlD,EAAO,UAAW,CACtC,YAAK,MAAQ,GACN,IACT,EAAG,MAAM,EAET,OAAwBA,EAAO,UAAW,CACxC,GAAI,KAAK,QAAQ,gBACf,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAChO,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACb,CAAC,EAEH,OAAO,IACT,EAAG,QAAQ,EAEX,KAAsBA,EAAO,SAASuC,EAAG,CACvC,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAChC,EAAG,MAAM,EAET,UAA2BvC,EAAO,UAAW,CAC3C,IAAI0D,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAQ,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAAG,WAAW,EAEd,cAA+B1D,EAAO,UAAW,CAC/C,IAAI2D,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KAChBA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAKA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAG,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CACjF,EAAG,eAAe,EAElB,aAA8B3D,EAAO,UAAW,CAC9C,IAAI4D,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACjD,EAAG,cAAc,EAEjB,WAA4B7D,EAAO,SAAS8D,EAAOC,EAAc,CAC/D,IAAItB,EAAOe,EAAOQ,EAmDlB,GAlDI,KAAK,QAAQ,kBACfA,EAAS,CACP,SAAU,KAAK,SACf,OAAQ,CACN,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC3B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACb,EACI,KAAK,QAAQ,SACfA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAGnDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACF,KAAK,UAAYA,EAAM,QAEzB,KAAK,OAAS,CACZ,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EAAQA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAAS,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MAC/I,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAE9D,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBrB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMsB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SACpB,KAAK,KAAO,IAEVtB,EACF,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1B,QAASxC,KAAK+D,EACZ,KAAK/D,CAAC,EAAI+D,EAAO/D,CAAC,EAEpB,MAAO,EACT,CACA,MAAO,EACT,EAAG,YAAY,EAEf,KAAsBD,EAAO,UAAW,CACtC,GAAI,KAAK,KACP,OAAO,KAAK,IAET,KAAK,SACR,KAAK,KAAO,IAEd,IAAIyC,EAAOqB,EAAOG,EAAWC,EACxB,KAAK,QACR,KAAK,OAAS,GACd,KAAK,MAAQ,IAGf,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAEhC,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGzD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAEhC,GADA3B,EAAQ,KAAK,WAAWwB,EAAWE,EAAMC,CAAC,CAAC,EACvC3B,IAAU,GACZ,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1BqB,EAAQ,GACR,QACF,KACE,OAAO,EAEX,SAAW,CAAC,KAAK,QAAQ,KACvB,MAIN,OAAIA,GACFrB,EAAQ,KAAK,WAAWqB,EAAOK,EAAMD,CAAK,CAAC,EACvCzB,IAAU,GACLA,EAEF,IAEL,KAAK,SAAW,GACX,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACtH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACb,CAAC,CAEL,EAAG,MAAM,EAET,IAAqBzC,EAAO,UAAe,CACzC,IAAI+C,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGK,KAAK,IAAI,CAEpB,EAAG,KAAK,EAER,MAAuB/C,EAAO,SAAeqE,EAAW,CACtD,KAAK,eAAe,KAAKA,CAAS,CACpC,EAAG,OAAO,EAEV,SAA0BrE,EAAO,UAAoB,CACnD,IAAIuC,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACC,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEhC,EAAG,UAAU,EAEb,cAA+BvC,EAAO,UAAyB,CAC7D,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAC3E,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAEtC,EAAG,eAAe,EAElB,SAA0BA,EAAO,SAAkBuC,EAAG,CAEpD,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACA,KAAK,eAAeA,CAAC,EAErB,SAEX,EAAG,UAAU,EAEb,UAA2BvC,EAAO,SAAmBqE,EAAW,CAC9D,KAAK,MAAMA,CAAS,CACtB,EAAG,WAAW,EAEd,eAAgCrE,EAAO,UAA0B,CAC/D,OAAO,KAAK,eAAe,MAC7B,EAAG,gBAAgB,EACnB,QAAS,CAAE,mBAAoB,EAAK,EACpC,cAA+BA,EAAO,SAAmBe,EAAIuD,EAAKC,EAA2BC,EAAU,CACrG,IAAIC,EAAUD,EACd,OAAQD,EAA2B,CACjC,IAAK,GACH,MACF,IAAK,GACH,MACF,IAAK,GACH,MAAO,IAET,IAAK,GACH,MACF,IAAK,GACH,MACF,IAAK,GACH,MAAO,GAET,IAAK,GACH,MAAO,IAET,IAAK,GACH,YAAK,MAAM,WAAW,EACf,GACP,MACF,IAAK,GACH,YAAK,SAAS,EACP,kBACP,MACF,IAAK,GACH,YAAK,MAAM,WAAW,EACf,GACP,MACF,IAAK,IACH,YAAK,SAAS,EACP,kBACP,MACF,IAAK,IACH,KAAK,MAAM,qBAAqB,EAChC,MACF,IAAK,IACH,KAAK,SAAS,EACd,MACF,IAAK,IACH,MAAO,4BAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,GAET,IAAK,IACH,MAAO,SAEX,CACF,EAAG,WAAW,EACd,MAAO,CAAC,sBAAuB,sBAAuB,cAAe,YAAa,gBAAiB,kBAAmB,wBAAyB,wBAAyB,wBAAyB,wBAAyB,wBAAyB,yBAA0B,aAAc,eAAgB,2BAA4B,kBAAmB,kBAAmB,UAAW,UAAW,SAAS,EAC5Y,WAAY,CAAE,oBAAuB,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,EAAE,EAAG,UAAa,EAAM,EAAG,UAAa,CAAE,MAAS,CAAC,CAAC,EAAG,UAAa,EAAM,EAAG,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAK,CAAE,CAC5R,EACA,OAAOrC,CACT,EAAE,EACFvB,EAAQ,MAAQ2C,EAChB,SAASoB,GAAS,CAChB,KAAK,GAAK,CAAC,CACb,CACA,OAAA1E,EAAO0E,EAAQ,QAAQ,EACvBA,EAAO,UAAY/D,EACnBA,EAAQ,OAAS+D,EACV,IAAIA,CACb,EAAE,EACF5E,EAAO,OAASA,EAChB,IAAI6E,GAAkB7E,EAGlB8E,EAAiB,GACjBC,EAAW,CAAC,EACZC,EAAQ,CAAC,EACTC,EAAW,CAAC,EACZC,GAAyBhF,EAAO,UAAW,CAC7C6E,EAAS,OAAS,EAClBC,EAAM,OAAS,EACfF,EAAiB,GACjBG,EAAS,OAAS,EAClBE,GAAM,CACR,EAAG,OAAO,EACNC,GAA6BlF,EAAO,SAASmF,EAAK,CACpDP,EAAiBO,EACjBN,EAAS,KAAKM,CAAG,CACnB,EAAG,YAAY,EACXC,GAA8BpF,EAAO,UAAW,CAClD,OAAO6E,CACT,EAAG,aAAa,EACZQ,GAA2BrF,EAAO,UAAW,CAC/C,IAAIsF,EAAoBC,GAAa,EAC/BC,EAAW,IACbC,EAAiB,EACrB,KAAO,CAACH,GAAqBG,EAAiBD,GAC5CF,EAAoBC,GAAa,EACjCE,IAEF,OAAAX,EAAM,KAAK,GAAGC,CAAQ,EACfD,CACT,EAAG,UAAU,EACTY,GAA+B1F,EAAO,UAAW,CACnD,IAAM2F,EAAa,CAAC,EACpB,OAAAb,EAAM,QAASc,GAAS,CAClBA,EAAK,QACPD,EAAW,KAAK,GAAGC,EAAK,MAAM,CAElC,CAAC,EAEM,CAAC,GADO,IAAI,IAAID,CAAU,CAChB,EAAE,KAAK,CAC1B,EAAG,cAAc,EACbE,GAA0B7F,EAAO,SAAS8F,EAAOC,EAAU,CAC7D,IAAMC,EAASD,EAAS,OAAO,CAAC,EAAE,MAAM,GAAG,EACvCE,EAAQ,EACRC,EAAQ,CAAC,EACTF,EAAO,SAAW,GACpBC,EAAQ,OAAOD,EAAO,CAAC,CAAC,EACxBE,EAAQ,CAAC,IAETD,EAAQ,OAAOD,EAAO,CAAC,CAAC,EACxBE,EAAQF,EAAO,CAAC,EAAE,MAAM,GAAG,GAE7B,IAAMG,EAAaD,EAAM,IAAKE,GAAMA,EAAE,KAAK,CAAC,EACtCC,EAAU,CACd,QAASzB,EACT,KAAMA,EACN,OAAQuB,EACR,KAAML,EACN,MAAAG,CACF,EACAlB,EAAS,KAAKsB,CAAO,CACvB,EAAG,SAAS,EACRC,GAA6BtG,EAAO,SAAS8F,EAAO,CACtD,IAAMS,EAAU,CACd,QAAS3B,EACT,KAAMA,EACN,YAAakB,EACb,KAAMA,EACN,QAAS,CAAC,CACZ,EACAhB,EAAM,KAAKyB,CAAO,CACpB,EAAG,YAAY,EACXhB,GAA+BvF,EAAO,UAAW,CACnD,IAAMwG,EAA8BxG,EAAO,SAASyG,EAAK,CACvD,OAAO1B,EAAS0B,CAAG,EAAE,SACvB,EAAG,aAAa,EACZC,EAAe,GACnB,OAAW,CAACtC,EAAGiC,CAAO,IAAKtB,EAAS,QAAQ,EAC1CyB,EAAYpC,CAAC,EACbsC,EAAeA,GAAgBL,EAAQ,UAEzC,OAAOK,CACT,EAAG,cAAc,EACbC,GAA4B3G,EAAO,UAAW,CAChD,OAAO0F,GAAa,CACtB,EAAG,WAAW,EACVkB,GAAoB,CACtB,UAA2B5G,EAAO,IAAM6G,EAAU,EAAE,QAAS,WAAW,EACxE,MAAO7B,GACP,gBAAA8B,GACA,gBAAAC,GACA,YAAAC,GACA,YAAAC,GACA,kBAAAC,GACA,kBAAAC,GACA,WAAAjC,GACA,YAAAE,GACA,SAAAC,GACA,QAAAQ,GACA,WAAAS,GACA,UAAAK,EACF,EAGIS,GAA4BpH,EAAQqH,GAAY;AAAA;AAAA;AAAA,aAGvCA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOhBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,YAInBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAOhBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,MAIxBA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAS5DA,EAAQ,OAAO;AAAA,cACbA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAYpBA,EAAQ,cAAc;AAAA;AAAA;AAAA;AAAA,cAIpBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,cAKjBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,wBAKPA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAWvCA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAWZA,EAAQ,aAAa;AAAA,wBACfA,EAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOjCA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA;AAAA,MAIrDA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA,EAElD,WAAW,EACVC,GAAiBF,GAOjBG,EAA4BvH,EAAO,SAASwH,EAAMC,EAAU,CAC9D,OAAOC,GAASF,EAAMC,CAAQ,CAChC,EAAG,UAAU,EACTE,GAA2B3H,EAAO,SAAS4H,EAASC,EAAU,CAEhE,IAAMC,EAAgBF,EAAQ,OAAO,QAAQ,EAAE,KAAK,KAAMC,EAAS,EAAE,EAAE,KAAK,KAAMA,EAAS,EAAE,EAAE,KAAK,QAAS,MAAM,EAAE,KAAK,IAAK,EAAM,EAAE,KAAK,eAAgB,CAAC,EAAE,KAAK,WAAY,SAAS,EACnLE,EAAOH,EAAQ,OAAO,GAAG,EAC/BG,EAAK,OAAO,QAAQ,EAAE,KAAK,KAAMF,EAAS,GAAK,GAAS,CAAC,EAAE,KAAK,KAAMA,EAAS,GAAK,GAAS,CAAC,EAAE,KAAK,IAAK,GAAG,EAAE,KAAK,eAAgB,CAAC,EAAE,KAAK,OAAQ,MAAM,EAAE,KAAK,SAAU,MAAM,EACjLE,EAAK,OAAO,QAAQ,EAAE,KAAK,KAAMF,EAAS,GAAK,GAAS,CAAC,EAAE,KAAK,KAAMA,EAAS,GAAK,GAAS,CAAC,EAAE,KAAK,IAAK,GAAG,EAAE,KAAK,eAAgB,CAAC,EAAE,KAAK,OAAQ,MAAM,EAAE,KAAK,SAAU,MAAM,EACjL,SAASG,EAAMC,EAAO,CACpB,IAAMC,EAAMC,EAAM,EAAE,WAAW,KAAK,GAAK,CAAC,EAAE,SAAS,GAAK,KAAK,GAAK,EAAE,EAAE,YAAY,GAAU,EAAE,YAAY,kBAAY,EACxHF,EAAM,OAAO,MAAM,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,IAAKC,CAAG,EAAE,KAAK,YAAa,aAAeL,EAAS,GAAK,KAAOA,EAAS,GAAK,GAAK,GAAG,CACzI,CACA7H,EAAOgI,EAAO,OAAO,EACrB,SAASI,EAAIH,EAAO,CAClB,IAAMC,EAAMC,EAAM,EAAE,WAAW,EAAI,KAAK,GAAK,CAAC,EAAE,SAAS,GAAK,KAAK,GAAK,EAAE,EAAE,YAAY,GAAU,EAAE,YAAY,kBAAY,EAC5HF,EAAM,OAAO,MAAM,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,IAAKC,CAAG,EAAE,KAAK,YAAa,aAAeL,EAAS,GAAK,KAAOA,EAAS,GAAK,GAAK,GAAG,CACzI,CACA7H,EAAOoI,EAAK,KAAK,EACjB,SAASC,EAAWJ,EAAO,CACzBA,EAAM,OAAO,MAAM,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,SAAU,CAAC,EAAE,KAAK,KAAMJ,EAAS,GAAK,CAAC,EAAE,KAAK,KAAMA,EAAS,GAAK,CAAC,EAAE,KAAK,KAAMA,EAAS,GAAK,CAAC,EAAE,KAAK,KAAMA,EAAS,GAAK,CAAC,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,eAAgB,KAAK,EAAE,KAAK,SAAU,MAAM,CACxP,CACA,OAAA7H,EAAOqI,EAAY,YAAY,EAC3BR,EAAS,MAAQ,EACnBG,EAAMD,CAAI,EACDF,EAAS,MAAQ,EAC1BO,EAAIL,CAAI,EAERM,EAAWN,CAAI,EAEVD,CACT,EAAG,UAAU,EACTQ,GAA6BtI,EAAO,SAAS4H,EAASW,EAAY,CACpE,IAAMT,EAAgBF,EAAQ,OAAO,QAAQ,EAC7C,OAAAE,EAAc,KAAK,KAAMS,EAAW,EAAE,EACtCT,EAAc,KAAK,KAAMS,EAAW,EAAE,EACtCT,EAAc,KAAK,QAAS,SAAWS,EAAW,GAAG,EACrDT,EAAc,KAAK,OAAQS,EAAW,IAAI,EAC1CT,EAAc,KAAK,SAAUS,EAAW,MAAM,EAC9CT,EAAc,KAAK,IAAKS,EAAW,CAAC,EAChCT,EAAc,QAAU,QAC1BA,EAAc,KAAK,QAASA,EAAc,KAAK,EAE7CS,EAAW,QAAU,QACvBT,EAAc,OAAO,OAAO,EAAE,KAAKS,EAAW,KAAK,EAE9CT,CACT,EAAG,YAAY,EACXU,GAA4BxI,EAAO,SAASwH,EAAMiB,EAAU,CAC9D,OAAOC,GAASlB,EAAMiB,CAAQ,CAChC,EAAG,UAAU,EACTE,GAA4B3I,EAAO,SAASwH,EAAMoB,EAAW,CAC/D,SAASC,EAAUC,EAAGC,EAAGC,EAAOC,EAAQC,EAAK,CAC3C,OAAOJ,EAAI,IAAMC,EAAI,KAAOD,EAAIE,GAAS,IAAMD,EAAI,KAAOD,EAAIE,GAAS,KAAOD,EAAIE,EAASC,GAAO,KAAOJ,EAAIE,EAAQE,EAAM,KAAO,KAAOH,EAAIE,GAAU,IAAMH,EAAI,KAAOC,EAAIE,EAC9K,CACAjJ,EAAO6I,EAAW,WAAW,EAC7B,IAAMM,EAAU3B,EAAK,OAAO,SAAS,EACrC2B,EAAQ,KAAK,SAAUN,EAAUD,EAAU,EAAGA,EAAU,EAAG,GAAI,GAAI,CAAC,CAAC,EACrEO,EAAQ,KAAK,QAAS,UAAU,EAChCP,EAAU,EAAIA,EAAU,EAAIA,EAAU,YACtCA,EAAU,EAAIA,EAAU,EAAI,GAAMA,EAAU,YAC5CJ,GAAUhB,EAAMoB,CAAS,CAC3B,EAAG,WAAW,EACVQ,GAA8BpJ,EAAO,SAASwH,EAAM6B,EAASC,EAAO,CACtE,IAAMC,EAAI/B,EAAK,OAAO,GAAG,EACnBgC,EAAOC,EAAY,EACzBD,EAAK,EAAIH,EAAQ,EACjBG,EAAK,EAAIH,EAAQ,EACjBG,EAAK,KAAOH,EAAQ,KACpBG,EAAK,MAAQF,EAAM,MAAQD,EAAQ,UACnCC,EAAM,gBAAkBD,EAAQ,UAAY,GAC5CG,EAAK,OAASF,EAAM,OACpBE,EAAK,MAAQ,gCAAkCH,EAAQ,IACvDG,EAAK,GAAK,EACVA,EAAK,GAAK,EACVjC,EAAUgC,EAAGC,CAAI,EACjBE,GAAuBJ,CAAK,EAC1BD,EAAQ,KACRE,EACAC,EAAK,EACLA,EAAK,EACLA,EAAK,MACLA,EAAK,OACL,CAAE,MAAO,gCAAkCH,EAAQ,GAAI,EACvDC,EACAD,EAAQ,MACV,CACF,EAAG,aAAa,EACZM,GAAY,GACZC,GAA2B5J,EAAO,SAASwH,EAAM5B,EAAM0D,EAAO,CAChE,IAAMO,EAASjE,EAAK,EAAI0D,EAAM,MAAQ,EAChCC,EAAI/B,EAAK,OAAO,GAAG,EACzBmC,KACA,IAAMG,EAAY,IAAM,EAAI,GAC5BP,EAAE,OAAO,MAAM,EAAE,KAAK,KAAM,OAASI,EAAS,EAAE,KAAK,KAAME,CAAM,EAAE,KAAK,KAAMjE,EAAK,CAAC,EAAE,KAAK,KAAMiE,CAAM,EAAE,KAAK,KAAMC,CAAS,EAAE,KAAK,QAAS,WAAW,EAAE,KAAK,eAAgB,KAAK,EAAE,KAAK,mBAAoB,KAAK,EAAE,KAAK,SAAU,MAAM,EAC3OnC,GAAS4B,EAAG,CACV,GAAIM,EACJ,GAAI,KAAO,EAAIjE,EAAK,OAAS,GAC7B,MAAOA,EAAK,KACd,CAAC,EACD,IAAM4D,EAAOC,EAAY,EACzBD,EAAK,EAAI5D,EAAK,EACd4D,EAAK,EAAI5D,EAAK,EACd4D,EAAK,KAAO5D,EAAK,KACjB4D,EAAK,MAAQF,EAAM,MACnBE,EAAK,OAASF,EAAM,OACpBE,EAAK,MAAQ,kBAAoB5D,EAAK,IACtC4D,EAAK,GAAK,EACVA,EAAK,GAAK,EACVjC,EAAUgC,EAAGC,CAAI,EACjB,IAAIO,EAAOnE,EAAK,EAAI,GACpBA,EAAK,OAAO,QAASoE,GAAW,CAC9B,IAAMC,EAASrE,EAAK,OAAOoE,CAAM,EAAE,MAC7BE,EAAS,CACb,GAAIH,EACJ,GAAInE,EAAK,EACT,EAAG,EACH,KAAMqE,EACN,OAAQ,OACR,MAAOD,EACP,IAAKpE,EAAK,OAAOoE,CAAM,EAAE,QAC3B,EACA1B,GAAWiB,EAAGW,CAAM,EACpBH,GAAQ,EACV,CAAC,EACDL,GAAuBJ,CAAK,EAC1B1D,EAAK,KACL2D,EACAC,EAAK,EACLA,EAAK,EACLA,EAAK,MACLA,EAAK,OACL,CAAE,MAAO,MAAO,EAChBF,EACA1D,EAAK,MACP,CACF,EAAG,UAAU,EACTuE,GAAsCnK,EAAO,SAASwH,EAAM4C,EAAS,CACvEC,GAAmB7C,EAAM4C,CAAO,CAClC,EAAG,oBAAoB,EACnBV,GAAyC,UAAW,CACtD,SAASY,EAAOC,EAAShB,EAAGT,EAAGC,EAAGC,EAAOC,EAAQuB,EAAWP,EAAQ,CAClE,IAAMQ,EAAOlB,EAAE,OAAO,MAAM,EAAE,KAAK,IAAKT,EAAIE,EAAQ,CAAC,EAAE,KAAK,IAAKD,EAAIE,EAAS,EAAI,CAAC,EAAE,MAAM,aAAcgB,CAAM,EAAE,MAAM,cAAe,QAAQ,EAAE,KAAKM,CAAO,EAC5JG,EAAcD,EAAMD,CAAS,CAC/B,CACAxK,EAAOsK,EAAQ,QAAQ,EACvB,SAASK,EAAQJ,EAAShB,EAAGT,EAAGC,EAAGC,EAAOC,EAAQuB,EAAWlB,EAAOW,EAAQ,CAC1E,GAAM,CAAE,aAAAW,EAAc,eAAAC,CAAe,EAAIvB,EACnC9F,EAAQ+G,EAAQ,MAAM,cAAc,EAC1C,QAASnG,EAAI,EAAGA,EAAIZ,EAAM,OAAQY,IAAK,CACrC,IAAM0G,EAAK1G,EAAIwG,EAAeA,GAAgBpH,EAAM,OAAS,GAAK,EAC5DiH,EAAOlB,EAAE,OAAO,MAAM,EAAE,KAAK,IAAKT,EAAIE,EAAQ,CAAC,EAAE,KAAK,IAAKD,CAAC,EAAE,KAAK,OAAQkB,CAAM,EAAE,MAAM,cAAe,QAAQ,EAAE,MAAM,YAAaW,CAAY,EAAE,MAAM,cAAeC,CAAc,EAC5LJ,EAAK,OAAO,OAAO,EAAE,KAAK,IAAK3B,EAAIE,EAAQ,CAAC,EAAE,KAAK,KAAM8B,CAAE,EAAE,KAAKtH,EAAMY,CAAC,CAAC,EAC1EqG,EAAK,KAAK,IAAK1B,EAAIE,EAAS,CAAC,EAAE,KAAK,oBAAqB,SAAS,EAAE,KAAK,qBAAsB,SAAS,EACxGyB,EAAcD,EAAMD,CAAS,CAC/B,CACF,CACAxK,EAAO2K,EAAS,SAAS,EACzB,SAASI,EAAKR,EAAShB,EAAGT,EAAGC,EAAGC,EAAOC,EAAQuB,EAAWlB,EAAO,CAC/D,IAAM0B,EAAOzB,EAAE,OAAO,QAAQ,EAExBkB,EADIO,EAAK,OAAO,eAAe,EAAE,KAAK,IAAKlC,CAAC,EAAE,KAAK,IAAKC,CAAC,EAAE,KAAK,QAASC,CAAK,EAAE,KAAK,SAAUC,CAAM,EAAE,KAAK,WAAY,OAAO,EACtH,OAAO,WAAW,EAAE,MAAM,UAAW,OAAO,EAAE,MAAM,SAAU,MAAM,EAAE,MAAM,QAAS,MAAM,EAC1GwB,EAAK,OAAO,KAAK,EAAE,KAAK,QAAS,OAAO,EAAE,MAAM,UAAW,YAAY,EAAE,MAAM,aAAc,QAAQ,EAAE,MAAM,iBAAkB,QAAQ,EAAE,KAAKF,CAAO,EACrJI,EAAQJ,EAASS,EAAMlC,EAAGC,EAAGC,EAAOC,EAAQuB,EAAWlB,CAAK,EAC5DoB,EAAcD,EAAMD,CAAS,CAC/B,CACAxK,EAAO+K,EAAM,MAAM,EACnB,SAASL,EAAcO,EAAQC,EAAmB,CAChD,QAAWC,KAAOD,EACZC,KAAOD,GACTD,EAAO,KAAKE,EAAKD,EAAkBC,CAAG,CAAC,CAG7C,CACA,OAAAnL,EAAO0K,EAAe,eAAe,EAC9B,SAASpB,EAAO,CACrB,OAAOA,EAAM,gBAAkB,KAAOyB,EAAOzB,EAAM,gBAAkB,MAAQgB,EAASK,CACxF,CACF,EAAE,EACES,GAA+BpL,EAAO,SAASqL,EAAU,CAC3DA,EAAS,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,WAAW,EAAE,KAAK,OAAQ,CAAC,EAAE,KAAK,OAAQ,CAAC,EAAE,KAAK,cAAe,CAAC,EAAE,KAAK,eAAgB,CAAC,EAAE,KAAK,SAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,kBAAkB,CACpN,EAAG,cAAc,EACbC,EAAkB,CACpB,SAAU/D,EACV,WAAAe,GACA,YAAAc,GACA,SAAUZ,GACV,UAAAG,GACA,SAAAiB,GACA,mBAAoBO,GACpB,aAAAiB,EACF,EAGIG,GAA0BvL,EAAO,SAASwL,EAAK,CACpC,OAAO,KAAKA,CAAG,EACvB,QAAQ,SAASL,EAAK,CACzBM,EAAKN,CAAG,EAAIK,EAAIL,CAAG,CACrB,CAAC,CACH,EAAG,SAAS,EACRO,EAAS,CAAC,EACd,SAASC,GAAgBC,EAAU,CACjC,IAAMtC,EAAQzC,EAAU,EAAE,QACtBgF,EAAO,GACX,OAAO,KAAKH,CAAM,EAAE,QAAS1B,GAAW,CACtC,IAAMC,EAASyB,EAAO1B,CAAM,EAAE,MACxBzB,EAAa,CACjB,GAAI,GACJ,GAAIsD,EACJ,EAAG,EACH,KAAM5B,EACN,OAAQ,OACR,IAAKyB,EAAO1B,CAAM,EAAE,QACtB,EACAsB,EAAgB,WAAWM,EAAUrD,CAAU,EAC/C,IAAMuD,EAAY,CAChB,EAAG,GACH,EAAGD,EAAO,EACV,KAAM,OACN,KAAM7B,EACN,WAAYV,EAAM,cAAgB,CACpC,EACAgC,EAAgB,SAASM,EAAUE,CAAS,EAC5CD,GAAQ,EACV,CAAC,CACH,CACA7L,EAAO2L,GAAiB,iBAAiB,EACzC,IAAIF,EAAO5E,EAAU,EAAE,QACnBkF,EAAcN,EAAK,WACnBO,GAAuBhM,EAAO,SAASyK,EAAMwB,EAAIC,EAASC,EAAS,CACrE,IAAM7C,EAAQzC,EAAU,EAAE,QACpBuF,EAAgBvF,EAAU,EAAE,cAC9BwF,EACAD,IAAkB,YACpBC,EAAiBC,EAAO,KAAOL,CAAE,GAEnC,IAAMM,EAAOH,IAAkB,UAAYE,EAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EAAIC,EAAO,MAAM,EACjHE,EAAO,KAAK,EACZ,IAAMZ,EAAWW,EAAK,OAAO,IAAMN,CAAE,EACrCX,EAAgB,aAAaM,CAAQ,EACrC,IAAMa,EAASN,EAAQ,GAAG,SAAS,EAC7BO,EAAQP,EAAQ,GAAG,gBAAgB,EACnCQ,EAAaR,EAAQ,GAAG,UAAU,EACxC,QAAWS,KAAUlB,EACnB,OAAOA,EAAOkB,CAAM,EAEtB,IAAIC,EAAW,EACfF,EAAW,QAASG,GAAc,CAChCpB,EAAOoB,CAAS,EAAI,CAClB,MAAOxD,EAAM,aAAauD,EAAWvD,EAAM,aAAa,MAAM,EAC9D,SAAUuD,CACZ,EACAA,GACF,CAAC,EACDlB,GAAgBC,CAAQ,EACxBY,EAAO,OAAO,EAAG,EAAGT,EAAa,OAAO,KAAKL,CAAM,EAAE,OAAS,EAAE,EAChEqB,GAAUnB,EAAUa,EAAQ,CAAC,EAC7B,IAAMO,EAAMR,EAAO,UAAU,EACzBE,GACFd,EAAS,OAAO,MAAM,EAAE,KAAKc,CAAK,EAAE,KAAK,IAAKX,CAAW,EAAE,KAAK,YAAa,KAAK,EAAE,KAAK,cAAe,MAAM,EAAE,KAAK,IAAK,EAAE,EAE9H,IAAM9C,EAAS+D,EAAI,MAAQA,EAAI,OAAS,EAAI1D,EAAM,eAC5CN,EAAQ+C,EAAciB,EAAI,MAAQ,EAAI1D,EAAM,eAClD2D,GAAiBrB,EAAU3C,EAAQD,EAAOM,EAAM,WAAW,EAC3DsC,EAAS,OAAO,MAAM,EAAE,KAAK,KAAMG,CAAW,EAAE,KAAK,KAAMzC,EAAM,OAAS,CAAC,EAAE,KAAK,KAAMN,EAAQ+C,EAAc,CAAC,EAAE,KAAK,KAAMzC,EAAM,OAAS,CAAC,EAAE,KAAK,eAAgB,CAAC,EAAE,KAAK,SAAU,OAAO,EAAE,KAAK,aAAc,iBAAiB,EAClO,IAAM4D,EAAoBR,EAAQ,GAAK,EACvCd,EAAS,KAAK,UAAW,GAAGoB,EAAI,MAAM,QAAQhE,CAAK,IAAIC,EAASiE,CAAiB,EAAE,EACnFtB,EAAS,KAAK,sBAAuB,eAAe,EACpDA,EAAS,KAAK,SAAU3C,EAASiE,EAAoB,EAAE,CACzD,EAAG,MAAM,EACLV,EAAS,CACX,KAAM,CACJ,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,MACT,EACA,YAAa,EACb,cAAe,CAAC,EAChB,KAAsBxM,EAAO,UAAW,CACtC,KAAK,cAAgB,CAAC,EACtB,KAAK,KAAO,CACV,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,MACT,EACA,KAAK,YAAc,CACrB,EAAG,MAAM,EACT,UAA2BA,EAAO,SAASmN,EAAKhC,EAAKiC,EAAKC,EAAK,CACzDF,EAAIhC,CAAG,IAAM,OACfgC,EAAIhC,CAAG,EAAIiC,EAEXD,EAAIhC,CAAG,EAAIkC,EAAID,EAAKD,EAAIhC,CAAG,CAAC,CAEhC,EAAG,WAAW,EACd,aAA8BnL,EAAO,SAASsN,EAAQC,EAAQC,EAAOC,EAAO,CAC1E,IAAMnE,EAAQzC,EAAU,EAAE,QACpB6G,EAAQ,KACVC,EAAM,EACV,SAASC,EAASC,EAAM,CACtB,OAAuB7N,EAAO,SAA0B8N,EAAM,CAC5DH,IACA,IAAMpL,EAAImL,EAAM,cAAc,OAASC,EAAM,EAC7CD,EAAM,UAAUI,EAAM,SAAUP,EAAShL,EAAI+G,EAAM,UAAW,KAAK,GAAG,EACtEoE,EAAM,UAAUI,EAAM,QAASL,EAAQlL,EAAI+G,EAAM,UAAW,KAAK,GAAG,EACpEoE,EAAM,UAAUlB,EAAO,KAAM,SAAUc,EAAS/K,EAAI+G,EAAM,UAAW,KAAK,GAAG,EAC7EoE,EAAM,UAAUlB,EAAO,KAAM,QAASgB,EAAQjL,EAAI+G,EAAM,UAAW,KAAK,GAAG,EACrEuE,IAAS,eACbH,EAAM,UAAUI,EAAM,SAAUR,EAAS/K,EAAI+G,EAAM,UAAW,KAAK,GAAG,EACtEoE,EAAM,UAAUI,EAAM,QAASN,EAAQjL,EAAI+G,EAAM,UAAW,KAAK,GAAG,EACpEoE,EAAM,UAAUlB,EAAO,KAAM,SAAUe,EAAShL,EAAI+G,EAAM,UAAW,KAAK,GAAG,EAC7EoE,EAAM,UAAUlB,EAAO,KAAM,QAASiB,EAAQlL,EAAI+G,EAAM,UAAW,KAAK,GAAG,EAE/E,EAAG,kBAAkB,CACvB,CACAtJ,EAAO4N,EAAU,UAAU,EAC3B,KAAK,cAAc,QAAQA,EAAS,CAAC,CACvC,EAAG,cAAc,EACjB,OAAwB5N,EAAO,SAASsN,EAAQC,EAAQC,EAAOC,EAAO,CACpE,IAAMM,EAAU,KAAK,IAAIT,EAAQE,CAAK,EAChCQ,EAAS,KAAK,IAAIV,EAAQE,CAAK,EAC/BS,EAAU,KAAK,IAAIV,EAAQE,CAAK,EAChCS,EAAS,KAAK,IAAIX,EAAQE,CAAK,EACrC,KAAK,UAAUjB,EAAO,KAAM,SAAUuB,EAAS,KAAK,GAAG,EACvD,KAAK,UAAUvB,EAAO,KAAM,SAAUyB,EAAS,KAAK,GAAG,EACvD,KAAK,UAAUzB,EAAO,KAAM,QAASwB,EAAQ,KAAK,GAAG,EACrD,KAAK,UAAUxB,EAAO,KAAM,QAAS0B,EAAQ,KAAK,GAAG,EACrD,KAAK,aAAaH,EAASE,EAASD,EAAQE,CAAM,CACpD,EAAG,QAAQ,EACX,gBAAiClO,EAAO,SAASmO,EAAM,CACrD,KAAK,YAAc,KAAK,YAAcA,EACtC,KAAK,KAAK,MAAQ,KAAK,WACzB,EAAG,iBAAiB,EACpB,eAAgCnO,EAAO,UAAW,CAChD,OAAO,KAAK,WACd,EAAG,gBAAgB,EACnB,UAA2BA,EAAO,UAAW,CAC3C,OAAO,KAAK,IACd,EAAG,WAAW,CAChB,EACIoO,EAAQ3C,EAAK,aACb4C,GAAc5C,EAAK,eACnBsB,GAA4B/M,EAAO,SAAS4L,EAAUa,EAAQ6B,EAAa,CAC7E,IAAMhF,EAAQzC,EAAU,EAAE,QACtB0H,EAAc,GACZC,EAAiBlF,EAAM,OAAS,EAAIA,EAAM,eAC1CmF,EAAUH,EAAcE,EAC1BE,EAAgB,EAChBC,EAAO,OACP1E,EAAS,QACT2E,EAAM,EACV,OAAW,CAACxK,EAAGwB,CAAI,IAAK6G,EAAO,QAAQ,EAAG,CACxC,GAAI8B,IAAgB3I,EAAK,QAAS,CAChC+I,EAAOP,EAAMM,EAAgBN,EAAM,MAAM,EACzCQ,EAAMF,EAAgBN,EAAM,OAC5BnE,EAASoE,GAAYK,EAAgBL,GAAY,MAAM,EACvD,IAAIQ,EAAqB,EACnBC,EAAkBlJ,EAAK,QAC7B,QAASmJ,EAAY3K,EAAG2K,EAAYtC,EAAO,QACrCA,EAAOsC,CAAS,EAAE,SAAWD,EADgBC,IAE/CF,EAAqBA,EAAqB,EAK9C,IAAMxF,EAAU,CACd,EAAGjF,EAAIkF,EAAM,WAAalF,EAAIkF,EAAM,MAAQyC,EAC5C,EAAG,GACH,KAAMnG,EAAK,QACX,KAAA+I,EACA,IAAAC,EACA,OAAA3E,EACA,UAAW4E,CACb,EACAvD,EAAgB,YAAYM,EAAUvC,EAASC,CAAK,EACpDiF,EAAc3I,EAAK,QACnB8I,GACF,CACA,IAAMM,EAAapJ,EAAK,OAAO,OAAO,CAACqJ,EAAKnC,KACtCpB,EAAOoB,CAAS,IAClBmC,EAAInC,CAAS,EAAIpB,EAAOoB,CAAS,GAE5BmC,GACN,CAAC,CAAC,EACLrJ,EAAK,EAAIxB,EAAIkF,EAAM,WAAalF,EAAIkF,EAAM,MAAQyC,EAClDnG,EAAK,EAAI6I,EACT7I,EAAK,MAAQ0D,EAAM,eACnB1D,EAAK,OAAS0D,EAAM,eACpB1D,EAAK,OAASqE,EACdrE,EAAK,KAAO+I,EACZ/I,EAAK,IAAMgJ,EACXhJ,EAAK,OAASoJ,EACd1D,EAAgB,SAASM,EAAUhG,EAAM0D,CAAK,EAC9CkD,EAAO,OAAO5G,EAAK,EAAGA,EAAK,EAAGA,EAAK,EAAIA,EAAK,MAAQ0D,EAAM,WAAY,IAAM,EAAI,EAAE,CACpF,CACF,EAAG,WAAW,EACV4F,GAA0B,CAC5B,QAAA3D,GACA,KAAAS,EACF,EAGImD,GAAU,CACZ,OAAQxK,GACR,GAAIiC,GACJ,SAAUsI,GACV,OAAQ5H,GACR,KAAsBtH,EAAQwL,GAAQ,CACpC0D,GAAwB,QAAQ1D,EAAI,OAAO,EAC3C5E,GAAkB,MAAM,CAC1B,EAAG,MAAM,CACX", "names": ["parser", "o", "__name", "k", "v", "o2", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "parser2", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer2", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "lexer", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "journey_default", "currentSection", "sections", "tasks", "rawTasks", "clear2", "clear", "addSection", "txt", "getSections", "getTasks", "allItemsProcessed", "compileTasks", "max<PERSON><PERSON><PERSON>", "iterationCount", "updateActors", "tempActors", "task", "addTask", "descr", "taskData", "pieces", "score", "peeps", "peopleList", "s", "rawTask", "addTaskOrg", "newTask", "compileTask", "pos", "allProcessed", "getActors", "journeyDb_default", "getConfig2", "setDiagramTitle", "getDiagramTitle", "setAccTitle", "getAccTitle", "setAccDescription", "getAccDescription", "getStyles", "options", "styles_default", "drawRect2", "elem", "rectData", "drawRect", "drawFace", "element", "faceData", "circleElement", "face", "smile", "face2", "arc", "arc_default", "sad", "ambivalent", "drawCircle", "circleData", "drawText2", "textData", "drawText", "drawLabel", "txtObject", "genPoints", "x", "y", "width", "height", "cut", "polygon", "drawSection", "section", "conf2", "g", "rect", "getNoteRect", "_drawTextCandidateFunc", "taskCount", "drawTask", "center", "maxHeight", "xPos", "person", "colour", "circle", "drawBackgroundRect2", "bounds2", "drawBackgroundRect", "byText", "content", "textAttrs", "text", "_setTextAttrs", "byTspan", "taskFontSize", "taskFontFamily", "dy", "byFo", "body", "toText", "fromTextAttrsDict", "key", "initGraphics", "graphics", "svgDraw_default", "setConf", "cnf", "conf", "actors", "drawActorLegend", "diagram2", "yPos", "labelData", "LEFT_MARGIN", "draw", "id", "version", "diagObj", "securityLevel", "sandboxElement", "select_default", "root", "bounds", "tasks2", "title", "<PERSON><PERSON><PERSON><PERSON>", "member", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "drawTasks", "box", "configureSvgSize", "extraVertForTitle", "obj", "val", "fun", "startx", "starty", "stopx", "stopy", "_self", "cnt", "updateFn", "type", "item", "_startx", "_stopx", "_starty", "_stopy", "bump", "fills", "textColours", "verticalPos", "lastSection", "sectionVHeight", "taskPos", "sectionNumber", "fill", "num", "taskInSectionCount", "currentSection2", "taskIndex", "taskActors", "acc", "journeyRenderer_default", "diagram"]}