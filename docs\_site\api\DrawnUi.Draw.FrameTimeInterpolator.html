<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class FrameTimeInterpolator | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class FrameTimeInterpolator | DrawnUi Documentation ">
      
      <meta name="description" content="Interpolated time between frames, works in seconds. See examples..">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FrameTimeInterpolator.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FrameTimeInterpolator%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.FrameTimeInterpolator">



  <h1 id="DrawnUi_Draw_FrameTimeInterpolator" data-uid="DrawnUi.Draw.FrameTimeInterpolator" class="text-break">
Class FrameTimeInterpolator  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L6"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>Interpolated time between frames, works in seconds. See examples..</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class FrameTimeInterpolator</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">FrameTimeInterpolator</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_FrameTimeInterpolator_FpsBuffer_" data-uid="DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer*"></a>

  <h3 id="DrawnUi_Draw_FrameTimeInterpolator_FpsBuffer" data-uid="DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer">
  FpsBuffer
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L56"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets or sets the FPS buffer below target where frame skipping begins</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float FpsBuffer { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_FrameTimeInterpolator_Instance_" data-uid="DrawnUi.Draw.FrameTimeInterpolator.Instance*"></a>

  <h3 id="DrawnUi_Draw_FrameTimeInterpolator_Instance" data-uid="DrawnUi.Draw.FrameTimeInterpolator.Instance">
  Instance
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L251"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static FrameTimeInterpolator Instance { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.FrameTimeInterpolator.html">FrameTimeInterpolator</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_FrameTimeInterpolator_IsSkippingFrames_" data-uid="DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames*"></a>

  <h3 id="DrawnUi_Draw_FrameTimeInterpolator_IsSkippingFrames" data-uid="DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames">
  IsSkippingFrames
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L74"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsSkippingFrames { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_FrameTimeInterpolator_LastFrameStep_" data-uid="DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep*"></a>

  <h3 id="DrawnUi_Draw_FrameTimeInterpolator_LastFrameStep" data-uid="DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep">
  LastFrameStep
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L73"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LastFrameStep { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_FrameTimeInterpolator_ShouldReduceQuality_" data-uid="DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality*"></a>

  <h3 id="DrawnUi_Draw_FrameTimeInterpolator_ShouldReduceQuality" data-uid="DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality">
  ShouldReduceQuality
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L75"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldReduceQuality { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_FrameTimeInterpolator_TargetFps_" data-uid="DrawnUi.Draw.FrameTimeInterpolator.TargetFps*"></a>

  <h3 id="DrawnUi_Draw_FrameTimeInterpolator_TargetFps" data-uid="DrawnUi.Draw.FrameTimeInterpolator.TargetFps">
  TargetFps
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L35"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets or sets the target FPS for the application</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float TargetFps { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_FrameTimeInterpolator_GetDeltaTime_" data-uid="DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime*"></a>

  <h3 id="DrawnUi_Draw_FrameTimeInterpolator_GetDeltaTime_System_Single_" data-uid="DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime(System.Single)">
  GetDeltaTime(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L83"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Calculates the delta time for the current frame based on performance monitoring
and the target FPS</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float GetDeltaTime(float currentFrameTime)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>currentFrameTime</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>The current frame time from the game loop</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>The delta time to use for game updates</p>
</dd>
  </dl>











  <a id="DrawnUi_Draw_FrameTimeInterpolator_Reset_" data-uid="DrawnUi.Draw.FrameTimeInterpolator.Reset*"></a>

  <h3 id="DrawnUi_Draw_FrameTimeInterpolator_Reset" data-uid="DrawnUi.Draw.FrameTimeInterpolator.Reset">
  Reset()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L133"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Resets the time interpolation state</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Reset()</code></pre>
  </div>














</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/FrameTimeInterpolator.cs/#L6" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
