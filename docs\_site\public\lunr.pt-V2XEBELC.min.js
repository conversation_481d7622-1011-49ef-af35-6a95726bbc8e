import{a as T}from"./chunk-OSRY5VT3.min.js";var Z=T((d,b)=>{(function(n,e){typeof define=="function"&&define.amd?define(e):typeof d=="object"?b.exports=e():e()(n.lunr)})(d,function(){return function(n){if(typeof n>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof n.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");n.pt=function(){this.pipeline.reset(),this.pipeline.add(n.pt.trimmer,n.pt.stopWordFilter,n.pt.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(n.pt.stemmer))},n.pt.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",n.pt.trimmer=n.trimmerSupport.generateTrimmer(n.pt.wordCharacters),n.Pipeline.registerFunction(n.pt.trimmer,"trimmer-pt"),n.pt.stemmer=function(){var e=n.stemmerSupport.Among,E=n.stemmerSupport.SnowballProgram,t=new function(){var f=[new e("",-1,3),new e("\xE3",0,1),new e("\xF5",0,2)],g=[new e("",-1,3),new e("a~",0,1),new e("o~",0,2)],k=[new e("ic",-1,-1),new e("ad",-1,-1),new e("os",-1,-1),new e("iv",-1,1)],F=[new e("ante",-1,1),new e("avel",-1,1),new e("\xEDvel",-1,1)],D=[new e("ic",-1,1),new e("abil",-1,1),new e("iv",-1,1)],A=[new e("ica",-1,1),new e("\xE2ncia",-1,1),new e("\xEAncia",-1,4),new e("ira",-1,9),new e("adora",-1,1),new e("osa",-1,1),new e("ista",-1,1),new e("iva",-1,8),new e("eza",-1,1),new e("log\xEDa",-1,2),new e("idade",-1,7),new e("ante",-1,1),new e("mente",-1,6),new e("amente",12,5),new e("\xE1vel",-1,1),new e("\xEDvel",-1,1),new e("uci\xF3n",-1,3),new e("ico",-1,1),new e("ismo",-1,1),new e("oso",-1,1),new e("amento",-1,1),new e("imento",-1,1),new e("ivo",-1,8),new e("a\xE7a~o",-1,1),new e("ador",-1,1),new e("icas",-1,1),new e("\xEAncias",-1,4),new e("iras",-1,9),new e("adoras",-1,1),new e("osas",-1,1),new e("istas",-1,1),new e("ivas",-1,8),new e("ezas",-1,1),new e("log\xEDas",-1,2),new e("idades",-1,7),new e("uciones",-1,3),new e("adores",-1,1),new e("antes",-1,1),new e("a\xE7o~es",-1,1),new e("icos",-1,1),new e("ismos",-1,1),new e("osos",-1,1),new e("amentos",-1,1),new e("imentos",-1,1),new e("ivos",-1,8)],q=[new e("ada",-1,1),new e("ida",-1,1),new e("ia",-1,1),new e("aria",2,1),new e("eria",2,1),new e("iria",2,1),new e("ara",-1,1),new e("era",-1,1),new e("ira",-1,1),new e("ava",-1,1),new e("asse",-1,1),new e("esse",-1,1),new e("isse",-1,1),new e("aste",-1,1),new e("este",-1,1),new e("iste",-1,1),new e("ei",-1,1),new e("arei",16,1),new e("erei",16,1),new e("irei",16,1),new e("am",-1,1),new e("iam",20,1),new e("ariam",21,1),new e("eriam",21,1),new e("iriam",21,1),new e("aram",20,1),new e("eram",20,1),new e("iram",20,1),new e("avam",20,1),new e("em",-1,1),new e("arem",29,1),new e("erem",29,1),new e("irem",29,1),new e("assem",29,1),new e("essem",29,1),new e("issem",29,1),new e("ado",-1,1),new e("ido",-1,1),new e("ando",-1,1),new e("endo",-1,1),new e("indo",-1,1),new e("ara~o",-1,1),new e("era~o",-1,1),new e("ira~o",-1,1),new e("ar",-1,1),new e("er",-1,1),new e("ir",-1,1),new e("as",-1,1),new e("adas",47,1),new e("idas",47,1),new e("ias",47,1),new e("arias",50,1),new e("erias",50,1),new e("irias",50,1),new e("aras",47,1),new e("eras",47,1),new e("iras",47,1),new e("avas",47,1),new e("es",-1,1),new e("ardes",58,1),new e("erdes",58,1),new e("irdes",58,1),new e("ares",58,1),new e("eres",58,1),new e("ires",58,1),new e("asses",58,1),new e("esses",58,1),new e("isses",58,1),new e("astes",58,1),new e("estes",58,1),new e("istes",58,1),new e("is",-1,1),new e("ais",71,1),new e("eis",71,1),new e("areis",73,1),new e("ereis",73,1),new e("ireis",73,1),new e("\xE1reis",73,1),new e("\xE9reis",73,1),new e("\xEDreis",73,1),new e("\xE1sseis",73,1),new e("\xE9sseis",73,1),new e("\xEDsseis",73,1),new e("\xE1veis",73,1),new e("\xEDeis",73,1),new e("ar\xEDeis",84,1),new e("er\xEDeis",84,1),new e("ir\xEDeis",84,1),new e("ados",-1,1),new e("idos",-1,1),new e("amos",-1,1),new e("\xE1ramos",90,1),new e("\xE9ramos",90,1),new e("\xEDramos",90,1),new e("\xE1vamos",90,1),new e("\xEDamos",90,1),new e("ar\xEDamos",95,1),new e("er\xEDamos",95,1),new e("ir\xEDamos",95,1),new e("emos",-1,1),new e("aremos",99,1),new e("eremos",99,1),new e("iremos",99,1),new e("\xE1ssemos",99,1),new e("\xEAssemos",99,1),new e("\xEDssemos",99,1),new e("imos",-1,1),new e("armos",-1,1),new e("ermos",-1,1),new e("irmos",-1,1),new e("\xE1mos",-1,1),new e("ar\xE1s",-1,1),new e("er\xE1s",-1,1),new e("ir\xE1s",-1,1),new e("eu",-1,1),new e("iu",-1,1),new e("ou",-1,1),new e("ar\xE1",-1,1),new e("er\xE1",-1,1),new e("ir\xE1",-1,1)],C=[new e("a",-1,1),new e("i",-1,1),new e("o",-1,1),new e("os",-1,1),new e("\xE1",-1,1),new e("\xED",-1,1),new e("\xF3",-1,1)],B=[new e("e",-1,1),new e("\xE7",-1,2),new e("\xE9",-1,1),new e("\xEA",-1,1)],o=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,0,3,19,12,2],l,_,u,r=new E;this.setCurrent=function(s){r.setCurrent(s)},this.getCurrent=function(){return r.getCurrent()};function j(){for(var s;;){if(r.bra=r.cursor,s=r.find_among(f,3),s)switch(r.ket=r.cursor,s){case 1:r.slice_from("a~");continue;case 2:r.slice_from("o~");continue;case 3:if(r.cursor>=r.limit)break;r.cursor++;continue}break}}function v(){if(r.out_grouping(o,97,250)){for(;!r.in_grouping(o,97,250);){if(r.cursor>=r.limit)return!0;r.cursor++}return!1}return!0}function P(){if(r.in_grouping(o,97,250))for(;!r.out_grouping(o,97,250);){if(r.cursor>=r.limit)return!1;r.cursor++}return u=r.cursor,!0}function x(){var s=r.cursor,a,m;if(r.in_grouping(o,97,250))if(a=r.cursor,v()){if(r.cursor=a,P())return}else u=r.cursor;if(r.cursor=s,r.out_grouping(o,97,250)){if(m=r.cursor,v()){if(r.cursor=m,!r.in_grouping(o,97,250)||r.cursor>=r.limit)return;r.cursor++}u=r.cursor}}function p(){for(;!r.in_grouping(o,97,250);){if(r.cursor>=r.limit)return!1;r.cursor++}for(;!r.out_grouping(o,97,250);){if(r.cursor>=r.limit)return!1;r.cursor++}return!0}function S(){var s=r.cursor;u=r.limit,_=u,l=u,x(),r.cursor=s,p()&&(_=r.cursor,p()&&(l=r.cursor))}function W(){for(var s;;){if(r.bra=r.cursor,s=r.find_among(g,3),s)switch(r.ket=r.cursor,s){case 1:r.slice_from("\xE3");continue;case 2:r.slice_from("\xF5");continue;case 3:if(r.cursor>=r.limit)break;r.cursor++;continue}break}}function w(){return u<=r.cursor}function L(){return _<=r.cursor}function i(){return l<=r.cursor}function y(){var s;if(r.ket=r.cursor,s=r.find_among_b(A,45),!s)return!1;switch(r.bra=r.cursor,s){case 1:if(!i())return!1;r.slice_del();break;case 2:if(!i())return!1;r.slice_from("log");break;case 3:if(!i())return!1;r.slice_from("u");break;case 4:if(!i())return!1;r.slice_from("ente");break;case 5:if(!L())return!1;r.slice_del(),r.ket=r.cursor,s=r.find_among_b(k,4),s&&(r.bra=r.cursor,i()&&(r.slice_del(),s==1&&(r.ket=r.cursor,r.eq_s_b(2,"at")&&(r.bra=r.cursor,i()&&r.slice_del()))));break;case 6:if(!i())return!1;r.slice_del(),r.ket=r.cursor,s=r.find_among_b(F,3),s&&(r.bra=r.cursor,s==1&&i()&&r.slice_del());break;case 7:if(!i())return!1;r.slice_del(),r.ket=r.cursor,s=r.find_among_b(D,3),s&&(r.bra=r.cursor,s==1&&i()&&r.slice_del());break;case 8:if(!i())return!1;r.slice_del(),r.ket=r.cursor,r.eq_s_b(2,"at")&&(r.bra=r.cursor,i()&&r.slice_del());break;case 9:if(!w()||!r.eq_s_b(1,"e"))return!1;r.slice_from("ir");break}return!0}function z(){var s,a;if(r.cursor>=u){if(a=r.limit_backward,r.limit_backward=u,r.ket=r.cursor,s=r.find_among_b(q,120),s)return r.bra=r.cursor,s==1&&r.slice_del(),r.limit_backward=a,!0;r.limit_backward=a}return!1}function I(){var s;r.ket=r.cursor,s=r.find_among_b(C,7),s&&(r.bra=r.cursor,s==1&&w()&&r.slice_del())}function h(s,a){if(r.eq_s_b(1,s)){r.bra=r.cursor;var m=r.limit-r.cursor;if(r.eq_s_b(1,a))return r.cursor=r.limit-m,w()&&r.slice_del(),!1}return!0}function R(){var s,a,m,G;if(r.ket=r.cursor,s=r.find_among_b(B,4),s)switch(r.bra=r.cursor,s){case 1:w()&&(r.slice_del(),r.ket=r.cursor,a=r.limit-r.cursor,h("u","g")&&h("i","c"));break;case 2:r.slice_from("c");break}}function V(){if(!y()&&(r.cursor=r.limit,!z())){r.cursor=r.limit,I();return}r.cursor=r.limit,r.ket=r.cursor,r.eq_s_b(1,"i")&&(r.bra=r.cursor,r.eq_s_b(1,"c")&&(r.cursor=r.limit,w()&&r.slice_del()))}this.stem=function(){var s=r.cursor;return j(),r.cursor=s,S(),r.limit_backward=s,r.cursor=r.limit,V(),r.cursor=r.limit,R(),r.cursor=r.limit_backward,W(),!0}};return function(c){return typeof c.update=="function"?c.update(function(f){return t.setCurrent(f),t.stem(),t.getCurrent()}):(t.setCurrent(c),t.stem(),t.getCurrent())}}(),n.Pipeline.registerFunction(n.pt.stemmer,"stemmer-pt"),n.pt.stopWordFilter=n.generateStopWordFilter("a ao aos aquela aquelas aquele aqueles aquilo as at\xE9 com como da das de dela delas dele deles depois do dos e ela elas ele eles em entre era eram essa essas esse esses esta estamos estas estava estavam este esteja estejam estejamos estes esteve estive estivemos estiver estivera estiveram estiverem estivermos estivesse estivessem estiv\xE9ramos estiv\xE9ssemos estou est\xE1 est\xE1vamos est\xE3o eu foi fomos for fora foram forem formos fosse fossem fui f\xF4ramos f\xF4ssemos haja hajam hajamos havemos hei houve houvemos houver houvera houveram houverei houverem houveremos houveria houveriam houvermos houver\xE1 houver\xE3o houver\xEDamos houvesse houvessem houv\xE9ramos houv\xE9ssemos h\xE1 h\xE3o isso isto j\xE1 lhe lhes mais mas me mesmo meu meus minha minhas muito na nas nem no nos nossa nossas nosso nossos num numa n\xE3o n\xF3s o os ou para pela pelas pelo pelos por qual quando que quem se seja sejam sejamos sem serei seremos seria seriam ser\xE1 ser\xE3o ser\xEDamos seu seus somos sou sua suas s\xE3o s\xF3 tamb\xE9m te tem temos tenha tenham tenhamos tenho terei teremos teria teriam ter\xE1 ter\xE3o ter\xEDamos teu teus teve tinha tinham tive tivemos tiver tivera tiveram tiverem tivermos tivesse tivessem tiv\xE9ramos tiv\xE9ssemos tu tua tuas t\xE9m t\xEDnhamos um uma voc\xEA voc\xEAs vos \xE0 \xE0s \xE9ramos".split(" ")),n.Pipeline.registerFunction(n.pt.stopWordFilter,"stopWordFilter-pt")}})});export default Z();
/*! Bundled license information:

lunr-languages/lunr.pt.js:
  (*!
   * Lunr languages, `Portuguese` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.pt-V2XEBELC.min.js.map
