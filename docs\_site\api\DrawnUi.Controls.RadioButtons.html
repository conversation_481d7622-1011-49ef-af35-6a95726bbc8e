<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class RadioButtons | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class RadioButtons | DrawnUi Documentation ">
      
      <meta name="description" content="Manages radio button groups, ensuring only one button is selected per group. Supports grouping by parent control or by string name.">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Controls.RadioButtons">



  <h1 id="DrawnUi_Controls_RadioButtons" data-uid="DrawnUi.Controls.RadioButtons" class="text-break">
Class RadioButtons  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Controls.html">Controls</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>Manages radio button groups, ensuring only one button is selected per group.
Supports grouping by parent control or by string name.</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class RadioButtons</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">RadioButtons</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Controls_RadioButtons__ctor_" data-uid="DrawnUi.Controls.RadioButtons.#ctor*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons__ctor" data-uid="DrawnUi.Controls.RadioButtons.#ctor">
  RadioButtons()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L131"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Initializes a new instance of the RadioButtons class.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public RadioButtons()</code></pre>
  </div>













  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Controls_RadioButtons_All_" data-uid="DrawnUi.Controls.RadioButtons.All*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_All" data-uid="DrawnUi.Controls.RadioButtons.All">
  All
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L14"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the singleton instance of the RadioButtons manager.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static RadioButtons All { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Controls.RadioButtons.html">RadioButtons</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_RadioButtons_GroupsByName_" data-uid="DrawnUi.Controls.RadioButtons.GroupsByName*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_GroupsByName" data-uid="DrawnUi.Controls.RadioButtons.GroupsByName">
  GroupsByName
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L125"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Dictionary&lt;string, List&lt;ISkiaRadioButton&gt;&gt; GroupsByName { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a>&gt;&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_RadioButtons_GroupsByParent_" data-uid="DrawnUi.Controls.RadioButtons.GroupsByParent*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_GroupsByParent" data-uid="DrawnUi.Controls.RadioButtons.GroupsByParent">
  GroupsByParent
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L126"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Dictionary&lt;SkiaControl, List&lt;ISkiaRadioButton&gt;&gt; GroupsByParent { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a>&gt;&gt;</dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Controls_RadioButtons_AddToGroup_" data-uid="DrawnUi.Controls.RadioButtons.AddToGroup*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_AddToGroup_DrawnUi_Controls_ISkiaRadioButton_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.RadioButtons.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,DrawnUi.Draw.SkiaControl)">
  AddToGroup(ISkiaRadioButton, SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L157"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds a radio button control to a parent-based group. Ensures at least one button in the group is selected.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddToGroup(ISkiaRadioButton control, SkiaControl parent)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></dt>
    <dd><p>The radio button control to add to the group.</p>
</dd>
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd><p>The parent control that defines the group.</p>
</dd>
  </dl>












  <a id="DrawnUi_Controls_RadioButtons_AddToGroup_" data-uid="DrawnUi.Controls.RadioButtons.AddToGroup*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_AddToGroup_DrawnUi_Controls_ISkiaRadioButton_System_String_" data-uid="DrawnUi.Controls.RadioButtons.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,System.String)">
  AddToGroup(ISkiaRadioButton, string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L142"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds a radio button control to a named group. Ensures at least one button in the group is selected.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddToGroup(ISkiaRadioButton control, string groupName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></dt>
    <dd><p>The radio button control to add to the group.</p>
</dd>
    <dt><code>groupName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The name of the group to add the control to.</p>
</dd>
  </dl>












  <a id="DrawnUi_Controls_RadioButtons_GetSelected_" data-uid="DrawnUi.Controls.RadioButtons.GetSelected*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_GetSelected_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.RadioButtons.GetSelected(DrawnUi.Draw.SkiaControl)">
  GetSelected(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L35"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the currently selected radio button in the group associated with the specified parent control.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetSelected(SkiaControl parent)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd><p>The parent control that defines the radio button group.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd><p>The selected SkiaControl, or null if no button is selected or group doesn't exist.</p>
</dd>
  </dl>











  <a id="DrawnUi_Controls_RadioButtons_GetSelected_" data-uid="DrawnUi.Controls.RadioButtons.GetSelected*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_GetSelected_System_String_" data-uid="DrawnUi.Controls.RadioButtons.GetSelected(System.String)">
  GetSelected(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L51"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the currently selected radio button in the group with the specified name.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetSelected(string groupName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>groupName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The name of the radio button group.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd><p>The selected SkiaControl, or null if no button is selected or group doesn't exist.</p>
</dd>
  </dl>











  <a id="DrawnUi_Controls_RadioButtons_GetSelectedIndex_" data-uid="DrawnUi.Controls.RadioButtons.GetSelectedIndex*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_GetSelectedIndex_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.RadioButtons.GetSelectedIndex(DrawnUi.Draw.SkiaControl)">
  GetSelectedIndex(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L67"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the index of the currently selected radio button in the group associated with the specified parent control.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetSelectedIndex(SkiaControl parent)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd><p>The parent control that defines the radio button group.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The zero-based index of the selected button, or -1 if no button is selected or group doesn't exist.</p>
</dd>
  </dl>











  <a id="DrawnUi_Controls_RadioButtons_GetSelectedIndex_" data-uid="DrawnUi.Controls.RadioButtons.GetSelectedIndex*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_GetSelectedIndex_System_String_" data-uid="DrawnUi.Controls.RadioButtons.GetSelectedIndex(System.String)">
  GetSelectedIndex(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L82"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the index of the currently selected radio button in the group with the specified name.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetSelectedIndex(string groupName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>groupName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The name of the radio button group.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The zero-based index of the selected button, or -1 if no button is selected or group doesn't exist.</p>
</dd>
  </dl>











  <a id="DrawnUi_Controls_RadioButtons_RemoveFromGroup_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroup*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_RemoveFromGroup_DrawnUi_Draw_SkiaControl_DrawnUi_Controls_ISkiaRadioButton_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroup(DrawnUi.Draw.SkiaControl,DrawnUi.Controls.ISkiaRadioButton)">
  RemoveFromGroup(SkiaControl, ISkiaRadioButton)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L186"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Removes a radio button control from a parent-based group. Ensures at least one button remains selected in the group.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveFromGroup(SkiaControl parent, ISkiaRadioButton control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd><p>The parent control that defines the group to remove the control from.</p>
</dd>
    <dt><code>control</code> <a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></dt>
    <dd><p>The radio button control to remove.</p>
</dd>
  </dl>












  <a id="DrawnUi_Controls_RadioButtons_RemoveFromGroup_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroup*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_RemoveFromGroup_System_String_DrawnUi_Controls_ISkiaRadioButton_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroup(System.String,DrawnUi.Controls.ISkiaRadioButton)">
  RemoveFromGroup(string, ISkiaRadioButton)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L172"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Removes a radio button control from a named group. Ensures at least one button remains selected in the group.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveFromGroup(string groupName, ISkiaRadioButton control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>groupName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The name of the group to remove the control from.</p>
</dd>
    <dt><code>control</code> <a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></dt>
    <dd><p>The radio button control to remove.</p>
</dd>
  </dl>












  <a id="DrawnUi_Controls_RadioButtons_RemoveFromGroups_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroups*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_RemoveFromGroups_DrawnUi_Controls_ISkiaRadioButton_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroups(DrawnUi.Controls.ISkiaRadioButton)">
  RemoveFromGroups(ISkiaRadioButton)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L199"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Removes a radio button control from all groups it belongs to. Ensures at least one button remains selected in affected groups.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveFromGroups(ISkiaRadioButton control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></dt>
    <dd><p>The radio button control to remove from all groups.</p>
</dd>
  </dl>












  <a id="DrawnUi_Controls_RadioButtons_ReportValueChange_" data-uid="DrawnUi.Controls.RadioButtons.ReportValueChange*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_ReportValueChange_DrawnUi_Controls_ISkiaRadioButton_System_Boolean_" data-uid="DrawnUi.Controls.RadioButtons.ReportValueChange(DrawnUi.Controls.ISkiaRadioButton,System.Boolean)">
  ReportValueChange(ISkiaRadioButton, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L246"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Called by radio button controls to report value changes. Manages mutual exclusion within groups and fires the Changed event.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ReportValueChange(ISkiaRadioButton control, bool newValue)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></dt>
    <dd><p>The radio button control reporting the change.</p>
</dd>
    <dt><code>newValue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd><p>The new value of the control (true for selected, false for unselected).</p>
</dd>
  </dl>












  <a id="DrawnUi_Controls_RadioButtons_Select_" data-uid="DrawnUi.Controls.RadioButtons.Select*"></a>

  <h3 id="DrawnUi_Controls_RadioButtons_Select_DrawnUi_Draw_SkiaControl_System_Int32_" data-uid="DrawnUi.Controls.RadioButtons.Select(DrawnUi.Draw.SkiaControl,System.Int32)">
  Select(SkiaControl, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L111"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Selects the radio button at the specified index in the group associated with the container control.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Select(SkiaControl container, int index)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>container</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd><p>The parent control that defines the radio button group.</p>
</dd>
    <dt><code>index</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The zero-based index of the button to select.</p>
</dd>
  </dl>












  <h2 class="section" id="events">Events
</h2>



  <h3 id="DrawnUi_Controls_RadioButtons_Changed" data-uid="DrawnUi.Controls.RadioButtons.Changed">
  Changed
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L28"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Occurs when a radio button selection changes in any group.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler Changed</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>








</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L7" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
