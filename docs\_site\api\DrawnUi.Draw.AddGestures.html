<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class AddGestures | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class AddGestures | DrawnUi Documentation ">
      
      <meta name="description" content="For fast and lazy gestures handling to attach to dran controls inside the canvas only">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_AddGestures.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.AddGestures%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.AddGestures">



  <h1 id="DrawnUi_Draw_AddGestures" data-uid="DrawnUi.Draw.AddGestures" class="text-break">
Class AddGestures  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L9"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>For fast and lazy gestures handling to attach to dran controls inside the canvas only</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class AddGestures</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">AddGestures</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_AddGestures_AnimationPressedProperty" data-uid="DrawnUi.Draw.AddGestures.AnimationPressedProperty">
  AnimationPressedProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L416"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty AnimationPressedProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_AnimationTappedProperty" data-uid="DrawnUi.Draw.AddGestures.AnimationTappedProperty">
  AnimationTappedProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L373"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty AnimationTappedProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_AttachedListeners" data-uid="DrawnUi.Draw.AddGestures.AttachedListeners">
  AttachedListeners
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L190"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Dictionary&lt;SkiaControl, AddGestures.GestureListener&gt; AttachedListeners</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>, <a class="xref" href="DrawnUi.Draw.AddGestures.html">AddGestures</a>.<a class="xref" href="DrawnUi.Draw.AddGestures.GestureListener.html">GestureListener</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_CommandLongPressingParameterProperty" data-uid="DrawnUi.Draw.AddGestures.CommandLongPressingParameterProperty">
  CommandLongPressingParameterProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L339"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty CommandLongPressingParameterProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_CommandLongPressingProperty" data-uid="DrawnUi.Draw.AddGestures.CommandLongPressingProperty">
  CommandLongPressingProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L319"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty CommandLongPressingProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_CommandPressedProperty" data-uid="DrawnUi.Draw.AddGestures.CommandPressedProperty">
  CommandPressedProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L396"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty CommandPressedProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_CommandTappedParameterProperty" data-uid="DrawnUi.Draw.AddGestures.CommandTappedParameterProperty">
  CommandTappedParameterProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L300"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty CommandTappedParameterProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_CommandTappedProperty" data-uid="DrawnUi.Draw.AddGestures.CommandTappedProperty">
  CommandTappedProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L280"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty CommandTappedProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_LockPanningProperty" data-uid="DrawnUi.Draw.AddGestures.LockPanningProperty">
  LockPanningProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L458"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty LockPanningProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_TouchEffectColorProperty" data-uid="DrawnUi.Draw.AddGestures.TouchEffectColorProperty">
  TouchEffectColorProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L434"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty TouchEffectColorProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_AddGestures_TransformViewProperty" data-uid="DrawnUi.Draw.AddGestures.TransformViewProperty">
  TransformViewProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L356"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty TransformViewProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_AddGestures_GetAnimationPressed_" data-uid="DrawnUi.Draw.AddGestures.GetAnimationPressed*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetAnimationPressed_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetAnimationPressed(Microsoft.Maui.Controls.BindableObject)">
  GetAnimationPressed(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L424"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaTouchAnimation GetAnimationPressed(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaTouchAnimation.html">SkiaTouchAnimation</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_GetAnimationTapped_" data-uid="DrawnUi.Draw.AddGestures.GetAnimationTapped*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetAnimationTapped_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetAnimationTapped(Microsoft.Maui.Controls.BindableObject)">
  GetAnimationTapped(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L381"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaTouchAnimation GetAnimationTapped(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaTouchAnimation.html">SkiaTouchAnimation</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_GetCommandLongPressing_" data-uid="DrawnUi.Draw.AddGestures.GetCommandLongPressing*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetCommandLongPressing_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetCommandLongPressing(Microsoft.Maui.Controls.BindableObject)">
  GetCommandLongPressing(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L327"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static ICommand GetCommandLongPressing(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.windows.input.icommand">ICommand</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_GetCommandLongPressingParameter_" data-uid="DrawnUi.Draw.AddGestures.GetCommandLongPressingParameter*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetCommandLongPressingParameter_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetCommandLongPressingParameter(Microsoft.Maui.Controls.BindableObject)">
  GetCommandLongPressingParameter(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L346"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static object GetCommandLongPressingParameter(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_GetCommandPressed_" data-uid="DrawnUi.Draw.AddGestures.GetCommandPressed*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetCommandPressed_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetCommandPressed(Microsoft.Maui.Controls.BindableObject)">
  GetCommandPressed(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L404"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static ICommand GetCommandPressed(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.windows.input.icommand">ICommand</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_GetCommandTapped_" data-uid="DrawnUi.Draw.AddGestures.GetCommandTapped*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetCommandTapped_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetCommandTapped(Microsoft.Maui.Controls.BindableObject)">
  GetCommandTapped(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L288"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static ICommand GetCommandTapped(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.windows.input.icommand">ICommand</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_GetCommandTappedParameter_" data-uid="DrawnUi.Draw.AddGestures.GetCommandTappedParameter*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetCommandTappedParameter_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetCommandTappedParameter(Microsoft.Maui.Controls.BindableObject)">
  GetCommandTappedParameter(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L307"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static object GetCommandTappedParameter(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_GetLockPanning_" data-uid="DrawnUi.Draw.AddGestures.GetLockPanning*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetLockPanning_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetLockPanning(Microsoft.Maui.Controls.BindableObject)">
  GetLockPanning(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L466"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetLockPanning(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_GetTouchEffectColor_" data-uid="DrawnUi.Draw.AddGestures.GetTouchEffectColor*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetTouchEffectColor_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetTouchEffectColor(Microsoft.Maui.Controls.BindableObject)">
  GetTouchEffectColor(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L441"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color GetTouchEffectColor(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_GetTransformView_" data-uid="DrawnUi.Draw.AddGestures.GetTransformView*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_GetTransformView_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.AddGestures.GetTransformView(Microsoft.Maui.Controls.BindableObject)">
  GetTransformView(BindableObject)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L363"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static object GetTransformView(BindableObject view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_IsActive_" data-uid="DrawnUi.Draw.AddGestures.IsActive*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_IsActive_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.AddGestures.IsActive(DrawnUi.Draw.SkiaControl)">
  IsActive(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L263"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsActive(SkiaControl listener)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>listener</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_AddGestures_ManageRegistration_" data-uid="DrawnUi.Draw.AddGestures.ManageRegistration*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_ManageRegistration_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.AddGestures.ManageRegistration(DrawnUi.Draw.SkiaControl)">
  ManageRegistration(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L236"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ManageRegistration(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetAnimationPressed_" data-uid="DrawnUi.Draw.AddGestures.SetAnimationPressed*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetAnimationPressed_Microsoft_Maui_Controls_BindableObject_DrawnUi_Draw_SkiaTouchAnimation_" data-uid="DrawnUi.Draw.AddGestures.SetAnimationPressed(Microsoft.Maui.Controls.BindableObject,DrawnUi.Draw.SkiaTouchAnimation)">
  SetAnimationPressed(BindableObject, SkiaTouchAnimation)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L429"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetAnimationPressed(BindableObject view, SkiaTouchAnimation value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="DrawnUi.Draw.SkiaTouchAnimation.html">SkiaTouchAnimation</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetAnimationTapped_" data-uid="DrawnUi.Draw.AddGestures.SetAnimationTapped*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetAnimationTapped_Microsoft_Maui_Controls_BindableObject_DrawnUi_Draw_SkiaTouchAnimation_" data-uid="DrawnUi.Draw.AddGestures.SetAnimationTapped(Microsoft.Maui.Controls.BindableObject,DrawnUi.Draw.SkiaTouchAnimation)">
  SetAnimationTapped(BindableObject, SkiaTouchAnimation)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L391"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetAnimationTapped(BindableObject view, SkiaTouchAnimation value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="DrawnUi.Draw.SkiaTouchAnimation.html">SkiaTouchAnimation</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetCommandLongPressing_" data-uid="DrawnUi.Draw.AddGestures.SetCommandLongPressing*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetCommandLongPressing_Microsoft_Maui_Controls_BindableObject_System_Windows_Input_ICommand_" data-uid="DrawnUi.Draw.AddGestures.SetCommandLongPressing(Microsoft.Maui.Controls.BindableObject,System.Windows.Input.ICommand)">
  SetCommandLongPressing(BindableObject, ICommand)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L332"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetCommandLongPressing(BindableObject view, ICommand value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.windows.input.icommand">ICommand</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetCommandLongPressingParameter_" data-uid="DrawnUi.Draw.AddGestures.SetCommandLongPressingParameter*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetCommandLongPressingParameter_Microsoft_Maui_Controls_BindableObject_System_Object_" data-uid="DrawnUi.Draw.AddGestures.SetCommandLongPressingParameter(Microsoft.Maui.Controls.BindableObject,System.Object)">
  SetCommandLongPressingParameter(BindableObject, object)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L351"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetCommandLongPressingParameter(BindableObject view, object value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetCommandPressed_" data-uid="DrawnUi.Draw.AddGestures.SetCommandPressed*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetCommandPressed_Microsoft_Maui_Controls_BindableObject_System_Windows_Input_ICommand_" data-uid="DrawnUi.Draw.AddGestures.SetCommandPressed(Microsoft.Maui.Controls.BindableObject,System.Windows.Input.ICommand)">
  SetCommandPressed(BindableObject, ICommand)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L409"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetCommandPressed(BindableObject view, ICommand value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.windows.input.icommand">ICommand</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetCommandTapped_" data-uid="DrawnUi.Draw.AddGestures.SetCommandTapped*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetCommandTapped_Microsoft_Maui_Controls_BindableObject_System_Windows_Input_ICommand_" data-uid="DrawnUi.Draw.AddGestures.SetCommandTapped(Microsoft.Maui.Controls.BindableObject,System.Windows.Input.ICommand)">
  SetCommandTapped(BindableObject, ICommand)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L293"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetCommandTapped(BindableObject view, ICommand value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.windows.input.icommand">ICommand</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetCommandTappedParameter_" data-uid="DrawnUi.Draw.AddGestures.SetCommandTappedParameter*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetCommandTappedParameter_Microsoft_Maui_Controls_BindableObject_System_Object_" data-uid="DrawnUi.Draw.AddGestures.SetCommandTappedParameter(Microsoft.Maui.Controls.BindableObject,System.Object)">
  SetCommandTappedParameter(BindableObject, object)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L312"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetCommandTappedParameter(BindableObject view, object value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetLockPanning_" data-uid="DrawnUi.Draw.AddGestures.SetLockPanning*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetLockPanning_Microsoft_Maui_Controls_BindableObject_System_Boolean_" data-uid="DrawnUi.Draw.AddGestures.SetLockPanning(Microsoft.Maui.Controls.BindableObject,System.Boolean)">
  SetLockPanning(BindableObject, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L471"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetLockPanning(BindableObject view, bool value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetTouchEffectColor_" data-uid="DrawnUi.Draw.AddGestures.SetTouchEffectColor*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetTouchEffectColor_Microsoft_Maui_Controls_BindableObject_Microsoft_Maui_Graphics_Color_" data-uid="DrawnUi.Draw.AddGestures.SetTouchEffectColor(Microsoft.Maui.Controls.BindableObject,Microsoft.Maui.Graphics.Color)">
  SetTouchEffectColor(BindableObject, Color)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L452"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetTouchEffectColor(BindableObject view, Color value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_AddGestures_SetTransformView_" data-uid="DrawnUi.Draw.AddGestures.SetTransformView*"></a>

  <h3 id="DrawnUi_Draw_AddGestures_SetTransformView_Microsoft_Maui_Controls_BindableObject_System_Object_" data-uid="DrawnUi.Draw.AddGestures.SetTransformView(Microsoft.Maui.Controls.BindableObject,System.Object)">
  SetTransformView(BindableObject, object)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L368"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetTransformView(BindableObject view, object value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>













</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Gestures/AddGestures.cs/#L9" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
