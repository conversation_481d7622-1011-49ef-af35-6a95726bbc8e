<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class ViewsAdapter | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class ViewsAdapter | DrawnUi Documentation ">
      
      <meta name="description" content="Top level class for working with ItemTemplates. Holds visible views.">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ViewsAdapter.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ViewsAdapter%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.ViewsAdapter">



  <h1 id="DrawnUi_Draw_ViewsAdapter" data-uid="DrawnUi.Draw.ViewsAdapter" class="text-break">
Class ViewsAdapter  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>Top level class for working with ItemTemplates. Holds visible views.</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class ViewsAdapter : IDisposable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">ViewsAdapter</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_ViewsAdapter__ctor_" data-uid="DrawnUi.Draw.ViewsAdapter.#ctor*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter__ctor_DrawnUi_Draw_SkiaLayout_" data-uid="DrawnUi.Draw.ViewsAdapter.#ctor(DrawnUi.Draw.SkiaLayout)">
  ViewsAdapter(SkiaLayout)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L786"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ViewsAdapter(SkiaLayout parent)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a></dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_ViewsAdapter_IsDisposed" data-uid="DrawnUi.Draw.ViewsAdapter.IsDisposed">
  IsDisposed
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L783"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsDisposed</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_ViewsAdapter_LogEnabled" data-uid="DrawnUi.Draw.ViewsAdapter.LogEnabled">
  LogEnabled
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L13"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool LogEnabled</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_ViewsAdapter_TemplatesBusy" data-uid="DrawnUi.Draw.ViewsAdapter.TemplatesBusy">
  TemplatesBusy
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1096"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool TemplatesBusy</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_ViewsAdapter_TemplesInvalidating" data-uid="DrawnUi.Draw.ViewsAdapter.TemplesInvalidating">
  TemplesInvalidating
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1079"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool TemplesInvalidating</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_ViewsAdapter__lockTemplates" data-uid="DrawnUi.Draw.ViewsAdapter._lockTemplates">
  _lockTemplates
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1074"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected readonly object _lockTemplates</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_ViewsAdapter_AddedMore_" data-uid="DrawnUi.Draw.ViewsAdapter.AddedMore*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_AddedMore" data-uid="DrawnUi.Draw.ViewsAdapter.AddedMore">
  AddedMore
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L972"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int AddedMore { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ViewsAdapter_PoolMaxSize_" data-uid="DrawnUi.Draw.ViewsAdapter.PoolMaxSize*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_PoolMaxSize" data-uid="DrawnUi.Draw.ViewsAdapter.PoolMaxSize">
  PoolMaxSize
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L900"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int PoolMaxSize { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ViewsAdapter_PoolSize_" data-uid="DrawnUi.Draw.ViewsAdapter.PoolSize*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_PoolSize" data-uid="DrawnUi.Draw.ViewsAdapter.PoolSize">
  PoolSize
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L913"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int PoolSize { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ViewsAdapter_TemplatesAvailable_" data-uid="DrawnUi.Draw.ViewsAdapter.TemplatesAvailable*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_TemplatesAvailable" data-uid="DrawnUi.Draw.ViewsAdapter.TemplatesAvailable">
  TemplatesAvailable
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1114"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>An important check to consider before consuming templates especially if you initialize templates in background</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool TemplatesAvailable { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_ViewsAdapter_TemplatesInvalidated_" data-uid="DrawnUi.Draw.ViewsAdapter.TemplatesInvalidated*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_TemplatesInvalidated" data-uid="DrawnUi.Draw.ViewsAdapter.TemplatesInvalidated">
  TemplatesInvalidated
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1083"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool TemplatesInvalidated { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_ViewsAdapter_AddMoreToPool_" data-uid="DrawnUi.Draw.ViewsAdapter.AddMoreToPool*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_AddMoreToPool_System_Int32_" data-uid="DrawnUi.Draw.ViewsAdapter.AddMoreToPool(System.Int32)">
  AddMoreToPool(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L979"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Keep pool size with <code>n</code> templated more oversized, so when we suddenly need more templates they would already be ready, avoiding lag spike,
This method is likely to reserve templated views once on layout size changed.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddMoreToPool(int oversize)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>oversize</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_AttachView_" data-uid="DrawnUi.Draw.ViewsAdapter.AttachView*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_AttachView_DrawnUi_Draw_SkiaControl_System_Int32_System_Boolean_" data-uid="DrawnUi.Draw.ViewsAdapter.AttachView(DrawnUi.Draw.SkiaControl,System.Int32,System.Boolean)">
  AttachView(SkiaControl, int, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L839"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void AttachView(SkiaControl view, int index, bool isMeasuring)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>index</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>isMeasuring</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_Dispose_" data-uid="DrawnUi.Draw.ViewsAdapter.Dispose*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_Dispose" data-uid="DrawnUi.Draw.ViewsAdapter.Dispose">
  Dispose()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L791"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Dispose()</code></pre>
  </div>













  <a id="DrawnUi_Draw_ViewsAdapter_DisposeViews_" data-uid="DrawnUi.Draw.ViewsAdapter.DisposeViews*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_DisposeViews" data-uid="DrawnUi.Draw.ViewsAdapter.DisposeViews">
  DisposeViews()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L830"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void DisposeViews()</code></pre>
  </div>













  <a id="DrawnUi_Draw_ViewsAdapter_DisposeVisibleViews_" data-uid="DrawnUi.Draw.ViewsAdapter.DisposeVisibleViews*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_DisposeVisibleViews" data-uid="DrawnUi.Draw.ViewsAdapter.DisposeVisibleViews">
  DisposeVisibleViews()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L814"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void DisposeVisibleViews()</code></pre>
  </div>













  <a id="DrawnUi_Draw_ViewsAdapter_DisposeWrapper_" data-uid="DrawnUi.Draw.ViewsAdapter.DisposeWrapper*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_DisposeWrapper" data-uid="DrawnUi.Draw.ViewsAdapter.DisposeWrapper">
  DisposeWrapper()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1171"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DisposeWrapper()</code></pre>
  </div>













  <a id="DrawnUi_Draw_ViewsAdapter_FillPool_" data-uid="DrawnUi.Draw.ViewsAdapter.FillPool*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_FillPool_System_Int32_" data-uid="DrawnUi.Draw.ViewsAdapter.FillPool(System.Int32)">
  FillPool(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1023"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Use to manually pre-create views from item templates so when we suddenly need more templates they would already be ready, avoiding lag spike,
This will respect pool MaxSize in order not to overpass it.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void FillPool(int size)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>size</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_FillPool_" data-uid="DrawnUi.Draw.ViewsAdapter.FillPool*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_FillPool_System_Int32_System_Collections_IList_" data-uid="DrawnUi.Draw.ViewsAdapter.FillPool(System.Int32,System.Collections.IList)">
  FillPool(int, IList)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1003"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Use to manually pre-create views from item templates so when we suddenly need more templates they would already be ready, avoiding lag spike,
This will respect pool MaxSize in order not to overpass it.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void FillPool(int size, IList context)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>size</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>context</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_GetChildrenCount_" data-uid="DrawnUi.Draw.ViewsAdapter.GetChildrenCount*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_GetChildrenCount" data-uid="DrawnUi.Draw.ViewsAdapter.GetChildrenCount">
  GetChildrenCount()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1050"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetChildrenCount()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_ViewsAdapter_GetDebugInfo_" data-uid="DrawnUi.Draw.ViewsAdapter.GetDebugInfo*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_GetDebugInfo" data-uid="DrawnUi.Draw.ViewsAdapter.GetDebugInfo">
  GetDebugInfo()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1039"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string GetDebugInfo()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_ViewsAdapter_GetOrCreateViewForIndexInternal_" data-uid="DrawnUi.Draw.ViewsAdapter.GetOrCreateViewForIndexInternal*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_GetOrCreateViewForIndexInternal_System_Int32_System_Single_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.ViewsAdapter.GetOrCreateViewForIndexInternal(System.Int32,System.Single,DrawnUi.Draw.SkiaControl)">
  GetOrCreateViewForIndexInternal(int, float, SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L690"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetOrCreateViewForIndexInternal(int index, float height = 0, SkiaControl template = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>index</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>height</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>template</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_ViewsAdapter_GetTemplateInstance_" data-uid="DrawnUi.Draw.ViewsAdapter.GetTemplateInstance*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_GetTemplateInstance" data-uid="DrawnUi.Draw.ViewsAdapter.GetTemplateInstance">
  GetTemplateInstance()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1197"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetTemplateInstance()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_ViewsAdapter_GetViewForIndex_" data-uid="DrawnUi.Draw.ViewsAdapter.GetViewForIndex*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_GetViewForIndex_System_Int32_DrawnUi_Draw_SkiaControl_System_Single_System_Boolean_" data-uid="DrawnUi.Draw.ViewsAdapter.GetViewForIndex(System.Int32,DrawnUi.Draw.SkiaControl,System.Single,System.Boolean)">
  GetViewForIndex(int, SkiaControl, float, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L609"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Creates view from template and returns already existing view for a specific index.
This uses cached views and tends to return same views matching index they already used.
When cell recycling is off this will be a perfect match at all times.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetViewForIndex(int index, SkiaControl template = null, float height = 0, bool isMeasuring = false)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>index</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>template</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>height</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>isMeasuring</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_ViewsAdapter_GetViewsIterator_" data-uid="DrawnUi.Draw.ViewsAdapter.GetViewsIterator*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_GetViewsIterator" data-uid="DrawnUi.Draw.ViewsAdapter.GetViewsIterator">
  GetViewsIterator()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1124"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ViewsIterator GetViewsIterator()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.ViewsIterator.html">ViewsIterator</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_ViewsAdapter_InitializeTemplates_" data-uid="DrawnUi.Draw.ViewsAdapter.InitializeTemplates*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_InitializeTemplates_System_Collections_Specialized_NotifyCollectionChangedEventArgs_System_Func_System_Object__System_Collections_IList_System_Int32_System_Int32_" data-uid="DrawnUi.Draw.ViewsAdapter.InitializeTemplates(System.Collections.Specialized.NotifyCollectionChangedEventArgs,System.Func{System.Object},System.Collections.IList,System.Int32,System.Int32)">
  InitializeTemplates(NotifyCollectionChangedEventArgs, Func&lt;object&gt;, IList, int, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L24"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Main method to initialize templates, can use InitializeTemplatesInBackground as an option.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InitializeTemplates(NotifyCollectionChangedEventArgs args, Func&lt;object&gt; template, IList dataContexts, int poolSize, int reserve = 0)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>args</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs">NotifyCollectionChangedEventArgs</a></dt>
    <dd></dd>
    <dt><code>template</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-1">Func</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
    <dt><code>dataContexts</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a></dt>
    <dd></dd>
    <dt><code>poolSize</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>reserve</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>Pre-create number of views to avoid lag spikes later, useful to do in backgound.</p>
</dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_MarkAllViewsAsHidden_" data-uid="DrawnUi.Draw.ViewsAdapter.MarkAllViewsAsHidden*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_MarkAllViewsAsHidden" data-uid="DrawnUi.Draw.ViewsAdapter.MarkAllViewsAsHidden">
  MarkAllViewsAsHidden()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L932"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void MarkAllViewsAsHidden()</code></pre>
  </div>













  <a id="DrawnUi_Draw_ViewsAdapter_MarkViewAsHidden_" data-uid="DrawnUi.Draw.ViewsAdapter.MarkViewAsHidden*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_MarkViewAsHidden_System_Int32_" data-uid="DrawnUi.Draw.ViewsAdapter.MarkViewAsHidden(System.Int32)">
  MarkViewAsHidden(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L944"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void MarkViewAsHidden(int index)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>index</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_PrintDebugVisible_" data-uid="DrawnUi.Draw.ViewsAdapter.PrintDebugVisible*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_PrintDebugVisible" data-uid="DrawnUi.Draw.ViewsAdapter.PrintDebugVisible">
  PrintDebugVisible()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1183"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PrintDebugVisible()</code></pre>
  </div>













  <a id="DrawnUi_Draw_ViewsAdapter_ReleaseTemplateInstance_" data-uid="DrawnUi.Draw.ViewsAdapter.ReleaseTemplateInstance*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_ReleaseTemplateInstance_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Draw.ViewsAdapter.ReleaseTemplateInstance(DrawnUi.Draw.SkiaControl,System.Boolean)">
  ReleaseTemplateInstance(SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1217"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Returns standalone view, used for measuring to its own separate pool.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ReleaseTemplateInstance(SkiaControl viewModel, bool reset = false)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>viewModel</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>reset</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_ReleaseViewInUse_" data-uid="DrawnUi.Draw.ViewsAdapter.ReleaseViewInUse*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_ReleaseViewInUse_System_Int32_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.ViewsAdapter.ReleaseViewInUse(System.Int32,DrawnUi.Draw.SkiaControl)">
  ReleaseViewInUse(int, SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L588"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ReleaseViewInUse(int index, SkiaControl view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>index</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>view</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_ReleaseViewToPool_" data-uid="DrawnUi.Draw.ViewsAdapter.ReleaseViewToPool*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_ReleaseViewToPool_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Draw.ViewsAdapter.ReleaseViewToPool(DrawnUi.Draw.SkiaControl,System.Boolean)">
  ReleaseViewToPool(SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L728"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Retuns view to the POOL and set parent to null. Doesn't set BindingContext to null !</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ReleaseViewToPool(SkiaControl view, bool reset = false)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>reset</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_UpdateViews_" data-uid="DrawnUi.Draw.ViewsAdapter.UpdateViews*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_UpdateViews_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__" data-uid="DrawnUi.Draw.ViewsAdapter.UpdateViews(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})">
  UpdateViews(IEnumerable&lt;SkiaControl&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L1102"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UpdateViews(IEnumerable&lt;SkiaControl&gt; views = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>views</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_ViewsAdapter_UpdateVisibleViews_" data-uid="DrawnUi.Draw.ViewsAdapter.UpdateVisibleViews*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter_UpdateVisibleViews" data-uid="DrawnUi.Draw.ViewsAdapter.UpdateVisibleViews">
  UpdateVisibleViews()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L800"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void UpdateVisibleViews()</code></pre>
  </div>













  <a id="DrawnUi_Draw_ViewsAdapter__HandleSmartCollectionChange_" data-uid="DrawnUi.Draw.ViewsAdapter._HandleSmartCollectionChange*"></a>

  <h3 id="DrawnUi_Draw_ViewsAdapter__HandleSmartCollectionChange_System_Collections_Specialized_NotifyCollectionChangedEventArgs_System_Collections_IList_System_Int32_System_Int32_" data-uid="DrawnUi.Draw.ViewsAdapter._HandleSmartCollectionChange(System.Collections.Specialized.NotifyCollectionChangedEventArgs,System.Collections.IList,System.Int32,System.Int32)">
  _HandleSmartCollectionChange(NotifyCollectionChangedEventArgs, IList, int, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L276"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Enhanced collection change handling with validation and better error handling</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool _HandleSmartCollectionChange(NotifyCollectionChangedEventArgs args, IList newDataContexts, int poolSize, int reserve = 0)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>args</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs">NotifyCollectionChangedEventArgs</a></dt>
    <dd></dd>
    <dt><code>newDataContexts</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a></dt>
    <dd></dd>
    <dt><code>poolSize</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>reserve</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs/#L11" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
