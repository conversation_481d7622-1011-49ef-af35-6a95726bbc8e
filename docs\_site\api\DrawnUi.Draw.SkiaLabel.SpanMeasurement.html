<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaLabel.SpanMeasurement | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaLabel.SpanMeasurement | DrawnUi Documentation ">
      
      <meta name="description" content="Span-based measurement methods to avoid string allocations">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement">



  <h1 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement" class="text-break">
Class SkiaLabel.SpanMeasurement  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L10"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>Span-based measurement methods to avoid string allocations</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class SkiaLabel.SpanMeasurement</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">SkiaLabel.SpanMeasurement</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendChar_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendChar*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendChar_System_Text_StringBuilder_System_Char_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendChar(System.Text.StringBuilder,System.Char)">
  AppendChar(StringBuilder, char)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L106"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Appends a single character to StringBuilder</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendChar(StringBuilder sb, char c)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>sb</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.text.stringbuilder">StringBuilder</a></dt>
    <dd></dd>
    <dt><code>c</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendSpan_System_Text_StringBuilder_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendSpan(System.Text.StringBuilder,System.ReadOnlySpan{System.Char})">
  AppendSpan(StringBuilder, ReadOnlySpan&lt;char&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L96"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Appends a ReadOnlySpan to StringBuilder without intermediate string allocation</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendSpan(StringBuilder sb, ReadOnlySpan&lt;char&gt; span)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>sb</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.text.stringbuilder">StringBuilder</a></dt>
    <dd></dd>
    <dt><code>span</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsGlyphAlwaysAvailableSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsGlyphAlwaysAvailableSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsGlyphAlwaysAvailableSpan_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsGlyphAlwaysAvailableSpan(System.ReadOnlySpan{System.Char})">
  IsGlyphAlwaysAvailableSpan(ReadOnlySpan&lt;char&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L48"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Checks if glyph is always available without string conversion</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsGlyphAlwaysAvailableSpan(ReadOnlySpan&lt;char&gt; glyphSpan)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>glyphSpan</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsSpaceSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsSpaceSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsSpaceSpan_System_ReadOnlySpan_System_Char__System_Char_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsSpaceSpan(System.ReadOnlySpan{System.Char},System.Char)">
  IsSpaceSpan(ReadOnlySpan&lt;char&gt;, char)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L24"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Checks if a span represents a single space character</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsSpaceSpan(ReadOnlySpan&lt;char&gt; span, char spaceChar)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>span</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</dt>
    <dd></dd>
    <dt><code>spaceChar</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_LastNonSpaceIndexSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.LastNonSpaceIndexSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_LastNonSpaceIndexSpan_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.LastNonSpaceIndexSpan(System.ReadOnlySpan{System.Char})">
  LastNonSpaceIndexSpan(ReadOnlySpan&lt;char&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L33"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Finds the last non-space character index in a span</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int LastNonSpaceIndexSpan(ReadOnlySpan&lt;char&gt; textSpan)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>textSpan</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasurePartialTextWidthSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasurePartialTextWidthSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasurePartialTextWidthSpan_SkiaSharp_SKPaint_System_ReadOnlySpan_System_Char__System_Boolean_System_Single_SkiaSharp_SKTypeface_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasurePartialTextWidthSpan(SkiaSharp.SKPaint,System.ReadOnlySpan{System.Char},System.Boolean,System.Single,SkiaSharp.SKTypeface)">
  MeasurePartialTextWidthSpan(SKPaint, ReadOnlySpan&lt;char&gt;, bool, float, SKTypeface)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L69"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Measures partial text width using span-based operations where possible
Falls back to string conversion only for cache compatibility</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float MeasurePartialTextWidthSpan(SKPaint paint, ReadOnlySpan&lt;char&gt; textSpan, bool needsShaping, float scale, SKTypeface paintTypeface)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>paint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpaint">SKPaint</a></dt>
    <dd></dd>
    <dt><code>textSpan</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</dt>
    <dd></dd>
    <dt><code>needsShaping</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>paintTypeface</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface">SKTypeface</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasureTextWidthWithAdvanceSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasureTextWidthWithAdvanceSpan*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasureTextWidthWithAdvanceSpan_SkiaSharp_SKPaint_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasureTextWidthWithAdvanceSpan(SkiaSharp.SKPaint,System.ReadOnlySpan{System.Char})">
  MeasureTextWidthWithAdvanceSpan(SKPaint, ReadOnlySpan&lt;char&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L15"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Measures text width using ReadOnlySpan without converting to string</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float MeasureTextWidthWithAdvanceSpan(SKPaint paint, ReadOnlySpan&lt;char&gt; textSpan)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>paint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpaint">SKPaint</a></dt>
    <dd></dd>
    <dt><code>textSpan</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_SpanToStringForCache_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.SpanToStringForCache*"></a>

  <h3 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_SpanToStringForCache_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.SpanToStringForCache(System.ReadOnlySpan{System.Char})">
  SpanToStringForCache(ReadOnlySpan&lt;char&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L58"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Converts ReadOnlySpan to string only when absolutely necessary for cache keys
This is the only place where we allow span-to-string conversion for caching</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string SpanToStringForCache(ReadOnlySpan&lt;char&gt; span)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>span</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L10" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
