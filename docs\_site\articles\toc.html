
<div id="sidetoggle">
  <div>
    <div class="sidefilter">
      <form class="toc-filter">
        <span class="glyphicon glyphicon-filter filter-icon"></span>
        <span class="glyphicon glyphicon-remove clear-icon" id="toc_filter_clear"></span>
        <input type="text" id="toc_filter_input" placeholder="Filter by title" onkeypress="if(event.keyCode==13) {return false;}">
      </form>
    </div>
    <div class="sidetoc">
      <div class="toc" id="toc">

          <ul class="nav level1">
                <li>
                    <a href="index.html" name="" title="Home">Home</a>
                </li>
                <li>
                    <span class="expand-stub"></span>
                    <a>Getting Started</a>

                    <ul class="nav level2">
                          <li>
                              <a href="getting-started.html" name="" title="Installation and Setup">Installation and Setup</a>
                          </li>
                          <li>
                              <a href="first-app.html" name="" title="Your First DrawnUi App">Your First DrawnUi App</a>
                          </li>
                          <li>
                              <a href="porting-maui.html" name="" title="Porting Native to Drawn">Porting Native to Drawn</a>
                          </li>
                    </ul>
                </li>
                <li>
                    <a href="samples.html" name="" title="Samples">Samples</a>
                </li>
                <li>
                    <span class="expand-stub"></span>
                    <a href="controls/index.html" name="" title="Controls">Controls</a>

                    <ul class="nav level2">
                          <li>
                              <a href="controls/index.html" name="" title="Controls Overview">Controls Overview</a>
                          </li>
                          <li>
                              <a href="controls/buttons.html" name="" title="Buttons">Buttons</a>
                          </li>
                          <li>
                              <a href="controls/switches.html" name="" title="Switches and Toggles">Switches and Toggles</a>
                          </li>
                          <li>
                              <a href="controls/input.html" name="" title="Input Controls">Input Controls</a>
                          </li>
                          <li>
                              <a href="controls/layouts.html" name="" title="Layout Controls">Layout Controls</a>
                          </li>
                          <li>
                              <a href="controls/scroll.html" name="" title="Scroll Views">Scroll Views</a>
                          </li>
                          <li>
                              <a href="controls/carousels.html" name="" title="Carousels">Carousels</a>
                          </li>
                          <li>
                              <a href="controls/drawers.html" name="" title="Drawers">Drawers</a>
                          </li>
                          <li>
                              <a href="controls/native-integration.html" name="" title="Native Integration">Native Integration</a>
                          </li>
                          <li>
                              <a href="controls/shapes.html" name="" title="Shapes">Shapes</a>
                          </li>
                          <li>
                              <a href="controls/text.html" name="" title="Text and Labels">Text and Labels</a>
                          </li>
                          <li>
                              <a href="controls/images.html" name="" title="Images">Images</a>
                          </li>
                          <li>
                              <a href="controls/sprites.html" name="" title="Sprites">Sprites</a>
                          </li>
                          <li>
                              <a href="controls/animations.html" name="" title="Animations">Animations</a>
                          </li>
                          <li>
                              <a href="controls/shell.html" name="" title="Navigation Shell">Navigation Shell</a>
                          </li>
                    </ul>
                </li>
                <li>
                    <span class="expand-stub"></span>
                    <a href="advanced/index.html" name="" title="Advanced">Advanced</a>

                    <ul class="nav level2">
                          <li>
                              <a href="drawing-pipeline.html" name="" title="Drawing Pipeline">Drawing Pipeline</a>
                          </li>
                          <li>
                              <a href="fluent-extensions.html" name="" title="Fluent C# Extensions">Fluent C# Extensions</a>
                          </li>
                          <li>
                              <a href="advanced/platform-styling.html" name="" title="Platform-Specific Styling">Platform-Specific Styling</a>
                          </li>
                          <li>
                              <a href="advanced/layout-system.html" name="" title="Layout System Architecture">Layout System Architecture</a>
                          </li>
                          <li>
                              <a href="advanced/gradients.html" name="" title="Gradients">Gradients</a>
                          </li>
                          <li>
                              <a href="advanced/game-ui.html" name="" title="Game UI &amp; Interactive Games">Game UI &amp; Interactive Games</a>
                          </li>
                          <li>
                              <a href="advanced/skiascroll.html" name="" title="SkiaScroll &amp; Virtualization">SkiaScroll &amp; Virtualization</a>
                          </li>
                          <li>
                              <a href="advanced/gestures.html" name="" title="Gestures &amp; Touch Input">Gestures &amp; Touch Input</a>
                          </li>
                    </ul>
                </li>
                <li>
                    <a href="../api/DrawnUi.html" name="../api/toc.html" title="API Documentation">API Documentation</a>
                </li>
          </ul>
      </div>
    </div>
  </div>
</div>
