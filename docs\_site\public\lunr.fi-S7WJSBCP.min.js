import{a as Z}from"./chunk-OSRY5VT3.min.js";var H=Z((b,v)=>{(function(t,e){typeof define=="function"&&define.amd?define(e):typeof b=="object"?v.exports=e():e()(t.lunr)})(b,function(){return function(t){if(typeof t>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof t.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");t.fi=function(){this.pipeline.reset(),this.pipeline.add(t.fi.trimmer,t.fi.stopWordFilter,t.fi.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(t.fi.stemmer))},t.fi.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",t.fi.trimmer=t.trimmerSupport.generateTrimmer(t.fi.wordCharacters),t.Pipeline.registerFunction(t.fi.trimmer,"trimmer-fi"),t.fi.stemmer=function(){var e=t.stemmerSupport.Among,E=t.stemmerSupport.SnowballProgram,l=new function(){var _=[new e("pa",-1,1),new e("sti",-1,2),new e("kaan",-1,1),new e("han",-1,1),new e("kin",-1,1),new e("h\xE4n",-1,1),new e("k\xE4\xE4n",-1,1),new e("ko",-1,1),new e("p\xE4",-1,1),new e("k\xF6",-1,1)],F=[new e("lla",-1,-1),new e("na",-1,-1),new e("ssa",-1,-1),new e("ta",-1,-1),new e("lta",3,-1),new e("sta",3,-1)],j=[new e("ll\xE4",-1,-1),new e("n\xE4",-1,-1),new e("ss\xE4",-1,-1),new e("t\xE4",-1,-1),new e("lt\xE4",3,-1),new e("st\xE4",3,-1)],A=[new e("lle",-1,-1),new e("ine",-1,-1)],q=[new e("nsa",-1,3),new e("mme",-1,3),new e("nne",-1,3),new e("ni",-1,2),new e("si",-1,1),new e("an",-1,4),new e("en",-1,6),new e("\xE4n",-1,5),new e("ns\xE4",-1,3)],C=[new e("aa",-1,-1),new e("ee",-1,-1),new e("ii",-1,-1),new e("oo",-1,-1),new e("uu",-1,-1),new e("\xE4\xE4",-1,-1),new e("\xF6\xF6",-1,-1)],B=[new e("a",-1,8),new e("lla",0,-1),new e("na",0,-1),new e("ssa",0,-1),new e("ta",0,-1),new e("lta",4,-1),new e("sta",4,-1),new e("tta",4,9),new e("lle",-1,-1),new e("ine",-1,-1),new e("ksi",-1,-1),new e("n",-1,7),new e("han",11,1),new e("den",11,-1,f),new e("seen",11,-1,k),new e("hen",11,2),new e("tten",11,-1,f),new e("hin",11,3),new e("siin",11,-1,f),new e("hon",11,4),new e("h\xE4n",11,5),new e("h\xF6n",11,6),new e("\xE4",-1,8),new e("ll\xE4",22,-1),new e("n\xE4",22,-1),new e("ss\xE4",22,-1),new e("t\xE4",22,-1),new e("lt\xE4",26,-1),new e("st\xE4",26,-1),new e("tt\xE4",26,9)],D=[new e("eja",-1,-1),new e("mma",-1,1),new e("imma",1,-1),new e("mpa",-1,1),new e("impa",3,-1),new e("mmi",-1,1),new e("immi",5,-1),new e("mpi",-1,1),new e("impi",7,-1),new e("ej\xE4",-1,-1),new e("mm\xE4",-1,1),new e("imm\xE4",10,-1),new e("mp\xE4",-1,1),new e("imp\xE4",12,-1)],P=[new e("i",-1,-1),new e("j",-1,-1)],S=[new e("mma",-1,1),new e("imma",0,-1)],x=[17,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8],a=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,8,0,32],y=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,0,8,0,32],L=[17,97,24,1,0,0,0,0,0,0,0,0,0,0,0,0,8,0,32],w,d,o,u,i=new E;this.setCurrent=function(r){i.setCurrent(r)},this.getCurrent=function(){return i.getCurrent()};function W(){u=i.limit,o=u,h()||(u=i.cursor,h()||(o=i.cursor))}function h(){for(var r;r=i.cursor,!i.in_grouping(a,97,246);){if(i.cursor=r,r>=i.limit)return!0;i.cursor++}for(i.cursor=r;!i.out_grouping(a,97,246);){if(i.cursor>=i.limit)return!0;i.cursor++}return!1}function I(){return o<=i.cursor}function V(){var r,n;if(i.cursor>=u)if(n=i.limit_backward,i.limit_backward=u,i.ket=i.cursor,r=i.find_among_b(_,10),r){switch(i.bra=i.cursor,i.limit_backward=n,r){case 1:if(!i.in_grouping_b(L,97,246))return;break;case 2:if(!I())return;break}i.slice_del()}else i.limit_backward=n}function z(){var r,n,s;if(i.cursor>=u)if(n=i.limit_backward,i.limit_backward=u,i.ket=i.cursor,r=i.find_among_b(q,9),r)switch(i.bra=i.cursor,i.limit_backward=n,r){case 1:s=i.limit-i.cursor,i.eq_s_b(1,"k")||(i.cursor=i.limit-s,i.slice_del());break;case 2:i.slice_del(),i.ket=i.cursor,i.eq_s_b(3,"kse")&&(i.bra=i.cursor,i.slice_from("ksi"));break;case 3:i.slice_del();break;case 4:i.find_among_b(F,6)&&i.slice_del();break;case 5:i.find_among_b(j,6)&&i.slice_del();break;case 6:i.find_among_b(A,2)&&i.slice_del();break}else i.limit_backward=n}function k(){return i.find_among_b(C,7)}function f(){return i.eq_s_b(1,"i")&&i.in_grouping_b(y,97,246)}function G(){var r,n,s;if(i.cursor>=u)if(n=i.limit_backward,i.limit_backward=u,i.ket=i.cursor,r=i.find_among_b(B,30),r){switch(i.bra=i.cursor,i.limit_backward=n,r){case 1:if(!i.eq_s_b(1,"a"))return;break;case 2:case 9:if(!i.eq_s_b(1,"e"))return;break;case 3:if(!i.eq_s_b(1,"i"))return;break;case 4:if(!i.eq_s_b(1,"o"))return;break;case 5:if(!i.eq_s_b(1,"\xE4"))return;break;case 6:if(!i.eq_s_b(1,"\xF6"))return;break;case 7:if(s=i.limit-i.cursor,!k()&&(i.cursor=i.limit-s,!i.eq_s_b(2,"ie"))){i.cursor=i.limit-s;break}if(i.cursor=i.limit-s,i.cursor<=i.limit_backward){i.cursor=i.limit-s;break}i.cursor--,i.bra=i.cursor;break;case 8:if(!i.in_grouping_b(a,97,246)||!i.out_grouping_b(a,97,246))return;break}i.slice_del(),w=!0}else i.limit_backward=n}function N(){var r,n,s;if(i.cursor>=o)if(n=i.limit_backward,i.limit_backward=o,i.ket=i.cursor,r=i.find_among_b(D,14),r){if(i.bra=i.cursor,i.limit_backward=n,r==1){if(s=i.limit-i.cursor,i.eq_s_b(2,"po"))return;i.cursor=i.limit-s}i.slice_del()}else i.limit_backward=n}function O(){var r;i.cursor>=u&&(r=i.limit_backward,i.limit_backward=u,i.ket=i.cursor,i.find_among_b(P,2)?(i.bra=i.cursor,i.limit_backward=r,i.slice_del()):i.limit_backward=r)}function R(){var r,n,s,c,p,g;if(i.cursor>=u){if(n=i.limit_backward,i.limit_backward=u,i.ket=i.cursor,i.eq_s_b(1,"t")&&(i.bra=i.cursor,s=i.limit-i.cursor,i.in_grouping_b(a,97,246)&&(i.cursor=i.limit-s,i.slice_del(),i.limit_backward=n,c=i.limit-i.cursor,i.cursor>=o&&(i.cursor=o,p=i.limit_backward,i.limit_backward=i.cursor,i.cursor=i.limit-c,i.ket=i.cursor,r=i.find_among_b(S,2),r)))){if(i.bra=i.cursor,i.limit_backward=p,r==1){if(g=i.limit-i.cursor,i.eq_s_b(2,"po"))return;i.cursor=i.limit-g}i.slice_del();return}i.limit_backward=n}}function T(){var r,n,s,c;if(i.cursor>=u){for(r=i.limit_backward,i.limit_backward=u,n=i.limit-i.cursor,k()&&(i.cursor=i.limit-n,i.ket=i.cursor,i.cursor>i.limit_backward&&(i.cursor--,i.bra=i.cursor,i.slice_del())),i.cursor=i.limit-n,i.ket=i.cursor,i.in_grouping_b(x,97,228)&&(i.bra=i.cursor,i.out_grouping_b(a,97,246)&&i.slice_del()),i.cursor=i.limit-n,i.ket=i.cursor,i.eq_s_b(1,"j")&&(i.bra=i.cursor,s=i.limit-i.cursor,i.eq_s_b(1,"o")?i.slice_del():(i.cursor=i.limit-s,i.eq_s_b(1,"u")&&i.slice_del())),i.cursor=i.limit-n,i.ket=i.cursor,i.eq_s_b(1,"o")&&(i.bra=i.cursor,i.eq_s_b(1,"j")&&i.slice_del()),i.cursor=i.limit-n,i.limit_backward=r;;){if(c=i.limit-i.cursor,i.out_grouping_b(a,97,246)){i.cursor=i.limit-c;break}if(i.cursor=i.limit-c,i.cursor<=i.limit_backward)return;i.cursor--}i.ket=i.cursor,i.cursor>i.limit_backward&&(i.cursor--,i.bra=i.cursor,d=i.slice_to(),i.eq_v_b(d)&&i.slice_del())}}this.stem=function(){var r=i.cursor;return W(),w=!1,i.limit_backward=r,i.cursor=i.limit,V(),i.cursor=i.limit,z(),i.cursor=i.limit,G(),i.cursor=i.limit,N(),i.cursor=i.limit,w?(O(),i.cursor=i.limit):(i.cursor=i.limit,R(),i.cursor=i.limit),T(),!0}};return function(m){return typeof m.update=="function"?m.update(function(_){return l.setCurrent(_),l.stem(),l.getCurrent()}):(l.setCurrent(m),l.stem(),l.getCurrent())}}(),t.Pipeline.registerFunction(t.fi.stemmer,"stemmer-fi"),t.fi.stopWordFilter=t.generateStopWordFilter("ei eiv\xE4t emme en et ette ett\xE4 he heid\xE4n heid\xE4t heihin heille heill\xE4 heilt\xE4 heiss\xE4 heist\xE4 heit\xE4 h\xE4n h\xE4neen h\xE4nelle h\xE4nell\xE4 h\xE4nelt\xE4 h\xE4nen h\xE4ness\xE4 h\xE4nest\xE4 h\xE4net h\xE4nt\xE4 itse ja johon joiden joihin joiksi joilla joille joilta joina joissa joista joita joka joksi jolla jolle jolta jona jonka jos jossa josta jota jotka kanssa keiden keihin keiksi keille keill\xE4 keilt\xE4 kein\xE4 keiss\xE4 keist\xE4 keit\xE4 keneen keneksi kenelle kenell\xE4 kenelt\xE4 kenen kenen\xE4 keness\xE4 kenest\xE4 kenet ketk\xE4 ketk\xE4 ket\xE4 koska kuin kuka kun me meid\xE4n meid\xE4t meihin meille meill\xE4 meilt\xE4 meiss\xE4 meist\xE4 meit\xE4 mihin miksi mik\xE4 mille mill\xE4 milt\xE4 mink\xE4 mink\xE4 minua minulla minulle minulta minun minussa minusta minut minuun min\xE4 min\xE4 miss\xE4 mist\xE4 mitk\xE4 mit\xE4 mukaan mutta ne niiden niihin niiksi niille niill\xE4 niilt\xE4 niin niin niin\xE4 niiss\xE4 niist\xE4 niit\xE4 noiden noihin noiksi noilla noille noilta noin noina noissa noista noita nuo nyt n\xE4iden n\xE4ihin n\xE4iksi n\xE4ille n\xE4ill\xE4 n\xE4ilt\xE4 n\xE4in\xE4 n\xE4iss\xE4 n\xE4ist\xE4 n\xE4it\xE4 n\xE4m\xE4 ole olemme olen olet olette oli olimme olin olisi olisimme olisin olisit olisitte olisivat olit olitte olivat olla olleet ollut on ovat poikki se sek\xE4 sen siihen siin\xE4 siit\xE4 siksi sille sill\xE4 sill\xE4 silt\xE4 sinua sinulla sinulle sinulta sinun sinussa sinusta sinut sinuun sin\xE4 sin\xE4 sit\xE4 tai te teid\xE4n teid\xE4t teihin teille teill\xE4 teilt\xE4 teiss\xE4 teist\xE4 teit\xE4 tuo tuohon tuoksi tuolla tuolle tuolta tuon tuona tuossa tuosta tuota t\xE4h\xE4n t\xE4ksi t\xE4lle t\xE4ll\xE4 t\xE4lt\xE4 t\xE4m\xE4 t\xE4m\xE4n t\xE4n\xE4 t\xE4ss\xE4 t\xE4st\xE4 t\xE4t\xE4 vaan vai vaikka yli".split(" ")),t.Pipeline.registerFunction(t.fi.stopWordFilter,"stopWordFilter-fi")}})});export default H();
/*! Bundled license information:

lunr-languages/lunr.fi.js:
  (*!
   * Lunr languages, `Finnish` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.fi-S7WJSBCP.min.js.map
