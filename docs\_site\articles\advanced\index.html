<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Advanced Topics | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Advanced Topics | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="../toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/index.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="advanced-topics">Advanced Topics</h1>

<p>This section covers advanced features and concepts for DrawnUi development.</p>
<h2 id="architecture-and-performance">Architecture and Performance</h2>
<ul>
<li><a href="layout-system.html">Layout System Architecture</a> - Deep dive into how the layout system works</li>
<li><a href="platform-styling.html">Platform-Specific Styling</a> - Creating platform-specific UI styles</li>
</ul>
<h2 id="visual-features">Visual Features</h2>
<ul>
<li><a href="gradients.html">Gradients</a> - Creating and using gradient effects</li>
<li><a href="skiascroll.html">SkiaScroll &amp; Virtualization</a> - Advanced scrolling and performance optimization</li>
</ul>
<h2 id="interaction">Interaction</h2>
<ul>
<li><a href="gestures.html">Gestures &amp; Touch Input</a> - Handling complex touch interactions</li>
</ul>
<h2 id="specialized-use-cases">Specialized Use Cases</h2>
<ul>
<li><a href="game-ui.html">Game UI &amp; Interactive Games</a> - Building game interfaces and interactive experiences</li>
</ul>
<h2 id="best-practices">Best Practices</h2>
<p>When working with advanced DrawnUi features:</p>
<ol>
<li><strong>Performance</strong>: Use appropriate caching strategies for your use case</li>
<li><strong>Platform Differences</strong>: Test platform-specific features on all target platforms</li>
<li><strong>Memory Management</strong>: Be mindful of resource usage, especially with images and animations</li>
<li><strong>Touch Handling</strong>: Understand the gesture system for complex interactions</li>
</ol>
<h2 id="getting-help">Getting Help</h2>
<p>For advanced topics not covered here, check:</p>
<ul>
<li>The source code examples in the Sandbox project</li>
<li>Community discussions and issues on GitHub</li>
<li>The API documentation for detailed method signatures</li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/index.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
