<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class VisualTreeHandler | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class VisualTreeHandler | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_VisualTreeHandler.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.VisualTreeHandler%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.VisualTreeHandler">



  <h1 id="DrawnUi_Draw_VisualTreeHandler" data-uid="DrawnUi.Draw.VisualTreeHandler" class="text-break">
Class VisualTreeHandler  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class VisualTreeHandler</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">VisualTreeHandler</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_VisualTreeHandler_ActiveTree" data-uid="DrawnUi.Draw.VisualTreeHandler.ActiveTree">
  ActiveTree
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L86"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This is used for rendering</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected VisualLayer ActiveTree</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_VisualTreeHandler_PreparedTree" data-uid="DrawnUi.Draw.VisualTreeHandler.PreparedTree">
  PreparedTree
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L91"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This is prepared and can be used to replace ActiveTree</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected VisualLayer PreparedTree</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_VisualTreeHandler_IsReady_" data-uid="DrawnUi.Draw.VisualTreeHandler.IsReady*"></a>

  <h3 id="DrawnUi_Draw_VisualTreeHandler_IsReady" data-uid="DrawnUi.Draw.VisualTreeHandler.IsReady">
  IsReady
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L67"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsReady { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_VisualTreeHandler_WasRendered_" data-uid="DrawnUi.Draw.VisualTreeHandler.WasRendered*"></a>

  <h3 id="DrawnUi_Draw_VisualTreeHandler_WasRendered" data-uid="DrawnUi.Draw.VisualTreeHandler.WasRendered">
  WasRendered
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L75"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool WasRendered { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_VisualTreeHandler_DumpActiveTree_" data-uid="DrawnUi.Draw.VisualTreeHandler.DumpActiveTree*"></a>

  <h3 id="DrawnUi_Draw_VisualTreeHandler_DumpActiveTree" data-uid="DrawnUi.Draw.VisualTreeHandler.DumpActiveTree">
  DumpActiveTree()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L15"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DumpActiveTree()</code></pre>
  </div>













  <a id="DrawnUi_Draw_VisualTreeHandler_DumpPreparedTree_" data-uid="DrawnUi.Draw.VisualTreeHandler.DumpPreparedTree*"></a>

  <h3 id="DrawnUi_Draw_VisualTreeHandler_DumpPreparedTree" data-uid="DrawnUi.Draw.VisualTreeHandler.DumpPreparedTree">
  DumpPreparedTree()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L9"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DumpPreparedTree()</code></pre>
  </div>













  <a id="DrawnUi_Draw_VisualTreeHandler_DumpTree_" data-uid="DrawnUi.Draw.VisualTreeHandler.DumpTree*"></a>

  <h3 id="DrawnUi_Draw_VisualTreeHandler_DumpTree_DrawnUi_Draw_VisualLayer_System_String_System_Boolean_System_Int32_" data-uid="DrawnUi.Draw.VisualTreeHandler.DumpTree(DrawnUi.Draw.VisualLayer,System.String,System.Boolean,System.Int32)">
  DumpTree(VisualLayer, string, bool, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L21"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DumpTree(VisualLayer node, string prefix = &quot;&quot;, bool isLast = true, int level = 0)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>node</code> <a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></dt>
    <dd></dd>
    <dt><code>prefix</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>isLast</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>level</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_VisualTreeHandler_PrepareRenderingTree_" data-uid="DrawnUi.Draw.VisualTreeHandler.PrepareRenderingTree*"></a>

  <h3 id="DrawnUi_Draw_VisualTreeHandler_PrepareRenderingTree_DrawnUi_Draw_DrawingContext_System_Single_System_Single_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.VisualTreeHandler.PrepareRenderingTree(DrawnUi.Draw.DrawingContext,System.Single,System.Single,DrawnUi.Draw.SkiaControl)">
  PrepareRenderingTree(DrawingContext, float, float, SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L98"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>STEP 1 (or Background thread) prepare rendering tree that will be used for rendering later.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PrepareRenderingTree(DrawingContext context, float widthRequest, float heightRequest, SkiaControl root)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
    <dt><code>widthRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>heightRequest</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>root</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_VisualTreeHandler_Render_" data-uid="DrawnUi.Draw.VisualTreeHandler.Render*"></a>

  <h3 id="DrawnUi_Draw_VisualTreeHandler_Render_DrawnUi_Draw_DrawingContext_" data-uid="DrawnUi.Draw.VisualTreeHandler.Render(DrawnUi.Draw.DrawingContext)">
  Render(DrawingContext)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L112"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>STEP 2 (or Main thread) use prepared rendering tree to draw its nodes</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Render(DrawingContext context)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_VisualTreeHandler_RenderTreeInternal_" data-uid="DrawnUi.Draw.VisualTreeHandler.RenderTreeInternal*"></a>

  <h3 id="DrawnUi_Draw_VisualTreeHandler_RenderTreeInternal_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_VisualLayer_" data-uid="DrawnUi.Draw.VisualTreeHandler.RenderTreeInternal(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.VisualLayer)">
  RenderTreeInternal(DrawingContext, VisualLayer)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L128"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Used by STEP 2 RenderTree method</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void RenderTreeInternal(DrawingContext context, VisualLayer node)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>context</code> <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></dt>
    <dd></dd>
    <dt><code>node</code> <a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></dt>
    <dd></dd>
  </dl>













</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Base/VisualTreeHandler.cs/#L5" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
