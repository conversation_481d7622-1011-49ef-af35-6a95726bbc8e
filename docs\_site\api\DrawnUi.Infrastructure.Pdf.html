<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class Pdf | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class Pdf | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Infrastructure_Pdf.md&amp;value=---%0A<PERSON>%3A%20DrawnUi.Infrastructure.Pdf%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Infrastructure.Pdf">



  <h1 id="DrawnUi_Infrastructure_Pdf" data-uid="DrawnUi.Infrastructure.Pdf" class="text-break">
Class Pdf  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Pdf/Pdf.cs/#L35"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Infrastructure.html">Infrastructure</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class Pdf</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">Pdf</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Infrastructure_Pdf_GetPaperSizeInInches_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizeInInches*"></a>

  <h3 id="DrawnUi_Infrastructure_Pdf_GetPaperSizeInInches_DrawnUi_Infrastructure_PaperFormat_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizeInInches(DrawnUi.Infrastructure.PaperFormat)">
  GetPaperSizeInInches(PaperFormat)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Pdf/Pdf.cs/#L244"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the paper size in inches for a given paper format.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKSize GetPaperSizeInInches(PaperFormat format)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>format</code> <a class="xref" href="DrawnUi.Infrastructure.PaperFormat.html">PaperFormat</a></dt>
    <dd><p>The paper format.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The paper size in inches as an SKSize.</p>
</dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pdf_GetPaperSizePixels_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizePixels*"></a>

  <h3 id="DrawnUi_Infrastructure_Pdf_GetPaperSizePixels_DrawnUi_Infrastructure_PaperFormat_System_Int32_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizePixels(DrawnUi.Infrastructure.PaperFormat,System.Int32)">
  GetPaperSizePixels(PaperFormat, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Pdf/Pdf.cs/#L43"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the paper size in pixels for a given paper format and DPI.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKSize GetPaperSizePixels(PaperFormat format, int dpi)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>format</code> <a class="xref" href="DrawnUi.Infrastructure.PaperFormat.html">PaperFormat</a></dt>
    <dd><p>The paper format.</p>
</dd>
    <dt><code>dpi</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The dots per inch (DPI) value.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The paper size in pixels as an SKSize.</p>
</dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pdf_GetPaperSizePixels_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizePixels*"></a>

  <h3 id="DrawnUi_Infrastructure_Pdf_GetPaperSizePixels_SkiaSharp_SKSize_System_Int32_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizePixels(SkiaSharp.SKSize,System.Int32)">
  GetPaperSizePixels(SKSize, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Pdf/Pdf.cs/#L55"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the paper size in pixels for a custom paper size in inches and DPI.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKSize GetPaperSizePixels(SKSize paperSizeInInches, int dpi)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>paperSizeInInches</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The paper size in inches.</p>
</dd>
    <dt><code>dpi</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The dots per inch (DPI) value.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The paper size in pixels as an SKSize.</p>
</dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pdf_GetPaperSizePixels_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizePixels*"></a>

  <h3 id="DrawnUi_Infrastructure_Pdf_GetPaperSizePixels_SkiaSharp_SKSize_System_Single_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizePixels(SkiaSharp.SKSize,System.Single)">
  GetPaperSizePixels(SKSize, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Pdf/Pdf.cs/#L232"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Calculates the paper size in pixels based on the paper size in inches and DPI.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKSize GetPaperSizePixels(SKSize paperSizeInInches, float dpi)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>paperSizeInInches</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The paper size in inches.</p>
</dd>
    <dt><code>dpi</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>The dots per inch (DPI) value.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The paper size in pixels as an SKSize.</p>
</dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pdf_GetPaperSizePixelsFromMillimeters_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizePixelsFromMillimeters*"></a>

  <h3 id="DrawnUi_Infrastructure_Pdf_GetPaperSizePixelsFromMillimeters_SkiaSharp_SKSize_System_Int32_" data-uid="DrawnUi.Infrastructure.Pdf.GetPaperSizePixelsFromMillimeters(SkiaSharp.SKSize,System.Int32)">
  GetPaperSizePixelsFromMillimeters(SKSize, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Pdf/Pdf.cs/#L68"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the paper size in pixels for a custom paper size in millimeters and DPI.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKSize GetPaperSizePixelsFromMillimeters(SKSize paperSizeInMillimeters, int dpi)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>paperSizeInMillimeters</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The paper size in millimeters.</p>
</dd>
    <dt><code>dpi</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd><p>The dots per inch (DPI) value.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The paper size in pixels as an SKSize.</p>
</dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pdf_SplitStackToPages_" data-uid="DrawnUi.Infrastructure.Pdf.SplitStackToPages*"></a>

  <h3 id="DrawnUi_Infrastructure_Pdf_SplitStackToPages_DrawnUi_Draw_SkiaControl_System_Boolean_SkiaSharp_SKSize_System_Single_" data-uid="DrawnUi.Infrastructure.Pdf.SplitStackToPages(DrawnUi.Draw.SkiaControl,System.Boolean,SkiaSharp.SKSize,System.Single)">
  SplitStackToPages(SkiaControl, bool, SKSize, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Pdf/Pdf.cs/#L157"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Pages will be split upon first found vertical stick children.
Must specify if stack is templated.
If no stack is found will split to pages as usual.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static List&lt;PdfPagePosition&gt; SplitStackToPages(SkiaControl control, bool isTemplated, SKSize paper, float scale = 1)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>isTemplated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>paper</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Infrastructure.PdfPagePosition.html">PdfPagePosition</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pdf_SplitToPages_" data-uid="DrawnUi.Infrastructure.Pdf.SplitToPages*"></a>

  <h3 id="DrawnUi_Infrastructure_Pdf_SplitToPages_SkiaSharp_SKSize_SkiaSharp_SKSize_" data-uid="DrawnUi.Infrastructure.Pdf.SplitToPages(SkiaSharp.SKSize,SkiaSharp.SKSize)">
  SplitToPages(SKSize, SKSize)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Pdf/Pdf.cs/#L80"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Splits a SkiaStack content into multiple pages based on the provided paper size, considering height only.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static List&lt;PdfPagePosition&gt; SplitToPages(SKSize content, SKSize paper)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>content</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The size of the content to be split.</p>
</dd>
    <dt><code>paper</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sksize">SKSize</a></dt>
    <dd><p>The size of the paper to split the content into.</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Infrastructure.PdfPagePosition.html">PdfPagePosition</a>&gt;</dt>
    <dd><p>A list of PdfPagePosition representing the positions of the pages.</p>
</dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Pdf/Pdf.cs/#L35" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
