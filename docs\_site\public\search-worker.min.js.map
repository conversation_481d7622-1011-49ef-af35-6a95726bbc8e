{"version": 3, "sources": ["../../node_modules/lunr/lunr.js", "../../node_modules/lunr-languages/lunr.stemmer.support.js", "../../node_modules/lunr-languages/tinyseg.js", "../../node_modules/lunr-languages/lunr.multi.js", "../src/search-worker.ts", "../../node_modules/idb-keyval/dist/index.js"], "sourcesContent": ["/**\n * lunr - http://lunrjs.com - A bit like Solr, but much smaller and not as bright - 2.3.9\n * Copyright (C) 2020 Oliver Nightingale\n * @license MIT\n */\n\n;(function(){\n\n/**\n * A convenience function for configuring and constructing\n * a new lunr Index.\n *\n * A lunr.Builder instance is created and the pipeline setup\n * with a trimmer, stop word filter and stemmer.\n *\n * This builder object is yielded to the configuration function\n * that is passed as a parameter, allowing the list of fields\n * and other builder parameters to be customised.\n *\n * All documents _must_ be added within the passed config function.\n *\n * @example\n * var idx = lunr(function () {\n *   this.field('title')\n *   this.field('body')\n *   this.ref('id')\n *\n *   documents.forEach(function (doc) {\n *     this.add(doc)\n *   }, this)\n * })\n *\n * @see {@link lunr.Builder}\n * @see {@link lunr.Pipeline}\n * @see {@link lunr.trimmer}\n * @see {@link lunr.stopWordFilter}\n * @see {@link lunr.stemmer}\n * @namespace {function} lunr\n */\nvar lunr = function (config) {\n  var builder = new lunr.Builder\n\n  builder.pipeline.add(\n    lunr.trimmer,\n    lunr.stop<PERSON>ord<PERSON>ilter,\n    lunr.stemmer\n  )\n\n  builder.searchPipeline.add(\n    lunr.stemmer\n  )\n\n  config.call(builder, builder)\n  return builder.build()\n}\n\nlunr.version = \"2.3.9\"\n/*!\n * lunr.utils\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A namespace containing utils for the rest of the lunr library\n * @namespace lunr.utils\n */\nlunr.utils = {}\n\n/**\n * Print a warning message to the console.\n *\n * @param {String} message The message to be printed.\n * @memberOf lunr.utils\n * @function\n */\nlunr.utils.warn = (function (global) {\n  /* eslint-disable no-console */\n  return function (message) {\n    if (global.console && console.warn) {\n      console.warn(message)\n    }\n  }\n  /* eslint-enable no-console */\n})(this)\n\n/**\n * Convert an object to a string.\n *\n * In the case of `null` and `undefined` the function returns\n * the empty string, in all other cases the result of calling\n * `toString` on the passed object is returned.\n *\n * @param {Any} obj The object to convert to a string.\n * @return {String} string representation of the passed object.\n * @memberOf lunr.utils\n */\nlunr.utils.asString = function (obj) {\n  if (obj === void 0 || obj === null) {\n    return \"\"\n  } else {\n    return obj.toString()\n  }\n}\n\n/**\n * Clones an object.\n *\n * Will create a copy of an existing object such that any mutations\n * on the copy cannot affect the original.\n *\n * Only shallow objects are supported, passing a nested object to this\n * function will cause a TypeError.\n *\n * Objects with primitives, and arrays of primitives are supported.\n *\n * @param {Object} obj The object to clone.\n * @return {Object} a clone of the passed object.\n * @throws {TypeError} when a nested object is passed.\n * @memberOf Utils\n */\nlunr.utils.clone = function (obj) {\n  if (obj === null || obj === undefined) {\n    return obj\n  }\n\n  var clone = Object.create(null),\n      keys = Object.keys(obj)\n\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i],\n        val = obj[key]\n\n    if (Array.isArray(val)) {\n      clone[key] = val.slice()\n      continue\n    }\n\n    if (typeof val === 'string' ||\n        typeof val === 'number' ||\n        typeof val === 'boolean') {\n      clone[key] = val\n      continue\n    }\n\n    throw new TypeError(\"clone is not deep and does not support nested objects\")\n  }\n\n  return clone\n}\nlunr.FieldRef = function (docRef, fieldName, stringValue) {\n  this.docRef = docRef\n  this.fieldName = fieldName\n  this._stringValue = stringValue\n}\n\nlunr.FieldRef.joiner = \"/\"\n\nlunr.FieldRef.fromString = function (s) {\n  var n = s.indexOf(lunr.FieldRef.joiner)\n\n  if (n === -1) {\n    throw \"malformed field ref string\"\n  }\n\n  var fieldRef = s.slice(0, n),\n      docRef = s.slice(n + 1)\n\n  return new lunr.FieldRef (docRef, fieldRef, s)\n}\n\nlunr.FieldRef.prototype.toString = function () {\n  if (this._stringValue == undefined) {\n    this._stringValue = this.fieldName + lunr.FieldRef.joiner + this.docRef\n  }\n\n  return this._stringValue\n}\n/*!\n * lunr.Set\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A lunr set.\n *\n * @constructor\n */\nlunr.Set = function (elements) {\n  this.elements = Object.create(null)\n\n  if (elements) {\n    this.length = elements.length\n\n    for (var i = 0; i < this.length; i++) {\n      this.elements[elements[i]] = true\n    }\n  } else {\n    this.length = 0\n  }\n}\n\n/**\n * A complete set that contains all elements.\n *\n * @static\n * @readonly\n * @type {lunr.Set}\n */\nlunr.Set.complete = {\n  intersect: function (other) {\n    return other\n  },\n\n  union: function () {\n    return this\n  },\n\n  contains: function () {\n    return true\n  }\n}\n\n/**\n * An empty set that contains no elements.\n *\n * @static\n * @readonly\n * @type {lunr.Set}\n */\nlunr.Set.empty = {\n  intersect: function () {\n    return this\n  },\n\n  union: function (other) {\n    return other\n  },\n\n  contains: function () {\n    return false\n  }\n}\n\n/**\n * Returns true if this set contains the specified object.\n *\n * @param {object} object - Object whose presence in this set is to be tested.\n * @returns {boolean} - True if this set contains the specified object.\n */\nlunr.Set.prototype.contains = function (object) {\n  return !!this.elements[object]\n}\n\n/**\n * Returns a new set containing only the elements that are present in both\n * this set and the specified set.\n *\n * @param {lunr.Set} other - set to intersect with this set.\n * @returns {lunr.Set} a new set that is the intersection of this and the specified set.\n */\n\nlunr.Set.prototype.intersect = function (other) {\n  var a, b, elements, intersection = []\n\n  if (other === lunr.Set.complete) {\n    return this\n  }\n\n  if (other === lunr.Set.empty) {\n    return other\n  }\n\n  if (this.length < other.length) {\n    a = this\n    b = other\n  } else {\n    a = other\n    b = this\n  }\n\n  elements = Object.keys(a.elements)\n\n  for (var i = 0; i < elements.length; i++) {\n    var element = elements[i]\n    if (element in b.elements) {\n      intersection.push(element)\n    }\n  }\n\n  return new lunr.Set (intersection)\n}\n\n/**\n * Returns a new set combining the elements of this and the specified set.\n *\n * @param {lunr.Set} other - set to union with this set.\n * @return {lunr.Set} a new set that is the union of this and the specified set.\n */\n\nlunr.Set.prototype.union = function (other) {\n  if (other === lunr.Set.complete) {\n    return lunr.Set.complete\n  }\n\n  if (other === lunr.Set.empty) {\n    return this\n  }\n\n  return new lunr.Set(Object.keys(this.elements).concat(Object.keys(other.elements)))\n}\n/**\n * A function to calculate the inverse document frequency for\n * a posting. This is shared between the builder and the index\n *\n * @private\n * @param {object} posting - The posting for a given term\n * @param {number} documentCount - The total number of documents.\n */\nlunr.idf = function (posting, documentCount) {\n  var documentsWithTerm = 0\n\n  for (var fieldName in posting) {\n    if (fieldName == '_index') continue // Ignore the term index, its not a field\n    documentsWithTerm += Object.keys(posting[fieldName]).length\n  }\n\n  var x = (documentCount - documentsWithTerm + 0.5) / (documentsWithTerm + 0.5)\n\n  return Math.log(1 + Math.abs(x))\n}\n\n/**\n * A token wraps a string representation of a token\n * as it is passed through the text processing pipeline.\n *\n * @constructor\n * @param {string} [str=''] - The string token being wrapped.\n * @param {object} [metadata={}] - Metadata associated with this token.\n */\nlunr.Token = function (str, metadata) {\n  this.str = str || \"\"\n  this.metadata = metadata || {}\n}\n\n/**\n * Returns the token string that is being wrapped by this object.\n *\n * @returns {string}\n */\nlunr.Token.prototype.toString = function () {\n  return this.str\n}\n\n/**\n * A token update function is used when updating or optionally\n * when cloning a token.\n *\n * @callback lunr.Token~updateFunction\n * @param {string} str - The string representation of the token.\n * @param {Object} metadata - All metadata associated with this token.\n */\n\n/**\n * Applies the given function to the wrapped string token.\n *\n * @example\n * token.update(function (str, metadata) {\n *   return str.toUpperCase()\n * })\n *\n * @param {lunr.Token~updateFunction} fn - A function to apply to the token string.\n * @returns {lunr.Token}\n */\nlunr.Token.prototype.update = function (fn) {\n  this.str = fn(this.str, this.metadata)\n  return this\n}\n\n/**\n * Creates a clone of this token. Optionally a function can be\n * applied to the cloned token.\n *\n * @param {lunr.Token~updateFunction} [fn] - An optional function to apply to the cloned token.\n * @returns {lunr.Token}\n */\nlunr.Token.prototype.clone = function (fn) {\n  fn = fn || function (s) { return s }\n  return new lunr.Token (fn(this.str, this.metadata), this.metadata)\n}\n/*!\n * lunr.tokenizer\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A function for splitting a string into tokens ready to be inserted into\n * the search index. Uses `lunr.tokenizer.separator` to split strings, change\n * the value of this property to change how strings are split into tokens.\n *\n * This tokenizer will convert its parameter to a string by calling `toString` and\n * then will split this string on the character in `lunr.tokenizer.separator`.\n * Arrays will have their elements converted to strings and wrapped in a lunr.Token.\n *\n * Optional metadata can be passed to the tokenizer, this metadata will be cloned and\n * added as metadata to every token that is created from the object to be tokenized.\n *\n * @static\n * @param {?(string|object|object[])} obj - The object to convert into tokens\n * @param {?object} metadata - Optional metadata to associate with every token\n * @returns {lunr.Token[]}\n * @see {@link lunr.Pipeline}\n */\nlunr.tokenizer = function (obj, metadata) {\n  if (obj == null || obj == undefined) {\n    return []\n  }\n\n  if (Array.isArray(obj)) {\n    return obj.map(function (t) {\n      return new lunr.Token(\n        lunr.utils.asString(t).toLowerCase(),\n        lunr.utils.clone(metadata)\n      )\n    })\n  }\n\n  var str = obj.toString().toLowerCase(),\n      len = str.length,\n      tokens = []\n\n  for (var sliceEnd = 0, sliceStart = 0; sliceEnd <= len; sliceEnd++) {\n    var char = str.charAt(sliceEnd),\n        sliceLength = sliceEnd - sliceStart\n\n    if ((char.match(lunr.tokenizer.separator) || sliceEnd == len)) {\n\n      if (sliceLength > 0) {\n        var tokenMetadata = lunr.utils.clone(metadata) || {}\n        tokenMetadata[\"position\"] = [sliceStart, sliceLength]\n        tokenMetadata[\"index\"] = tokens.length\n\n        tokens.push(\n          new lunr.Token (\n            str.slice(sliceStart, sliceEnd),\n            tokenMetadata\n          )\n        )\n      }\n\n      sliceStart = sliceEnd + 1\n    }\n\n  }\n\n  return tokens\n}\n\n/**\n * The separator used to split a string into tokens. Override this property to change the behaviour of\n * `lunr.tokenizer` behaviour when tokenizing strings. By default this splits on whitespace and hyphens.\n *\n * @static\n * @see lunr.tokenizer\n */\nlunr.tokenizer.separator = /[\\s\\-]+/\n/*!\n * lunr.Pipeline\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * lunr.Pipelines maintain an ordered list of functions to be applied to all\n * tokens in documents entering the search index and queries being ran against\n * the index.\n *\n * An instance of lunr.Index created with the lunr shortcut will contain a\n * pipeline with a stop word filter and an English language stemmer. Extra\n * functions can be added before or after either of these functions or these\n * default functions can be removed.\n *\n * When run the pipeline will call each function in turn, passing a token, the\n * index of that token in the original list of all tokens and finally a list of\n * all the original tokens.\n *\n * The output of functions in the pipeline will be passed to the next function\n * in the pipeline. To exclude a token from entering the index the function\n * should return undefined, the rest of the pipeline will not be called with\n * this token.\n *\n * For serialisation of pipelines to work, all functions used in an instance of\n * a pipeline should be registered with lunr.Pipeline. Registered functions can\n * then be loaded. If trying to load a serialised pipeline that uses functions\n * that are not registered an error will be thrown.\n *\n * If not planning on serialising the pipeline then registering pipeline functions\n * is not necessary.\n *\n * @constructor\n */\nlunr.Pipeline = function () {\n  this._stack = []\n}\n\nlunr.Pipeline.registeredFunctions = Object.create(null)\n\n/**\n * A pipeline function maps lunr.Token to lunr.Token. A lunr.Token contains the token\n * string as well as all known metadata. A pipeline function can mutate the token string\n * or mutate (or add) metadata for a given token.\n *\n * A pipeline function can indicate that the passed token should be discarded by returning\n * null, undefined or an empty string. This token will not be passed to any downstream pipeline\n * functions and will not be added to the index.\n *\n * Multiple tokens can be returned by returning an array of tokens. Each token will be passed\n * to any downstream pipeline functions and all will returned tokens will be added to the index.\n *\n * Any number of pipeline functions may be chained together using a lunr.Pipeline.\n *\n * @interface lunr.PipelineFunction\n * @param {lunr.Token} token - A token from the document being processed.\n * @param {number} i - The index of this token in the complete list of tokens for this document/field.\n * @param {lunr.Token[]} tokens - All tokens for this document/field.\n * @returns {(?lunr.Token|lunr.Token[])}\n */\n\n/**\n * Register a function with the pipeline.\n *\n * Functions that are used in the pipeline should be registered if the pipeline\n * needs to be serialised, or a serialised pipeline needs to be loaded.\n *\n * Registering a function does not add it to a pipeline, functions must still be\n * added to instances of the pipeline for them to be used when running a pipeline.\n *\n * @param {lunr.PipelineFunction} fn - The function to check for.\n * @param {String} label - The label to register this function with\n */\nlunr.Pipeline.registerFunction = function (fn, label) {\n  if (label in this.registeredFunctions) {\n    lunr.utils.warn('Overwriting existing registered function: ' + label)\n  }\n\n  fn.label = label\n  lunr.Pipeline.registeredFunctions[fn.label] = fn\n}\n\n/**\n * Warns if the function is not registered as a Pipeline function.\n *\n * @param {lunr.PipelineFunction} fn - The function to check for.\n * @private\n */\nlunr.Pipeline.warnIfFunctionNotRegistered = function (fn) {\n  var isRegistered = fn.label && (fn.label in this.registeredFunctions)\n\n  if (!isRegistered) {\n    lunr.utils.warn('Function is not registered with pipeline. This may cause problems when serialising the index.\\n', fn)\n  }\n}\n\n/**\n * Loads a previously serialised pipeline.\n *\n * All functions to be loaded must already be registered with lunr.Pipeline.\n * If any function from the serialised data has not been registered then an\n * error will be thrown.\n *\n * @param {Object} serialised - The serialised pipeline to load.\n * @returns {lunr.Pipeline}\n */\nlunr.Pipeline.load = function (serialised) {\n  var pipeline = new lunr.Pipeline\n\n  serialised.forEach(function (fnName) {\n    var fn = lunr.Pipeline.registeredFunctions[fnName]\n\n    if (fn) {\n      pipeline.add(fn)\n    } else {\n      throw new Error('Cannot load unregistered function: ' + fnName)\n    }\n  })\n\n  return pipeline\n}\n\n/**\n * Adds new functions to the end of the pipeline.\n *\n * Logs a warning if the function has not been registered.\n *\n * @param {lunr.PipelineFunction[]} functions - Any number of functions to add to the pipeline.\n */\nlunr.Pipeline.prototype.add = function () {\n  var fns = Array.prototype.slice.call(arguments)\n\n  fns.forEach(function (fn) {\n    lunr.Pipeline.warnIfFunctionNotRegistered(fn)\n    this._stack.push(fn)\n  }, this)\n}\n\n/**\n * Adds a single function after a function that already exists in the\n * pipeline.\n *\n * Logs a warning if the function has not been registered.\n *\n * @param {lunr.PipelineFunction} existingFn - A function that already exists in the pipeline.\n * @param {lunr.PipelineFunction} newFn - The new function to add to the pipeline.\n */\nlunr.Pipeline.prototype.after = function (existingFn, newFn) {\n  lunr.Pipeline.warnIfFunctionNotRegistered(newFn)\n\n  var pos = this._stack.indexOf(existingFn)\n  if (pos == -1) {\n    throw new Error('Cannot find existingFn')\n  }\n\n  pos = pos + 1\n  this._stack.splice(pos, 0, newFn)\n}\n\n/**\n * Adds a single function before a function that already exists in the\n * pipeline.\n *\n * Logs a warning if the function has not been registered.\n *\n * @param {lunr.PipelineFunction} existingFn - A function that already exists in the pipeline.\n * @param {lunr.PipelineFunction} newFn - The new function to add to the pipeline.\n */\nlunr.Pipeline.prototype.before = function (existingFn, newFn) {\n  lunr.Pipeline.warnIfFunctionNotRegistered(newFn)\n\n  var pos = this._stack.indexOf(existingFn)\n  if (pos == -1) {\n    throw new Error('Cannot find existingFn')\n  }\n\n  this._stack.splice(pos, 0, newFn)\n}\n\n/**\n * Removes a function from the pipeline.\n *\n * @param {lunr.PipelineFunction} fn The function to remove from the pipeline.\n */\nlunr.Pipeline.prototype.remove = function (fn) {\n  var pos = this._stack.indexOf(fn)\n  if (pos == -1) {\n    return\n  }\n\n  this._stack.splice(pos, 1)\n}\n\n/**\n * Runs the current list of functions that make up the pipeline against the\n * passed tokens.\n *\n * @param {Array} tokens The tokens to run through the pipeline.\n * @returns {Array}\n */\nlunr.Pipeline.prototype.run = function (tokens) {\n  var stackLength = this._stack.length\n\n  for (var i = 0; i < stackLength; i++) {\n    var fn = this._stack[i]\n    var memo = []\n\n    for (var j = 0; j < tokens.length; j++) {\n      var result = fn(tokens[j], j, tokens)\n\n      if (result === null || result === void 0 || result === '') continue\n\n      if (Array.isArray(result)) {\n        for (var k = 0; k < result.length; k++) {\n          memo.push(result[k])\n        }\n      } else {\n        memo.push(result)\n      }\n    }\n\n    tokens = memo\n  }\n\n  return tokens\n}\n\n/**\n * Convenience method for passing a string through a pipeline and getting\n * strings out. This method takes care of wrapping the passed string in a\n * token and mapping the resulting tokens back to strings.\n *\n * @param {string} str - The string to pass through the pipeline.\n * @param {?object} metadata - Optional metadata to associate with the token\n * passed to the pipeline.\n * @returns {string[]}\n */\nlunr.Pipeline.prototype.runString = function (str, metadata) {\n  var token = new lunr.Token (str, metadata)\n\n  return this.run([token]).map(function (t) {\n    return t.toString()\n  })\n}\n\n/**\n * Resets the pipeline by removing any existing processors.\n *\n */\nlunr.Pipeline.prototype.reset = function () {\n  this._stack = []\n}\n\n/**\n * Returns a representation of the pipeline ready for serialisation.\n *\n * Logs a warning if the function has not been registered.\n *\n * @returns {Array}\n */\nlunr.Pipeline.prototype.toJSON = function () {\n  return this._stack.map(function (fn) {\n    lunr.Pipeline.warnIfFunctionNotRegistered(fn)\n\n    return fn.label\n  })\n}\n/*!\n * lunr.Vector\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A vector is used to construct the vector space of documents and queries. These\n * vectors support operations to determine the similarity between two documents or\n * a document and a query.\n *\n * Normally no parameters are required for initializing a vector, but in the case of\n * loading a previously dumped vector the raw elements can be provided to the constructor.\n *\n * For performance reasons vectors are implemented with a flat array, where an elements\n * index is immediately followed by its value. E.g. [index, value, index, value]. This\n * allows the underlying array to be as sparse as possible and still offer decent\n * performance when being used for vector calculations.\n *\n * @constructor\n * @param {Number[]} [elements] - The flat list of element index and element value pairs.\n */\nlunr.Vector = function (elements) {\n  this._magnitude = 0\n  this.elements = elements || []\n}\n\n\n/**\n * Calculates the position within the vector to insert a given index.\n *\n * This is used internally by insert and upsert. If there are duplicate indexes then\n * the position is returned as if the value for that index were to be updated, but it\n * is the callers responsibility to check whether there is a duplicate at that index\n *\n * @param {Number} insertIdx - The index at which the element should be inserted.\n * @returns {Number}\n */\nlunr.Vector.prototype.positionForIndex = function (index) {\n  // For an empty vector the tuple can be inserted at the beginning\n  if (this.elements.length == 0) {\n    return 0\n  }\n\n  var start = 0,\n      end = this.elements.length / 2,\n      sliceLength = end - start,\n      pivotPoint = Math.floor(sliceLength / 2),\n      pivotIndex = this.elements[pivotPoint * 2]\n\n  while (sliceLength > 1) {\n    if (pivotIndex < index) {\n      start = pivotPoint\n    }\n\n    if (pivotIndex > index) {\n      end = pivotPoint\n    }\n\n    if (pivotIndex == index) {\n      break\n    }\n\n    sliceLength = end - start\n    pivotPoint = start + Math.floor(sliceLength / 2)\n    pivotIndex = this.elements[pivotPoint * 2]\n  }\n\n  if (pivotIndex == index) {\n    return pivotPoint * 2\n  }\n\n  if (pivotIndex > index) {\n    return pivotPoint * 2\n  }\n\n  if (pivotIndex < index) {\n    return (pivotPoint + 1) * 2\n  }\n}\n\n/**\n * Inserts an element at an index within the vector.\n *\n * Does not allow duplicates, will throw an error if there is already an entry\n * for this index.\n *\n * @param {Number} insertIdx - The index at which the element should be inserted.\n * @param {Number} val - The value to be inserted into the vector.\n */\nlunr.Vector.prototype.insert = function (insertIdx, val) {\n  this.upsert(insertIdx, val, function () {\n    throw \"duplicate index\"\n  })\n}\n\n/**\n * Inserts or updates an existing index within the vector.\n *\n * @param {Number} insertIdx - The index at which the element should be inserted.\n * @param {Number} val - The value to be inserted into the vector.\n * @param {function} fn - A function that is called for updates, the existing value and the\n * requested value are passed as arguments\n */\nlunr.Vector.prototype.upsert = function (insertIdx, val, fn) {\n  this._magnitude = 0\n  var position = this.positionForIndex(insertIdx)\n\n  if (this.elements[position] == insertIdx) {\n    this.elements[position + 1] = fn(this.elements[position + 1], val)\n  } else {\n    this.elements.splice(position, 0, insertIdx, val)\n  }\n}\n\n/**\n * Calculates the magnitude of this vector.\n *\n * @returns {Number}\n */\nlunr.Vector.prototype.magnitude = function () {\n  if (this._magnitude) return this._magnitude\n\n  var sumOfSquares = 0,\n      elementsLength = this.elements.length\n\n  for (var i = 1; i < elementsLength; i += 2) {\n    var val = this.elements[i]\n    sumOfSquares += val * val\n  }\n\n  return this._magnitude = Math.sqrt(sumOfSquares)\n}\n\n/**\n * Calculates the dot product of this vector and another vector.\n *\n * @param {lunr.Vector} otherVector - The vector to compute the dot product with.\n * @returns {Number}\n */\nlunr.Vector.prototype.dot = function (otherVector) {\n  var dotProduct = 0,\n      a = this.elements, b = otherVector.elements,\n      aLen = a.length, bLen = b.length,\n      aVal = 0, bVal = 0,\n      i = 0, j = 0\n\n  while (i < aLen && j < bLen) {\n    aVal = a[i], bVal = b[j]\n    if (aVal < bVal) {\n      i += 2\n    } else if (aVal > bVal) {\n      j += 2\n    } else if (aVal == bVal) {\n      dotProduct += a[i + 1] * b[j + 1]\n      i += 2\n      j += 2\n    }\n  }\n\n  return dotProduct\n}\n\n/**\n * Calculates the similarity between this vector and another vector.\n *\n * @param {lunr.Vector} otherVector - The other vector to calculate the\n * similarity with.\n * @returns {Number}\n */\nlunr.Vector.prototype.similarity = function (otherVector) {\n  return this.dot(otherVector) / this.magnitude() || 0\n}\n\n/**\n * Converts the vector to an array of the elements within the vector.\n *\n * @returns {Number[]}\n */\nlunr.Vector.prototype.toArray = function () {\n  var output = new Array (this.elements.length / 2)\n\n  for (var i = 1, j = 0; i < this.elements.length; i += 2, j++) {\n    output[j] = this.elements[i]\n  }\n\n  return output\n}\n\n/**\n * A JSON serializable representation of the vector.\n *\n * @returns {Number[]}\n */\nlunr.Vector.prototype.toJSON = function () {\n  return this.elements\n}\n/* eslint-disable */\n/*!\n * lunr.stemmer\n * Copyright (C) 2020 Oliver Nightingale\n * Includes code from - http://tartarus.org/~martin/PorterStemmer/js.txt\n */\n\n/**\n * lunr.stemmer is an english language stemmer, this is a JavaScript\n * implementation of the PorterStemmer taken from http://tartarus.org/~martin\n *\n * @static\n * @implements {lunr.PipelineFunction}\n * @param {lunr.Token} token - The string to stem\n * @returns {lunr.Token}\n * @see {@link lunr.Pipeline}\n * @function\n */\nlunr.stemmer = (function(){\n  var step2list = {\n      \"ational\" : \"ate\",\n      \"tional\" : \"tion\",\n      \"enci\" : \"ence\",\n      \"anci\" : \"ance\",\n      \"izer\" : \"ize\",\n      \"bli\" : \"ble\",\n      \"alli\" : \"al\",\n      \"entli\" : \"ent\",\n      \"eli\" : \"e\",\n      \"ousli\" : \"ous\",\n      \"ization\" : \"ize\",\n      \"ation\" : \"ate\",\n      \"ator\" : \"ate\",\n      \"alism\" : \"al\",\n      \"iveness\" : \"ive\",\n      \"fulness\" : \"ful\",\n      \"ousness\" : \"ous\",\n      \"aliti\" : \"al\",\n      \"iviti\" : \"ive\",\n      \"biliti\" : \"ble\",\n      \"logi\" : \"log\"\n    },\n\n    step3list = {\n      \"icate\" : \"ic\",\n      \"ative\" : \"\",\n      \"alize\" : \"al\",\n      \"iciti\" : \"ic\",\n      \"ical\" : \"ic\",\n      \"ful\" : \"\",\n      \"ness\" : \"\"\n    },\n\n    c = \"[^aeiou]\",          // consonant\n    v = \"[aeiouy]\",          // vowel\n    C = c + \"[^aeiouy]*\",    // consonant sequence\n    V = v + \"[aeiou]*\",      // vowel sequence\n\n    mgr0 = \"^(\" + C + \")?\" + V + C,               // [C]VC... is m>0\n    meq1 = \"^(\" + C + \")?\" + V + C + \"(\" + V + \")?$\",  // [C]VC[V] is m=1\n    mgr1 = \"^(\" + C + \")?\" + V + C + V + C,       // [C]VCVC... is m>1\n    s_v = \"^(\" + C + \")?\" + v;                   // vowel in stem\n\n  var re_mgr0 = new RegExp(mgr0);\n  var re_mgr1 = new RegExp(mgr1);\n  var re_meq1 = new RegExp(meq1);\n  var re_s_v = new RegExp(s_v);\n\n  var re_1a = /^(.+?)(ss|i)es$/;\n  var re2_1a = /^(.+?)([^s])s$/;\n  var re_1b = /^(.+?)eed$/;\n  var re2_1b = /^(.+?)(ed|ing)$/;\n  var re_1b_2 = /.$/;\n  var re2_1b_2 = /(at|bl|iz)$/;\n  var re3_1b_2 = new RegExp(\"([^aeiouylsz])\\\\1$\");\n  var re4_1b_2 = new RegExp(\"^\" + C + v + \"[^aeiouwxy]$\");\n\n  var re_1c = /^(.+?[^aeiou])y$/;\n  var re_2 = /^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/;\n\n  var re_3 = /^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/;\n\n  var re_4 = /^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/;\n  var re2_4 = /^(.+?)(s|t)(ion)$/;\n\n  var re_5 = /^(.+?)e$/;\n  var re_5_1 = /ll$/;\n  var re3_5 = new RegExp(\"^\" + C + v + \"[^aeiouwxy]$\");\n\n  var porterStemmer = function porterStemmer(w) {\n    var stem,\n      suffix,\n      firstch,\n      re,\n      re2,\n      re3,\n      re4;\n\n    if (w.length < 3) { return w; }\n\n    firstch = w.substr(0,1);\n    if (firstch == \"y\") {\n      w = firstch.toUpperCase() + w.substr(1);\n    }\n\n    // Step 1a\n    re = re_1a\n    re2 = re2_1a;\n\n    if (re.test(w)) { w = w.replace(re,\"$1$2\"); }\n    else if (re2.test(w)) { w = w.replace(re2,\"$1$2\"); }\n\n    // Step 1b\n    re = re_1b;\n    re2 = re2_1b;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      re = re_mgr0;\n      if (re.test(fp[1])) {\n        re = re_1b_2;\n        w = w.replace(re,\"\");\n      }\n    } else if (re2.test(w)) {\n      var fp = re2.exec(w);\n      stem = fp[1];\n      re2 = re_s_v;\n      if (re2.test(stem)) {\n        w = stem;\n        re2 = re2_1b_2;\n        re3 = re3_1b_2;\n        re4 = re4_1b_2;\n        if (re2.test(w)) { w = w + \"e\"; }\n        else if (re3.test(w)) { re = re_1b_2; w = w.replace(re,\"\"); }\n        else if (re4.test(w)) { w = w + \"e\"; }\n      }\n    }\n\n    // Step 1c - replace suffix y or Y by i if preceded by a non-vowel which is not the first letter of the word (so cry -> cri, by -> by, say -> say)\n    re = re_1c;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      w = stem + \"i\";\n    }\n\n    // Step 2\n    re = re_2;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      suffix = fp[2];\n      re = re_mgr0;\n      if (re.test(stem)) {\n        w = stem + step2list[suffix];\n      }\n    }\n\n    // Step 3\n    re = re_3;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      suffix = fp[2];\n      re = re_mgr0;\n      if (re.test(stem)) {\n        w = stem + step3list[suffix];\n      }\n    }\n\n    // Step 4\n    re = re_4;\n    re2 = re2_4;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      re = re_mgr1;\n      if (re.test(stem)) {\n        w = stem;\n      }\n    } else if (re2.test(w)) {\n      var fp = re2.exec(w);\n      stem = fp[1] + fp[2];\n      re2 = re_mgr1;\n      if (re2.test(stem)) {\n        w = stem;\n      }\n    }\n\n    // Step 5\n    re = re_5;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      re = re_mgr1;\n      re2 = re_meq1;\n      re3 = re3_5;\n      if (re.test(stem) || (re2.test(stem) && !(re3.test(stem)))) {\n        w = stem;\n      }\n    }\n\n    re = re_5_1;\n    re2 = re_mgr1;\n    if (re.test(w) && re2.test(w)) {\n      re = re_1b_2;\n      w = w.replace(re,\"\");\n    }\n\n    // and turn initial Y back to y\n\n    if (firstch == \"y\") {\n      w = firstch.toLowerCase() + w.substr(1);\n    }\n\n    return w;\n  };\n\n  return function (token) {\n    return token.update(porterStemmer);\n  }\n})();\n\nlunr.Pipeline.registerFunction(lunr.stemmer, 'stemmer')\n/*!\n * lunr.stopWordFilter\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * lunr.generateStopWordFilter builds a stopWordFilter function from the provided\n * list of stop words.\n *\n * The built in lunr.stopWordFilter is built using this generator and can be used\n * to generate custom stopWordFilters for applications or non English languages.\n *\n * @function\n * @param {Array} token The token to pass through the filter\n * @returns {lunr.PipelineFunction}\n * @see lunr.Pipeline\n * @see lunr.stopWordFilter\n */\nlunr.generateStopWordFilter = function (stopWords) {\n  var words = stopWords.reduce(function (memo, stopWord) {\n    memo[stopWord] = stopWord\n    return memo\n  }, {})\n\n  return function (token) {\n    if (token && words[token.toString()] !== token.toString()) return token\n  }\n}\n\n/**\n * lunr.stopWordFilter is an English language stop word list filter, any words\n * contained in the list will not be passed through the filter.\n *\n * This is intended to be used in the Pipeline. If the token does not pass the\n * filter then undefined will be returned.\n *\n * @function\n * @implements {lunr.PipelineFunction}\n * @params {lunr.Token} token - A token to check for being a stop word.\n * @returns {lunr.Token}\n * @see {@link lunr.Pipeline}\n */\nlunr.stopWordFilter = lunr.generateStopWordFilter([\n  'a',\n  'able',\n  'about',\n  'across',\n  'after',\n  'all',\n  'almost',\n  'also',\n  'am',\n  'among',\n  'an',\n  'and',\n  'any',\n  'are',\n  'as',\n  'at',\n  'be',\n  'because',\n  'been',\n  'but',\n  'by',\n  'can',\n  'cannot',\n  'could',\n  'dear',\n  'did',\n  'do',\n  'does',\n  'either',\n  'else',\n  'ever',\n  'every',\n  'for',\n  'from',\n  'get',\n  'got',\n  'had',\n  'has',\n  'have',\n  'he',\n  'her',\n  'hers',\n  'him',\n  'his',\n  'how',\n  'however',\n  'i',\n  'if',\n  'in',\n  'into',\n  'is',\n  'it',\n  'its',\n  'just',\n  'least',\n  'let',\n  'like',\n  'likely',\n  'may',\n  'me',\n  'might',\n  'most',\n  'must',\n  'my',\n  'neither',\n  'no',\n  'nor',\n  'not',\n  'of',\n  'off',\n  'often',\n  'on',\n  'only',\n  'or',\n  'other',\n  'our',\n  'own',\n  'rather',\n  'said',\n  'say',\n  'says',\n  'she',\n  'should',\n  'since',\n  'so',\n  'some',\n  'than',\n  'that',\n  'the',\n  'their',\n  'them',\n  'then',\n  'there',\n  'these',\n  'they',\n  'this',\n  'tis',\n  'to',\n  'too',\n  'twas',\n  'us',\n  'wants',\n  'was',\n  'we',\n  'were',\n  'what',\n  'when',\n  'where',\n  'which',\n  'while',\n  'who',\n  'whom',\n  'why',\n  'will',\n  'with',\n  'would',\n  'yet',\n  'you',\n  'your'\n])\n\nlunr.Pipeline.registerFunction(lunr.stopWordFilter, 'stopWordFilter')\n/*!\n * lunr.trimmer\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * lunr.trimmer is a pipeline function for trimming non word\n * characters from the beginning and end of tokens before they\n * enter the index.\n *\n * This implementation may not work correctly for non latin\n * characters and should either be removed or adapted for use\n * with languages with non-latin characters.\n *\n * @static\n * @implements {lunr.PipelineFunction}\n * @param {lunr.Token} token The token to pass through the filter\n * @returns {lunr.Token}\n * @see lunr.Pipeline\n */\nlunr.trimmer = function (token) {\n  return token.update(function (s) {\n    return s.replace(/^\\W+/, '').replace(/\\W+$/, '')\n  })\n}\n\nlunr.Pipeline.registerFunction(lunr.trimmer, 'trimmer')\n/*!\n * lunr.TokenSet\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A token set is used to store the unique list of all tokens\n * within an index. Token sets are also used to represent an\n * incoming query to the index, this query token set and index\n * token set are then intersected to find which tokens to look\n * up in the inverted index.\n *\n * A token set can hold multiple tokens, as in the case of the\n * index token set, or it can hold a single token as in the\n * case of a simple query token set.\n *\n * Additionally token sets are used to perform wildcard matching.\n * Leading, contained and trailing wildcards are supported, and\n * from this edit distance matching can also be provided.\n *\n * Token sets are implemented as a minimal finite state automata,\n * where both common prefixes and suffixes are shared between tokens.\n * This helps to reduce the space used for storing the token set.\n *\n * @constructor\n */\nlunr.TokenSet = function () {\n  this.final = false\n  this.edges = {}\n  this.id = lunr.TokenSet._nextId\n  lunr.TokenSet._nextId += 1\n}\n\n/**\n * Keeps track of the next, auto increment, identifier to assign\n * to a new tokenSet.\n *\n * TokenSets require a unique identifier to be correctly minimised.\n *\n * @private\n */\nlunr.TokenSet._nextId = 1\n\n/**\n * Creates a TokenSet instance from the given sorted array of words.\n *\n * @param {String[]} arr - A sorted array of strings to create the set from.\n * @returns {lunr.TokenSet}\n * @throws Will throw an error if the input array is not sorted.\n */\nlunr.TokenSet.fromArray = function (arr) {\n  var builder = new lunr.TokenSet.Builder\n\n  for (var i = 0, len = arr.length; i < len; i++) {\n    builder.insert(arr[i])\n  }\n\n  builder.finish()\n  return builder.root\n}\n\n/**\n * Creates a token set from a query clause.\n *\n * @private\n * @param {Object} clause - A single clause from lunr.Query.\n * @param {string} clause.term - The query clause term.\n * @param {number} [clause.editDistance] - The optional edit distance for the term.\n * @returns {lunr.TokenSet}\n */\nlunr.TokenSet.fromClause = function (clause) {\n  if ('editDistance' in clause) {\n    return lunr.TokenSet.fromFuzzyString(clause.term, clause.editDistance)\n  } else {\n    return lunr.TokenSet.fromString(clause.term)\n  }\n}\n\n/**\n * Creates a token set representing a single string with a specified\n * edit distance.\n *\n * Insertions, deletions, substitutions and transpositions are each\n * treated as an edit distance of 1.\n *\n * Increasing the allowed edit distance will have a dramatic impact\n * on the performance of both creating and intersecting these TokenSets.\n * It is advised to keep the edit distance less than 3.\n *\n * @param {string} str - The string to create the token set from.\n * @param {number} editDistance - The allowed edit distance to match.\n * @returns {lunr.Vector}\n */\nlunr.TokenSet.fromFuzzyString = function (str, editDistance) {\n  var root = new lunr.TokenSet\n\n  var stack = [{\n    node: root,\n    editsRemaining: editDistance,\n    str: str\n  }]\n\n  while (stack.length) {\n    var frame = stack.pop()\n\n    // no edit\n    if (frame.str.length > 0) {\n      var char = frame.str.charAt(0),\n          noEditNode\n\n      if (char in frame.node.edges) {\n        noEditNode = frame.node.edges[char]\n      } else {\n        noEditNode = new lunr.TokenSet\n        frame.node.edges[char] = noEditNode\n      }\n\n      if (frame.str.length == 1) {\n        noEditNode.final = true\n      }\n\n      stack.push({\n        node: noEditNode,\n        editsRemaining: frame.editsRemaining,\n        str: frame.str.slice(1)\n      })\n    }\n\n    if (frame.editsRemaining == 0) {\n      continue\n    }\n\n    // insertion\n    if (\"*\" in frame.node.edges) {\n      var insertionNode = frame.node.edges[\"*\"]\n    } else {\n      var insertionNode = new lunr.TokenSet\n      frame.node.edges[\"*\"] = insertionNode\n    }\n\n    if (frame.str.length == 0) {\n      insertionNode.final = true\n    }\n\n    stack.push({\n      node: insertionNode,\n      editsRemaining: frame.editsRemaining - 1,\n      str: frame.str\n    })\n\n    // deletion\n    // can only do a deletion if we have enough edits remaining\n    // and if there are characters left to delete in the string\n    if (frame.str.length > 1) {\n      stack.push({\n        node: frame.node,\n        editsRemaining: frame.editsRemaining - 1,\n        str: frame.str.slice(1)\n      })\n    }\n\n    // deletion\n    // just removing the last character from the str\n    if (frame.str.length == 1) {\n      frame.node.final = true\n    }\n\n    // substitution\n    // can only do a substitution if we have enough edits remaining\n    // and if there are characters left to substitute\n    if (frame.str.length >= 1) {\n      if (\"*\" in frame.node.edges) {\n        var substitutionNode = frame.node.edges[\"*\"]\n      } else {\n        var substitutionNode = new lunr.TokenSet\n        frame.node.edges[\"*\"] = substitutionNode\n      }\n\n      if (frame.str.length == 1) {\n        substitutionNode.final = true\n      }\n\n      stack.push({\n        node: substitutionNode,\n        editsRemaining: frame.editsRemaining - 1,\n        str: frame.str.slice(1)\n      })\n    }\n\n    // transposition\n    // can only do a transposition if there are edits remaining\n    // and there are enough characters to transpose\n    if (frame.str.length > 1) {\n      var charA = frame.str.charAt(0),\n          charB = frame.str.charAt(1),\n          transposeNode\n\n      if (charB in frame.node.edges) {\n        transposeNode = frame.node.edges[charB]\n      } else {\n        transposeNode = new lunr.TokenSet\n        frame.node.edges[charB] = transposeNode\n      }\n\n      if (frame.str.length == 1) {\n        transposeNode.final = true\n      }\n\n      stack.push({\n        node: transposeNode,\n        editsRemaining: frame.editsRemaining - 1,\n        str: charA + frame.str.slice(2)\n      })\n    }\n  }\n\n  return root\n}\n\n/**\n * Creates a TokenSet from a string.\n *\n * The string may contain one or more wildcard characters (*)\n * that will allow wildcard matching when intersecting with\n * another TokenSet.\n *\n * @param {string} str - The string to create a TokenSet from.\n * @returns {lunr.TokenSet}\n */\nlunr.TokenSet.fromString = function (str) {\n  var node = new lunr.TokenSet,\n      root = node\n\n  /*\n   * Iterates through all characters within the passed string\n   * appending a node for each character.\n   *\n   * When a wildcard character is found then a self\n   * referencing edge is introduced to continually match\n   * any number of any characters.\n   */\n  for (var i = 0, len = str.length; i < len; i++) {\n    var char = str[i],\n        final = (i == len - 1)\n\n    if (char == \"*\") {\n      node.edges[char] = node\n      node.final = final\n\n    } else {\n      var next = new lunr.TokenSet\n      next.final = final\n\n      node.edges[char] = next\n      node = next\n    }\n  }\n\n  return root\n}\n\n/**\n * Converts this TokenSet into an array of strings\n * contained within the TokenSet.\n *\n * This is not intended to be used on a TokenSet that\n * contains wildcards, in these cases the results are\n * undefined and are likely to cause an infinite loop.\n *\n * @returns {string[]}\n */\nlunr.TokenSet.prototype.toArray = function () {\n  var words = []\n\n  var stack = [{\n    prefix: \"\",\n    node: this\n  }]\n\n  while (stack.length) {\n    var frame = stack.pop(),\n        edges = Object.keys(frame.node.edges),\n        len = edges.length\n\n    if (frame.node.final) {\n      /* In Safari, at this point the prefix is sometimes corrupted, see:\n       * https://github.com/olivernn/lunr.js/issues/279 Calling any\n       * String.prototype method forces Safari to \"cast\" this string to what\n       * it's supposed to be, fixing the bug. */\n      frame.prefix.charAt(0)\n      words.push(frame.prefix)\n    }\n\n    for (var i = 0; i < len; i++) {\n      var edge = edges[i]\n\n      stack.push({\n        prefix: frame.prefix.concat(edge),\n        node: frame.node.edges[edge]\n      })\n    }\n  }\n\n  return words\n}\n\n/**\n * Generates a string representation of a TokenSet.\n *\n * This is intended to allow TokenSets to be used as keys\n * in objects, largely to aid the construction and minimisation\n * of a TokenSet. As such it is not designed to be a human\n * friendly representation of the TokenSet.\n *\n * @returns {string}\n */\nlunr.TokenSet.prototype.toString = function () {\n  // NOTE: Using Object.keys here as this.edges is very likely\n  // to enter 'hash-mode' with many keys being added\n  //\n  // avoiding a for-in loop here as it leads to the function\n  // being de-optimised (at least in V8). From some simple\n  // benchmarks the performance is comparable, but allowing\n  // V8 to optimize may mean easy performance wins in the future.\n\n  if (this._str) {\n    return this._str\n  }\n\n  var str = this.final ? '1' : '0',\n      labels = Object.keys(this.edges).sort(),\n      len = labels.length\n\n  for (var i = 0; i < len; i++) {\n    var label = labels[i],\n        node = this.edges[label]\n\n    str = str + label + node.id\n  }\n\n  return str\n}\n\n/**\n * Returns a new TokenSet that is the intersection of\n * this TokenSet and the passed TokenSet.\n *\n * This intersection will take into account any wildcards\n * contained within the TokenSet.\n *\n * @param {lunr.TokenSet} b - An other TokenSet to intersect with.\n * @returns {lunr.TokenSet}\n */\nlunr.TokenSet.prototype.intersect = function (b) {\n  var output = new lunr.TokenSet,\n      frame = undefined\n\n  var stack = [{\n    qNode: b,\n    output: output,\n    node: this\n  }]\n\n  while (stack.length) {\n    frame = stack.pop()\n\n    // NOTE: As with the #toString method, we are using\n    // Object.keys and a for loop instead of a for-in loop\n    // as both of these objects enter 'hash' mode, causing\n    // the function to be de-optimised in V8\n    var qEdges = Object.keys(frame.qNode.edges),\n        qLen = qEdges.length,\n        nEdges = Object.keys(frame.node.edges),\n        nLen = nEdges.length\n\n    for (var q = 0; q < qLen; q++) {\n      var qEdge = qEdges[q]\n\n      for (var n = 0; n < nLen; n++) {\n        var nEdge = nEdges[n]\n\n        if (nEdge == qEdge || qEdge == '*') {\n          var node = frame.node.edges[nEdge],\n              qNode = frame.qNode.edges[qEdge],\n              final = node.final && qNode.final,\n              next = undefined\n\n          if (nEdge in frame.output.edges) {\n            // an edge already exists for this character\n            // no need to create a new node, just set the finality\n            // bit unless this node is already final\n            next = frame.output.edges[nEdge]\n            next.final = next.final || final\n\n          } else {\n            // no edge exists yet, must create one\n            // set the finality bit and insert it\n            // into the output\n            next = new lunr.TokenSet\n            next.final = final\n            frame.output.edges[nEdge] = next\n          }\n\n          stack.push({\n            qNode: qNode,\n            output: next,\n            node: node\n          })\n        }\n      }\n    }\n  }\n\n  return output\n}\nlunr.TokenSet.Builder = function () {\n  this.previousWord = \"\"\n  this.root = new lunr.TokenSet\n  this.uncheckedNodes = []\n  this.minimizedNodes = {}\n}\n\nlunr.TokenSet.Builder.prototype.insert = function (word) {\n  var node,\n      commonPrefix = 0\n\n  if (word < this.previousWord) {\n    throw new Error (\"Out of order word insertion\")\n  }\n\n  for (var i = 0; i < word.length && i < this.previousWord.length; i++) {\n    if (word[i] != this.previousWord[i]) break\n    commonPrefix++\n  }\n\n  this.minimize(commonPrefix)\n\n  if (this.uncheckedNodes.length == 0) {\n    node = this.root\n  } else {\n    node = this.uncheckedNodes[this.uncheckedNodes.length - 1].child\n  }\n\n  for (var i = commonPrefix; i < word.length; i++) {\n    var nextNode = new lunr.TokenSet,\n        char = word[i]\n\n    node.edges[char] = nextNode\n\n    this.uncheckedNodes.push({\n      parent: node,\n      char: char,\n      child: nextNode\n    })\n\n    node = nextNode\n  }\n\n  node.final = true\n  this.previousWord = word\n}\n\nlunr.TokenSet.Builder.prototype.finish = function () {\n  this.minimize(0)\n}\n\nlunr.TokenSet.Builder.prototype.minimize = function (downTo) {\n  for (var i = this.uncheckedNodes.length - 1; i >= downTo; i--) {\n    var node = this.uncheckedNodes[i],\n        childKey = node.child.toString()\n\n    if (childKey in this.minimizedNodes) {\n      node.parent.edges[node.char] = this.minimizedNodes[childKey]\n    } else {\n      // Cache the key for this node since\n      // we know it can't change anymore\n      node.child._str = childKey\n\n      this.minimizedNodes[childKey] = node.child\n    }\n\n    this.uncheckedNodes.pop()\n  }\n}\n/*!\n * lunr.Index\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * An index contains the built index of all documents and provides a query interface\n * to the index.\n *\n * Usually instances of lunr.Index will not be created using this constructor, instead\n * lunr.Builder should be used to construct new indexes, or lunr.Index.load should be\n * used to load previously built and serialized indexes.\n *\n * @constructor\n * @param {Object} attrs - The attributes of the built search index.\n * @param {Object} attrs.invertedIndex - An index of term/field to document reference.\n * @param {Object<string, lunr.Vector>} attrs.fieldVectors - Field vectors\n * @param {lunr.TokenSet} attrs.tokenSet - An set of all corpus tokens.\n * @param {string[]} attrs.fields - The names of indexed document fields.\n * @param {lunr.Pipeline} attrs.pipeline - The pipeline to use for search terms.\n */\nlunr.Index = function (attrs) {\n  this.invertedIndex = attrs.invertedIndex\n  this.fieldVectors = attrs.fieldVectors\n  this.tokenSet = attrs.tokenSet\n  this.fields = attrs.fields\n  this.pipeline = attrs.pipeline\n}\n\n/**\n * A result contains details of a document matching a search query.\n * @typedef {Object} lunr.Index~Result\n * @property {string} ref - The reference of the document this result represents.\n * @property {number} score - A number between 0 and 1 representing how similar this document is to the query.\n * @property {lunr.MatchData} matchData - Contains metadata about this match including which term(s) caused the match.\n */\n\n/**\n * Although lunr provides the ability to create queries using lunr.Query, it also provides a simple\n * query language which itself is parsed into an instance of lunr.Query.\n *\n * For programmatically building queries it is advised to directly use lunr.Query, the query language\n * is best used for human entered text rather than program generated text.\n *\n * At its simplest queries can just be a single term, e.g. `hello`, multiple terms are also supported\n * and will be combined with OR, e.g `hello world` will match documents that contain either 'hello'\n * or 'world', though those that contain both will rank higher in the results.\n *\n * Wildcards can be included in terms to match one or more unspecified characters, these wildcards can\n * be inserted anywhere within the term, and more than one wildcard can exist in a single term. Adding\n * wildcards will increase the number of documents that will be found but can also have a negative\n * impact on query performance, especially with wildcards at the beginning of a term.\n *\n * Terms can be restricted to specific fields, e.g. `title:hello`, only documents with the term\n * hello in the title field will match this query. Using a field not present in the index will lead\n * to an error being thrown.\n *\n * Modifiers can also be added to terms, lunr supports edit distance and boost modifiers on terms. A term\n * boost will make documents matching that term score higher, e.g. `foo^5`. Edit distance is also supported\n * to provide fuzzy matching, e.g. 'hello~2' will match documents with hello with an edit distance of 2.\n * Avoid large values for edit distance to improve query performance.\n *\n * Each term also supports a presence modifier. By default a term's presence in document is optional, however\n * this can be changed to either required or prohibited. For a term's presence to be required in a document the\n * term should be prefixed with a '+', e.g. `+foo bar` is a search for documents that must contain 'foo' and\n * optionally contain 'bar'. Conversely a leading '-' sets the terms presence to prohibited, i.e. it must not\n * appear in a document, e.g. `-foo bar` is a search for documents that do not contain 'foo' but may contain 'bar'.\n *\n * To escape special characters the backslash character '\\' can be used, this allows searches to include\n * characters that would normally be considered modifiers, e.g. `foo\\~2` will search for a term \"foo~2\" instead\n * of attempting to apply a boost of 2 to the search term \"foo\".\n *\n * @typedef {string} lunr.Index~QueryString\n * @example <caption>Simple single term query</caption>\n * hello\n * @example <caption>Multiple term query</caption>\n * hello world\n * @example <caption>term scoped to a field</caption>\n * title:hello\n * @example <caption>term with a boost of 10</caption>\n * hello^10\n * @example <caption>term with an edit distance of 2</caption>\n * hello~2\n * @example <caption>terms with presence modifiers</caption>\n * -foo +bar baz\n */\n\n/**\n * Performs a search against the index using lunr query syntax.\n *\n * Results will be returned sorted by their score, the most relevant results\n * will be returned first.  For details on how the score is calculated, please see\n * the {@link https://lunrjs.com/guides/searching.html#scoring|guide}.\n *\n * For more programmatic querying use lunr.Index#query.\n *\n * @param {lunr.Index~QueryString} queryString - A string containing a lunr query.\n * @throws {lunr.QueryParseError} If the passed query string cannot be parsed.\n * @returns {lunr.Index~Result[]}\n */\nlunr.Index.prototype.search = function (queryString) {\n  return this.query(function (query) {\n    var parser = new lunr.QueryParser(queryString, query)\n    parser.parse()\n  })\n}\n\n/**\n * A query builder callback provides a query object to be used to express\n * the query to perform on the index.\n *\n * @callback lunr.Index~queryBuilder\n * @param {lunr.Query} query - The query object to build up.\n * @this lunr.Query\n */\n\n/**\n * Performs a query against the index using the yielded lunr.Query object.\n *\n * If performing programmatic queries against the index, this method is preferred\n * over lunr.Index#search so as to avoid the additional query parsing overhead.\n *\n * A query object is yielded to the supplied function which should be used to\n * express the query to be run against the index.\n *\n * Note that although this function takes a callback parameter it is _not_ an\n * asynchronous operation, the callback is just yielded a query object to be\n * customized.\n *\n * @param {lunr.Index~queryBuilder} fn - A function that is used to build the query.\n * @returns {lunr.Index~Result[]}\n */\nlunr.Index.prototype.query = function (fn) {\n  // for each query clause\n  // * process terms\n  // * expand terms from token set\n  // * find matching documents and metadata\n  // * get document vectors\n  // * score documents\n\n  var query = new lunr.Query(this.fields),\n      matchingFields = Object.create(null),\n      queryVectors = Object.create(null),\n      termFieldCache = Object.create(null),\n      requiredMatches = Object.create(null),\n      prohibitedMatches = Object.create(null)\n\n  /*\n   * To support field level boosts a query vector is created per\n   * field. An empty vector is eagerly created to support negated\n   * queries.\n   */\n  for (var i = 0; i < this.fields.length; i++) {\n    queryVectors[this.fields[i]] = new lunr.Vector\n  }\n\n  fn.call(query, query)\n\n  for (var i = 0; i < query.clauses.length; i++) {\n    /*\n     * Unless the pipeline has been disabled for this term, which is\n     * the case for terms with wildcards, we need to pass the clause\n     * term through the search pipeline. A pipeline returns an array\n     * of processed terms. Pipeline functions may expand the passed\n     * term, which means we may end up performing multiple index lookups\n     * for a single query term.\n     */\n    var clause = query.clauses[i],\n        terms = null,\n        clauseMatches = lunr.Set.empty\n\n    if (clause.usePipeline) {\n      terms = this.pipeline.runString(clause.term, {\n        fields: clause.fields\n      })\n    } else {\n      terms = [clause.term]\n    }\n\n    for (var m = 0; m < terms.length; m++) {\n      var term = terms[m]\n\n      /*\n       * Each term returned from the pipeline needs to use the same query\n       * clause object, e.g. the same boost and or edit distance. The\n       * simplest way to do this is to re-use the clause object but mutate\n       * its term property.\n       */\n      clause.term = term\n\n      /*\n       * From the term in the clause we create a token set which will then\n       * be used to intersect the indexes token set to get a list of terms\n       * to lookup in the inverted index\n       */\n      var termTokenSet = lunr.TokenSet.fromClause(clause),\n          expandedTerms = this.tokenSet.intersect(termTokenSet).toArray()\n\n      /*\n       * If a term marked as required does not exist in the tokenSet it is\n       * impossible for the search to return any matches. We set all the field\n       * scoped required matches set to empty and stop examining any further\n       * clauses.\n       */\n      if (expandedTerms.length === 0 && clause.presence === lunr.Query.presence.REQUIRED) {\n        for (var k = 0; k < clause.fields.length; k++) {\n          var field = clause.fields[k]\n          requiredMatches[field] = lunr.Set.empty\n        }\n\n        break\n      }\n\n      for (var j = 0; j < expandedTerms.length; j++) {\n        /*\n         * For each term get the posting and termIndex, this is required for\n         * building the query vector.\n         */\n        var expandedTerm = expandedTerms[j],\n            posting = this.invertedIndex[expandedTerm],\n            termIndex = posting._index\n\n        for (var k = 0; k < clause.fields.length; k++) {\n          /*\n           * For each field that this query term is scoped by (by default\n           * all fields are in scope) we need to get all the document refs\n           * that have this term in that field.\n           *\n           * The posting is the entry in the invertedIndex for the matching\n           * term from above.\n           */\n          var field = clause.fields[k],\n              fieldPosting = posting[field],\n              matchingDocumentRefs = Object.keys(fieldPosting),\n              termField = expandedTerm + \"/\" + field,\n              matchingDocumentsSet = new lunr.Set(matchingDocumentRefs)\n\n          /*\n           * if the presence of this term is required ensure that the matching\n           * documents are added to the set of required matches for this clause.\n           *\n           */\n          if (clause.presence == lunr.Query.presence.REQUIRED) {\n            clauseMatches = clauseMatches.union(matchingDocumentsSet)\n\n            if (requiredMatches[field] === undefined) {\n              requiredMatches[field] = lunr.Set.complete\n            }\n          }\n\n          /*\n           * if the presence of this term is prohibited ensure that the matching\n           * documents are added to the set of prohibited matches for this field,\n           * creating that set if it does not yet exist.\n           */\n          if (clause.presence == lunr.Query.presence.PROHIBITED) {\n            if (prohibitedMatches[field] === undefined) {\n              prohibitedMatches[field] = lunr.Set.empty\n            }\n\n            prohibitedMatches[field] = prohibitedMatches[field].union(matchingDocumentsSet)\n\n            /*\n             * Prohibited matches should not be part of the query vector used for\n             * similarity scoring and no metadata should be extracted so we continue\n             * to the next field\n             */\n            continue\n          }\n\n          /*\n           * The query field vector is populated using the termIndex found for\n           * the term and a unit value with the appropriate boost applied.\n           * Using upsert because there could already be an entry in the vector\n           * for the term we are working with. In that case we just add the scores\n           * together.\n           */\n          queryVectors[field].upsert(termIndex, clause.boost, function (a, b) { return a + b })\n\n          /**\n           * If we've already seen this term, field combo then we've already collected\n           * the matching documents and metadata, no need to go through all that again\n           */\n          if (termFieldCache[termField]) {\n            continue\n          }\n\n          for (var l = 0; l < matchingDocumentRefs.length; l++) {\n            /*\n             * All metadata for this term/field/document triple\n             * are then extracted and collected into an instance\n             * of lunr.MatchData ready to be returned in the query\n             * results\n             */\n            var matchingDocumentRef = matchingDocumentRefs[l],\n                matchingFieldRef = new lunr.FieldRef (matchingDocumentRef, field),\n                metadata = fieldPosting[matchingDocumentRef],\n                fieldMatch\n\n            if ((fieldMatch = matchingFields[matchingFieldRef]) === undefined) {\n              matchingFields[matchingFieldRef] = new lunr.MatchData (expandedTerm, field, metadata)\n            } else {\n              fieldMatch.add(expandedTerm, field, metadata)\n            }\n\n          }\n\n          termFieldCache[termField] = true\n        }\n      }\n    }\n\n    /**\n     * If the presence was required we need to update the requiredMatches field sets.\n     * We do this after all fields for the term have collected their matches because\n     * the clause terms presence is required in _any_ of the fields not _all_ of the\n     * fields.\n     */\n    if (clause.presence === lunr.Query.presence.REQUIRED) {\n      for (var k = 0; k < clause.fields.length; k++) {\n        var field = clause.fields[k]\n        requiredMatches[field] = requiredMatches[field].intersect(clauseMatches)\n      }\n    }\n  }\n\n  /**\n   * Need to combine the field scoped required and prohibited\n   * matching documents into a global set of required and prohibited\n   * matches\n   */\n  var allRequiredMatches = lunr.Set.complete,\n      allProhibitedMatches = lunr.Set.empty\n\n  for (var i = 0; i < this.fields.length; i++) {\n    var field = this.fields[i]\n\n    if (requiredMatches[field]) {\n      allRequiredMatches = allRequiredMatches.intersect(requiredMatches[field])\n    }\n\n    if (prohibitedMatches[field]) {\n      allProhibitedMatches = allProhibitedMatches.union(prohibitedMatches[field])\n    }\n  }\n\n  var matchingFieldRefs = Object.keys(matchingFields),\n      results = [],\n      matches = Object.create(null)\n\n  /*\n   * If the query is negated (contains only prohibited terms)\n   * we need to get _all_ fieldRefs currently existing in the\n   * index. This is only done when we know that the query is\n   * entirely prohibited terms to avoid any cost of getting all\n   * fieldRefs unnecessarily.\n   *\n   * Additionally, blank MatchData must be created to correctly\n   * populate the results.\n   */\n  if (query.isNegated()) {\n    matchingFieldRefs = Object.keys(this.fieldVectors)\n\n    for (var i = 0; i < matchingFieldRefs.length; i++) {\n      var matchingFieldRef = matchingFieldRefs[i]\n      var fieldRef = lunr.FieldRef.fromString(matchingFieldRef)\n      matchingFields[matchingFieldRef] = new lunr.MatchData\n    }\n  }\n\n  for (var i = 0; i < matchingFieldRefs.length; i++) {\n    /*\n     * Currently we have document fields that match the query, but we\n     * need to return documents. The matchData and scores are combined\n     * from multiple fields belonging to the same document.\n     *\n     * Scores are calculated by field, using the query vectors created\n     * above, and combined into a final document score using addition.\n     */\n    var fieldRef = lunr.FieldRef.fromString(matchingFieldRefs[i]),\n        docRef = fieldRef.docRef\n\n    if (!allRequiredMatches.contains(docRef)) {\n      continue\n    }\n\n    if (allProhibitedMatches.contains(docRef)) {\n      continue\n    }\n\n    var fieldVector = this.fieldVectors[fieldRef],\n        score = queryVectors[fieldRef.fieldName].similarity(fieldVector),\n        docMatch\n\n    if ((docMatch = matches[docRef]) !== undefined) {\n      docMatch.score += score\n      docMatch.matchData.combine(matchingFields[fieldRef])\n    } else {\n      var match = {\n        ref: docRef,\n        score: score,\n        matchData: matchingFields[fieldRef]\n      }\n      matches[docRef] = match\n      results.push(match)\n    }\n  }\n\n  /*\n   * Sort the results objects by score, highest first.\n   */\n  return results.sort(function (a, b) {\n    return b.score - a.score\n  })\n}\n\n/**\n * Prepares the index for JSON serialization.\n *\n * The schema for this JSON blob will be described in a\n * separate JSON schema file.\n *\n * @returns {Object}\n */\nlunr.Index.prototype.toJSON = function () {\n  var invertedIndex = Object.keys(this.invertedIndex)\n    .sort()\n    .map(function (term) {\n      return [term, this.invertedIndex[term]]\n    }, this)\n\n  var fieldVectors = Object.keys(this.fieldVectors)\n    .map(function (ref) {\n      return [ref, this.fieldVectors[ref].toJSON()]\n    }, this)\n\n  return {\n    version: lunr.version,\n    fields: this.fields,\n    fieldVectors: fieldVectors,\n    invertedIndex: invertedIndex,\n    pipeline: this.pipeline.toJSON()\n  }\n}\n\n/**\n * Loads a previously serialized lunr.Index\n *\n * @param {Object} serializedIndex - A previously serialized lunr.Index\n * @returns {lunr.Index}\n */\nlunr.Index.load = function (serializedIndex) {\n  var attrs = {},\n      fieldVectors = {},\n      serializedVectors = serializedIndex.fieldVectors,\n      invertedIndex = Object.create(null),\n      serializedInvertedIndex = serializedIndex.invertedIndex,\n      tokenSetBuilder = new lunr.TokenSet.Builder,\n      pipeline = lunr.Pipeline.load(serializedIndex.pipeline)\n\n  if (serializedIndex.version != lunr.version) {\n    lunr.utils.warn(\"Version mismatch when loading serialised index. Current version of lunr '\" + lunr.version + \"' does not match serialized index '\" + serializedIndex.version + \"'\")\n  }\n\n  for (var i = 0; i < serializedVectors.length; i++) {\n    var tuple = serializedVectors[i],\n        ref = tuple[0],\n        elements = tuple[1]\n\n    fieldVectors[ref] = new lunr.Vector(elements)\n  }\n\n  for (var i = 0; i < serializedInvertedIndex.length; i++) {\n    var tuple = serializedInvertedIndex[i],\n        term = tuple[0],\n        posting = tuple[1]\n\n    tokenSetBuilder.insert(term)\n    invertedIndex[term] = posting\n  }\n\n  tokenSetBuilder.finish()\n\n  attrs.fields = serializedIndex.fields\n\n  attrs.fieldVectors = fieldVectors\n  attrs.invertedIndex = invertedIndex\n  attrs.tokenSet = tokenSetBuilder.root\n  attrs.pipeline = pipeline\n\n  return new lunr.Index(attrs)\n}\n/*!\n * lunr.Builder\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * lunr.Builder performs indexing on a set of documents and\n * returns instances of lunr.Index ready for querying.\n *\n * All configuration of the index is done via the builder, the\n * fields to index, the document reference, the text processing\n * pipeline and document scoring parameters are all set on the\n * builder before indexing.\n *\n * @constructor\n * @property {string} _ref - Internal reference to the document reference field.\n * @property {string[]} _fields - Internal reference to the document fields to index.\n * @property {object} invertedIndex - The inverted index maps terms to document fields.\n * @property {object} documentTermFrequencies - Keeps track of document term frequencies.\n * @property {object} documentLengths - Keeps track of the length of documents added to the index.\n * @property {lunr.tokenizer} tokenizer - Function for splitting strings into tokens for indexing.\n * @property {lunr.Pipeline} pipeline - The pipeline performs text processing on tokens before indexing.\n * @property {lunr.Pipeline} searchPipeline - A pipeline for processing search terms before querying the index.\n * @property {number} documentCount - Keeps track of the total number of documents indexed.\n * @property {number} _b - A parameter to control field length normalization, setting this to 0 disabled normalization, 1 fully normalizes field lengths, the default value is 0.75.\n * @property {number} _k1 - A parameter to control how quickly an increase in term frequency results in term frequency saturation, the default value is 1.2.\n * @property {number} termIndex - A counter incremented for each unique term, used to identify a terms position in the vector space.\n * @property {array} metadataWhitelist - A list of metadata keys that have been whitelisted for entry in the index.\n */\nlunr.Builder = function () {\n  this._ref = \"id\"\n  this._fields = Object.create(null)\n  this._documents = Object.create(null)\n  this.invertedIndex = Object.create(null)\n  this.fieldTermFrequencies = {}\n  this.fieldLengths = {}\n  this.tokenizer = lunr.tokenizer\n  this.pipeline = new lunr.Pipeline\n  this.searchPipeline = new lunr.Pipeline\n  this.documentCount = 0\n  this._b = 0.75\n  this._k1 = 1.2\n  this.termIndex = 0\n  this.metadataWhitelist = []\n}\n\n/**\n * Sets the document field used as the document reference. Every document must have this field.\n * The type of this field in the document should be a string, if it is not a string it will be\n * coerced into a string by calling toString.\n *\n * The default ref is 'id'.\n *\n * The ref should _not_ be changed during indexing, it should be set before any documents are\n * added to the index. Changing it during indexing can lead to inconsistent results.\n *\n * @param {string} ref - The name of the reference field in the document.\n */\nlunr.Builder.prototype.ref = function (ref) {\n  this._ref = ref\n}\n\n/**\n * A function that is used to extract a field from a document.\n *\n * Lunr expects a field to be at the top level of a document, if however the field\n * is deeply nested within a document an extractor function can be used to extract\n * the right field for indexing.\n *\n * @callback fieldExtractor\n * @param {object} doc - The document being added to the index.\n * @returns {?(string|object|object[])} obj - The object that will be indexed for this field.\n * @example <caption>Extracting a nested field</caption>\n * function (doc) { return doc.nested.field }\n */\n\n/**\n * Adds a field to the list of document fields that will be indexed. Every document being\n * indexed should have this field. Null values for this field in indexed documents will\n * not cause errors but will limit the chance of that document being retrieved by searches.\n *\n * All fields should be added before adding documents to the index. Adding fields after\n * a document has been indexed will have no effect on already indexed documents.\n *\n * Fields can be boosted at build time. This allows terms within that field to have more\n * importance when ranking search results. Use a field boost to specify that matches within\n * one field are more important than other fields.\n *\n * @param {string} fieldName - The name of a field to index in all documents.\n * @param {object} attributes - Optional attributes associated with this field.\n * @param {number} [attributes.boost=1] - Boost applied to all terms within this field.\n * @param {fieldExtractor} [attributes.extractor] - Function to extract a field from a document.\n * @throws {RangeError} fieldName cannot contain unsupported characters '/'\n */\nlunr.Builder.prototype.field = function (fieldName, attributes) {\n  if (/\\//.test(fieldName)) {\n    throw new RangeError (\"Field '\" + fieldName + \"' contains illegal character '/'\")\n  }\n\n  this._fields[fieldName] = attributes || {}\n}\n\n/**\n * A parameter to tune the amount of field length normalisation that is applied when\n * calculating relevance scores. A value of 0 will completely disable any normalisation\n * and a value of 1 will fully normalise field lengths. The default is 0.75. Values of b\n * will be clamped to the range 0 - 1.\n *\n * @param {number} number - The value to set for this tuning parameter.\n */\nlunr.Builder.prototype.b = function (number) {\n  if (number < 0) {\n    this._b = 0\n  } else if (number > 1) {\n    this._b = 1\n  } else {\n    this._b = number\n  }\n}\n\n/**\n * A parameter that controls the speed at which a rise in term frequency results in term\n * frequency saturation. The default value is 1.2. Setting this to a higher value will give\n * slower saturation levels, a lower value will result in quicker saturation.\n *\n * @param {number} number - The value to set for this tuning parameter.\n */\nlunr.Builder.prototype.k1 = function (number) {\n  this._k1 = number\n}\n\n/**\n * Adds a document to the index.\n *\n * Before adding fields to the index the index should have been fully setup, with the document\n * ref and all fields to index already having been specified.\n *\n * The document must have a field name as specified by the ref (by default this is 'id') and\n * it should have all fields defined for indexing, though null or undefined values will not\n * cause errors.\n *\n * Entire documents can be boosted at build time. Applying a boost to a document indicates that\n * this document should rank higher in search results than other documents.\n *\n * @param {object} doc - The document to add to the index.\n * @param {object} attributes - Optional attributes associated with this document.\n * @param {number} [attributes.boost=1] - Boost applied to all terms within this document.\n */\nlunr.Builder.prototype.add = function (doc, attributes) {\n  var docRef = doc[this._ref],\n      fields = Object.keys(this._fields)\n\n  this._documents[docRef] = attributes || {}\n  this.documentCount += 1\n\n  for (var i = 0; i < fields.length; i++) {\n    var fieldName = fields[i],\n        extractor = this._fields[fieldName].extractor,\n        field = extractor ? extractor(doc) : doc[fieldName],\n        tokens = this.tokenizer(field, {\n          fields: [fieldName]\n        }),\n        terms = this.pipeline.run(tokens),\n        fieldRef = new lunr.FieldRef (docRef, fieldName),\n        fieldTerms = Object.create(null)\n\n    this.fieldTermFrequencies[fieldRef] = fieldTerms\n    this.fieldLengths[fieldRef] = 0\n\n    // store the length of this field for this document\n    this.fieldLengths[fieldRef] += terms.length\n\n    // calculate term frequencies for this field\n    for (var j = 0; j < terms.length; j++) {\n      var term = terms[j]\n\n      if (fieldTerms[term] == undefined) {\n        fieldTerms[term] = 0\n      }\n\n      fieldTerms[term] += 1\n\n      // add to inverted index\n      // create an initial posting if one doesn't exist\n      if (this.invertedIndex[term] == undefined) {\n        var posting = Object.create(null)\n        posting[\"_index\"] = this.termIndex\n        this.termIndex += 1\n\n        for (var k = 0; k < fields.length; k++) {\n          posting[fields[k]] = Object.create(null)\n        }\n\n        this.invertedIndex[term] = posting\n      }\n\n      // add an entry for this term/fieldName/docRef to the invertedIndex\n      if (this.invertedIndex[term][fieldName][docRef] == undefined) {\n        this.invertedIndex[term][fieldName][docRef] = Object.create(null)\n      }\n\n      // store all whitelisted metadata about this token in the\n      // inverted index\n      for (var l = 0; l < this.metadataWhitelist.length; l++) {\n        var metadataKey = this.metadataWhitelist[l],\n            metadata = term.metadata[metadataKey]\n\n        if (this.invertedIndex[term][fieldName][docRef][metadataKey] == undefined) {\n          this.invertedIndex[term][fieldName][docRef][metadataKey] = []\n        }\n\n        this.invertedIndex[term][fieldName][docRef][metadataKey].push(metadata)\n      }\n    }\n\n  }\n}\n\n/**\n * Calculates the average document length for this index\n *\n * @private\n */\nlunr.Builder.prototype.calculateAverageFieldLengths = function () {\n\n  var fieldRefs = Object.keys(this.fieldLengths),\n      numberOfFields = fieldRefs.length,\n      accumulator = {},\n      documentsWithField = {}\n\n  for (var i = 0; i < numberOfFields; i++) {\n    var fieldRef = lunr.FieldRef.fromString(fieldRefs[i]),\n        field = fieldRef.fieldName\n\n    documentsWithField[field] || (documentsWithField[field] = 0)\n    documentsWithField[field] += 1\n\n    accumulator[field] || (accumulator[field] = 0)\n    accumulator[field] += this.fieldLengths[fieldRef]\n  }\n\n  var fields = Object.keys(this._fields)\n\n  for (var i = 0; i < fields.length; i++) {\n    var fieldName = fields[i]\n    accumulator[fieldName] = accumulator[fieldName] / documentsWithField[fieldName]\n  }\n\n  this.averageFieldLength = accumulator\n}\n\n/**\n * Builds a vector space model of every document using lunr.Vector\n *\n * @private\n */\nlunr.Builder.prototype.createFieldVectors = function () {\n  var fieldVectors = {},\n      fieldRefs = Object.keys(this.fieldTermFrequencies),\n      fieldRefsLength = fieldRefs.length,\n      termIdfCache = Object.create(null)\n\n  for (var i = 0; i < fieldRefsLength; i++) {\n    var fieldRef = lunr.FieldRef.fromString(fieldRefs[i]),\n        fieldName = fieldRef.fieldName,\n        fieldLength = this.fieldLengths[fieldRef],\n        fieldVector = new lunr.Vector,\n        termFrequencies = this.fieldTermFrequencies[fieldRef],\n        terms = Object.keys(termFrequencies),\n        termsLength = terms.length\n\n\n    var fieldBoost = this._fields[fieldName].boost || 1,\n        docBoost = this._documents[fieldRef.docRef].boost || 1\n\n    for (var j = 0; j < termsLength; j++) {\n      var term = terms[j],\n          tf = termFrequencies[term],\n          termIndex = this.invertedIndex[term]._index,\n          idf, score, scoreWithPrecision\n\n      if (termIdfCache[term] === undefined) {\n        idf = lunr.idf(this.invertedIndex[term], this.documentCount)\n        termIdfCache[term] = idf\n      } else {\n        idf = termIdfCache[term]\n      }\n\n      score = idf * ((this._k1 + 1) * tf) / (this._k1 * (1 - this._b + this._b * (fieldLength / this.averageFieldLength[fieldName])) + tf)\n      score *= fieldBoost\n      score *= docBoost\n      scoreWithPrecision = Math.round(score * 1000) / 1000\n      // Converts 1.23456789 to 1.234.\n      // Reducing the precision so that the vectors take up less\n      // space when serialised. Doing it now so that they behave\n      // the same before and after serialisation. Also, this is\n      // the fastest approach to reducing a number's precision in\n      // JavaScript.\n\n      fieldVector.insert(termIndex, scoreWithPrecision)\n    }\n\n    fieldVectors[fieldRef] = fieldVector\n  }\n\n  this.fieldVectors = fieldVectors\n}\n\n/**\n * Creates a token set of all tokens in the index using lunr.TokenSet\n *\n * @private\n */\nlunr.Builder.prototype.createTokenSet = function () {\n  this.tokenSet = lunr.TokenSet.fromArray(\n    Object.keys(this.invertedIndex).sort()\n  )\n}\n\n/**\n * Builds the index, creating an instance of lunr.Index.\n *\n * This completes the indexing process and should only be called\n * once all documents have been added to the index.\n *\n * @returns {lunr.Index}\n */\nlunr.Builder.prototype.build = function () {\n  this.calculateAverageFieldLengths()\n  this.createFieldVectors()\n  this.createTokenSet()\n\n  return new lunr.Index({\n    invertedIndex: this.invertedIndex,\n    fieldVectors: this.fieldVectors,\n    tokenSet: this.tokenSet,\n    fields: Object.keys(this._fields),\n    pipeline: this.searchPipeline\n  })\n}\n\n/**\n * Applies a plugin to the index builder.\n *\n * A plugin is a function that is called with the index builder as its context.\n * Plugins can be used to customise or extend the behaviour of the index\n * in some way. A plugin is just a function, that encapsulated the custom\n * behaviour that should be applied when building the index.\n *\n * The plugin function will be called with the index builder as its argument, additional\n * arguments can also be passed when calling use. The function will be called\n * with the index builder as its context.\n *\n * @param {Function} plugin The plugin to apply.\n */\nlunr.Builder.prototype.use = function (fn) {\n  var args = Array.prototype.slice.call(arguments, 1)\n  args.unshift(this)\n  fn.apply(this, args)\n}\n/**\n * Contains and collects metadata about a matching document.\n * A single instance of lunr.MatchData is returned as part of every\n * lunr.Index~Result.\n *\n * @constructor\n * @param {string} term - The term this match data is associated with\n * @param {string} field - The field in which the term was found\n * @param {object} metadata - The metadata recorded about this term in this field\n * @property {object} metadata - A cloned collection of metadata associated with this document.\n * @see {@link lunr.Index~Result}\n */\nlunr.MatchData = function (term, field, metadata) {\n  var clonedMetadata = Object.create(null),\n      metadataKeys = Object.keys(metadata || {})\n\n  // Cloning the metadata to prevent the original\n  // being mutated during match data combination.\n  // Metadata is kept in an array within the inverted\n  // index so cloning the data can be done with\n  // Array#slice\n  for (var i = 0; i < metadataKeys.length; i++) {\n    var key = metadataKeys[i]\n    clonedMetadata[key] = metadata[key].slice()\n  }\n\n  this.metadata = Object.create(null)\n\n  if (term !== undefined) {\n    this.metadata[term] = Object.create(null)\n    this.metadata[term][field] = clonedMetadata\n  }\n}\n\n/**\n * An instance of lunr.MatchData will be created for every term that matches a\n * document. However only one instance is required in a lunr.Index~Result. This\n * method combines metadata from another instance of lunr.MatchData with this\n * objects metadata.\n *\n * @param {lunr.MatchData} otherMatchData - Another instance of match data to merge with this one.\n * @see {@link lunr.Index~Result}\n */\nlunr.MatchData.prototype.combine = function (otherMatchData) {\n  var terms = Object.keys(otherMatchData.metadata)\n\n  for (var i = 0; i < terms.length; i++) {\n    var term = terms[i],\n        fields = Object.keys(otherMatchData.metadata[term])\n\n    if (this.metadata[term] == undefined) {\n      this.metadata[term] = Object.create(null)\n    }\n\n    for (var j = 0; j < fields.length; j++) {\n      var field = fields[j],\n          keys = Object.keys(otherMatchData.metadata[term][field])\n\n      if (this.metadata[term][field] == undefined) {\n        this.metadata[term][field] = Object.create(null)\n      }\n\n      for (var k = 0; k < keys.length; k++) {\n        var key = keys[k]\n\n        if (this.metadata[term][field][key] == undefined) {\n          this.metadata[term][field][key] = otherMatchData.metadata[term][field][key]\n        } else {\n          this.metadata[term][field][key] = this.metadata[term][field][key].concat(otherMatchData.metadata[term][field][key])\n        }\n\n      }\n    }\n  }\n}\n\n/**\n * Add metadata for a term/field pair to this instance of match data.\n *\n * @param {string} term - The term this match data is associated with\n * @param {string} field - The field in which the term was found\n * @param {object} metadata - The metadata recorded about this term in this field\n */\nlunr.MatchData.prototype.add = function (term, field, metadata) {\n  if (!(term in this.metadata)) {\n    this.metadata[term] = Object.create(null)\n    this.metadata[term][field] = metadata\n    return\n  }\n\n  if (!(field in this.metadata[term])) {\n    this.metadata[term][field] = metadata\n    return\n  }\n\n  var metadataKeys = Object.keys(metadata)\n\n  for (var i = 0; i < metadataKeys.length; i++) {\n    var key = metadataKeys[i]\n\n    if (key in this.metadata[term][field]) {\n      this.metadata[term][field][key] = this.metadata[term][field][key].concat(metadata[key])\n    } else {\n      this.metadata[term][field][key] = metadata[key]\n    }\n  }\n}\n/**\n * A lunr.Query provides a programmatic way of defining queries to be performed\n * against a {@link lunr.Index}.\n *\n * Prefer constructing a lunr.Query using the {@link lunr.Index#query} method\n * so the query object is pre-initialized with the right index fields.\n *\n * @constructor\n * @property {lunr.Query~Clause[]} clauses - An array of query clauses.\n * @property {string[]} allFields - An array of all available fields in a lunr.Index.\n */\nlunr.Query = function (allFields) {\n  this.clauses = []\n  this.allFields = allFields\n}\n\n/**\n * Constants for indicating what kind of automatic wildcard insertion will be used when constructing a query clause.\n *\n * This allows wildcards to be added to the beginning and end of a term without having to manually do any string\n * concatenation.\n *\n * The wildcard constants can be bitwise combined to select both leading and trailing wildcards.\n *\n * @constant\n * @default\n * @property {number} wildcard.NONE - The term will have no wildcards inserted, this is the default behaviour\n * @property {number} wildcard.LEADING - Prepend the term with a wildcard, unless a leading wildcard already exists\n * @property {number} wildcard.TRAILING - Append a wildcard to the term, unless a trailing wildcard already exists\n * @see lunr.Query~Clause\n * @see lunr.Query#clause\n * @see lunr.Query#term\n * @example <caption>query term with trailing wildcard</caption>\n * query.term('foo', { wildcard: lunr.Query.wildcard.TRAILING })\n * @example <caption>query term with leading and trailing wildcard</caption>\n * query.term('foo', {\n *   wildcard: lunr.Query.wildcard.LEADING | lunr.Query.wildcard.TRAILING\n * })\n */\n\nlunr.Query.wildcard = new String (\"*\")\nlunr.Query.wildcard.NONE = 0\nlunr.Query.wildcard.LEADING = 1\nlunr.Query.wildcard.TRAILING = 2\n\n/**\n * Constants for indicating what kind of presence a term must have in matching documents.\n *\n * @constant\n * @enum {number}\n * @see lunr.Query~Clause\n * @see lunr.Query#clause\n * @see lunr.Query#term\n * @example <caption>query term with required presence</caption>\n * query.term('foo', { presence: lunr.Query.presence.REQUIRED })\n */\nlunr.Query.presence = {\n  /**\n   * Term's presence in a document is optional, this is the default value.\n   */\n  OPTIONAL: 1,\n\n  /**\n   * Term's presence in a document is required, documents that do not contain\n   * this term will not be returned.\n   */\n  REQUIRED: 2,\n\n  /**\n   * Term's presence in a document is prohibited, documents that do contain\n   * this term will not be returned.\n   */\n  PROHIBITED: 3\n}\n\n/**\n * A single clause in a {@link lunr.Query} contains a term and details on how to\n * match that term against a {@link lunr.Index}.\n *\n * @typedef {Object} lunr.Query~Clause\n * @property {string[]} fields - The fields in an index this clause should be matched against.\n * @property {number} [boost=1] - Any boost that should be applied when matching this clause.\n * @property {number} [editDistance] - Whether the term should have fuzzy matching applied, and how fuzzy the match should be.\n * @property {boolean} [usePipeline] - Whether the term should be passed through the search pipeline.\n * @property {number} [wildcard=lunr.Query.wildcard.NONE] - Whether the term should have wildcards appended or prepended.\n * @property {number} [presence=lunr.Query.presence.OPTIONAL] - The terms presence in any matching documents.\n */\n\n/**\n * Adds a {@link lunr.Query~Clause} to this query.\n *\n * Unless the clause contains the fields to be matched all fields will be matched. In addition\n * a default boost of 1 is applied to the clause.\n *\n * @param {lunr.Query~Clause} clause - The clause to add to this query.\n * @see lunr.Query~Clause\n * @returns {lunr.Query}\n */\nlunr.Query.prototype.clause = function (clause) {\n  if (!('fields' in clause)) {\n    clause.fields = this.allFields\n  }\n\n  if (!('boost' in clause)) {\n    clause.boost = 1\n  }\n\n  if (!('usePipeline' in clause)) {\n    clause.usePipeline = true\n  }\n\n  if (!('wildcard' in clause)) {\n    clause.wildcard = lunr.Query.wildcard.NONE\n  }\n\n  if ((clause.wildcard & lunr.Query.wildcard.LEADING) && (clause.term.charAt(0) != lunr.Query.wildcard)) {\n    clause.term = \"*\" + clause.term\n  }\n\n  if ((clause.wildcard & lunr.Query.wildcard.TRAILING) && (clause.term.slice(-1) != lunr.Query.wildcard)) {\n    clause.term = \"\" + clause.term + \"*\"\n  }\n\n  if (!('presence' in clause)) {\n    clause.presence = lunr.Query.presence.OPTIONAL\n  }\n\n  this.clauses.push(clause)\n\n  return this\n}\n\n/**\n * A negated query is one in which every clause has a presence of\n * prohibited. These queries require some special processing to return\n * the expected results.\n *\n * @returns boolean\n */\nlunr.Query.prototype.isNegated = function () {\n  for (var i = 0; i < this.clauses.length; i++) {\n    if (this.clauses[i].presence != lunr.Query.presence.PROHIBITED) {\n      return false\n    }\n  }\n\n  return true\n}\n\n/**\n * Adds a term to the current query, under the covers this will create a {@link lunr.Query~Clause}\n * to the list of clauses that make up this query.\n *\n * The term is used as is, i.e. no tokenization will be performed by this method. Instead conversion\n * to a token or token-like string should be done before calling this method.\n *\n * The term will be converted to a string by calling `toString`. Multiple terms can be passed as an\n * array, each term in the array will share the same options.\n *\n * @param {object|object[]} term - The term(s) to add to the query.\n * @param {object} [options] - Any additional properties to add to the query clause.\n * @returns {lunr.Query}\n * @see lunr.Query#clause\n * @see lunr.Query~Clause\n * @example <caption>adding a single term to a query</caption>\n * query.term(\"foo\")\n * @example <caption>adding a single term to a query and specifying search fields, term boost and automatic trailing wildcard</caption>\n * query.term(\"foo\", {\n *   fields: [\"title\"],\n *   boost: 10,\n *   wildcard: lunr.Query.wildcard.TRAILING\n * })\n * @example <caption>using lunr.tokenizer to convert a string to tokens before using them as terms</caption>\n * query.term(lunr.tokenizer(\"foo bar\"))\n */\nlunr.Query.prototype.term = function (term, options) {\n  if (Array.isArray(term)) {\n    term.forEach(function (t) { this.term(t, lunr.utils.clone(options)) }, this)\n    return this\n  }\n\n  var clause = options || {}\n  clause.term = term.toString()\n\n  this.clause(clause)\n\n  return this\n}\nlunr.QueryParseError = function (message, start, end) {\n  this.name = \"QueryParseError\"\n  this.message = message\n  this.start = start\n  this.end = end\n}\n\nlunr.QueryParseError.prototype = new Error\nlunr.QueryLexer = function (str) {\n  this.lexemes = []\n  this.str = str\n  this.length = str.length\n  this.pos = 0\n  this.start = 0\n  this.escapeCharPositions = []\n}\n\nlunr.QueryLexer.prototype.run = function () {\n  var state = lunr.QueryLexer.lexText\n\n  while (state) {\n    state = state(this)\n  }\n}\n\nlunr.QueryLexer.prototype.sliceString = function () {\n  var subSlices = [],\n      sliceStart = this.start,\n      sliceEnd = this.pos\n\n  for (var i = 0; i < this.escapeCharPositions.length; i++) {\n    sliceEnd = this.escapeCharPositions[i]\n    subSlices.push(this.str.slice(sliceStart, sliceEnd))\n    sliceStart = sliceEnd + 1\n  }\n\n  subSlices.push(this.str.slice(sliceStart, this.pos))\n  this.escapeCharPositions.length = 0\n\n  return subSlices.join('')\n}\n\nlunr.QueryLexer.prototype.emit = function (type) {\n  this.lexemes.push({\n    type: type,\n    str: this.sliceString(),\n    start: this.start,\n    end: this.pos\n  })\n\n  this.start = this.pos\n}\n\nlunr.QueryLexer.prototype.escapeCharacter = function () {\n  this.escapeCharPositions.push(this.pos - 1)\n  this.pos += 1\n}\n\nlunr.QueryLexer.prototype.next = function () {\n  if (this.pos >= this.length) {\n    return lunr.QueryLexer.EOS\n  }\n\n  var char = this.str.charAt(this.pos)\n  this.pos += 1\n  return char\n}\n\nlunr.QueryLexer.prototype.width = function () {\n  return this.pos - this.start\n}\n\nlunr.QueryLexer.prototype.ignore = function () {\n  if (this.start == this.pos) {\n    this.pos += 1\n  }\n\n  this.start = this.pos\n}\n\nlunr.QueryLexer.prototype.backup = function () {\n  this.pos -= 1\n}\n\nlunr.QueryLexer.prototype.acceptDigitRun = function () {\n  var char, charCode\n\n  do {\n    char = this.next()\n    charCode = char.charCodeAt(0)\n  } while (charCode > 47 && charCode < 58)\n\n  if (char != lunr.QueryLexer.EOS) {\n    this.backup()\n  }\n}\n\nlunr.QueryLexer.prototype.more = function () {\n  return this.pos < this.length\n}\n\nlunr.QueryLexer.EOS = 'EOS'\nlunr.QueryLexer.FIELD = 'FIELD'\nlunr.QueryLexer.TERM = 'TERM'\nlunr.QueryLexer.EDIT_DISTANCE = 'EDIT_DISTANCE'\nlunr.QueryLexer.BOOST = 'BOOST'\nlunr.QueryLexer.PRESENCE = 'PRESENCE'\n\nlunr.QueryLexer.lexField = function (lexer) {\n  lexer.backup()\n  lexer.emit(lunr.QueryLexer.FIELD)\n  lexer.ignore()\n  return lunr.QueryLexer.lexText\n}\n\nlunr.QueryLexer.lexTerm = function (lexer) {\n  if (lexer.width() > 1) {\n    lexer.backup()\n    lexer.emit(lunr.QueryLexer.TERM)\n  }\n\n  lexer.ignore()\n\n  if (lexer.more()) {\n    return lunr.QueryLexer.lexText\n  }\n}\n\nlunr.QueryLexer.lexEditDistance = function (lexer) {\n  lexer.ignore()\n  lexer.acceptDigitRun()\n  lexer.emit(lunr.QueryLexer.EDIT_DISTANCE)\n  return lunr.QueryLexer.lexText\n}\n\nlunr.QueryLexer.lexBoost = function (lexer) {\n  lexer.ignore()\n  lexer.acceptDigitRun()\n  lexer.emit(lunr.QueryLexer.BOOST)\n  return lunr.QueryLexer.lexText\n}\n\nlunr.QueryLexer.lexEOS = function (lexer) {\n  if (lexer.width() > 0) {\n    lexer.emit(lunr.QueryLexer.TERM)\n  }\n}\n\n// This matches the separator used when tokenising fields\n// within a document. These should match otherwise it is\n// not possible to search for some tokens within a document.\n//\n// It is possible for the user to change the separator on the\n// tokenizer so it _might_ clash with any other of the special\n// characters already used within the search string, e.g. :.\n//\n// This means that it is possible to change the separator in\n// such a way that makes some words unsearchable using a search\n// string.\nlunr.QueryLexer.termSeparator = lunr.tokenizer.separator\n\nlunr.QueryLexer.lexText = function (lexer) {\n  while (true) {\n    var char = lexer.next()\n\n    if (char == lunr.QueryLexer.EOS) {\n      return lunr.QueryLexer.lexEOS\n    }\n\n    // Escape character is '\\'\n    if (char.charCodeAt(0) == 92) {\n      lexer.escapeCharacter()\n      continue\n    }\n\n    if (char == \":\") {\n      return lunr.QueryLexer.lexField\n    }\n\n    if (char == \"~\") {\n      lexer.backup()\n      if (lexer.width() > 0) {\n        lexer.emit(lunr.QueryLexer.TERM)\n      }\n      return lunr.QueryLexer.lexEditDistance\n    }\n\n    if (char == \"^\") {\n      lexer.backup()\n      if (lexer.width() > 0) {\n        lexer.emit(lunr.QueryLexer.TERM)\n      }\n      return lunr.QueryLexer.lexBoost\n    }\n\n    // \"+\" indicates term presence is required\n    // checking for length to ensure that only\n    // leading \"+\" are considered\n    if (char == \"+\" && lexer.width() === 1) {\n      lexer.emit(lunr.QueryLexer.PRESENCE)\n      return lunr.QueryLexer.lexText\n    }\n\n    // \"-\" indicates term presence is prohibited\n    // checking for length to ensure that only\n    // leading \"-\" are considered\n    if (char == \"-\" && lexer.width() === 1) {\n      lexer.emit(lunr.QueryLexer.PRESENCE)\n      return lunr.QueryLexer.lexText\n    }\n\n    if (char.match(lunr.QueryLexer.termSeparator)) {\n      return lunr.QueryLexer.lexTerm\n    }\n  }\n}\n\nlunr.QueryParser = function (str, query) {\n  this.lexer = new lunr.QueryLexer (str)\n  this.query = query\n  this.currentClause = {}\n  this.lexemeIdx = 0\n}\n\nlunr.QueryParser.prototype.parse = function () {\n  this.lexer.run()\n  this.lexemes = this.lexer.lexemes\n\n  var state = lunr.QueryParser.parseClause\n\n  while (state) {\n    state = state(this)\n  }\n\n  return this.query\n}\n\nlunr.QueryParser.prototype.peekLexeme = function () {\n  return this.lexemes[this.lexemeIdx]\n}\n\nlunr.QueryParser.prototype.consumeLexeme = function () {\n  var lexeme = this.peekLexeme()\n  this.lexemeIdx += 1\n  return lexeme\n}\n\nlunr.QueryParser.prototype.nextClause = function () {\n  var completedClause = this.currentClause\n  this.query.clause(completedClause)\n  this.currentClause = {}\n}\n\nlunr.QueryParser.parseClause = function (parser) {\n  var lexeme = parser.peekLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  switch (lexeme.type) {\n    case lunr.QueryLexer.PRESENCE:\n      return lunr.QueryParser.parsePresence\n    case lunr.QueryLexer.FIELD:\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.TERM:\n      return lunr.QueryParser.parseTerm\n    default:\n      var errorMessage = \"expected either a field or a term, found \" + lexeme.type\n\n      if (lexeme.str.length >= 1) {\n        errorMessage += \" with value '\" + lexeme.str + \"'\"\n      }\n\n      throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n}\n\nlunr.QueryParser.parsePresence = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  switch (lexeme.str) {\n    case \"-\":\n      parser.currentClause.presence = lunr.Query.presence.PROHIBITED\n      break\n    case \"+\":\n      parser.currentClause.presence = lunr.Query.presence.REQUIRED\n      break\n    default:\n      var errorMessage = \"unrecognised presence operator'\" + lexeme.str + \"'\"\n      throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    var errorMessage = \"expecting term or field, found nothing\"\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.FIELD:\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.TERM:\n      return lunr.QueryParser.parseTerm\n    default:\n      var errorMessage = \"expecting term or field, found '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\nlunr.QueryParser.parseField = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  if (parser.query.allFields.indexOf(lexeme.str) == -1) {\n    var possibleFields = parser.query.allFields.map(function (f) { return \"'\" + f + \"'\" }).join(', '),\n        errorMessage = \"unrecognised field '\" + lexeme.str + \"', possible fields: \" + possibleFields\n\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  parser.currentClause.fields = [lexeme.str]\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    var errorMessage = \"expecting term, found nothing\"\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.TERM:\n      return lunr.QueryParser.parseTerm\n    default:\n      var errorMessage = \"expecting term, found '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\nlunr.QueryParser.parseTerm = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  parser.currentClause.term = lexeme.str.toLowerCase()\n\n  if (lexeme.str.indexOf(\"*\") != -1) {\n    parser.currentClause.usePipeline = false\n  }\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    parser.nextClause()\n    return\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.TERM:\n      parser.nextClause()\n      return lunr.QueryParser.parseTerm\n    case lunr.QueryLexer.FIELD:\n      parser.nextClause()\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.EDIT_DISTANCE:\n      return lunr.QueryParser.parseEditDistance\n    case lunr.QueryLexer.BOOST:\n      return lunr.QueryParser.parseBoost\n    case lunr.QueryLexer.PRESENCE:\n      parser.nextClause()\n      return lunr.QueryParser.parsePresence\n    default:\n      var errorMessage = \"Unexpected lexeme type '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\nlunr.QueryParser.parseEditDistance = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  var editDistance = parseInt(lexeme.str, 10)\n\n  if (isNaN(editDistance)) {\n    var errorMessage = \"edit distance must be numeric\"\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  parser.currentClause.editDistance = editDistance\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    parser.nextClause()\n    return\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.TERM:\n      parser.nextClause()\n      return lunr.QueryParser.parseTerm\n    case lunr.QueryLexer.FIELD:\n      parser.nextClause()\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.EDIT_DISTANCE:\n      return lunr.QueryParser.parseEditDistance\n    case lunr.QueryLexer.BOOST:\n      return lunr.QueryParser.parseBoost\n    case lunr.QueryLexer.PRESENCE:\n      parser.nextClause()\n      return lunr.QueryParser.parsePresence\n    default:\n      var errorMessage = \"Unexpected lexeme type '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\nlunr.QueryParser.parseBoost = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  var boost = parseInt(lexeme.str, 10)\n\n  if (isNaN(boost)) {\n    var errorMessage = \"boost must be numeric\"\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  parser.currentClause.boost = boost\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    parser.nextClause()\n    return\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.TERM:\n      parser.nextClause()\n      return lunr.QueryParser.parseTerm\n    case lunr.QueryLexer.FIELD:\n      parser.nextClause()\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.EDIT_DISTANCE:\n      return lunr.QueryParser.parseEditDistance\n    case lunr.QueryLexer.BOOST:\n      return lunr.QueryParser.parseBoost\n    case lunr.QueryLexer.PRESENCE:\n      parser.nextClause()\n      return lunr.QueryParser.parsePresence\n    default:\n      var errorMessage = \"Unexpected lexeme type '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\n  /**\n   * export the module via AMD, CommonJS or as a browser global\n   * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n   */\n  ;(function (root, factory) {\n    if (typeof define === 'function' && define.amd) {\n      // AMD. Register as an anonymous module.\n      define(factory)\n    } else if (typeof exports === 'object') {\n      /**\n       * Node. Does not work with strict CommonJS, but\n       * only CommonJS-like enviroments that support module.exports,\n       * like Node.\n       */\n      module.exports = factory()\n    } else {\n      // Browser globals (root is window)\n      root.lunr = factory()\n    }\n  }(this, function () {\n    /**\n     * Just return a value to define the module export.\n     * This example returns an object, but the module\n     * can return a function as the exported value.\n     */\n    return lunr\n  }))\n})();\n", "/*!\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;(function (root, factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define(factory)\n    } else if (typeof exports === 'object') {\n        /**\n         * Node. Does not work with strict CommonJS, but\n         * only CommonJS-like environments that support module.exports,\n         * like Node.\n         */\n        module.exports = factory()\n    } else {\n        // Browser globals (root is window)\n        factory()(root.lunr);\n    }\n}(this, function () {\n    /**\n     * Just return a value to define the module export.\n     * This example returns an object, but the module\n     * can return a function as the exported value.\n     */\n    return function(lunr) {\n        /* provides utilities for the included stemmers */\n        lunr.stemmerSupport = {\n            Among: function(s, substring_i, result, method) {\n                this.toCharArray = function(s) {\n                    var sLength = s.length, charArr = new Array(sLength);\n                    for (var i = 0; i < sLength; i++)\n                        charArr[i] = s.charCodeAt(i);\n                    return charArr;\n                };\n\n                if ((!s && s != \"\") || (!substring_i && (substring_i != 0)) || !result)\n                    throw (\"Bad Among initialisation: s:\" + s + \", substring_i: \"\n                        + substring_i + \", result: \" + result);\n                this.s_size = s.length;\n                this.s = this.toCharArray(s);\n                this.substring_i = substring_i;\n                this.result = result;\n                this.method = method;\n            },\n            SnowballProgram: function() {\n                var current;\n                return {\n                    bra : 0,\n                    ket : 0,\n                    limit : 0,\n                    cursor : 0,\n                    limit_backward : 0,\n                    setCurrent : function(word) {\n                        current = word;\n                        this.cursor = 0;\n                        this.limit = word.length;\n                        this.limit_backward = 0;\n                        this.bra = this.cursor;\n                        this.ket = this.limit;\n                    },\n                    getCurrent : function() {\n                        var result = current;\n                        current = null;\n                        return result;\n                    },\n                    in_grouping : function(s, min, max) {\n                        if (this.cursor < this.limit) {\n                            var ch = current.charCodeAt(this.cursor);\n                            if (ch <= max && ch >= min) {\n                                ch -= min;\n                                if (s[ch >> 3] & (0X1 << (ch & 0X7))) {\n                                    this.cursor++;\n                                    return true;\n                                }\n                            }\n                        }\n                        return false;\n                    },\n                    in_grouping_b : function(s, min, max) {\n                        if (this.cursor > this.limit_backward) {\n                            var ch = current.charCodeAt(this.cursor - 1);\n                            if (ch <= max && ch >= min) {\n                                ch -= min;\n                                if (s[ch >> 3] & (0X1 << (ch & 0X7))) {\n                                    this.cursor--;\n                                    return true;\n                                }\n                            }\n                        }\n                        return false;\n                    },\n                    out_grouping : function(s, min, max) {\n                        if (this.cursor < this.limit) {\n                            var ch = current.charCodeAt(this.cursor);\n                            if (ch > max || ch < min) {\n                                this.cursor++;\n                                return true;\n                            }\n                            ch -= min;\n                            if (!(s[ch >> 3] & (0X1 << (ch & 0X7)))) {\n                                this.cursor++;\n                                return true;\n                            }\n                        }\n                        return false;\n                    },\n                    out_grouping_b : function(s, min, max) {\n                        if (this.cursor > this.limit_backward) {\n                            var ch = current.charCodeAt(this.cursor - 1);\n                            if (ch > max || ch < min) {\n                                this.cursor--;\n                                return true;\n                            }\n                            ch -= min;\n                            if (!(s[ch >> 3] & (0X1 << (ch & 0X7)))) {\n                                this.cursor--;\n                                return true;\n                            }\n                        }\n                        return false;\n                    },\n                    eq_s : function(s_size, s) {\n                        if (this.limit - this.cursor < s_size)\n                            return false;\n                        for (var i = 0; i < s_size; i++)\n                            if (current.charCodeAt(this.cursor + i) != s.charCodeAt(i))\n                                return false;\n                        this.cursor += s_size;\n                        return true;\n                    },\n                    eq_s_b : function(s_size, s) {\n                        if (this.cursor - this.limit_backward < s_size)\n                            return false;\n                        for (var i = 0; i < s_size; i++)\n                            if (current.charCodeAt(this.cursor - s_size + i) != s\n                                .charCodeAt(i))\n                                return false;\n                        this.cursor -= s_size;\n                        return true;\n                    },\n                    find_among : function(v, v_size) {\n                        var i = 0, j = v_size, c = this.cursor, l = this.limit, common_i = 0, common_j = 0, first_key_inspected = false;\n                        while (true) {\n                            var k = i + ((j - i) >> 1), diff = 0, common = common_i < common_j\n                                ? common_i\n                                : common_j, w = v[k];\n                            for (var i2 = common; i2 < w.s_size; i2++) {\n                                if (c + common == l) {\n                                    diff = -1;\n                                    break;\n                                }\n                                diff = current.charCodeAt(c + common) - w.s[i2];\n                                if (diff)\n                                    break;\n                                common++;\n                            }\n                            if (diff < 0) {\n                                j = k;\n                                common_j = common;\n                            } else {\n                                i = k;\n                                common_i = common;\n                            }\n                            if (j - i <= 1) {\n                                if (i > 0 || j == i || first_key_inspected)\n                                    break;\n                                first_key_inspected = true;\n                            }\n                        }\n                        while (true) {\n                            var w = v[i];\n                            if (common_i >= w.s_size) {\n                                this.cursor = c + w.s_size;\n                                if (!w.method)\n                                    return w.result;\n                                var res = w.method();\n                                this.cursor = c + w.s_size;\n                                if (res)\n                                    return w.result;\n                            }\n                            i = w.substring_i;\n                            if (i < 0)\n                                return 0;\n                        }\n                    },\n                    find_among_b : function(v, v_size) {\n                        var i = 0, j = v_size, c = this.cursor, lb = this.limit_backward, common_i = 0, common_j = 0, first_key_inspected = false;\n                        while (true) {\n                            var k = i + ((j - i) >> 1), diff = 0, common = common_i < common_j\n                                ? common_i\n                                : common_j, w = v[k];\n                            for (var i2 = w.s_size - 1 - common; i2 >= 0; i2--) {\n                                if (c - common == lb) {\n                                    diff = -1;\n                                    break;\n                                }\n                                diff = current.charCodeAt(c - 1 - common) - w.s[i2];\n                                if (diff)\n                                    break;\n                                common++;\n                            }\n                            if (diff < 0) {\n                                j = k;\n                                common_j = common;\n                            } else {\n                                i = k;\n                                common_i = common;\n                            }\n                            if (j - i <= 1) {\n                                if (i > 0 || j == i || first_key_inspected)\n                                    break;\n                                first_key_inspected = true;\n                            }\n                        }\n                        while (true) {\n                            var w = v[i];\n                            if (common_i >= w.s_size) {\n                                this.cursor = c - w.s_size;\n                                if (!w.method)\n                                    return w.result;\n                                var res = w.method();\n                                this.cursor = c - w.s_size;\n                                if (res)\n                                    return w.result;\n                            }\n                            i = w.substring_i;\n                            if (i < 0)\n                                return 0;\n                        }\n                    },\n                    replace_s : function(c_bra, c_ket, s) {\n                        var adjustment = s.length - (c_ket - c_bra), left = current\n                            .substring(0, c_bra), right = current.substring(c_ket);\n                        current = left + s + right;\n                        this.limit += adjustment;\n                        if (this.cursor >= c_ket)\n                            this.cursor += adjustment;\n                        else if (this.cursor > c_bra)\n                            this.cursor = c_bra;\n                        return adjustment;\n                    },\n                    slice_check : function() {\n                        if (this.bra < 0 || this.bra > this.ket || this.ket > this.limit\n                            || this.limit > current.length)\n                            throw (\"faulty slice operation\");\n                    },\n                    slice_from : function(s) {\n                        this.slice_check();\n                        this.replace_s(this.bra, this.ket, s);\n                    },\n                    slice_del : function() {\n                        this.slice_from(\"\");\n                    },\n                    insert : function(c_bra, c_ket, s) {\n                        var adjustment = this.replace_s(c_bra, c_ket, s);\n                        if (c_bra <= this.bra)\n                            this.bra += adjustment;\n                        if (c_bra <= this.ket)\n                            this.ket += adjustment;\n                    },\n                    slice_to : function() {\n                        this.slice_check();\n                        return current.substring(this.bra, this.ket);\n                    },\n                    eq_v_b : function(s) {\n                        return this.eq_s_b(s.length, s);\n                    }\n                };\n            }\n        };\n\n        lunr.trimmerSupport = {\n            generateTrimmer: function(wordCharacters) {\n                var startRegex = new RegExp(\"^[^\" + wordCharacters + \"]+\")\n                var endRegex = new RegExp(\"[^\" + wordCharacters + \"]+$\")\n\n                return function(token) {\n                    // for lunr version 2\n                    if (typeof token.update === \"function\") {\n                        return token.update(function (s) {\n                            return s\n                                .replace(startRegex, '')\n                                .replace(endRegex, '');\n                        })\n                    } else { // for lunr version 1\n                        return token\n                            .replace(startRegex, '')\n                            .replace(endRegex, '');\n                    }\n                };\n            }\n        }\n    }\n}));\n", "/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;(function (root, factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define(factory)\n    } else if (typeof exports === 'object') {\n        /**\n         * Node. Does not work with strict CommonJS, but\n         * only CommonJS-like environments that support module.exports,\n         * like Node.\n         */\n        module.exports = factory()\n    } else {\n        // Browser globals (root is window)\n        factory()(root.lunr);\n    }\n}(this, function () {\n    /**\n     * Just return a value to define the module export.\n     * This example returns an object, but the module\n     * can return a function as the exported value.\n     */\n\n    return function(lunr) {\n        // TinySegmenter 0.1 -- Super compact Japanese tokenizer in Javascript\n        // (c) 2008 Taku <PERSON> <<EMAIL>>\n        // TinySegmenter is freely distributable under the terms of a new BSD licence.\n        // For details, see http://chasen.org/~taku/software/TinySegmenter/LICENCE.txt\n\n        function TinySegmenter() {\n          var patterns = {\n            \"[一二三四五六七八九十百千万億兆]\":\"M\",\n            \"[一-龠々〆ヵヶ]\":\"H\",\n            \"[ぁ-ん]\":\"I\",\n            \"[ァ-ヴーｱ-ﾝﾞｰ]\":\"K\",\n            \"[a-zA-Zａ-ｚＡ-Ｚ]\":\"A\",\n            \"[0-9０-９]\":\"N\"\n          }\n          this.chartype_ = [];\n          for (var i in patterns) {\n            var regexp = new RegExp(i);\n            this.chartype_.push([regexp, patterns[i]]);\n          }\n\n          this.BIAS__ = -332\n          this.BC1__ = {\"HH\":6,\"II\":2461,\"KH\":406,\"OH\":-1378};\n          this.BC2__ = {\"AA\":-3267,\"AI\":2744,\"AN\":-878,\"HH\":-4070,\"HM\":-1711,\"HN\":4012,\"HO\":3761,\"IA\":1327,\"IH\":-1184,\"II\":-1332,\"IK\":1721,\"IO\":5492,\"KI\":3831,\"KK\":-8741,\"MH\":-3132,\"MK\":3334,\"OO\":-2920};\n          this.BC3__ = {\"HH\":996,\"HI\":626,\"HK\":-721,\"HN\":-1307,\"HO\":-836,\"IH\":-301,\"KK\":2762,\"MK\":1079,\"MM\":4034,\"OA\":-1652,\"OH\":266};\n          this.BP1__ = {\"BB\":295,\"OB\":304,\"OO\":-125,\"UB\":352};\n          this.BP2__ = {\"BO\":60,\"OO\":-1762};\n          this.BQ1__ = {\"BHH\":1150,\"BHM\":1521,\"BII\":-1158,\"BIM\":886,\"BMH\":1208,\"BNH\":449,\"BOH\":-91,\"BOO\":-2597,\"OHI\":451,\"OIH\":-296,\"OKA\":1851,\"OKH\":-1020,\"OKK\":904,\"OOO\":2965};\n          this.BQ2__ = {\"BHH\":118,\"BHI\":-1159,\"BHM\":466,\"BIH\":-919,\"BKK\":-1720,\"BKO\":864,\"OHH\":-1139,\"OHM\":-181,\"OIH\":153,\"UHI\":-1146};\n          this.BQ3__ = {\"BHH\":-792,\"BHI\":2664,\"BII\":-299,\"BKI\":419,\"BMH\":937,\"BMM\":8335,\"BNN\":998,\"BOH\":775,\"OHH\":2174,\"OHM\":439,\"OII\":280,\"OKH\":1798,\"OKI\":-793,\"OKO\":-2242,\"OMH\":-2402,\"OOO\":11699};\n          this.BQ4__ = {\"BHH\":-3895,\"BIH\":3761,\"BII\":-4654,\"BIK\":1348,\"BKK\":-1806,\"BMI\":-3385,\"BOO\":-12396,\"OAH\":926,\"OHH\":266,\"OHK\":-2036,\"ONN\":-973};\n          this.BW1__ = {\",と\":660,\",同\":727,\"B1あ\":1404,\"B1同\":542,\"、と\":660,\"、同\":727,\"」と\":1682,\"あっ\":1505,\"いう\":1743,\"いっ\":-2055,\"いる\":672,\"うし\":-4817,\"うん\":665,\"から\":3472,\"がら\":600,\"こう\":-790,\"こと\":2083,\"こん\":-1262,\"さら\":-4143,\"さん\":4573,\"した\":2641,\"して\":1104,\"すで\":-3399,\"そこ\":1977,\"それ\":-871,\"たち\":1122,\"ため\":601,\"った\":3463,\"つい\":-802,\"てい\":805,\"てき\":1249,\"でき\":1127,\"です\":3445,\"では\":844,\"とい\":-4915,\"とみ\":1922,\"どこ\":3887,\"ない\":5713,\"なっ\":3015,\"など\":7379,\"なん\":-1113,\"にし\":2468,\"には\":1498,\"にも\":1671,\"に対\":-912,\"の一\":-501,\"の中\":741,\"ませ\":2448,\"まで\":1711,\"まま\":2600,\"まる\":-2155,\"やむ\":-1947,\"よっ\":-2565,\"れた\":2369,\"れで\":-913,\"をし\":1860,\"を見\":731,\"亡く\":-1886,\"京都\":2558,\"取り\":-2784,\"大き\":-2604,\"大阪\":1497,\"平方\":-2314,\"引き\":-1336,\"日本\":-195,\"本当\":-2423,\"毎日\":-2113,\"目指\":-724,\"Ｂ１あ\":1404,\"Ｂ１同\":542,\"｣と\":1682};\n          this.BW2__ = {\"..\":-11822,\"11\":-669,\"――\":-5730,\"−−\":-13175,\"いう\":-1609,\"うか\":2490,\"かし\":-1350,\"かも\":-602,\"から\":-7194,\"かれ\":4612,\"がい\":853,\"がら\":-3198,\"きた\":1941,\"くな\":-1597,\"こと\":-8392,\"この\":-4193,\"させ\":4533,\"され\":13168,\"さん\":-3977,\"しい\":-1819,\"しか\":-545,\"した\":5078,\"して\":972,\"しな\":939,\"その\":-3744,\"たい\":-1253,\"たた\":-662,\"ただ\":-3857,\"たち\":-786,\"たと\":1224,\"たは\":-939,\"った\":4589,\"って\":1647,\"っと\":-2094,\"てい\":6144,\"てき\":3640,\"てく\":2551,\"ては\":-3110,\"ても\":-3065,\"でい\":2666,\"でき\":-1528,\"でし\":-3828,\"です\":-4761,\"でも\":-4203,\"とい\":1890,\"とこ\":-1746,\"とと\":-2279,\"との\":720,\"とみ\":5168,\"とも\":-3941,\"ない\":-2488,\"なが\":-1313,\"など\":-6509,\"なの\":2614,\"なん\":3099,\"にお\":-1615,\"にし\":2748,\"にな\":2454,\"によ\":-7236,\"に対\":-14943,\"に従\":-4688,\"に関\":-11388,\"のか\":2093,\"ので\":-7059,\"のに\":-6041,\"のの\":-6125,\"はい\":1073,\"はが\":-1033,\"はず\":-2532,\"ばれ\":1813,\"まし\":-1316,\"まで\":-6621,\"まれ\":5409,\"めて\":-3153,\"もい\":2230,\"もの\":-10713,\"らか\":-944,\"らし\":-1611,\"らに\":-1897,\"りし\":651,\"りま\":1620,\"れた\":4270,\"れて\":849,\"れば\":4114,\"ろう\":6067,\"われ\":7901,\"を通\":-11877,\"んだ\":728,\"んな\":-4115,\"一人\":602,\"一方\":-1375,\"一日\":970,\"一部\":-1051,\"上が\":-4479,\"会社\":-1116,\"出て\":2163,\"分の\":-7758,\"同党\":970,\"同日\":-913,\"大阪\":-2471,\"委員\":-1250,\"少な\":-1050,\"年度\":-8669,\"年間\":-1626,\"府県\":-2363,\"手権\":-1982,\"新聞\":-4066,\"日新\":-722,\"日本\":-7068,\"日米\":3372,\"曜日\":-601,\"朝鮮\":-2355,\"本人\":-2697,\"東京\":-1543,\"然と\":-1384,\"社会\":-1276,\"立て\":-990,\"第に\":-1612,\"米国\":-4268,\"１１\":-669};\n          this.BW3__ = {\"あた\":-2194,\"あり\":719,\"ある\":3846,\"い.\":-1185,\"い。\":-1185,\"いい\":5308,\"いえ\":2079,\"いく\":3029,\"いた\":2056,\"いっ\":1883,\"いる\":5600,\"いわ\":1527,\"うち\":1117,\"うと\":4798,\"えと\":1454,\"か.\":2857,\"か。\":2857,\"かけ\":-743,\"かっ\":-4098,\"かに\":-669,\"から\":6520,\"かり\":-2670,\"が,\":1816,\"が、\":1816,\"がき\":-4855,\"がけ\":-1127,\"がっ\":-913,\"がら\":-4977,\"がり\":-2064,\"きた\":1645,\"けど\":1374,\"こと\":7397,\"この\":1542,\"ころ\":-2757,\"さい\":-714,\"さを\":976,\"し,\":1557,\"し、\":1557,\"しい\":-3714,\"した\":3562,\"して\":1449,\"しな\":2608,\"しま\":1200,\"す.\":-1310,\"す。\":-1310,\"する\":6521,\"ず,\":3426,\"ず、\":3426,\"ずに\":841,\"そう\":428,\"た.\":8875,\"た。\":8875,\"たい\":-594,\"たの\":812,\"たり\":-1183,\"たる\":-853,\"だ.\":4098,\"だ。\":4098,\"だっ\":1004,\"った\":-4748,\"って\":300,\"てい\":6240,\"てお\":855,\"ても\":302,\"です\":1437,\"でに\":-1482,\"では\":2295,\"とう\":-1387,\"とし\":2266,\"との\":541,\"とも\":-3543,\"どう\":4664,\"ない\":1796,\"なく\":-903,\"など\":2135,\"に,\":-1021,\"に、\":-1021,\"にし\":1771,\"にな\":1906,\"には\":2644,\"の,\":-724,\"の、\":-724,\"の子\":-1000,\"は,\":1337,\"は、\":1337,\"べき\":2181,\"まし\":1113,\"ます\":6943,\"まっ\":-1549,\"まで\":6154,\"まれ\":-793,\"らし\":1479,\"られ\":6820,\"るる\":3818,\"れ,\":854,\"れ、\":854,\"れた\":1850,\"れて\":1375,\"れば\":-3246,\"れる\":1091,\"われ\":-605,\"んだ\":606,\"んで\":798,\"カ月\":990,\"会議\":860,\"入り\":1232,\"大会\":2217,\"始め\":1681,\"市\":965,\"新聞\":-5055,\"日,\":974,\"日、\":974,\"社会\":2024,\"ｶ月\":990};\n          this.TC1__ = {\"AAA\":1093,\"HHH\":1029,\"HHM\":580,\"HII\":998,\"HOH\":-390,\"HOM\":-331,\"IHI\":1169,\"IOH\":-142,\"IOI\":-1015,\"IOM\":467,\"MMH\":187,\"OOI\":-1832};\n          this.TC2__ = {\"HHO\":2088,\"HII\":-1023,\"HMM\":-1154,\"IHI\":-1965,\"KKH\":703,\"OII\":-2649};\n          this.TC3__ = {\"AAA\":-294,\"HHH\":346,\"HHI\":-341,\"HII\":-1088,\"HIK\":731,\"HOH\":-1486,\"IHH\":128,\"IHI\":-3041,\"IHO\":-1935,\"IIH\":-825,\"IIM\":-1035,\"IOI\":-542,\"KHH\":-1216,\"KKA\":491,\"KKH\":-1217,\"KOK\":-1009,\"MHH\":-2694,\"MHM\":-457,\"MHO\":123,\"MMH\":-471,\"NNH\":-1689,\"NNO\":662,\"OHO\":-3393};\n          this.TC4__ = {\"HHH\":-203,\"HHI\":1344,\"HHK\":365,\"HHM\":-122,\"HHN\":182,\"HHO\":669,\"HIH\":804,\"HII\":679,\"HOH\":446,\"IHH\":695,\"IHO\":-2324,\"IIH\":321,\"III\":1497,\"IIO\":656,\"IOO\":54,\"KAK\":4845,\"KKA\":3386,\"KKK\":3065,\"MHH\":-405,\"MHI\":201,\"MMH\":-241,\"MMM\":661,\"MOM\":841};\n          this.TQ1__ = {\"BHHH\":-227,\"BHHI\":316,\"BHIH\":-132,\"BIHH\":60,\"BIII\":1595,\"BNHH\":-744,\"BOHH\":225,\"BOOO\":-908,\"OAKK\":482,\"OHHH\":281,\"OHIH\":249,\"OIHI\":200,\"OIIH\":-68};\n          this.TQ2__ = {\"BIHH\":-1401,\"BIII\":-1033,\"BKAK\":-543,\"BOOO\":-5591};\n          this.TQ3__ = {\"BHHH\":478,\"BHHM\":-1073,\"BHIH\":222,\"BHII\":-504,\"BIIH\":-116,\"BIII\":-105,\"BMHI\":-863,\"BMHM\":-464,\"BOMH\":620,\"OHHH\":346,\"OHHI\":1729,\"OHII\":997,\"OHMH\":481,\"OIHH\":623,\"OIIH\":1344,\"OKAK\":2792,\"OKHH\":587,\"OKKA\":679,\"OOHH\":110,\"OOII\":-685};\n          this.TQ4__ = {\"BHHH\":-721,\"BHHM\":-3604,\"BHII\":-966,\"BIIH\":-607,\"BIII\":-2181,\"OAAA\":-2763,\"OAKK\":180,\"OHHH\":-294,\"OHHI\":2446,\"OHHO\":480,\"OHIH\":-1573,\"OIHH\":1935,\"OIHI\":-493,\"OIIH\":626,\"OIII\":-4007,\"OKAK\":-8156};\n          this.TW1__ = {\"につい\":-4681,\"東京都\":2026};\n          this.TW2__ = {\"ある程\":-2049,\"いった\":-1256,\"ころが\":-2434,\"しょう\":3873,\"その後\":-4430,\"だって\":-1049,\"ていた\":1833,\"として\":-4657,\"ともに\":-4517,\"もので\":1882,\"一気に\":-792,\"初めて\":-1512,\"同時に\":-8097,\"大きな\":-1255,\"対して\":-2721,\"社会党\":-3216};\n          this.TW3__ = {\"いただ\":-1734,\"してい\":1314,\"として\":-4314,\"につい\":-5483,\"にとっ\":-5989,\"に当た\":-6247,\"ので,\":-727,\"ので、\":-727,\"のもの\":-600,\"れから\":-3752,\"十二月\":-2287};\n          this.TW4__ = {\"いう.\":8576,\"いう。\":8576,\"からな\":-2348,\"してい\":2958,\"たが,\":1516,\"たが、\":1516,\"ている\":1538,\"という\":1349,\"ました\":5543,\"ません\":1097,\"ようと\":-4258,\"よると\":5865};\n          this.UC1__ = {\"A\":484,\"K\":93,\"M\":645,\"O\":-505};\n          this.UC2__ = {\"A\":819,\"H\":1059,\"I\":409,\"M\":3987,\"N\":5775,\"O\":646};\n          this.UC3__ = {\"A\":-1370,\"I\":2311};\n          this.UC4__ = {\"A\":-2643,\"H\":1809,\"I\":-1032,\"K\":-3450,\"M\":3565,\"N\":3876,\"O\":6646};\n          this.UC5__ = {\"H\":313,\"I\":-1238,\"K\":-799,\"M\":539,\"O\":-831};\n          this.UC6__ = {\"H\":-506,\"I\":-253,\"K\":87,\"M\":247,\"O\":-387};\n          this.UP1__ = {\"O\":-214};\n          this.UP2__ = {\"B\":69,\"O\":935};\n          this.UP3__ = {\"B\":189};\n          this.UQ1__ = {\"BH\":21,\"BI\":-12,\"BK\":-99,\"BN\":142,\"BO\":-56,\"OH\":-95,\"OI\":477,\"OK\":410,\"OO\":-2422};\n          this.UQ2__ = {\"BH\":216,\"BI\":113,\"OK\":1759};\n          this.UQ3__ = {\"BA\":-479,\"BH\":42,\"BI\":1913,\"BK\":-7198,\"BM\":3160,\"BN\":6427,\"BO\":14761,\"OI\":-827,\"ON\":-3212};\n          this.UW1__ = {\",\":156,\"、\":156,\"「\":-463,\"あ\":-941,\"う\":-127,\"が\":-553,\"き\":121,\"こ\":505,\"で\":-201,\"と\":-547,\"ど\":-123,\"に\":-789,\"の\":-185,\"は\":-847,\"も\":-466,\"や\":-470,\"よ\":182,\"ら\":-292,\"り\":208,\"れ\":169,\"を\":-446,\"ん\":-137,\"・\":-135,\"主\":-402,\"京\":-268,\"区\":-912,\"午\":871,\"国\":-460,\"大\":561,\"委\":729,\"市\":-411,\"日\":-141,\"理\":361,\"生\":-408,\"県\":-386,\"都\":-718,\"｢\":-463,\"･\":-135};\n          this.UW2__ = {\",\":-829,\"、\":-829,\"〇\":892,\"「\":-645,\"」\":3145,\"あ\":-538,\"い\":505,\"う\":134,\"お\":-502,\"か\":1454,\"が\":-856,\"く\":-412,\"こ\":1141,\"さ\":878,\"ざ\":540,\"し\":1529,\"す\":-675,\"せ\":300,\"そ\":-1011,\"た\":188,\"だ\":1837,\"つ\":-949,\"て\":-291,\"で\":-268,\"と\":-981,\"ど\":1273,\"な\":1063,\"に\":-1764,\"の\":130,\"は\":-409,\"ひ\":-1273,\"べ\":1261,\"ま\":600,\"も\":-1263,\"や\":-402,\"よ\":1639,\"り\":-579,\"る\":-694,\"れ\":571,\"を\":-2516,\"ん\":2095,\"ア\":-587,\"カ\":306,\"キ\":568,\"ッ\":831,\"三\":-758,\"不\":-2150,\"世\":-302,\"中\":-968,\"主\":-861,\"事\":492,\"人\":-123,\"会\":978,\"保\":362,\"入\":548,\"初\":-3025,\"副\":-1566,\"北\":-3414,\"区\":-422,\"大\":-1769,\"天\":-865,\"太\":-483,\"子\":-1519,\"学\":760,\"実\":1023,\"小\":-2009,\"市\":-813,\"年\":-1060,\"強\":1067,\"手\":-1519,\"揺\":-1033,\"政\":1522,\"文\":-1355,\"新\":-1682,\"日\":-1815,\"明\":-1462,\"最\":-630,\"朝\":-1843,\"本\":-1650,\"東\":-931,\"果\":-665,\"次\":-2378,\"民\":-180,\"気\":-1740,\"理\":752,\"発\":529,\"目\":-1584,\"相\":-242,\"県\":-1165,\"立\":-763,\"第\":810,\"米\":509,\"自\":-1353,\"行\":838,\"西\":-744,\"見\":-3874,\"調\":1010,\"議\":1198,\"込\":3041,\"開\":1758,\"間\":-1257,\"｢\":-645,\"｣\":3145,\"ｯ\":831,\"ｱ\":-587,\"ｶ\":306,\"ｷ\":568};\n          this.UW3__ = {\",\":4889,\"1\":-800,\"−\":-1723,\"、\":4889,\"々\":-2311,\"〇\":5827,\"」\":2670,\"〓\":-3573,\"あ\":-2696,\"い\":1006,\"う\":2342,\"え\":1983,\"お\":-4864,\"か\":-1163,\"が\":3271,\"く\":1004,\"け\":388,\"げ\":401,\"こ\":-3552,\"ご\":-3116,\"さ\":-1058,\"し\":-395,\"す\":584,\"せ\":3685,\"そ\":-5228,\"た\":842,\"ち\":-521,\"っ\":-1444,\"つ\":-1081,\"て\":6167,\"で\":2318,\"と\":1691,\"ど\":-899,\"な\":-2788,\"に\":2745,\"の\":4056,\"は\":4555,\"ひ\":-2171,\"ふ\":-1798,\"へ\":1199,\"ほ\":-5516,\"ま\":-4384,\"み\":-120,\"め\":1205,\"も\":2323,\"や\":-788,\"よ\":-202,\"ら\":727,\"り\":649,\"る\":5905,\"れ\":2773,\"わ\":-1207,\"を\":6620,\"ん\":-518,\"ア\":551,\"グ\":1319,\"ス\":874,\"ッ\":-1350,\"ト\":521,\"ム\":1109,\"ル\":1591,\"ロ\":2201,\"ン\":278,\"・\":-3794,\"一\":-1619,\"下\":-1759,\"世\":-2087,\"両\":3815,\"中\":653,\"主\":-758,\"予\":-1193,\"二\":974,\"人\":2742,\"今\":792,\"他\":1889,\"以\":-1368,\"低\":811,\"何\":4265,\"作\":-361,\"保\":-2439,\"元\":4858,\"党\":3593,\"全\":1574,\"公\":-3030,\"六\":755,\"共\":-1880,\"円\":5807,\"再\":3095,\"分\":457,\"初\":2475,\"別\":1129,\"前\":2286,\"副\":4437,\"力\":365,\"動\":-949,\"務\":-1872,\"化\":1327,\"北\":-1038,\"区\":4646,\"千\":-2309,\"午\":-783,\"協\":-1006,\"口\":483,\"右\":1233,\"各\":3588,\"合\":-241,\"同\":3906,\"和\":-837,\"員\":4513,\"国\":642,\"型\":1389,\"場\":1219,\"外\":-241,\"妻\":2016,\"学\":-1356,\"安\":-423,\"実\":-1008,\"家\":1078,\"小\":-513,\"少\":-3102,\"州\":1155,\"市\":3197,\"平\":-1804,\"年\":2416,\"広\":-1030,\"府\":1605,\"度\":1452,\"建\":-2352,\"当\":-3885,\"得\":1905,\"思\":-1291,\"性\":1822,\"戸\":-488,\"指\":-3973,\"政\":-2013,\"教\":-1479,\"数\":3222,\"文\":-1489,\"新\":1764,\"日\":2099,\"旧\":5792,\"昨\":-661,\"時\":-1248,\"曜\":-951,\"最\":-937,\"月\":4125,\"期\":360,\"李\":3094,\"村\":364,\"東\":-805,\"核\":5156,\"森\":2438,\"業\":484,\"氏\":2613,\"民\":-1694,\"決\":-1073,\"法\":1868,\"海\":-495,\"無\":979,\"物\":461,\"特\":-3850,\"生\":-273,\"用\":914,\"町\":1215,\"的\":7313,\"直\":-1835,\"省\":792,\"県\":6293,\"知\":-1528,\"私\":4231,\"税\":401,\"立\":-960,\"第\":1201,\"米\":7767,\"系\":3066,\"約\":3663,\"級\":1384,\"統\":-4229,\"総\":1163,\"線\":1255,\"者\":6457,\"能\":725,\"自\":-2869,\"英\":785,\"見\":1044,\"調\":-562,\"財\":-733,\"費\":1777,\"車\":1835,\"軍\":1375,\"込\":-1504,\"通\":-1136,\"選\":-681,\"郎\":1026,\"郡\":4404,\"部\":1200,\"金\":2163,\"長\":421,\"開\":-1432,\"間\":1302,\"関\":-1282,\"雨\":2009,\"電\":-1045,\"非\":2066,\"駅\":1620,\"１\":-800,\"｣\":2670,\"･\":-3794,\"ｯ\":-1350,\"ｱ\":551,\"ｸﾞ\":1319,\"ｽ\":874,\"ﾄ\":521,\"ﾑ\":1109,\"ﾙ\":1591,\"ﾛ\":2201,\"ﾝ\":278};\n          this.UW4__ = {\",\":3930,\".\":3508,\"―\":-4841,\"、\":3930,\"。\":3508,\"〇\":4999,\"「\":1895,\"」\":3798,\"〓\":-5156,\"あ\":4752,\"い\":-3435,\"う\":-640,\"え\":-2514,\"お\":2405,\"か\":530,\"が\":6006,\"き\":-4482,\"ぎ\":-3821,\"く\":-3788,\"け\":-4376,\"げ\":-4734,\"こ\":2255,\"ご\":1979,\"さ\":2864,\"し\":-843,\"じ\":-2506,\"す\":-731,\"ず\":1251,\"せ\":181,\"そ\":4091,\"た\":5034,\"だ\":5408,\"ち\":-3654,\"っ\":-5882,\"つ\":-1659,\"て\":3994,\"で\":7410,\"と\":4547,\"な\":5433,\"に\":6499,\"ぬ\":1853,\"ね\":1413,\"の\":7396,\"は\":8578,\"ば\":1940,\"ひ\":4249,\"び\":-4134,\"ふ\":1345,\"へ\":6665,\"べ\":-744,\"ほ\":1464,\"ま\":1051,\"み\":-2082,\"む\":-882,\"め\":-5046,\"も\":4169,\"ゃ\":-2666,\"や\":2795,\"ょ\":-1544,\"よ\":3351,\"ら\":-2922,\"り\":-9726,\"る\":-14896,\"れ\":-2613,\"ろ\":-4570,\"わ\":-1783,\"を\":13150,\"ん\":-2352,\"カ\":2145,\"コ\":1789,\"セ\":1287,\"ッ\":-724,\"ト\":-403,\"メ\":-1635,\"ラ\":-881,\"リ\":-541,\"ル\":-856,\"ン\":-3637,\"・\":-4371,\"ー\":-11870,\"一\":-2069,\"中\":2210,\"予\":782,\"事\":-190,\"井\":-1768,\"人\":1036,\"以\":544,\"会\":950,\"体\":-1286,\"作\":530,\"側\":4292,\"先\":601,\"党\":-2006,\"共\":-1212,\"内\":584,\"円\":788,\"初\":1347,\"前\":1623,\"副\":3879,\"力\":-302,\"動\":-740,\"務\":-2715,\"化\":776,\"区\":4517,\"協\":1013,\"参\":1555,\"合\":-1834,\"和\":-681,\"員\":-910,\"器\":-851,\"回\":1500,\"国\":-619,\"園\":-1200,\"地\":866,\"場\":-1410,\"塁\":-2094,\"士\":-1413,\"多\":1067,\"大\":571,\"子\":-4802,\"学\":-1397,\"定\":-1057,\"寺\":-809,\"小\":1910,\"屋\":-1328,\"山\":-1500,\"島\":-2056,\"川\":-2667,\"市\":2771,\"年\":374,\"庁\":-4556,\"後\":456,\"性\":553,\"感\":916,\"所\":-1566,\"支\":856,\"改\":787,\"政\":2182,\"教\":704,\"文\":522,\"方\":-856,\"日\":1798,\"時\":1829,\"最\":845,\"月\":-9066,\"木\":-485,\"来\":-442,\"校\":-360,\"業\":-1043,\"氏\":5388,\"民\":-2716,\"気\":-910,\"沢\":-939,\"済\":-543,\"物\":-735,\"率\":672,\"球\":-1267,\"生\":-1286,\"産\":-1101,\"田\":-2900,\"町\":1826,\"的\":2586,\"目\":922,\"省\":-3485,\"県\":2997,\"空\":-867,\"立\":-2112,\"第\":788,\"米\":2937,\"系\":786,\"約\":2171,\"経\":1146,\"統\":-1169,\"総\":940,\"線\":-994,\"署\":749,\"者\":2145,\"能\":-730,\"般\":-852,\"行\":-792,\"規\":792,\"警\":-1184,\"議\":-244,\"谷\":-1000,\"賞\":730,\"車\":-1481,\"軍\":1158,\"輪\":-1433,\"込\":-3370,\"近\":929,\"道\":-1291,\"選\":2596,\"郎\":-4866,\"都\":1192,\"野\":-1100,\"銀\":-2213,\"長\":357,\"間\":-2344,\"院\":-2297,\"際\":-2604,\"電\":-878,\"領\":-1659,\"題\":-792,\"館\":-1984,\"首\":1749,\"高\":2120,\"｢\":1895,\"｣\":3798,\"･\":-4371,\"ｯ\":-724,\"ｰ\":-11870,\"ｶ\":2145,\"ｺ\":1789,\"ｾ\":1287,\"ﾄ\":-403,\"ﾒ\":-1635,\"ﾗ\":-881,\"ﾘ\":-541,\"ﾙ\":-856,\"ﾝ\":-3637};\n          this.UW5__ = {\",\":465,\".\":-299,\"1\":-514,\"E2\":-32768,\"]\":-2762,\"、\":465,\"。\":-299,\"「\":363,\"あ\":1655,\"い\":331,\"う\":-503,\"え\":1199,\"お\":527,\"か\":647,\"が\":-421,\"き\":1624,\"ぎ\":1971,\"く\":312,\"げ\":-983,\"さ\":-1537,\"し\":-1371,\"す\":-852,\"だ\":-1186,\"ち\":1093,\"っ\":52,\"つ\":921,\"て\":-18,\"で\":-850,\"と\":-127,\"ど\":1682,\"な\":-787,\"に\":-1224,\"の\":-635,\"は\":-578,\"べ\":1001,\"み\":502,\"め\":865,\"ゃ\":3350,\"ょ\":854,\"り\":-208,\"る\":429,\"れ\":504,\"わ\":419,\"を\":-1264,\"ん\":327,\"イ\":241,\"ル\":451,\"ン\":-343,\"中\":-871,\"京\":722,\"会\":-1153,\"党\":-654,\"務\":3519,\"区\":-901,\"告\":848,\"員\":2104,\"大\":-1296,\"学\":-548,\"定\":1785,\"嵐\":-1304,\"市\":-2991,\"席\":921,\"年\":1763,\"思\":872,\"所\":-814,\"挙\":1618,\"新\":-1682,\"日\":218,\"月\":-4353,\"査\":932,\"格\":1356,\"機\":-1508,\"氏\":-1347,\"田\":240,\"町\":-3912,\"的\":-3149,\"相\":1319,\"省\":-1052,\"県\":-4003,\"研\":-997,\"社\":-278,\"空\":-813,\"統\":1955,\"者\":-2233,\"表\":663,\"語\":-1073,\"議\":1219,\"選\":-1018,\"郎\":-368,\"長\":786,\"間\":1191,\"題\":2368,\"館\":-689,\"１\":-514,\"Ｅ２\":-32768,\"｢\":363,\"ｲ\":241,\"ﾙ\":451,\"ﾝ\":-343};\n          this.UW6__ = {\",\":227,\".\":808,\"1\":-270,\"E1\":306,\"、\":227,\"。\":808,\"あ\":-307,\"う\":189,\"か\":241,\"が\":-73,\"く\":-121,\"こ\":-200,\"じ\":1782,\"す\":383,\"た\":-428,\"っ\":573,\"て\":-1014,\"で\":101,\"と\":-105,\"な\":-253,\"に\":-149,\"の\":-417,\"は\":-236,\"も\":-206,\"り\":187,\"る\":-135,\"を\":195,\"ル\":-673,\"ン\":-496,\"一\":-277,\"中\":201,\"件\":-800,\"会\":624,\"前\":302,\"区\":1792,\"員\":-1212,\"委\":798,\"学\":-960,\"市\":887,\"広\":-695,\"後\":535,\"業\":-697,\"相\":753,\"社\":-507,\"福\":974,\"空\":-822,\"者\":1811,\"連\":463,\"郎\":1082,\"１\":-270,\"Ｅ１\":306,\"ﾙ\":-673,\"ﾝ\":-496};\n          \n          return this;\n        }\n        TinySegmenter.prototype.ctype_ = function(str) {\n          for (var i in this.chartype_) {\n            if (str.match(this.chartype_[i][0])) {\n              return this.chartype_[i][1];\n            }\n          }\n          return \"O\";\n        }\n\n        TinySegmenter.prototype.ts_ = function(v) {\n          if (v) { return v; }\n          return 0;\n        }\n\n        TinySegmenter.prototype.segment = function(input) {\n          if (input == null || input == undefined || input == \"\") {\n            return [];\n          }\n          var result = [];\n          var seg = [\"B3\",\"B2\",\"B1\"];\n          var ctype = [\"O\",\"O\",\"O\"];\n          var o = input.split(\"\");\n          for (i = 0; i < o.length; ++i) {\n            seg.push(o[i]);\n            ctype.push(this.ctype_(o[i]))\n          }\n          seg.push(\"E1\");\n          seg.push(\"E2\");\n          seg.push(\"E3\");\n          ctype.push(\"O\");\n          ctype.push(\"O\");\n          ctype.push(\"O\");\n          var word = seg[3];\n          var p1 = \"U\";\n          var p2 = \"U\";\n          var p3 = \"U\";\n          for (var i = 4; i < seg.length - 3; ++i) {\n            var score = this.BIAS__;\n            var w1 = seg[i-3];\n            var w2 = seg[i-2];\n            var w3 = seg[i-1];\n            var w4 = seg[i];\n            var w5 = seg[i+1];\n            var w6 = seg[i+2];\n            var c1 = ctype[i-3];\n            var c2 = ctype[i-2];\n            var c3 = ctype[i-1];\n            var c4 = ctype[i];\n            var c5 = ctype[i+1];\n            var c6 = ctype[i+2];\n            score += this.ts_(this.UP1__[p1]);\n            score += this.ts_(this.UP2__[p2]);\n            score += this.ts_(this.UP3__[p3]);\n            score += this.ts_(this.BP1__[p1 + p2]);\n            score += this.ts_(this.BP2__[p2 + p3]);\n            score += this.ts_(this.UW1__[w1]);\n            score += this.ts_(this.UW2__[w2]);\n            score += this.ts_(this.UW3__[w3]);\n            score += this.ts_(this.UW4__[w4]);\n            score += this.ts_(this.UW5__[w5]);\n            score += this.ts_(this.UW6__[w6]);\n            score += this.ts_(this.BW1__[w2 + w3]);\n            score += this.ts_(this.BW2__[w3 + w4]);\n            score += this.ts_(this.BW3__[w4 + w5]);\n            score += this.ts_(this.TW1__[w1 + w2 + w3]);\n            score += this.ts_(this.TW2__[w2 + w3 + w4]);\n            score += this.ts_(this.TW3__[w3 + w4 + w5]);\n            score += this.ts_(this.TW4__[w4 + w5 + w6]);\n            score += this.ts_(this.UC1__[c1]);\n            score += this.ts_(this.UC2__[c2]);\n            score += this.ts_(this.UC3__[c3]);\n            score += this.ts_(this.UC4__[c4]);\n            score += this.ts_(this.UC5__[c5]);\n            score += this.ts_(this.UC6__[c6]);\n            score += this.ts_(this.BC1__[c2 + c3]);\n            score += this.ts_(this.BC2__[c3 + c4]);\n            score += this.ts_(this.BC3__[c4 + c5]);\n            score += this.ts_(this.TC1__[c1 + c2 + c3]);\n            score += this.ts_(this.TC2__[c2 + c3 + c4]);\n            score += this.ts_(this.TC3__[c3 + c4 + c5]);\n            score += this.ts_(this.TC4__[c4 + c5 + c6]);\n        //  score += this.ts_(this.TC5__[c4 + c5 + c6]);    \n            score += this.ts_(this.UQ1__[p1 + c1]);\n            score += this.ts_(this.UQ2__[p2 + c2]);\n            score += this.ts_(this.UQ3__[p3 + c3]);\n            score += this.ts_(this.BQ1__[p2 + c2 + c3]);\n            score += this.ts_(this.BQ2__[p2 + c3 + c4]);\n            score += this.ts_(this.BQ3__[p3 + c2 + c3]);\n            score += this.ts_(this.BQ4__[p3 + c3 + c4]);\n            score += this.ts_(this.TQ1__[p2 + c1 + c2 + c3]);\n            score += this.ts_(this.TQ2__[p2 + c2 + c3 + c4]);\n            score += this.ts_(this.TQ3__[p3 + c1 + c2 + c3]);\n            score += this.ts_(this.TQ4__[p3 + c2 + c3 + c4]);\n            var p = \"O\";\n            if (score > 0) {\n              result.push(word);\n              word = \"\";\n              p = \"B\";\n            }\n            p1 = p2;\n            p2 = p3;\n            p3 = p;\n            word += seg[i];\n          }\n          result.push(word);\n\n          return result;\n        }\n\n        lunr.TinySegmenter = TinySegmenter;\n    };\n\n}));", "/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;(function (root, factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define(factory)\n    } else if (typeof exports === 'object') {\n        /**\n         * Node. Does not work with strict CommonJS, but\n         * only CommonJS-like environments that support module.exports,\n         * like Node.\n         */\n        module.exports = factory()\n    } else {\n        // Browser globals (root is window)\n        factory()(root.lunr);\n    }\n}(this, function () {\n    /**\n     * Just return a value to define the module export.\n     * This example returns an object, but the module\n     * can return a function as the exported value.\n     */\n    return function(lunr) {\n        /* Set up the pipeline for indexing content in multiple languages. The\n           corresponding lunr.{lang} files must be loaded before calling this\n           function; English ('en') is built in.\n\n           Returns: a lunr plugin for use in your indexer.\n\n           Known drawback: every word will be stemmed with stemmers for every\n           language. This could mean that sometimes words that have the same\n           stemming root will not be stemmed as such.\n           */\n        lunr.multiLanguage = function(/* lang1, lang2, ... */) {\n            var languages = Array.prototype.slice.call(arguments);\n            var nameSuffix = languages.join('-');\n            var wordCharacters = \"\";\n            var pipeline = [];\n            var searchPipeline = [];\n            for (var i = 0; i < languages.length; ++i) {\n                if (languages[i] == 'en') {\n                    wordCharacters += '\\\\w';\n                    pipeline.unshift(lunr.stopWordFilter);\n                    pipeline.push(lunr.stemmer);\n                    searchPipeline.push(lunr.stemmer);\n                } else {\n                    wordCharacters += lunr[languages[i]].wordCharacters;\n                    if (lunr[languages[i]].stopWordFilter) {\n                        pipeline.unshift(lunr[languages[i]].stopWordFilter);\n                    }\n                    if (lunr[languages[i]].stemmer) {\n                        pipeline.push(lunr[languages[i]].stemmer);\n                        searchPipeline.push(lunr[languages[i]].stemmer);\n                    }\n                }\n            };\n            var multiTrimmer = lunr.trimmerSupport.generateTrimmer(wordCharacters);\n            lunr.Pipeline.registerFunction(multiTrimmer, 'lunr-multi-trimmer-' + nameSuffix);\n            pipeline.unshift(multiTrimmer);\n\n            return function() {\n                this.pipeline.reset();\n\n                this.pipeline.add.apply(this.pipeline, pipeline);\n\n                // for lunr version 2\n                // this is necessary so that every searched word is also stemmed before\n                // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n                if (this.searchPipeline) {\n                    this.searchPipeline.reset();\n                    this.searchPipeline.add.apply(this.searchPipeline, searchPipeline);\n                }\n            };\n        }\n    }\n}));\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n\nimport lunr from 'lunr'\nimport stemmer from 'lunr-languages/lunr.stemmer.support'\nimport tinyseg from 'lunr-languages/tinyseg'\nimport multi from 'lunr-languages/lunr.multi'\nimport { get, set, createStore } from 'idb-keyval'\n\ntype SearchHit = {\n  href: string\n  title: string\n  summary: string\n  keywords: string\n}\n\nlet search: (q: string) => SearchHit[]\n\nasync function loadIndex({ lunrLanguages }: { lunrLanguages?: string[] }) {\n  const { index, data } = await loadIndexCore()\n  search = q => index.search(q).map(({ ref }) => data[ref])\n  postMessage({ e: 'index-ready' })\n\n  async function loadIndexCore() {\n    const res = await fetch('../index.json')\n    const etag = res.headers.get('etag')\n    const data = await res.json() as { [key: string]: SearchHit }\n    const cache = createStore('docfx', 'lunr')\n\n    if (lunrLanguages && lunrLanguages.length > 0) {\n      multi(lunr)\n      stemmer(lunr)\n      if (lunrLanguages.includes('ja')) {\n        tinyseg(lunr)\n      }\n      await Promise.all(lunrLanguages.map(initLanguage))\n    }\n\n    if (etag) {\n      const value = JSON.parse(await get('index', cache) || '{}')\n      if (value && value.etag === etag) {\n        return { index: lunr.Index.load(value), data }\n      }\n    }\n\n    const index = lunr(function() {\n      lunr.tokenizer.separator = /[\\s\\-.()]+/\n\n      this.ref('href')\n      this.field('title', { boost: 50 })\n      this.field('keywords', { boost: 40 })\n      this.field('summary', { boost: 20 })\n\n      if (lunrLanguages && lunrLanguages.length > 0) {\n        this.use(lunr.multiLanguage(...lunrLanguages))\n      }\n\n      for (const key in data) {\n        this.add(data[key])\n      }\n    })\n\n    if (etag) {\n      await set('index', JSON.stringify(Object.assign(index.toJSON(), { etag })), cache)\n    }\n\n    return { index, data }\n  }\n}\n\nonmessage = function(e) {\n  if (e.data.q && search) {\n    postMessage({ e: 'query-ready', d: search(e.data.q) })\n  } else if (e.data.init) {\n    loadIndex(e.data.init).catch(console.error)\n  }\n}\n\nconst langMap = {\n  ar: () => import('lunr-languages/lunr.ar.js'),\n  da: () => import('lunr-languages/lunr.da.js'),\n  de: () => import('lunr-languages/lunr.de.js'),\n  du: () => import('lunr-languages/lunr.du.js'),\n  el: () => import('lunr-languages/lunr.el.js'),\n  es: () => import('lunr-languages/lunr.es.js'),\n  fi: () => import('lunr-languages/lunr.fi.js'),\n  fr: () => import('lunr-languages/lunr.fr.js'),\n  he: () => import('lunr-languages/lunr.he.js'),\n  hi: () => import('lunr-languages/lunr.hi.js'),\n  hu: () => import('lunr-languages/lunr.hu.js'),\n  hy: () => import('lunr-languages/lunr.hy.js'),\n  it: () => import('lunr-languages/lunr.it.js'),\n  ja: () => import('lunr-languages/lunr.ja.js'),\n  jp: () => import('lunr-languages/lunr.jp.js'),\n  kn: () => import('lunr-languages/lunr.kn.js'),\n  ko: () => import('lunr-languages/lunr.ko.js'),\n  nl: () => import('lunr-languages/lunr.nl.js'),\n  no: () => import('lunr-languages/lunr.no.js'),\n  pt: () => import('lunr-languages/lunr.pt.js'),\n  ro: () => import('lunr-languages/lunr.ro.js'),\n  ru: () => import('lunr-languages/lunr.ru.js'),\n  sa: () => import('lunr-languages/lunr.sa.js'),\n  sv: () => import('lunr-languages/lunr.sv.js'),\n  ta: () => import('lunr-languages/lunr.ta.js'),\n  te: () => import('lunr-languages/lunr.te.js'),\n  th: () => import('lunr-languages/lunr.th.js'),\n  tr: () => import('lunr-languages/lunr.tr.js'),\n  vi: () => import('lunr-languages/lunr.vi.js')\n\n  // zh is currently not supported due to dependency on NodeJS.\n  // zh: () => import('lunr-languages/lunr.zh.js')\n}\n\nasync function initLanguage(lang: string) {\n  if (lang !== 'en') {\n    const { default: init } = await langMap[lang]()\n    init(lunr)\n  }\n}\n", "function promisifyRequest(request) {\n    return new Promise((resolve, reject) => {\n        // @ts-ignore - file size hacks\n        request.oncomplete = request.onsuccess = () => resolve(request.result);\n        // @ts-ignore - file size hacks\n        request.onabort = request.onerror = () => reject(request.error);\n    });\n}\nfunction createStore(dbName, storeName) {\n    const request = indexedDB.open(dbName);\n    request.onupgradeneeded = () => request.result.createObjectStore(storeName);\n    const dbp = promisifyRequest(request);\n    return (txMode, callback) => dbp.then((db) => callback(db.transaction(storeName, txMode).objectStore(storeName)));\n}\nlet defaultGetStoreFunc;\nfunction defaultGetStore() {\n    if (!defaultGetStoreFunc) {\n        defaultGetStoreFunc = createStore('keyval-store', 'keyval');\n    }\n    return defaultGetStoreFunc;\n}\n/**\n * Get a value by its key.\n *\n * @param key\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction get(key, customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => promisifyRequest(store.get(key)));\n}\n/**\n * Set a value with a key.\n *\n * @param key\n * @param value\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction set(key, value, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        store.put(value, key);\n        return promisifyRequest(store.transaction);\n    });\n}\n/**\n * Set multiple values at once. This is faster than calling set() multiple times.\n * It's also atomic – if one of the pairs can't be added, none will be added.\n *\n * @param entries Array of entries, where each entry is an array of `[key, value]`.\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction setMany(entries, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        entries.forEach((entry) => store.put(entry[1], entry[0]));\n        return promisifyRequest(store.transaction);\n    });\n}\n/**\n * Get multiple values by their keys\n *\n * @param keys\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction getMany(keys, customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => Promise.all(keys.map((key) => promisifyRequest(store.get(key)))));\n}\n/**\n * Update a value. This lets you see the old value and update it as an atomic operation.\n *\n * @param key\n * @param updater A callback that takes the old value and returns a new value.\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction update(key, updater, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => \n    // Need to create the promise manually.\n    // If I try to chain promises, the transaction closes in browsers\n    // that use a promise polyfill (IE10/11).\n    new Promise((resolve, reject) => {\n        store.get(key).onsuccess = function () {\n            try {\n                store.put(updater(this.result), key);\n                resolve(promisifyRequest(store.transaction));\n            }\n            catch (err) {\n                reject(err);\n            }\n        };\n    }));\n}\n/**\n * Delete a particular key from the store.\n *\n * @param key\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction del(key, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        store.delete(key);\n        return promisifyRequest(store.transaction);\n    });\n}\n/**\n * Delete multiple keys at once.\n *\n * @param keys List of keys to delete.\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction delMany(keys, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        keys.forEach((key) => store.delete(key));\n        return promisifyRequest(store.transaction);\n    });\n}\n/**\n * Clear all values in the store.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction clear(customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        store.clear();\n        return promisifyRequest(store.transaction);\n    });\n}\nfunction eachCursor(store, callback) {\n    store.openCursor().onsuccess = function () {\n        if (!this.result)\n            return;\n        callback(this.result);\n        this.result.continue();\n    };\n    return promisifyRequest(store.transaction);\n}\n/**\n * Get all keys in the store.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction keys(customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => {\n        // Fast path for modern browsers\n        if (store.getAllKeys) {\n            return promisifyRequest(store.getAllKeys());\n        }\n        const items = [];\n        return eachCursor(store, (cursor) => items.push(cursor.key)).then(() => items);\n    });\n}\n/**\n * Get all values in the store.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction values(customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => {\n        // Fast path for modern browsers\n        if (store.getAll) {\n            return promisifyRequest(store.getAll());\n        }\n        const items = [];\n        return eachCursor(store, (cursor) => items.push(cursor.value)).then(() => items);\n    });\n}\n/**\n * Get all entries in the store. Each entry is an array of `[key, value]`.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction entries(customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => {\n        // Fast path for modern browsers\n        // (although, hopefully we'll get a simpler path some day)\n        if (store.getAll && store.getAllKeys) {\n            return Promise.all([\n                promisifyRequest(store.getAllKeys()),\n                promisifyRequest(store.getAll()),\n            ]).then(([keys, values]) => keys.map((key, i) => [key, values[i]]));\n        }\n        const items = [];\n        return customStore('readonly', (store) => eachCursor(store, (cursor) => items.push([cursor.key, cursor.value])).then(() => items));\n    });\n}\n\nexport { clear, createStore, del, delMany, entries, get, getMany, keys, promisifyRequest, set, setMany, update, values };\n"], "mappings": "mDAAA,IAAAA,GAAAC,EAAA,CAAAC,EAAAC,IAAA,EAME,UAAU,CAiCZ,IAAIC,EAAO,SAAUC,EAAQ,CAC3B,IAAIC,EAAU,IAAIF,EAAK,QAEvB,OAAAE,EAAQ,SAAS,IACfF,EAAK,QACLA,EAAK,eACLA,EAAK,OACP,EAEAE,EAAQ,eAAe,IACrBF,EAAK,OACP,EAEAC,EAAO,KAAKC,EAASA,CAAO,EACrBA,EAAQ,MAAM,CACvB,EAEAF,EAAK,QAAU,QAUfA,EAAK,MAAQ,CAAC,EASdA,EAAK,MAAM,KAAQ,SAAUG,EAAQ,CAEnC,OAAO,SAAUC,EAAS,CACpBD,EAAO,SAAW,QAAQ,MAC5B,QAAQ,KAAKC,CAAO,CAExB,CAEF,EAAG,IAAI,EAaPJ,EAAK,MAAM,SAAW,SAAUK,EAAK,CACnC,OAAsBA,GAAQ,KACrB,GAEAA,EAAI,SAAS,CAExB,EAkBAL,EAAK,MAAM,MAAQ,SAAUK,EAAK,CAChC,GAAIA,GAAQ,KACV,OAAOA,EAMT,QAHIC,EAAQ,OAAO,OAAO,IAAI,EAC1BC,EAAO,OAAO,KAAKF,CAAG,EAEjB,EAAI,EAAG,EAAIE,EAAK,OAAQ,IAAK,CACpC,IAAIC,EAAMD,EAAK,CAAC,EACZE,EAAMJ,EAAIG,CAAG,EAEjB,GAAI,MAAM,QAAQC,CAAG,EAAG,CACtBH,EAAME,CAAG,EAAIC,EAAI,MAAM,EACvB,QACF,CAEA,GAAI,OAAOA,GAAQ,UACf,OAAOA,GAAQ,UACf,OAAOA,GAAQ,UAAW,CAC5BH,EAAME,CAAG,EAAIC,EACb,QACF,CAEA,MAAM,IAAI,UAAU,uDAAuD,CAC7E,CAEA,OAAOH,CACT,EACAN,EAAK,SAAW,SAAUU,EAAQC,EAAWC,EAAa,CACxD,KAAK,OAASF,EACd,KAAK,UAAYC,EACjB,KAAK,aAAeC,CACtB,EAEAZ,EAAK,SAAS,OAAS,IAEvBA,EAAK,SAAS,WAAa,SAAUa,EAAG,CACtC,IAAIC,EAAID,EAAE,QAAQb,EAAK,SAAS,MAAM,EAEtC,GAAIc,IAAM,GACR,KAAM,6BAGR,IAAIC,EAAWF,EAAE,MAAM,EAAGC,CAAC,EACvBJ,EAASG,EAAE,MAAMC,EAAI,CAAC,EAE1B,OAAO,IAAId,EAAK,SAAUU,EAAQK,EAAUF,CAAC,CAC/C,EAEAb,EAAK,SAAS,UAAU,SAAW,UAAY,CAC7C,OAAI,KAAK,cAAgB,OACvB,KAAK,aAAe,KAAK,UAAYA,EAAK,SAAS,OAAS,KAAK,QAG5D,KAAK,YACd,EAWAA,EAAK,IAAM,SAAUgB,EAAU,CAG7B,GAFA,KAAK,SAAW,OAAO,OAAO,IAAI,EAE9BA,EAAU,CACZ,KAAK,OAASA,EAAS,OAEvB,QAASC,EAAI,EAAGA,EAAI,KAAK,OAAQA,IAC/B,KAAK,SAASD,EAASC,CAAC,CAAC,EAAI,EAEjC,MACE,KAAK,OAAS,CAElB,EASAjB,EAAK,IAAI,SAAW,CAClB,UAAW,SAAUkB,EAAO,CAC1B,OAAOA,CACT,EAEA,MAAO,UAAY,CACjB,OAAO,IACT,EAEA,SAAU,UAAY,CACpB,MAAO,EACT,CACF,EASAlB,EAAK,IAAI,MAAQ,CACf,UAAW,UAAY,CACrB,OAAO,IACT,EAEA,MAAO,SAAUkB,EAAO,CACtB,OAAOA,CACT,EAEA,SAAU,UAAY,CACpB,MAAO,EACT,CACF,EAQAlB,EAAK,IAAI,UAAU,SAAW,SAAUmB,EAAQ,CAC9C,MAAO,CAAC,CAAC,KAAK,SAASA,CAAM,CAC/B,EAUAnB,EAAK,IAAI,UAAU,UAAY,SAAUkB,EAAO,CAC9C,IAAIE,EAAGC,EAAGL,EAAUM,EAAe,CAAC,EAEpC,GAAIJ,IAAUlB,EAAK,IAAI,SACrB,OAAO,KAGT,GAAIkB,IAAUlB,EAAK,IAAI,MACrB,OAAOkB,EAGL,KAAK,OAASA,EAAM,QACtBE,EAAI,KACJC,EAAIH,IAEJE,EAAIF,EACJG,EAAI,MAGNL,EAAW,OAAO,KAAKI,EAAE,QAAQ,EAEjC,QAASH,EAAI,EAAGA,EAAID,EAAS,OAAQC,IAAK,CACxC,IAAIM,EAAUP,EAASC,CAAC,EACpBM,KAAWF,EAAE,UACfC,EAAa,KAAKC,CAAO,CAE7B,CAEA,OAAO,IAAIvB,EAAK,IAAKsB,CAAY,CACnC,EASAtB,EAAK,IAAI,UAAU,MAAQ,SAAUkB,EAAO,CAC1C,OAAIA,IAAUlB,EAAK,IAAI,SACdA,EAAK,IAAI,SAGdkB,IAAUlB,EAAK,IAAI,MACd,KAGF,IAAIA,EAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,OAAO,OAAO,KAAKkB,EAAM,QAAQ,CAAC,CAAC,CACpF,EASAlB,EAAK,IAAM,SAAUwB,EAASC,EAAe,CAC3C,IAAIC,EAAoB,EAExB,QAASf,KAAaa,EAChBb,GAAa,WACjBe,GAAqB,OAAO,KAAKF,EAAQb,CAAS,CAAC,EAAE,QAGvD,IAAIgB,GAAKF,EAAgBC,EAAoB,KAAQA,EAAoB,IAEzE,OAAO,KAAK,IAAI,EAAI,KAAK,IAAIC,CAAC,CAAC,CACjC,EAUA3B,EAAK,MAAQ,SAAU4B,EAAKC,EAAU,CACpC,KAAK,IAAMD,GAAO,GAClB,KAAK,SAAWC,GAAY,CAAC,CAC/B,EAOA7B,EAAK,MAAM,UAAU,SAAW,UAAY,CAC1C,OAAO,KAAK,GACd,EAsBAA,EAAK,MAAM,UAAU,OAAS,SAAU8B,EAAI,CAC1C,YAAK,IAAMA,EAAG,KAAK,IAAK,KAAK,QAAQ,EAC9B,IACT,EASA9B,EAAK,MAAM,UAAU,MAAQ,SAAU8B,EAAI,CACzC,OAAAA,EAAKA,GAAM,SAAUjB,EAAG,CAAE,OAAOA,CAAE,EAC5B,IAAIb,EAAK,MAAO8B,EAAG,KAAK,IAAK,KAAK,QAAQ,EAAG,KAAK,QAAQ,CACnE,EAwBA9B,EAAK,UAAY,SAAUK,EAAKwB,EAAU,CACxC,GAAIxB,GAAO,MAAQA,GAAO,KACxB,MAAO,CAAC,EAGV,GAAI,MAAM,QAAQA,CAAG,EACnB,OAAOA,EAAI,IAAI,SAAU0B,EAAG,CAC1B,OAAO,IAAI/B,EAAK,MACdA,EAAK,MAAM,SAAS+B,CAAC,EAAE,YAAY,EACnC/B,EAAK,MAAM,MAAM6B,CAAQ,CAC3B,CACF,CAAC,EAOH,QAJID,EAAMvB,EAAI,SAAS,EAAE,YAAY,EACjC2B,EAAMJ,EAAI,OACVK,EAAS,CAAC,EAELC,EAAW,EAAGC,EAAa,EAAGD,GAAYF,EAAKE,IAAY,CAClE,IAAIE,EAAOR,EAAI,OAAOM,CAAQ,EAC1BG,EAAcH,EAAWC,EAE7B,GAAKC,EAAK,MAAMpC,EAAK,UAAU,SAAS,GAAKkC,GAAYF,EAAM,CAE7D,GAAIK,EAAc,EAAG,CACnB,IAAIC,EAAgBtC,EAAK,MAAM,MAAM6B,CAAQ,GAAK,CAAC,EACnDS,EAAc,SAAc,CAACH,EAAYE,CAAW,EACpDC,EAAc,MAAWL,EAAO,OAEhCA,EAAO,KACL,IAAIjC,EAAK,MACP4B,EAAI,MAAMO,EAAYD,CAAQ,EAC9BI,CACF,CACF,CACF,CAEAH,EAAaD,EAAW,CAC1B,CAEF,CAEA,OAAOD,CACT,EASAjC,EAAK,UAAU,UAAY,UAmC3BA,EAAK,SAAW,UAAY,CAC1B,KAAK,OAAS,CAAC,CACjB,EAEAA,EAAK,SAAS,oBAAsB,OAAO,OAAO,IAAI,EAmCtDA,EAAK,SAAS,iBAAmB,SAAU8B,EAAIS,EAAO,CAChDA,KAAS,KAAK,qBAChBvC,EAAK,MAAM,KAAK,6CAA+CuC,CAAK,EAGtET,EAAG,MAAQS,EACXvC,EAAK,SAAS,oBAAoB8B,EAAG,KAAK,EAAIA,CAChD,EAQA9B,EAAK,SAAS,4BAA8B,SAAU8B,EAAI,CACxD,IAAIU,EAAeV,EAAG,OAAUA,EAAG,SAAS,KAAK,oBAE5CU,GACHxC,EAAK,MAAM,KAAK;AAAA,EAAmG8B,CAAE,CAEzH,EAYA9B,EAAK,SAAS,KAAO,SAAUyC,EAAY,CACzC,IAAIC,EAAW,IAAI1C,EAAK,SAExB,OAAAyC,EAAW,QAAQ,SAAUE,EAAQ,CACnC,IAAIb,EAAK9B,EAAK,SAAS,oBAAoB2C,CAAM,EAEjD,GAAIb,EACFY,EAAS,IAAIZ,CAAE,MAEf,OAAM,IAAI,MAAM,sCAAwCa,CAAM,CAElE,CAAC,EAEMD,CACT,EASA1C,EAAK,SAAS,UAAU,IAAM,UAAY,CACxC,IAAI4C,EAAM,MAAM,UAAU,MAAM,KAAK,SAAS,EAE9CA,EAAI,QAAQ,SAAUd,EAAI,CACxB9B,EAAK,SAAS,4BAA4B8B,CAAE,EAC5C,KAAK,OAAO,KAAKA,CAAE,CACrB,EAAG,IAAI,CACT,EAWA9B,EAAK,SAAS,UAAU,MAAQ,SAAU6C,EAAYC,EAAO,CAC3D9C,EAAK,SAAS,4BAA4B8C,CAAK,EAE/C,IAAIC,EAAM,KAAK,OAAO,QAAQF,CAAU,EACxC,GAAIE,GAAO,GACT,MAAM,IAAI,MAAM,wBAAwB,EAG1CA,EAAMA,EAAM,EACZ,KAAK,OAAO,OAAOA,EAAK,EAAGD,CAAK,CAClC,EAWA9C,EAAK,SAAS,UAAU,OAAS,SAAU6C,EAAYC,EAAO,CAC5D9C,EAAK,SAAS,4BAA4B8C,CAAK,EAE/C,IAAIC,EAAM,KAAK,OAAO,QAAQF,CAAU,EACxC,GAAIE,GAAO,GACT,MAAM,IAAI,MAAM,wBAAwB,EAG1C,KAAK,OAAO,OAAOA,EAAK,EAAGD,CAAK,CAClC,EAOA9C,EAAK,SAAS,UAAU,OAAS,SAAU8B,EAAI,CAC7C,IAAIiB,EAAM,KAAK,OAAO,QAAQjB,CAAE,EAC5BiB,GAAO,IAIX,KAAK,OAAO,OAAOA,EAAK,CAAC,CAC3B,EASA/C,EAAK,SAAS,UAAU,IAAM,SAAUiC,EAAQ,CAG9C,QAFIe,EAAc,KAAK,OAAO,OAErB/B,EAAI,EAAGA,EAAI+B,EAAa/B,IAAK,CAIpC,QAHIa,EAAK,KAAK,OAAOb,CAAC,EAClBgC,EAAO,CAAC,EAEHC,EAAI,EAAGA,EAAIjB,EAAO,OAAQiB,IAAK,CACtC,IAAIC,EAASrB,EAAGG,EAAOiB,CAAC,EAAGA,EAAGjB,CAAM,EAEpC,GAAI,EAAAkB,GAAW,MAA6BA,IAAW,IAEvD,GAAI,MAAM,QAAQA,CAAM,EACtB,QAASC,EAAI,EAAGA,EAAID,EAAO,OAAQC,IACjCH,EAAK,KAAKE,EAAOC,CAAC,CAAC,OAGrBH,EAAK,KAAKE,CAAM,CAEpB,CAEAlB,EAASgB,CACX,CAEA,OAAOhB,CACT,EAYAjC,EAAK,SAAS,UAAU,UAAY,SAAU4B,EAAKC,EAAU,CAC3D,IAAIwB,EAAQ,IAAIrD,EAAK,MAAO4B,EAAKC,CAAQ,EAEzC,OAAO,KAAK,IAAI,CAACwB,CAAK,CAAC,EAAE,IAAI,SAAUtB,EAAG,CACxC,OAAOA,EAAE,SAAS,CACpB,CAAC,CACH,EAMA/B,EAAK,SAAS,UAAU,MAAQ,UAAY,CAC1C,KAAK,OAAS,CAAC,CACjB,EASAA,EAAK,SAAS,UAAU,OAAS,UAAY,CAC3C,OAAO,KAAK,OAAO,IAAI,SAAU8B,EAAI,CACnC,OAAA9B,EAAK,SAAS,4BAA4B8B,CAAE,EAErCA,EAAG,KACZ,CAAC,CACH,EAsBA9B,EAAK,OAAS,SAAUgB,EAAU,CAChC,KAAK,WAAa,EAClB,KAAK,SAAWA,GAAY,CAAC,CAC/B,EAaAhB,EAAK,OAAO,UAAU,iBAAmB,SAAUsD,EAAO,CAExD,GAAI,KAAK,SAAS,QAAU,EAC1B,MAAO,GAST,QANIC,EAAQ,EACRC,EAAM,KAAK,SAAS,OAAS,EAC7BnB,EAAcmB,EAAMD,EACpBE,EAAa,KAAK,MAAMpB,EAAc,CAAC,EACvCqB,EAAa,KAAK,SAASD,EAAa,CAAC,EAEtCpB,EAAc,IACfqB,EAAaJ,IACfC,EAAQE,GAGNC,EAAaJ,IACfE,EAAMC,GAGJC,GAAcJ,IAIlBjB,EAAcmB,EAAMD,EACpBE,EAAaF,EAAQ,KAAK,MAAMlB,EAAc,CAAC,EAC/CqB,EAAa,KAAK,SAASD,EAAa,CAAC,EAO3C,GAJIC,GAAcJ,GAIdI,EAAaJ,EACf,OAAOG,EAAa,EAGtB,GAAIC,EAAaJ,EACf,OAAQG,EAAa,GAAK,CAE9B,EAWAzD,EAAK,OAAO,UAAU,OAAS,SAAU2D,EAAWlD,EAAK,CACvD,KAAK,OAAOkD,EAAWlD,EAAK,UAAY,CACtC,KAAM,iBACR,CAAC,CACH,EAUAT,EAAK,OAAO,UAAU,OAAS,SAAU2D,EAAWlD,EAAKqB,EAAI,CAC3D,KAAK,WAAa,EAClB,IAAI8B,EAAW,KAAK,iBAAiBD,CAAS,EAE1C,KAAK,SAASC,CAAQ,GAAKD,EAC7B,KAAK,SAASC,EAAW,CAAC,EAAI9B,EAAG,KAAK,SAAS8B,EAAW,CAAC,EAAGnD,CAAG,EAEjE,KAAK,SAAS,OAAOmD,EAAU,EAAGD,EAAWlD,CAAG,CAEpD,EAOAT,EAAK,OAAO,UAAU,UAAY,UAAY,CAC5C,GAAI,KAAK,WAAY,OAAO,KAAK,WAKjC,QAHI6D,EAAe,EACfC,EAAiB,KAAK,SAAS,OAE1B7C,EAAI,EAAGA,EAAI6C,EAAgB7C,GAAK,EAAG,CAC1C,IAAIR,EAAM,KAAK,SAASQ,CAAC,EACzB4C,GAAgBpD,EAAMA,CACxB,CAEA,OAAO,KAAK,WAAa,KAAK,KAAKoD,CAAY,CACjD,EAQA7D,EAAK,OAAO,UAAU,IAAM,SAAU+D,EAAa,CAOjD,QANIC,EAAa,EACb5C,EAAI,KAAK,SAAUC,EAAI0C,EAAY,SACnCE,EAAO7C,EAAE,OAAQ8C,EAAO7C,EAAE,OAC1B8C,EAAO,EAAGC,EAAO,EACjBnD,EAAI,EAAGiC,EAAI,EAERjC,EAAIgD,GAAQf,EAAIgB,GACrBC,EAAO/C,EAAEH,CAAC,EAAGmD,EAAO/C,EAAE6B,CAAC,EACnBiB,EAAOC,EACTnD,GAAK,EACIkD,EAAOC,EAChBlB,GAAK,EACIiB,GAAQC,IACjBJ,GAAc5C,EAAEH,EAAI,CAAC,EAAII,EAAE6B,EAAI,CAAC,EAChCjC,GAAK,EACLiC,GAAK,GAIT,OAAOc,CACT,EASAhE,EAAK,OAAO,UAAU,WAAa,SAAU+D,EAAa,CACxD,OAAO,KAAK,IAAIA,CAAW,EAAI,KAAK,UAAU,GAAK,CACrD,EAOA/D,EAAK,OAAO,UAAU,QAAU,UAAY,CAG1C,QAFIqE,EAAS,IAAI,MAAO,KAAK,SAAS,OAAS,CAAC,EAEvCpD,EAAI,EAAGiC,EAAI,EAAGjC,EAAI,KAAK,SAAS,OAAQA,GAAK,EAAGiC,IACvDmB,EAAOnB,CAAC,EAAI,KAAK,SAASjC,CAAC,EAG7B,OAAOoD,CACT,EAOArE,EAAK,OAAO,UAAU,OAAS,UAAY,CACzC,OAAO,KAAK,QACd,EAmBAA,EAAK,QAAW,UAAU,CACxB,IAAIsE,EAAY,CACZ,QAAY,MACZ,OAAW,OACX,KAAS,OACT,KAAS,OACT,KAAS,MACT,IAAQ,MACR,KAAS,KACT,MAAU,MACV,IAAQ,IACR,MAAU,MACV,QAAY,MACZ,MAAU,MACV,KAAS,MACT,MAAU,KACV,QAAY,MACZ,QAAY,MACZ,QAAY,MACZ,MAAU,KACV,MAAU,MACV,OAAW,MACX,KAAS,KACX,EAEAC,EAAY,CACV,MAAU,KACV,MAAU,GACV,MAAU,KACV,MAAU,KACV,KAAS,KACT,IAAQ,GACR,KAAS,EACX,EAEAC,EAAI,WACJC,EAAI,WACJC,EAAIF,EAAI,aACRG,EAAIF,EAAI,WAERG,EAAO,KAAOF,EAAI,KAAOC,EAAID,EAC7BG,EAAO,KAAOH,EAAI,KAAOC,EAAID,EAAI,IAAMC,EAAI,MAC3CG,EAAO,KAAOJ,EAAI,KAAOC,EAAID,EAAIC,EAAID,EACrCK,EAAM,KAAOL,EAAI,KAAOD,EAEtBO,EAAU,IAAI,OAAOJ,CAAI,EACzBK,EAAU,IAAI,OAAOH,CAAI,EACzBI,EAAU,IAAI,OAAOL,CAAI,EACzBM,EAAS,IAAI,OAAOJ,CAAG,EAEvBK,EAAQ,kBACRC,EAAS,iBACTC,EAAQ,aACRC,EAAS,kBACTC,EAAU,KACVC,EAAW,cACXC,EAAW,IAAI,OAAO,oBAAoB,EAC1CC,EAAW,IAAI,OAAO,IAAMjB,EAAID,EAAI,cAAc,EAElDmB,EAAQ,mBACRC,EAAO,2IAEPC,EAAO,iDAEPC,EAAO,sFACPC,EAAQ,oBAERC,EAAO,WACPC,EAAS,MACTC,EAAQ,IAAI,OAAO,IAAMzB,EAAID,EAAI,cAAc,EAE/C2B,EAAgB,SAAuBC,EAAG,CAC5C,IAAIC,EACFC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEF,GAAIP,EAAE,OAAS,EAAK,OAAOA,EAiB3B,GAfAG,EAAUH,EAAE,OAAO,EAAE,CAAC,EAClBG,GAAW,MACbH,EAAIG,EAAQ,YAAY,EAAIH,EAAE,OAAO,CAAC,GAIxCI,EAAKrB,EACLsB,EAAMrB,EAEFoB,EAAG,KAAKJ,CAAC,EAAKA,EAAIA,EAAE,QAAQI,EAAG,MAAM,EAChCC,EAAI,KAAKL,CAAC,IAAKA,EAAIA,EAAE,QAAQK,EAAI,MAAM,GAGhDD,EAAKnB,EACLoB,EAAMnB,EACFkB,EAAG,KAAKJ,CAAC,EAAG,CACd,IAAIQ,EAAKJ,EAAG,KAAKJ,CAAC,EAClBI,EAAKzB,EACDyB,EAAG,KAAKI,EAAG,CAAC,CAAC,IACfJ,EAAKjB,EACLa,EAAIA,EAAE,QAAQI,EAAG,EAAE,EAEvB,SAAWC,EAAI,KAAKL,CAAC,EAAG,CACtB,IAAIQ,EAAKH,EAAI,KAAKL,CAAC,EACnBC,EAAOO,EAAG,CAAC,EACXH,EAAMvB,EACFuB,EAAI,KAAKJ,CAAI,IACfD,EAAIC,EACJI,EAAMjB,EACNkB,EAAMjB,EACNkB,EAAMjB,EACFe,EAAI,KAAKL,CAAC,EAAKA,EAAIA,EAAI,IAClBM,EAAI,KAAKN,CAAC,GAAKI,EAAKjB,EAASa,EAAIA,EAAE,QAAQI,EAAG,EAAE,GAChDG,EAAI,KAAKP,CAAC,IAAKA,EAAIA,EAAI,KAEpC,CAIA,GADAI,EAAKb,EACDa,EAAG,KAAKJ,CAAC,EAAG,CACd,IAAIQ,EAAKJ,EAAG,KAAKJ,CAAC,EAClBC,EAAOO,EAAG,CAAC,EACXR,EAAIC,EAAO,GACb,CAIA,GADAG,EAAKZ,EACDY,EAAG,KAAKJ,CAAC,EAAG,CACd,IAAIQ,EAAKJ,EAAG,KAAKJ,CAAC,EAClBC,EAAOO,EAAG,CAAC,EACXN,EAASM,EAAG,CAAC,EACbJ,EAAKzB,EACDyB,EAAG,KAAKH,CAAI,IACdD,EAAIC,EAAOhC,EAAUiC,CAAM,EAE/B,CAIA,GADAE,EAAKX,EACDW,EAAG,KAAKJ,CAAC,EAAG,CACd,IAAIQ,EAAKJ,EAAG,KAAKJ,CAAC,EAClBC,EAAOO,EAAG,CAAC,EACXN,EAASM,EAAG,CAAC,EACbJ,EAAKzB,EACDyB,EAAG,KAAKH,CAAI,IACdD,EAAIC,EAAO/B,EAAUgC,CAAM,EAE/B,CAKA,GAFAE,EAAKV,EACLW,EAAMV,EACFS,EAAG,KAAKJ,CAAC,EAAG,CACd,IAAIQ,EAAKJ,EAAG,KAAKJ,CAAC,EAClBC,EAAOO,EAAG,CAAC,EACXJ,EAAKxB,EACDwB,EAAG,KAAKH,CAAI,IACdD,EAAIC,EAER,SAAWI,EAAI,KAAKL,CAAC,EAAG,CACtB,IAAIQ,EAAKH,EAAI,KAAKL,CAAC,EACnBC,EAAOO,EAAG,CAAC,EAAIA,EAAG,CAAC,EACnBH,EAAMzB,EACFyB,EAAI,KAAKJ,CAAI,IACfD,EAAIC,EAER,CAIA,GADAG,EAAKR,EACDQ,EAAG,KAAKJ,CAAC,EAAG,CACd,IAAIQ,EAAKJ,EAAG,KAAKJ,CAAC,EAClBC,EAAOO,EAAG,CAAC,EACXJ,EAAKxB,EACLyB,EAAMxB,EACNyB,EAAMR,GACFM,EAAG,KAAKH,CAAI,GAAMI,EAAI,KAAKJ,CAAI,GAAK,CAAEK,EAAI,KAAKL,CAAI,KACrDD,EAAIC,EAER,CAEA,OAAAG,EAAKP,EACLQ,EAAMzB,EACFwB,EAAG,KAAKJ,CAAC,GAAKK,EAAI,KAAKL,CAAC,IAC1BI,EAAKjB,EACLa,EAAIA,EAAE,QAAQI,EAAG,EAAE,GAKjBD,GAAW,MACbH,EAAIG,EAAQ,YAAY,EAAIH,EAAE,OAAO,CAAC,GAGjCA,CACT,EAEA,OAAO,SAAUhD,EAAO,CACtB,OAAOA,EAAM,OAAO+C,CAAa,CACnC,CACF,EAAG,EAEHpG,EAAK,SAAS,iBAAiBA,EAAK,QAAS,SAAS,EAmBtDA,EAAK,uBAAyB,SAAU8G,EAAW,CACjD,IAAIC,EAAQD,EAAU,OAAO,SAAU7D,EAAM+D,EAAU,CACrD,OAAA/D,EAAK+D,CAAQ,EAAIA,EACV/D,CACT,EAAG,CAAC,CAAC,EAEL,OAAO,SAAUI,EAAO,CACtB,GAAIA,GAAS0D,EAAM1D,EAAM,SAAS,CAAC,IAAMA,EAAM,SAAS,EAAG,OAAOA,CACpE,CACF,EAeArD,EAAK,eAAiBA,EAAK,uBAAuB,CAChD,IACA,OACA,QACA,SACA,QACA,MACA,SACA,OACA,KACA,QACA,KACA,MACA,MACA,MACA,KACA,KACA,KACA,UACA,OACA,MACA,KACA,MACA,SACA,QACA,OACA,MACA,KACA,OACA,SACA,OACA,OACA,QACA,MACA,OACA,MACA,MACA,MACA,MACA,OACA,KACA,MACA,OACA,MACA,MACA,MACA,UACA,IACA,KACA,KACA,OACA,KACA,KACA,MACA,OACA,QACA,MACA,OACA,SACA,MACA,KACA,QACA,OACA,OACA,KACA,UACA,KACA,MACA,MACA,KACA,MACA,QACA,KACA,OACA,KACA,QACA,MACA,MACA,SACA,OACA,MACA,OACA,MACA,SACA,QACA,KACA,OACA,OACA,OACA,MACA,QACA,OACA,OACA,QACA,QACA,OACA,OACA,MACA,KACA,MACA,OACA,KACA,QACA,MACA,KACA,OACA,OACA,OACA,QACA,QACA,QACA,MACA,OACA,MACA,OACA,OACA,QACA,MACA,MACA,MACF,CAAC,EAEDA,EAAK,SAAS,iBAAiBA,EAAK,eAAgB,gBAAgB,EAqBpEA,EAAK,QAAU,SAAUqD,EAAO,CAC9B,OAAOA,EAAM,OAAO,SAAUxC,EAAG,CAC/B,OAAOA,EAAE,QAAQ,OAAQ,EAAE,EAAE,QAAQ,OAAQ,EAAE,CACjD,CAAC,CACH,EAEAb,EAAK,SAAS,iBAAiBA,EAAK,QAAS,SAAS,EA2BtDA,EAAK,SAAW,UAAY,CAC1B,KAAK,MAAQ,GACb,KAAK,MAAQ,CAAC,EACd,KAAK,GAAKA,EAAK,SAAS,QACxBA,EAAK,SAAS,SAAW,CAC3B,EAUAA,EAAK,SAAS,QAAU,EASxBA,EAAK,SAAS,UAAY,SAAUiH,EAAK,CAGvC,QAFI/G,EAAU,IAAIF,EAAK,SAAS,QAEvBiB,EAAI,EAAGe,EAAMiF,EAAI,OAAQhG,EAAIe,EAAKf,IACzCf,EAAQ,OAAO+G,EAAIhG,CAAC,CAAC,EAGvB,OAAAf,EAAQ,OAAO,EACRA,EAAQ,IACjB,EAWAF,EAAK,SAAS,WAAa,SAAUkH,EAAQ,CAC3C,MAAI,iBAAkBA,EACblH,EAAK,SAAS,gBAAgBkH,EAAO,KAAMA,EAAO,YAAY,EAE9DlH,EAAK,SAAS,WAAWkH,EAAO,IAAI,CAE/C,EAiBAlH,EAAK,SAAS,gBAAkB,SAAU4B,EAAKuF,EAAc,CAS3D,QARIC,EAAO,IAAIpH,EAAK,SAEhBqH,EAAQ,CAAC,CACX,KAAMD,EACN,eAAgBD,EAChB,IAAKvF,CACP,CAAC,EAEMyF,EAAM,QAAQ,CACnB,IAAIC,EAAQD,EAAM,IAAI,EAGtB,GAAIC,EAAM,IAAI,OAAS,EAAG,CACxB,IAAIlF,EAAOkF,EAAM,IAAI,OAAO,CAAC,EACzBC,EAEAnF,KAAQkF,EAAM,KAAK,MACrBC,EAAaD,EAAM,KAAK,MAAMlF,CAAI,GAElCmF,EAAa,IAAIvH,EAAK,SACtBsH,EAAM,KAAK,MAAMlF,CAAI,EAAImF,GAGvBD,EAAM,IAAI,QAAU,IACtBC,EAAW,MAAQ,IAGrBF,EAAM,KAAK,CACT,KAAME,EACN,eAAgBD,EAAM,eACtB,IAAKA,EAAM,IAAI,MAAM,CAAC,CACxB,CAAC,CACH,CAEA,GAAIA,EAAM,gBAAkB,EAK5B,IAAI,MAAOA,EAAM,KAAK,MACpB,IAAIE,EAAgBF,EAAM,KAAK,MAAM,GAAG,MACnC,CACL,IAAIE,EAAgB,IAAIxH,EAAK,SAC7BsH,EAAM,KAAK,MAAM,GAAG,EAAIE,CAC1B,CAgCA,GA9BIF,EAAM,IAAI,QAAU,IACtBE,EAAc,MAAQ,IAGxBH,EAAM,KAAK,CACT,KAAMG,EACN,eAAgBF,EAAM,eAAiB,EACvC,IAAKA,EAAM,GACb,CAAC,EAKGA,EAAM,IAAI,OAAS,GACrBD,EAAM,KAAK,CACT,KAAMC,EAAM,KACZ,eAAgBA,EAAM,eAAiB,EACvC,IAAKA,EAAM,IAAI,MAAM,CAAC,CACxB,CAAC,EAKCA,EAAM,IAAI,QAAU,IACtBA,EAAM,KAAK,MAAQ,IAMjBA,EAAM,IAAI,QAAU,EAAG,CACzB,GAAI,MAAOA,EAAM,KAAK,MACpB,IAAIG,EAAmBH,EAAM,KAAK,MAAM,GAAG,MACtC,CACL,IAAIG,EAAmB,IAAIzH,EAAK,SAChCsH,EAAM,KAAK,MAAM,GAAG,EAAIG,CAC1B,CAEIH,EAAM,IAAI,QAAU,IACtBG,EAAiB,MAAQ,IAG3BJ,EAAM,KAAK,CACT,KAAMI,EACN,eAAgBH,EAAM,eAAiB,EACvC,IAAKA,EAAM,IAAI,MAAM,CAAC,CACxB,CAAC,CACH,CAKA,GAAIA,EAAM,IAAI,OAAS,EAAG,CACxB,IAAII,EAAQJ,EAAM,IAAI,OAAO,CAAC,EAC1BK,EAAQL,EAAM,IAAI,OAAO,CAAC,EAC1BM,EAEAD,KAASL,EAAM,KAAK,MACtBM,EAAgBN,EAAM,KAAK,MAAMK,CAAK,GAEtCC,EAAgB,IAAI5H,EAAK,SACzBsH,EAAM,KAAK,MAAMK,CAAK,EAAIC,GAGxBN,EAAM,IAAI,QAAU,IACtBM,EAAc,MAAQ,IAGxBP,EAAM,KAAK,CACT,KAAMO,EACN,eAAgBN,EAAM,eAAiB,EACvC,IAAKI,EAAQJ,EAAM,IAAI,MAAM,CAAC,CAChC,CAAC,CACH,EACF,CAEA,OAAOF,CACT,EAYApH,EAAK,SAAS,WAAa,SAAU4B,EAAK,CAYxC,QAXIiG,EAAO,IAAI7H,EAAK,SAChBoH,EAAOS,EAUF,EAAI,EAAG7F,EAAMJ,EAAI,OAAQ,EAAII,EAAK,IAAK,CAC9C,IAAII,EAAOR,EAAI,CAAC,EACZkG,EAAS,GAAK9F,EAAM,EAExB,GAAII,GAAQ,IACVyF,EAAK,MAAMzF,CAAI,EAAIyF,EACnBA,EAAK,MAAQC,MAER,CACL,IAAIC,EAAO,IAAI/H,EAAK,SACpB+H,EAAK,MAAQD,EAEbD,EAAK,MAAMzF,CAAI,EAAI2F,EACnBF,EAAOE,CACT,CACF,CAEA,OAAOX,CACT,EAYApH,EAAK,SAAS,UAAU,QAAU,UAAY,CAQ5C,QAPI+G,EAAQ,CAAC,EAETM,EAAQ,CAAC,CACX,OAAQ,GACR,KAAM,IACR,CAAC,EAEMA,EAAM,QAAQ,CACnB,IAAIC,EAAQD,EAAM,IAAI,EAClBW,EAAQ,OAAO,KAAKV,EAAM,KAAK,KAAK,EACpCtF,EAAMgG,EAAM,OAEZV,EAAM,KAAK,QAKbA,EAAM,OAAO,OAAO,CAAC,EACrBP,EAAM,KAAKO,EAAM,MAAM,GAGzB,QAASrG,EAAI,EAAGA,EAAIe,EAAKf,IAAK,CAC5B,IAAIgH,EAAOD,EAAM/G,CAAC,EAElBoG,EAAM,KAAK,CACT,OAAQC,EAAM,OAAO,OAAOW,CAAI,EAChC,KAAMX,EAAM,KAAK,MAAMW,CAAI,CAC7B,CAAC,CACH,CACF,CAEA,OAAOlB,CACT,EAYA/G,EAAK,SAAS,UAAU,SAAW,UAAY,CAS7C,GAAI,KAAK,KACP,OAAO,KAAK,KAOd,QAJI4B,EAAM,KAAK,MAAQ,IAAM,IACzBsG,EAAS,OAAO,KAAK,KAAK,KAAK,EAAE,KAAK,EACtClG,EAAMkG,EAAO,OAER,EAAI,EAAG,EAAIlG,EAAK,IAAK,CAC5B,IAAIO,EAAQ2F,EAAO,CAAC,EAChBL,EAAO,KAAK,MAAMtF,CAAK,EAE3BX,EAAMA,EAAMW,EAAQsF,EAAK,EAC3B,CAEA,OAAOjG,CACT,EAYA5B,EAAK,SAAS,UAAU,UAAY,SAAUqB,EAAG,CAU/C,QATIgD,EAAS,IAAIrE,EAAK,SAClBsH,EAAQ,OAERD,EAAQ,CAAC,CACX,MAAOhG,EACP,OAAQgD,EACR,KAAM,IACR,CAAC,EAEMgD,EAAM,QAAQ,CACnBC,EAAQD,EAAM,IAAI,EAWlB,QALIc,EAAS,OAAO,KAAKb,EAAM,MAAM,KAAK,EACtCc,EAAOD,EAAO,OACdE,EAAS,OAAO,KAAKf,EAAM,KAAK,KAAK,EACrCgB,EAAOD,EAAO,OAETE,EAAI,EAAGA,EAAIH,EAAMG,IAGxB,QAFIC,EAAQL,EAAOI,CAAC,EAEXzH,EAAI,EAAGA,EAAIwH,EAAMxH,IAAK,CAC7B,IAAI2H,EAAQJ,EAAOvH,CAAC,EAEpB,GAAI2H,GAASD,GAASA,GAAS,IAAK,CAClC,IAAIX,EAAOP,EAAM,KAAK,MAAMmB,CAAK,EAC7BC,EAAQpB,EAAM,MAAM,MAAMkB,CAAK,EAC/BV,EAAQD,EAAK,OAASa,EAAM,MAC5BX,EAAO,OAEPU,KAASnB,EAAM,OAAO,OAIxBS,EAAOT,EAAM,OAAO,MAAMmB,CAAK,EAC/BV,EAAK,MAAQA,EAAK,OAASD,IAM3BC,EAAO,IAAI/H,EAAK,SAChB+H,EAAK,MAAQD,EACbR,EAAM,OAAO,MAAMmB,CAAK,EAAIV,GAG9BV,EAAM,KAAK,CACT,MAAOqB,EACP,OAAQX,EACR,KAAMF,CACR,CAAC,CACH,CACF,CAEJ,CAEA,OAAOxD,CACT,EACArE,EAAK,SAAS,QAAU,UAAY,CAClC,KAAK,aAAe,GACpB,KAAK,KAAO,IAAIA,EAAK,SACrB,KAAK,eAAiB,CAAC,EACvB,KAAK,eAAiB,CAAC,CACzB,EAEAA,EAAK,SAAS,QAAQ,UAAU,OAAS,SAAU2I,EAAM,CACvD,IAAId,EACAe,EAAe,EAEnB,GAAID,EAAO,KAAK,aACd,MAAM,IAAI,MAAO,6BAA6B,EAGhD,QAAS,EAAI,EAAG,EAAIA,EAAK,QAAU,EAAI,KAAK,aAAa,QACnDA,EAAK,CAAC,GAAK,KAAK,aAAa,CAAC,EAD6B,IAE/DC,IAGF,KAAK,SAASA,CAAY,EAEtB,KAAK,eAAe,QAAU,EAChCf,EAAO,KAAK,KAEZA,EAAO,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAAE,MAG7D,QAAS,EAAIe,EAAc,EAAID,EAAK,OAAQ,IAAK,CAC/C,IAAIE,EAAW,IAAI7I,EAAK,SACpBoC,EAAOuG,EAAK,CAAC,EAEjBd,EAAK,MAAMzF,CAAI,EAAIyG,EAEnB,KAAK,eAAe,KAAK,CACvB,OAAQhB,EACR,KAAMzF,EACN,MAAOyG,CACT,CAAC,EAEDhB,EAAOgB,CACT,CAEAhB,EAAK,MAAQ,GACb,KAAK,aAAec,CACtB,EAEA3I,EAAK,SAAS,QAAQ,UAAU,OAAS,UAAY,CACnD,KAAK,SAAS,CAAC,CACjB,EAEAA,EAAK,SAAS,QAAQ,UAAU,SAAW,SAAU8I,EAAQ,CAC3D,QAAS7H,EAAI,KAAK,eAAe,OAAS,EAAGA,GAAK6H,EAAQ7H,IAAK,CAC7D,IAAI4G,EAAO,KAAK,eAAe5G,CAAC,EAC5B8H,EAAWlB,EAAK,MAAM,SAAS,EAE/BkB,KAAY,KAAK,eACnBlB,EAAK,OAAO,MAAMA,EAAK,IAAI,EAAI,KAAK,eAAekB,CAAQ,GAI3DlB,EAAK,MAAM,KAAOkB,EAElB,KAAK,eAAeA,CAAQ,EAAIlB,EAAK,OAGvC,KAAK,eAAe,IAAI,CAC1B,CACF,EAsBA7H,EAAK,MAAQ,SAAUgJ,EAAO,CAC5B,KAAK,cAAgBA,EAAM,cAC3B,KAAK,aAAeA,EAAM,aAC1B,KAAK,SAAWA,EAAM,SACtB,KAAK,OAASA,EAAM,OACpB,KAAK,SAAWA,EAAM,QACxB,EAyEAhJ,EAAK,MAAM,UAAU,OAAS,SAAUiJ,EAAa,CACnD,OAAO,KAAK,MAAM,SAAUC,EAAO,CACjC,IAAIC,EAAS,IAAInJ,EAAK,YAAYiJ,EAAaC,CAAK,EACpDC,EAAO,MAAM,CACf,CAAC,CACH,EA2BAnJ,EAAK,MAAM,UAAU,MAAQ,SAAU8B,EAAI,CAoBzC,QAZIoH,EAAQ,IAAIlJ,EAAK,MAAM,KAAK,MAAM,EAClCoJ,EAAiB,OAAO,OAAO,IAAI,EACnCC,EAAe,OAAO,OAAO,IAAI,EACjCC,EAAiB,OAAO,OAAO,IAAI,EACnCC,EAAkB,OAAO,OAAO,IAAI,EACpCC,EAAoB,OAAO,OAAO,IAAI,EAOjCvI,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IACtCoI,EAAa,KAAK,OAAOpI,CAAC,CAAC,EAAI,IAAIjB,EAAK,OAG1C8B,EAAG,KAAKoH,EAAOA,CAAK,EAEpB,QAASjI,EAAI,EAAGA,EAAIiI,EAAM,QAAQ,OAAQjI,IAAK,CAS7C,IAAIiG,EAASgC,EAAM,QAAQjI,CAAC,EACxBwI,EAAQ,KACRC,EAAgB1J,EAAK,IAAI,MAEzBkH,EAAO,YACTuC,EAAQ,KAAK,SAAS,UAAUvC,EAAO,KAAM,CAC3C,OAAQA,EAAO,MACjB,CAAC,EAEDuC,EAAQ,CAACvC,EAAO,IAAI,EAGtB,QAASyC,EAAI,EAAGA,EAAIF,EAAM,OAAQE,IAAK,CACrC,IAAIC,EAAOH,EAAME,CAAC,EAQlBzC,EAAO,KAAO0C,EAOd,IAAIC,EAAe7J,EAAK,SAAS,WAAWkH,CAAM,EAC9C4C,EAAgB,KAAK,SAAS,UAAUD,CAAY,EAAE,QAAQ,EAQlE,GAAIC,EAAc,SAAW,GAAK5C,EAAO,WAAalH,EAAK,MAAM,SAAS,SAAU,CAClF,QAASoD,EAAI,EAAGA,EAAI8D,EAAO,OAAO,OAAQ9D,IAAK,CAC7C,IAAI2G,EAAQ7C,EAAO,OAAO9D,CAAC,EAC3BmG,EAAgBQ,CAAK,EAAI/J,EAAK,IAAI,KACpC,CAEA,KACF,CAEA,QAASkD,EAAI,EAAGA,EAAI4G,EAAc,OAAQ5G,IASxC,QAJI8G,EAAeF,EAAc5G,CAAC,EAC9B1B,EAAU,KAAK,cAAcwI,CAAY,EACzCC,EAAYzI,EAAQ,OAEf4B,EAAI,EAAGA,EAAI8D,EAAO,OAAO,OAAQ9D,IAAK,CAS7C,IAAI2G,EAAQ7C,EAAO,OAAO9D,CAAC,EACvB8G,EAAe1I,EAAQuI,CAAK,EAC5BI,EAAuB,OAAO,KAAKD,CAAY,EAC/CE,EAAYJ,EAAe,IAAMD,EACjCM,EAAuB,IAAIrK,EAAK,IAAImK,CAAoB,EAoB5D,GAbIjD,EAAO,UAAYlH,EAAK,MAAM,SAAS,WACzC0J,EAAgBA,EAAc,MAAMW,CAAoB,EAEpDd,EAAgBQ,CAAK,IAAM,SAC7BR,EAAgBQ,CAAK,EAAI/J,EAAK,IAAI,WASlCkH,EAAO,UAAYlH,EAAK,MAAM,SAAS,WAAY,CACjDwJ,EAAkBO,CAAK,IAAM,SAC/BP,EAAkBO,CAAK,EAAI/J,EAAK,IAAI,OAGtCwJ,EAAkBO,CAAK,EAAIP,EAAkBO,CAAK,EAAE,MAAMM,CAAoB,EAO9E,QACF,CAeA,GANAhB,EAAaU,CAAK,EAAE,OAAOE,EAAW/C,EAAO,MAAO,SAAU9F,GAAGC,GAAG,CAAE,OAAOD,GAAIC,EAAE,CAAC,EAMhF,CAAAiI,EAAec,CAAS,EAI5B,SAASE,EAAI,EAAGA,EAAIH,EAAqB,OAAQG,IAAK,CAOpD,IAAIC,EAAsBJ,EAAqBG,CAAC,EAC5CE,EAAmB,IAAIxK,EAAK,SAAUuK,EAAqBR,CAAK,EAChElI,EAAWqI,EAAaK,CAAmB,EAC3CE,GAECA,EAAarB,EAAeoB,CAAgB,KAAO,OACtDpB,EAAeoB,CAAgB,EAAI,IAAIxK,EAAK,UAAWgK,EAAcD,EAAOlI,CAAQ,EAEpF4I,EAAW,IAAIT,EAAcD,EAAOlI,CAAQ,CAGhD,CAEAyH,EAAec,CAAS,EAAI,GAC9B,CAEJ,CAQA,GAAIlD,EAAO,WAAalH,EAAK,MAAM,SAAS,SAC1C,QAASoD,EAAI,EAAGA,EAAI8D,EAAO,OAAO,OAAQ9D,IAAK,CAC7C,IAAI2G,EAAQ7C,EAAO,OAAO9D,CAAC,EAC3BmG,EAAgBQ,CAAK,EAAIR,EAAgBQ,CAAK,EAAE,UAAUL,CAAa,CACzE,CAEJ,CAUA,QAHIgB,EAAqB1K,EAAK,IAAI,SAC9B2K,EAAuB3K,EAAK,IAAI,MAE3BiB,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IAAK,CAC3C,IAAI8I,EAAQ,KAAK,OAAO9I,CAAC,EAErBsI,EAAgBQ,CAAK,IACvBW,EAAqBA,EAAmB,UAAUnB,EAAgBQ,CAAK,CAAC,GAGtEP,EAAkBO,CAAK,IACzBY,EAAuBA,EAAqB,MAAMnB,EAAkBO,CAAK,CAAC,EAE9E,CAEA,IAAIa,EAAoB,OAAO,KAAKxB,CAAc,EAC9CyB,EAAU,CAAC,EACXC,EAAU,OAAO,OAAO,IAAI,EAYhC,GAAI5B,EAAM,UAAU,EAAG,CACrB0B,EAAoB,OAAO,KAAK,KAAK,YAAY,EAEjD,QAAS3J,EAAI,EAAGA,EAAI2J,EAAkB,OAAQ3J,IAAK,CACjD,IAAIuJ,EAAmBI,EAAkB3J,CAAC,EACtCF,EAAWf,EAAK,SAAS,WAAWwK,CAAgB,EACxDpB,EAAeoB,CAAgB,EAAI,IAAIxK,EAAK,SAC9C,CACF,CAEA,QAASiB,EAAI,EAAGA,EAAI2J,EAAkB,OAAQ3J,IAAK,CASjD,IAAIF,EAAWf,EAAK,SAAS,WAAW4K,EAAkB3J,CAAC,CAAC,EACxDP,EAASK,EAAS,OAEtB,GAAK2J,EAAmB,SAAShK,CAAM,GAInC,CAAAiK,EAAqB,SAASjK,CAAM,EAIxC,KAAIqK,EAAc,KAAK,aAAahK,CAAQ,EACxCiK,EAAQ3B,EAAatI,EAAS,SAAS,EAAE,WAAWgK,CAAW,EAC/DE,EAEJ,IAAKA,EAAWH,EAAQpK,CAAM,KAAO,OACnCuK,EAAS,OAASD,EAClBC,EAAS,UAAU,QAAQ7B,EAAerI,CAAQ,CAAC,MAC9C,CACL,IAAImK,EAAQ,CACV,IAAKxK,EACL,MAAOsK,EACP,UAAW5B,EAAerI,CAAQ,CACpC,EACA+J,EAAQpK,CAAM,EAAIwK,EAClBL,EAAQ,KAAKK,CAAK,CACpB,EACF,CAKA,OAAOL,EAAQ,KAAK,SAAUzJ,GAAGC,GAAG,CAClC,OAAOA,GAAE,MAAQD,GAAE,KACrB,CAAC,CACH,EAUApB,EAAK,MAAM,UAAU,OAAS,UAAY,CACxC,IAAImL,EAAgB,OAAO,KAAK,KAAK,aAAa,EAC/C,KAAK,EACL,IAAI,SAAUvB,EAAM,CACnB,MAAO,CAACA,EAAM,KAAK,cAAcA,CAAI,CAAC,CACxC,EAAG,IAAI,EAELwB,EAAe,OAAO,KAAK,KAAK,YAAY,EAC7C,IAAI,SAAUC,EAAK,CAClB,MAAO,CAACA,EAAK,KAAK,aAAaA,CAAG,EAAE,OAAO,CAAC,CAC9C,EAAG,IAAI,EAET,MAAO,CACL,QAASrL,EAAK,QACd,OAAQ,KAAK,OACb,aAAcoL,EACd,cAAeD,EACf,SAAU,KAAK,SAAS,OAAO,CACjC,CACF,EAQAnL,EAAK,MAAM,KAAO,SAAUsL,EAAiB,CAC3C,IAAItC,EAAQ,CAAC,EACToC,EAAe,CAAC,EAChBG,EAAoBD,EAAgB,aACpCH,EAAgB,OAAO,OAAO,IAAI,EAClCK,EAA0BF,EAAgB,cAC1CG,EAAkB,IAAIzL,EAAK,SAAS,QACpC0C,EAAW1C,EAAK,SAAS,KAAKsL,EAAgB,QAAQ,EAEtDA,EAAgB,SAAWtL,EAAK,SAClCA,EAAK,MAAM,KAAK,4EAA8EA,EAAK,QAAU,sCAAwCsL,EAAgB,QAAU,GAAG,EAGpL,QAASrK,EAAI,EAAGA,EAAIsK,EAAkB,OAAQtK,IAAK,CACjD,IAAIyK,EAAQH,EAAkBtK,CAAC,EAC3BoK,EAAMK,EAAM,CAAC,EACb1K,EAAW0K,EAAM,CAAC,EAEtBN,EAAaC,CAAG,EAAI,IAAIrL,EAAK,OAAOgB,CAAQ,CAC9C,CAEA,QAASC,EAAI,EAAGA,EAAIuK,EAAwB,OAAQvK,IAAK,CACvD,IAAIyK,EAAQF,EAAwBvK,CAAC,EACjC2I,EAAO8B,EAAM,CAAC,EACdlK,EAAUkK,EAAM,CAAC,EAErBD,EAAgB,OAAO7B,CAAI,EAC3BuB,EAAcvB,CAAI,EAAIpI,CACxB,CAEA,OAAAiK,EAAgB,OAAO,EAEvBzC,EAAM,OAASsC,EAAgB,OAE/BtC,EAAM,aAAeoC,EACrBpC,EAAM,cAAgBmC,EACtBnC,EAAM,SAAWyC,EAAgB,KACjCzC,EAAM,SAAWtG,EAEV,IAAI1C,EAAK,MAAMgJ,CAAK,CAC7B,EA8BAhJ,EAAK,QAAU,UAAY,CACzB,KAAK,KAAO,KACZ,KAAK,QAAU,OAAO,OAAO,IAAI,EACjC,KAAK,WAAa,OAAO,OAAO,IAAI,EACpC,KAAK,cAAgB,OAAO,OAAO,IAAI,EACvC,KAAK,qBAAuB,CAAC,EAC7B,KAAK,aAAe,CAAC,EACrB,KAAK,UAAYA,EAAK,UACtB,KAAK,SAAW,IAAIA,EAAK,SACzB,KAAK,eAAiB,IAAIA,EAAK,SAC/B,KAAK,cAAgB,EACrB,KAAK,GAAK,IACV,KAAK,IAAM,IACX,KAAK,UAAY,EACjB,KAAK,kBAAoB,CAAC,CAC5B,EAcAA,EAAK,QAAQ,UAAU,IAAM,SAAUqL,EAAK,CAC1C,KAAK,KAAOA,CACd,EAkCArL,EAAK,QAAQ,UAAU,MAAQ,SAAUW,EAAWgL,EAAY,CAC9D,GAAI,KAAK,KAAKhL,CAAS,EACrB,MAAM,IAAI,WAAY,UAAYA,EAAY,kCAAkC,EAGlF,KAAK,QAAQA,CAAS,EAAIgL,GAAc,CAAC,CAC3C,EAUA3L,EAAK,QAAQ,UAAU,EAAI,SAAU4L,EAAQ,CACvCA,EAAS,EACX,KAAK,GAAK,EACDA,EAAS,EAClB,KAAK,GAAK,EAEV,KAAK,GAAKA,CAEd,EASA5L,EAAK,QAAQ,UAAU,GAAK,SAAU4L,EAAQ,CAC5C,KAAK,IAAMA,CACb,EAmBA5L,EAAK,QAAQ,UAAU,IAAM,SAAU6L,EAAKF,EAAY,CACtD,IAAIjL,EAASmL,EAAI,KAAK,IAAI,EACtBC,EAAS,OAAO,KAAK,KAAK,OAAO,EAErC,KAAK,WAAWpL,CAAM,EAAIiL,GAAc,CAAC,EACzC,KAAK,eAAiB,EAEtB,QAAS1K,EAAI,EAAGA,EAAI6K,EAAO,OAAQ7K,IAAK,CACtC,IAAIN,EAAYmL,EAAO7K,CAAC,EACpB8K,EAAY,KAAK,QAAQpL,CAAS,EAAE,UACpCoJ,EAAQgC,EAAYA,EAAUF,CAAG,EAAIA,EAAIlL,CAAS,EAClDsB,EAAS,KAAK,UAAU8H,EAAO,CAC7B,OAAQ,CAACpJ,CAAS,CACpB,CAAC,EACD8I,EAAQ,KAAK,SAAS,IAAIxH,CAAM,EAChClB,EAAW,IAAIf,EAAK,SAAUU,EAAQC,CAAS,EAC/CqL,EAAa,OAAO,OAAO,IAAI,EAEnC,KAAK,qBAAqBjL,CAAQ,EAAIiL,EACtC,KAAK,aAAajL,CAAQ,EAAI,EAG9B,KAAK,aAAaA,CAAQ,GAAK0I,EAAM,OAGrC,QAASvG,EAAI,EAAGA,EAAIuG,EAAM,OAAQvG,IAAK,CACrC,IAAI0G,EAAOH,EAAMvG,CAAC,EAUlB,GARI8I,EAAWpC,CAAI,GAAK,OACtBoC,EAAWpC,CAAI,EAAI,GAGrBoC,EAAWpC,CAAI,GAAK,EAIhB,KAAK,cAAcA,CAAI,GAAK,KAAW,CACzC,IAAIpI,EAAU,OAAO,OAAO,IAAI,EAChCA,EAAQ,OAAY,KAAK,UACzB,KAAK,WAAa,EAElB,QAAS4B,EAAI,EAAGA,EAAI0I,EAAO,OAAQ1I,IACjC5B,EAAQsK,EAAO1I,CAAC,CAAC,EAAI,OAAO,OAAO,IAAI,EAGzC,KAAK,cAAcwG,CAAI,EAAIpI,CAC7B,CAGI,KAAK,cAAcoI,CAAI,EAAEjJ,CAAS,EAAED,CAAM,GAAK,OACjD,KAAK,cAAckJ,CAAI,EAAEjJ,CAAS,EAAED,CAAM,EAAI,OAAO,OAAO,IAAI,GAKlE,QAAS4J,EAAI,EAAGA,EAAI,KAAK,kBAAkB,OAAQA,IAAK,CACtD,IAAI2B,EAAc,KAAK,kBAAkB3B,CAAC,EACtCzI,EAAW+H,EAAK,SAASqC,CAAW,EAEpC,KAAK,cAAcrC,CAAI,EAAEjJ,CAAS,EAAED,CAAM,EAAEuL,CAAW,GAAK,OAC9D,KAAK,cAAcrC,CAAI,EAAEjJ,CAAS,EAAED,CAAM,EAAEuL,CAAW,EAAI,CAAC,GAG9D,KAAK,cAAcrC,CAAI,EAAEjJ,CAAS,EAAED,CAAM,EAAEuL,CAAW,EAAE,KAAKpK,CAAQ,CACxE,CACF,CAEF,CACF,EAOA7B,EAAK,QAAQ,UAAU,6BAA+B,UAAY,CAOhE,QALIkM,EAAY,OAAO,KAAK,KAAK,YAAY,EACzCC,EAAiBD,EAAU,OAC3BE,EAAc,CAAC,EACfC,EAAqB,CAAC,EAEjBpL,EAAI,EAAGA,EAAIkL,EAAgBlL,IAAK,CACvC,IAAIF,EAAWf,EAAK,SAAS,WAAWkM,EAAUjL,CAAC,CAAC,EAChD8I,EAAQhJ,EAAS,UAErBsL,EAAmBtC,CAAK,IAAMsC,EAAmBtC,CAAK,EAAI,GAC1DsC,EAAmBtC,CAAK,GAAK,EAE7BqC,EAAYrC,CAAK,IAAMqC,EAAYrC,CAAK,EAAI,GAC5CqC,EAAYrC,CAAK,GAAK,KAAK,aAAahJ,CAAQ,CAClD,CAIA,QAFI+K,EAAS,OAAO,KAAK,KAAK,OAAO,EAE5B7K,EAAI,EAAGA,EAAI6K,EAAO,OAAQ7K,IAAK,CACtC,IAAIN,EAAYmL,EAAO7K,CAAC,EACxBmL,EAAYzL,CAAS,EAAIyL,EAAYzL,CAAS,EAAI0L,EAAmB1L,CAAS,CAChF,CAEA,KAAK,mBAAqByL,CAC5B,EAOApM,EAAK,QAAQ,UAAU,mBAAqB,UAAY,CAMtD,QALIoL,EAAe,CAAC,EAChBc,EAAY,OAAO,KAAK,KAAK,oBAAoB,EACjDI,EAAkBJ,EAAU,OAC5BK,EAAe,OAAO,OAAO,IAAI,EAE5BtL,EAAI,EAAGA,EAAIqL,EAAiBrL,IAAK,CAaxC,QAZIF,EAAWf,EAAK,SAAS,WAAWkM,EAAUjL,CAAC,CAAC,EAChDN,EAAYI,EAAS,UACrByL,EAAc,KAAK,aAAazL,CAAQ,EACxCgK,EAAc,IAAI/K,EAAK,OACvByM,EAAkB,KAAK,qBAAqB1L,CAAQ,EACpD0I,EAAQ,OAAO,KAAKgD,CAAe,EACnCC,EAAcjD,EAAM,OAGpBkD,EAAa,KAAK,QAAQhM,CAAS,EAAE,OAAS,EAC9CiM,EAAW,KAAK,WAAW7L,EAAS,MAAM,EAAE,OAAS,EAEhDmC,EAAI,EAAGA,EAAIwJ,EAAaxJ,IAAK,CACpC,IAAI0G,EAAOH,EAAMvG,CAAC,EACd2J,EAAKJ,EAAgB7C,CAAI,EACzBK,EAAY,KAAK,cAAcL,CAAI,EAAE,OACrCkD,EAAK9B,EAAO+B,EAEZR,EAAa3C,CAAI,IAAM,QACzBkD,EAAM9M,EAAK,IAAI,KAAK,cAAc4J,CAAI,EAAG,KAAK,aAAa,EAC3D2C,EAAa3C,CAAI,EAAIkD,GAErBA,EAAMP,EAAa3C,CAAI,EAGzBoB,EAAQ8B,IAAQ,KAAK,IAAM,GAAKD,IAAO,KAAK,KAAO,EAAI,KAAK,GAAK,KAAK,IAAML,EAAc,KAAK,mBAAmB7L,CAAS,IAAMkM,GACjI7B,GAAS2B,EACT3B,GAAS4B,EACTG,EAAqB,KAAK,MAAM/B,EAAQ,GAAI,EAAI,IAQhDD,EAAY,OAAOd,EAAW8C,CAAkB,CAClD,CAEA3B,EAAarK,CAAQ,EAAIgK,CAC3B,CAEA,KAAK,aAAeK,CACtB,EAOApL,EAAK,QAAQ,UAAU,eAAiB,UAAY,CAClD,KAAK,SAAWA,EAAK,SAAS,UAC5B,OAAO,KAAK,KAAK,aAAa,EAAE,KAAK,CACvC,CACF,EAUAA,EAAK,QAAQ,UAAU,MAAQ,UAAY,CACzC,YAAK,6BAA6B,EAClC,KAAK,mBAAmB,EACxB,KAAK,eAAe,EAEb,IAAIA,EAAK,MAAM,CACpB,cAAe,KAAK,cACpB,aAAc,KAAK,aACnB,SAAU,KAAK,SACf,OAAQ,OAAO,KAAK,KAAK,OAAO,EAChC,SAAU,KAAK,cACjB,CAAC,CACH,EAgBAA,EAAK,QAAQ,UAAU,IAAM,SAAU8B,EAAI,CACzC,IAAIkL,EAAO,MAAM,UAAU,MAAM,KAAK,UAAW,CAAC,EAClDA,EAAK,QAAQ,IAAI,EACjBlL,EAAG,MAAM,KAAMkL,CAAI,CACrB,EAaAhN,EAAK,UAAY,SAAU4J,EAAMG,EAAOlI,EAAU,CAShD,QARIoL,EAAiB,OAAO,OAAO,IAAI,EACnCC,EAAe,OAAO,KAAKrL,GAAY,CAAC,CAAC,EAOpCZ,EAAI,EAAGA,EAAIiM,EAAa,OAAQjM,IAAK,CAC5C,IAAIT,EAAM0M,EAAajM,CAAC,EACxBgM,EAAezM,CAAG,EAAIqB,EAASrB,CAAG,EAAE,MAAM,CAC5C,CAEA,KAAK,SAAW,OAAO,OAAO,IAAI,EAE9BoJ,IAAS,SACX,KAAK,SAASA,CAAI,EAAI,OAAO,OAAO,IAAI,EACxC,KAAK,SAASA,CAAI,EAAEG,CAAK,EAAIkD,EAEjC,EAWAjN,EAAK,UAAU,UAAU,QAAU,SAAUmN,EAAgB,CAG3D,QAFI1D,EAAQ,OAAO,KAAK0D,EAAe,QAAQ,EAEtClM,EAAI,EAAGA,EAAIwI,EAAM,OAAQxI,IAAK,CACrC,IAAI2I,EAAOH,EAAMxI,CAAC,EACd6K,EAAS,OAAO,KAAKqB,EAAe,SAASvD,CAAI,CAAC,EAElD,KAAK,SAASA,CAAI,GAAK,OACzB,KAAK,SAASA,CAAI,EAAI,OAAO,OAAO,IAAI,GAG1C,QAAS1G,EAAI,EAAGA,EAAI4I,EAAO,OAAQ5I,IAAK,CACtC,IAAI6G,EAAQ+B,EAAO5I,CAAC,EAChB3C,EAAO,OAAO,KAAK4M,EAAe,SAASvD,CAAI,EAAEG,CAAK,CAAC,EAEvD,KAAK,SAASH,CAAI,EAAEG,CAAK,GAAK,OAChC,KAAK,SAASH,CAAI,EAAEG,CAAK,EAAI,OAAO,OAAO,IAAI,GAGjD,QAAS3G,EAAI,EAAGA,EAAI7C,EAAK,OAAQ6C,IAAK,CACpC,IAAI5C,EAAMD,EAAK6C,CAAC,EAEZ,KAAK,SAASwG,CAAI,EAAEG,CAAK,EAAEvJ,CAAG,GAAK,KACrC,KAAK,SAASoJ,CAAI,EAAEG,CAAK,EAAEvJ,CAAG,EAAI2M,EAAe,SAASvD,CAAI,EAAEG,CAAK,EAAEvJ,CAAG,EAE1E,KAAK,SAASoJ,CAAI,EAAEG,CAAK,EAAEvJ,CAAG,EAAI,KAAK,SAASoJ,CAAI,EAAEG,CAAK,EAAEvJ,CAAG,EAAE,OAAO2M,EAAe,SAASvD,CAAI,EAAEG,CAAK,EAAEvJ,CAAG,CAAC,CAGtH,CACF,CACF,CACF,EASAR,EAAK,UAAU,UAAU,IAAM,SAAU4J,EAAMG,EAAOlI,EAAU,CAC9D,GAAI,EAAE+H,KAAQ,KAAK,UAAW,CAC5B,KAAK,SAASA,CAAI,EAAI,OAAO,OAAO,IAAI,EACxC,KAAK,SAASA,CAAI,EAAEG,CAAK,EAAIlI,EAC7B,MACF,CAEA,GAAI,EAAEkI,KAAS,KAAK,SAASH,CAAI,GAAI,CACnC,KAAK,SAASA,CAAI,EAAEG,CAAK,EAAIlI,EAC7B,MACF,CAIA,QAFIqL,EAAe,OAAO,KAAKrL,CAAQ,EAE9BZ,EAAI,EAAGA,EAAIiM,EAAa,OAAQjM,IAAK,CAC5C,IAAIT,EAAM0M,EAAajM,CAAC,EAEpBT,KAAO,KAAK,SAASoJ,CAAI,EAAEG,CAAK,EAClC,KAAK,SAASH,CAAI,EAAEG,CAAK,EAAEvJ,CAAG,EAAI,KAAK,SAASoJ,CAAI,EAAEG,CAAK,EAAEvJ,CAAG,EAAE,OAAOqB,EAASrB,CAAG,CAAC,EAEtF,KAAK,SAASoJ,CAAI,EAAEG,CAAK,EAAEvJ,CAAG,EAAIqB,EAASrB,CAAG,CAElD,CACF,EAYAR,EAAK,MAAQ,SAAUoN,EAAW,CAChC,KAAK,QAAU,CAAC,EAChB,KAAK,UAAYA,CACnB,EA0BApN,EAAK,MAAM,SAAW,IAAI,OAAQ,GAAG,EACrCA,EAAK,MAAM,SAAS,KAAO,EAC3BA,EAAK,MAAM,SAAS,QAAU,EAC9BA,EAAK,MAAM,SAAS,SAAW,EAa/BA,EAAK,MAAM,SAAW,CAIpB,SAAU,EAMV,SAAU,EAMV,WAAY,CACd,EAyBAA,EAAK,MAAM,UAAU,OAAS,SAAUkH,EAAQ,CAC9C,MAAM,WAAYA,IAChBA,EAAO,OAAS,KAAK,WAGjB,UAAWA,IACfA,EAAO,MAAQ,GAGX,gBAAiBA,IACrBA,EAAO,YAAc,IAGjB,aAAcA,IAClBA,EAAO,SAAWlH,EAAK,MAAM,SAAS,MAGnCkH,EAAO,SAAWlH,EAAK,MAAM,SAAS,SAAakH,EAAO,KAAK,OAAO,CAAC,GAAKlH,EAAK,MAAM,WAC1FkH,EAAO,KAAO,IAAMA,EAAO,MAGxBA,EAAO,SAAWlH,EAAK,MAAM,SAAS,UAAckH,EAAO,KAAK,MAAM,EAAE,GAAKlH,EAAK,MAAM,WAC3FkH,EAAO,KAAO,GAAKA,EAAO,KAAO,KAG7B,aAAcA,IAClBA,EAAO,SAAWlH,EAAK,MAAM,SAAS,UAGxC,KAAK,QAAQ,KAAKkH,CAAM,EAEjB,IACT,EASAlH,EAAK,MAAM,UAAU,UAAY,UAAY,CAC3C,QAASiB,EAAI,EAAGA,EAAI,KAAK,QAAQ,OAAQA,IACvC,GAAI,KAAK,QAAQA,CAAC,EAAE,UAAYjB,EAAK,MAAM,SAAS,WAClD,MAAO,GAIX,MAAO,EACT,EA4BAA,EAAK,MAAM,UAAU,KAAO,SAAU4J,EAAMyD,EAAS,CACnD,GAAI,MAAM,QAAQzD,CAAI,EACpB,OAAAA,EAAK,QAAQ,SAAU7H,EAAG,CAAE,KAAK,KAAKA,EAAG/B,EAAK,MAAM,MAAMqN,CAAO,CAAC,CAAE,EAAG,IAAI,EACpE,KAGT,IAAInG,EAASmG,GAAW,CAAC,EACzB,OAAAnG,EAAO,KAAO0C,EAAK,SAAS,EAE5B,KAAK,OAAO1C,CAAM,EAEX,IACT,EACAlH,EAAK,gBAAkB,SAAUI,EAASmD,EAAOC,EAAK,CACpD,KAAK,KAAO,kBACZ,KAAK,QAAUpD,EACf,KAAK,MAAQmD,EACb,KAAK,IAAMC,CACb,EAEAxD,EAAK,gBAAgB,UAAY,IAAI,MACrCA,EAAK,WAAa,SAAU4B,EAAK,CAC/B,KAAK,QAAU,CAAC,EAChB,KAAK,IAAMA,EACX,KAAK,OAASA,EAAI,OAClB,KAAK,IAAM,EACX,KAAK,MAAQ,EACb,KAAK,oBAAsB,CAAC,CAC9B,EAEA5B,EAAK,WAAW,UAAU,IAAM,UAAY,CAG1C,QAFIsN,EAAQtN,EAAK,WAAW,QAErBsN,GACLA,EAAQA,EAAM,IAAI,CAEtB,EAEAtN,EAAK,WAAW,UAAU,YAAc,UAAY,CAKlD,QAJIuN,EAAY,CAAC,EACbpL,EAAa,KAAK,MAClBD,EAAW,KAAK,IAEX,EAAI,EAAG,EAAI,KAAK,oBAAoB,OAAQ,IACnDA,EAAW,KAAK,oBAAoB,CAAC,EACrCqL,EAAU,KAAK,KAAK,IAAI,MAAMpL,EAAYD,CAAQ,CAAC,EACnDC,EAAaD,EAAW,EAG1B,OAAAqL,EAAU,KAAK,KAAK,IAAI,MAAMpL,EAAY,KAAK,GAAG,CAAC,EACnD,KAAK,oBAAoB,OAAS,EAE3BoL,EAAU,KAAK,EAAE,CAC1B,EAEAvN,EAAK,WAAW,UAAU,KAAO,SAAUwN,EAAM,CAC/C,KAAK,QAAQ,KAAK,CAChB,KAAMA,EACN,IAAK,KAAK,YAAY,EACtB,MAAO,KAAK,MACZ,IAAK,KAAK,GACZ,CAAC,EAED,KAAK,MAAQ,KAAK,GACpB,EAEAxN,EAAK,WAAW,UAAU,gBAAkB,UAAY,CACtD,KAAK,oBAAoB,KAAK,KAAK,IAAM,CAAC,EAC1C,KAAK,KAAO,CACd,EAEAA,EAAK,WAAW,UAAU,KAAO,UAAY,CAC3C,GAAI,KAAK,KAAO,KAAK,OACnB,OAAOA,EAAK,WAAW,IAGzB,IAAIoC,EAAO,KAAK,IAAI,OAAO,KAAK,GAAG,EACnC,YAAK,KAAO,EACLA,CACT,EAEApC,EAAK,WAAW,UAAU,MAAQ,UAAY,CAC5C,OAAO,KAAK,IAAM,KAAK,KACzB,EAEAA,EAAK,WAAW,UAAU,OAAS,UAAY,CACzC,KAAK,OAAS,KAAK,MACrB,KAAK,KAAO,GAGd,KAAK,MAAQ,KAAK,GACpB,EAEAA,EAAK,WAAW,UAAU,OAAS,UAAY,CAC7C,KAAK,KAAO,CACd,EAEAA,EAAK,WAAW,UAAU,eAAiB,UAAY,CACrD,IAAIoC,EAAMqL,EAEV,GACErL,EAAO,KAAK,KAAK,EACjBqL,EAAWrL,EAAK,WAAW,CAAC,QACrBqL,EAAW,IAAMA,EAAW,IAEjCrL,GAAQpC,EAAK,WAAW,KAC1B,KAAK,OAAO,CAEhB,EAEAA,EAAK,WAAW,UAAU,KAAO,UAAY,CAC3C,OAAO,KAAK,IAAM,KAAK,MACzB,EAEAA,EAAK,WAAW,IAAM,MACtBA,EAAK,WAAW,MAAQ,QACxBA,EAAK,WAAW,KAAO,OACvBA,EAAK,WAAW,cAAgB,gBAChCA,EAAK,WAAW,MAAQ,QACxBA,EAAK,WAAW,SAAW,WAE3BA,EAAK,WAAW,SAAW,SAAU0N,EAAO,CAC1C,OAAAA,EAAM,OAAO,EACbA,EAAM,KAAK1N,EAAK,WAAW,KAAK,EAChC0N,EAAM,OAAO,EACN1N,EAAK,WAAW,OACzB,EAEAA,EAAK,WAAW,QAAU,SAAU0N,EAAO,CAQzC,GAPIA,EAAM,MAAM,EAAI,IAClBA,EAAM,OAAO,EACbA,EAAM,KAAK1N,EAAK,WAAW,IAAI,GAGjC0N,EAAM,OAAO,EAETA,EAAM,KAAK,EACb,OAAO1N,EAAK,WAAW,OAE3B,EAEAA,EAAK,WAAW,gBAAkB,SAAU0N,EAAO,CACjD,OAAAA,EAAM,OAAO,EACbA,EAAM,eAAe,EACrBA,EAAM,KAAK1N,EAAK,WAAW,aAAa,EACjCA,EAAK,WAAW,OACzB,EAEAA,EAAK,WAAW,SAAW,SAAU0N,EAAO,CAC1C,OAAAA,EAAM,OAAO,EACbA,EAAM,eAAe,EACrBA,EAAM,KAAK1N,EAAK,WAAW,KAAK,EACzBA,EAAK,WAAW,OACzB,EAEAA,EAAK,WAAW,OAAS,SAAU0N,EAAO,CACpCA,EAAM,MAAM,EAAI,GAClBA,EAAM,KAAK1N,EAAK,WAAW,IAAI,CAEnC,EAaAA,EAAK,WAAW,cAAgBA,EAAK,UAAU,UAE/CA,EAAK,WAAW,QAAU,SAAU0N,EAAO,CACzC,OAAa,CACX,IAAItL,EAAOsL,EAAM,KAAK,EAEtB,GAAItL,GAAQpC,EAAK,WAAW,IAC1B,OAAOA,EAAK,WAAW,OAIzB,GAAIoC,EAAK,WAAW,CAAC,GAAK,GAAI,CAC5BsL,EAAM,gBAAgB,EACtB,QACF,CAEA,GAAItL,GAAQ,IACV,OAAOpC,EAAK,WAAW,SAGzB,GAAIoC,GAAQ,IACV,OAAAsL,EAAM,OAAO,EACTA,EAAM,MAAM,EAAI,GAClBA,EAAM,KAAK1N,EAAK,WAAW,IAAI,EAE1BA,EAAK,WAAW,gBAGzB,GAAIoC,GAAQ,IACV,OAAAsL,EAAM,OAAO,EACTA,EAAM,MAAM,EAAI,GAClBA,EAAM,KAAK1N,EAAK,WAAW,IAAI,EAE1BA,EAAK,WAAW,SAczB,GARIoC,GAAQ,KAAOsL,EAAM,MAAM,IAAM,GAQjCtL,GAAQ,KAAOsL,EAAM,MAAM,IAAM,EACnC,OAAAA,EAAM,KAAK1N,EAAK,WAAW,QAAQ,EAC5BA,EAAK,WAAW,QAGzB,GAAIoC,EAAK,MAAMpC,EAAK,WAAW,aAAa,EAC1C,OAAOA,EAAK,WAAW,OAE3B,CACF,EAEAA,EAAK,YAAc,SAAU4B,EAAKsH,EAAO,CACvC,KAAK,MAAQ,IAAIlJ,EAAK,WAAY4B,CAAG,EACrC,KAAK,MAAQsH,EACb,KAAK,cAAgB,CAAC,EACtB,KAAK,UAAY,CACnB,EAEAlJ,EAAK,YAAY,UAAU,MAAQ,UAAY,CAC7C,KAAK,MAAM,IAAI,EACf,KAAK,QAAU,KAAK,MAAM,QAI1B,QAFIsN,EAAQtN,EAAK,YAAY,YAEtBsN,GACLA,EAAQA,EAAM,IAAI,EAGpB,OAAO,KAAK,KACd,EAEAtN,EAAK,YAAY,UAAU,WAAa,UAAY,CAClD,OAAO,KAAK,QAAQ,KAAK,SAAS,CACpC,EAEAA,EAAK,YAAY,UAAU,cAAgB,UAAY,CACrD,IAAI2N,EAAS,KAAK,WAAW,EAC7B,YAAK,WAAa,EACXA,CACT,EAEA3N,EAAK,YAAY,UAAU,WAAa,UAAY,CAClD,IAAI4N,EAAkB,KAAK,cAC3B,KAAK,MAAM,OAAOA,CAAe,EACjC,KAAK,cAAgB,CAAC,CACxB,EAEA5N,EAAK,YAAY,YAAc,SAAUmJ,EAAQ,CAC/C,IAAIwE,EAASxE,EAAO,WAAW,EAE/B,GAAIwE,GAAU,KAId,OAAQA,EAAO,KAAM,CACnB,KAAK3N,EAAK,WAAW,SACnB,OAAOA,EAAK,YAAY,cAC1B,KAAKA,EAAK,WAAW,MACnB,OAAOA,EAAK,YAAY,WAC1B,KAAKA,EAAK,WAAW,KACnB,OAAOA,EAAK,YAAY,UAC1B,QACE,IAAI6N,EAAe,4CAA8CF,EAAO,KAExE,MAAIA,EAAO,IAAI,QAAU,IACvBE,GAAgB,gBAAkBF,EAAO,IAAM,KAG3C,IAAI3N,EAAK,gBAAiB6N,EAAcF,EAAO,MAAOA,EAAO,GAAG,CAC1E,CACF,EAEA3N,EAAK,YAAY,cAAgB,SAAUmJ,EAAQ,CACjD,IAAIwE,EAASxE,EAAO,cAAc,EAElC,GAAIwE,GAAU,KAId,QAAQA,EAAO,IAAK,CAClB,IAAK,IACHxE,EAAO,cAAc,SAAWnJ,EAAK,MAAM,SAAS,WACpD,MACF,IAAK,IACHmJ,EAAO,cAAc,SAAWnJ,EAAK,MAAM,SAAS,SACpD,MACF,QACE,IAAI6N,EAAe,kCAAoCF,EAAO,IAAM,IACpE,MAAM,IAAI3N,EAAK,gBAAiB6N,EAAcF,EAAO,MAAOA,EAAO,GAAG,CAC1E,CAEA,IAAIG,EAAa3E,EAAO,WAAW,EAEnC,GAAI2E,GAAc,KAAW,CAC3B,IAAID,EAAe,yCACnB,MAAM,IAAI7N,EAAK,gBAAiB6N,EAAcF,EAAO,MAAOA,EAAO,GAAG,CACxE,CAEA,OAAQG,EAAW,KAAM,CACvB,KAAK9N,EAAK,WAAW,MACnB,OAAOA,EAAK,YAAY,WAC1B,KAAKA,EAAK,WAAW,KACnB,OAAOA,EAAK,YAAY,UAC1B,QACE,IAAI6N,EAAe,mCAAqCC,EAAW,KAAO,IAC1E,MAAM,IAAI9N,EAAK,gBAAiB6N,EAAcC,EAAW,MAAOA,EAAW,GAAG,CAClF,EACF,EAEA9N,EAAK,YAAY,WAAa,SAAUmJ,EAAQ,CAC9C,IAAIwE,EAASxE,EAAO,cAAc,EAElC,GAAIwE,GAAU,KAId,IAAIxE,EAAO,MAAM,UAAU,QAAQwE,EAAO,GAAG,GAAK,GAAI,CACpD,IAAII,EAAiB5E,EAAO,MAAM,UAAU,IAAI,SAAU6E,EAAG,CAAE,MAAO,IAAMA,EAAI,GAAI,CAAC,EAAE,KAAK,IAAI,EAC5FH,EAAe,uBAAyBF,EAAO,IAAM,uBAAyBI,EAElF,MAAM,IAAI/N,EAAK,gBAAiB6N,EAAcF,EAAO,MAAOA,EAAO,GAAG,CACxE,CAEAxE,EAAO,cAAc,OAAS,CAACwE,EAAO,GAAG,EAEzC,IAAIG,EAAa3E,EAAO,WAAW,EAEnC,GAAI2E,GAAc,KAAW,CAC3B,IAAID,EAAe,gCACnB,MAAM,IAAI7N,EAAK,gBAAiB6N,EAAcF,EAAO,MAAOA,EAAO,GAAG,CACxE,CAEA,OAAQG,EAAW,KAAM,CACvB,KAAK9N,EAAK,WAAW,KACnB,OAAOA,EAAK,YAAY,UAC1B,QACE,IAAI6N,EAAe,0BAA4BC,EAAW,KAAO,IACjE,MAAM,IAAI9N,EAAK,gBAAiB6N,EAAcC,EAAW,MAAOA,EAAW,GAAG,CAClF,EACF,EAEA9N,EAAK,YAAY,UAAY,SAAUmJ,EAAQ,CAC7C,IAAIwE,EAASxE,EAAO,cAAc,EAElC,GAAIwE,GAAU,KAId,CAAAxE,EAAO,cAAc,KAAOwE,EAAO,IAAI,YAAY,EAE/CA,EAAO,IAAI,QAAQ,GAAG,GAAK,KAC7BxE,EAAO,cAAc,YAAc,IAGrC,IAAI2E,EAAa3E,EAAO,WAAW,EAEnC,GAAI2E,GAAc,KAAW,CAC3B3E,EAAO,WAAW,EAClB,MACF,CAEA,OAAQ2E,EAAW,KAAM,CACvB,KAAK9N,EAAK,WAAW,KACnB,OAAAmJ,EAAO,WAAW,EACXnJ,EAAK,YAAY,UAC1B,KAAKA,EAAK,WAAW,MACnB,OAAAmJ,EAAO,WAAW,EACXnJ,EAAK,YAAY,WAC1B,KAAKA,EAAK,WAAW,cACnB,OAAOA,EAAK,YAAY,kBAC1B,KAAKA,EAAK,WAAW,MACnB,OAAOA,EAAK,YAAY,WAC1B,KAAKA,EAAK,WAAW,SACnB,OAAAmJ,EAAO,WAAW,EACXnJ,EAAK,YAAY,cAC1B,QACE,IAAI6N,EAAe,2BAA6BC,EAAW,KAAO,IAClE,MAAM,IAAI9N,EAAK,gBAAiB6N,EAAcC,EAAW,MAAOA,EAAW,GAAG,CAClF,EACF,EAEA9N,EAAK,YAAY,kBAAoB,SAAUmJ,EAAQ,CACrD,IAAIwE,EAASxE,EAAO,cAAc,EAElC,GAAIwE,GAAU,KAId,KAAIxG,EAAe,SAASwG,EAAO,IAAK,EAAE,EAE1C,GAAI,MAAMxG,CAAY,EAAG,CACvB,IAAI0G,EAAe,gCACnB,MAAM,IAAI7N,EAAK,gBAAiB6N,EAAcF,EAAO,MAAOA,EAAO,GAAG,CACxE,CAEAxE,EAAO,cAAc,aAAehC,EAEpC,IAAI2G,EAAa3E,EAAO,WAAW,EAEnC,GAAI2E,GAAc,KAAW,CAC3B3E,EAAO,WAAW,EAClB,MACF,CAEA,OAAQ2E,EAAW,KAAM,CACvB,KAAK9N,EAAK,WAAW,KACnB,OAAAmJ,EAAO,WAAW,EACXnJ,EAAK,YAAY,UAC1B,KAAKA,EAAK,WAAW,MACnB,OAAAmJ,EAAO,WAAW,EACXnJ,EAAK,YAAY,WAC1B,KAAKA,EAAK,WAAW,cACnB,OAAOA,EAAK,YAAY,kBAC1B,KAAKA,EAAK,WAAW,MACnB,OAAOA,EAAK,YAAY,WAC1B,KAAKA,EAAK,WAAW,SACnB,OAAAmJ,EAAO,WAAW,EACXnJ,EAAK,YAAY,cAC1B,QACE,IAAI6N,EAAe,2BAA6BC,EAAW,KAAO,IAClE,MAAM,IAAI9N,EAAK,gBAAiB6N,EAAcC,EAAW,MAAOA,EAAW,GAAG,CAClF,EACF,EAEA9N,EAAK,YAAY,WAAa,SAAUmJ,EAAQ,CAC9C,IAAIwE,EAASxE,EAAO,cAAc,EAElC,GAAIwE,GAAU,KAId,KAAIM,EAAQ,SAASN,EAAO,IAAK,EAAE,EAEnC,GAAI,MAAMM,CAAK,EAAG,CAChB,IAAIJ,EAAe,wBACnB,MAAM,IAAI7N,EAAK,gBAAiB6N,EAAcF,EAAO,MAAOA,EAAO,GAAG,CACxE,CAEAxE,EAAO,cAAc,MAAQ8E,EAE7B,IAAIH,EAAa3E,EAAO,WAAW,EAEnC,GAAI2E,GAAc,KAAW,CAC3B3E,EAAO,WAAW,EAClB,MACF,CAEA,OAAQ2E,EAAW,KAAM,CACvB,KAAK9N,EAAK,WAAW,KACnB,OAAAmJ,EAAO,WAAW,EACXnJ,EAAK,YAAY,UAC1B,KAAKA,EAAK,WAAW,MACnB,OAAAmJ,EAAO,WAAW,EACXnJ,EAAK,YAAY,WAC1B,KAAKA,EAAK,WAAW,cACnB,OAAOA,EAAK,YAAY,kBAC1B,KAAKA,EAAK,WAAW,MACnB,OAAOA,EAAK,YAAY,WAC1B,KAAKA,EAAK,WAAW,SACnB,OAAAmJ,EAAO,WAAW,EACXnJ,EAAK,YAAY,cAC1B,QACE,IAAI6N,EAAe,2BAA6BC,EAAW,KAAO,IAClE,MAAM,IAAI9N,EAAK,gBAAiB6N,EAAcC,EAAW,MAAOA,EAAW,GAAG,CAClF,EACF,EAMI,SAAU1G,EAAM8G,EAAS,CACrB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOpO,GAAY,SAM5BC,EAAO,QAAUmO,EAAQ,EAGzB9G,EAAK,KAAO8G,EAAQ,CAExB,EAAE,KAAM,UAAY,CAMlB,OAAOlO,CACT,CAAC,CACH,GAAG,ICl5GH,IAAAmO,GAAAC,EAAA,CAAAC,EAAAC,KAAA,EAaE,SAAUC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEvC,OAAOA,CAAO,EACP,OAAOH,GAAY,SAM1BC,GAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAE3B,GAAEF,EAAM,UAAY,CAMhB,OAAO,SAASI,EAAM,CAElBA,EAAK,eAAiB,CAClB,MAAO,SAASC,EAAGC,EAAaC,EAAQC,EAAQ,CAQ5C,GAPA,KAAK,YAAc,SAAS,EAAG,CAE3B,QADIC,EAAU,EAAE,OAAQC,EAAU,IAAI,MAAMD,CAAO,EAC1CE,EAAI,EAAGA,EAAIF,EAASE,IACzBD,EAAQC,CAAC,EAAI,EAAE,WAAWA,CAAC,EAC/B,OAAOD,CACX,EAEK,CAACL,GAAKA,GAAK,IAAQ,CAACC,GAAgBA,GAAe,GAAO,CAACC,EAC5D,KAAO,+BAAiCF,EAAI,kBACtCC,EAAc,aAAeC,EACvC,KAAK,OAASF,EAAE,OAChB,KAAK,EAAI,KAAK,YAAYA,CAAC,EAC3B,KAAK,YAAcC,EACnB,KAAK,OAASC,EACd,KAAK,OAASC,CAClB,EACA,gBAAiB,UAAW,CACxB,IAAII,EACJ,MAAO,CACH,IAAM,EACN,IAAM,EACN,MAAQ,EACR,OAAS,EACT,eAAiB,EACjB,WAAa,SAASC,EAAM,CACxBD,EAAUC,EACV,KAAK,OAAS,EACd,KAAK,MAAQA,EAAK,OAClB,KAAK,eAAiB,EACtB,KAAK,IAAM,KAAK,OAChB,KAAK,IAAM,KAAK,KACpB,EACA,WAAa,UAAW,CACpB,IAAIN,EAASK,EACb,OAAAA,EAAU,KACHL,CACX,EACA,YAAc,SAASF,EAAGS,EAAKC,EAAK,CAChC,GAAI,KAAK,OAAS,KAAK,MAAO,CAC1B,IAAIC,EAAKJ,EAAQ,WAAW,KAAK,MAAM,EACvC,GAAII,GAAMD,GAAOC,GAAMF,IACnBE,GAAMF,EACFT,EAAEW,GAAM,CAAC,EAAK,IAAQA,EAAK,IAC3B,YAAK,SACE,EAGnB,CACA,MAAO,EACX,EACA,cAAgB,SAASX,EAAGS,EAAKC,EAAK,CAClC,GAAI,KAAK,OAAS,KAAK,eAAgB,CACnC,IAAIC,EAAKJ,EAAQ,WAAW,KAAK,OAAS,CAAC,EAC3C,GAAII,GAAMD,GAAOC,GAAMF,IACnBE,GAAMF,EACFT,EAAEW,GAAM,CAAC,EAAK,IAAQA,EAAK,IAC3B,YAAK,SACE,EAGnB,CACA,MAAO,EACX,EACA,aAAe,SAASX,EAAGS,EAAKC,EAAK,CACjC,GAAI,KAAK,OAAS,KAAK,MAAO,CAC1B,IAAIC,EAAKJ,EAAQ,WAAW,KAAK,MAAM,EACvC,GAAII,EAAKD,GAAOC,EAAKF,EACjB,YAAK,SACE,GAGX,GADAE,GAAMF,EACF,EAAET,EAAEW,GAAM,CAAC,EAAK,IAAQA,EAAK,IAC7B,YAAK,SACE,EAEf,CACA,MAAO,EACX,EACA,eAAiB,SAASX,EAAGS,EAAKC,EAAK,CACnC,GAAI,KAAK,OAAS,KAAK,eAAgB,CACnC,IAAIC,EAAKJ,EAAQ,WAAW,KAAK,OAAS,CAAC,EAC3C,GAAII,EAAKD,GAAOC,EAAKF,EACjB,YAAK,SACE,GAGX,GADAE,GAAMF,EACF,EAAET,EAAEW,GAAM,CAAC,EAAK,IAAQA,EAAK,IAC7B,YAAK,SACE,EAEf,CACA,MAAO,EACX,EACA,KAAO,SAASC,EAAQZ,EAAG,CACvB,GAAI,KAAK,MAAQ,KAAK,OAASY,EAC3B,MAAO,GACX,QAAS,EAAI,EAAG,EAAIA,EAAQ,IACxB,GAAIL,EAAQ,WAAW,KAAK,OAAS,CAAC,GAAKP,EAAE,WAAW,CAAC,EACrD,MAAO,GACf,YAAK,QAAUY,EACR,EACX,EACA,OAAS,SAASA,EAAQZ,EAAG,CACzB,GAAI,KAAK,OAAS,KAAK,eAAiBY,EACpC,MAAO,GACX,QAAS,EAAI,EAAG,EAAIA,EAAQ,IACxB,GAAIL,EAAQ,WAAW,KAAK,OAASK,EAAS,CAAC,GAAKZ,EAC/C,WAAW,CAAC,EACb,MAAO,GACf,YAAK,QAAUY,EACR,EACX,EACA,WAAa,SAASC,EAAGC,EAAQ,CAE7B,QADI,EAAI,EAAGC,EAAID,EAAQE,EAAI,KAAK,OAAQC,EAAI,KAAK,MAAOC,EAAW,EAAGC,EAAW,EAAGC,EAAsB,KAC7F,CAIT,QAHIC,EAAI,GAAMN,EAAI,GAAM,GAAIO,EAAO,EAAGC,EAASL,EAAWC,EACpDD,EACAC,EAAUK,EAAIX,EAAEQ,CAAC,EACdI,EAAKF,EAAQE,EAAKD,EAAE,OAAQC,IAAM,CACvC,GAAIT,EAAIO,GAAUN,EAAG,CACjBK,EAAO,GACP,KACJ,CAEA,GADAA,EAAOf,EAAQ,WAAWS,EAAIO,CAAM,EAAIC,EAAE,EAAEC,CAAE,EAC1CH,EACA,MACJC,GACJ,CAQA,GAPID,EAAO,GACPP,EAAIM,EACJF,EAAWI,IAEX,EAAIF,EACJH,EAAWK,GAEXR,EAAI,GAAK,EAAG,CACZ,GAAI,EAAI,GAAKA,GAAK,GAAKK,EACnB,MACJA,EAAsB,EAC1B,CACJ,CACA,OAAa,CACT,IAAII,EAAIX,EAAE,CAAC,EACX,GAAIK,GAAYM,EAAE,OAAQ,CAEtB,GADA,KAAK,OAASR,EAAIQ,EAAE,OAChB,CAACA,EAAE,OACH,OAAOA,EAAE,OACb,IAAIE,EAAMF,EAAE,OAAO,EAEnB,GADA,KAAK,OAASR,EAAIQ,EAAE,OAChBE,EACA,OAAOF,EAAE,MACjB,CAEA,GADA,EAAIA,EAAE,YACF,EAAI,EACJ,MAAO,EACf,CACJ,EACA,aAAe,SAASX,EAAGC,EAAQ,CAE/B,QADI,EAAI,EAAGC,EAAID,EAAQE,EAAI,KAAK,OAAQW,EAAK,KAAK,eAAgBT,EAAW,EAAGC,EAAW,EAAGC,EAAsB,KACvG,CAIT,QAHIC,EAAI,GAAMN,EAAI,GAAM,GAAIO,EAAO,EAAGC,EAASL,EAAWC,EACpDD,EACAC,EAAUK,EAAIX,EAAEQ,CAAC,EACdI,EAAKD,EAAE,OAAS,EAAID,EAAQE,GAAM,EAAGA,IAAM,CAChD,GAAIT,EAAIO,GAAUI,EAAI,CAClBL,EAAO,GACP,KACJ,CAEA,GADAA,EAAOf,EAAQ,WAAWS,EAAI,EAAIO,CAAM,EAAIC,EAAE,EAAEC,CAAE,EAC9CH,EACA,MACJC,GACJ,CAQA,GAPID,EAAO,GACPP,EAAIM,EACJF,EAAWI,IAEX,EAAIF,EACJH,EAAWK,GAEXR,EAAI,GAAK,EAAG,CACZ,GAAI,EAAI,GAAKA,GAAK,GAAKK,EACnB,MACJA,EAAsB,EAC1B,CACJ,CACA,OAAa,CACT,IAAII,EAAIX,EAAE,CAAC,EACX,GAAIK,GAAYM,EAAE,OAAQ,CAEtB,GADA,KAAK,OAASR,EAAIQ,EAAE,OAChB,CAACA,EAAE,OACH,OAAOA,EAAE,OACb,IAAIE,EAAMF,EAAE,OAAO,EAEnB,GADA,KAAK,OAASR,EAAIQ,EAAE,OAChBE,EACA,OAAOF,EAAE,MACjB,CAEA,GADA,EAAIA,EAAE,YACF,EAAI,EACJ,MAAO,EACf,CACJ,EACA,UAAY,SAASI,EAAOC,EAAO7B,EAAG,CAClC,IAAI8B,EAAa9B,EAAE,QAAU6B,EAAQD,GAAQG,EAAOxB,EAC/C,UAAU,EAAGqB,CAAK,EAAGI,EAAQzB,EAAQ,UAAUsB,CAAK,EACzD,OAAAtB,EAAUwB,EAAO/B,EAAIgC,EACrB,KAAK,OAASF,EACV,KAAK,QAAUD,EACf,KAAK,QAAUC,EACV,KAAK,OAASF,IACnB,KAAK,OAASA,GACXE,CACX,EACA,YAAc,UAAW,CACrB,GAAI,KAAK,IAAM,GAAK,KAAK,IAAM,KAAK,KAAO,KAAK,IAAM,KAAK,OACpD,KAAK,MAAQvB,EAAQ,OACxB,KAAO,wBACf,EACA,WAAa,SAASP,EAAG,CACrB,KAAK,YAAY,EACjB,KAAK,UAAU,KAAK,IAAK,KAAK,IAAKA,CAAC,CACxC,EACA,UAAY,UAAW,CACnB,KAAK,WAAW,EAAE,CACtB,EACA,OAAS,SAAS4B,EAAOC,EAAO7B,EAAG,CAC/B,IAAI8B,EAAa,KAAK,UAAUF,EAAOC,EAAO7B,CAAC,EAC3C4B,GAAS,KAAK,MACd,KAAK,KAAOE,GACZF,GAAS,KAAK,MACd,KAAK,KAAOE,EACpB,EACA,SAAW,UAAW,CAClB,YAAK,YAAY,EACVvB,EAAQ,UAAU,KAAK,IAAK,KAAK,GAAG,CAC/C,EACA,OAAS,SAASP,EAAG,CACjB,OAAO,KAAK,OAAOA,EAAE,OAAQA,CAAC,CAClC,CACJ,CACJ,CACJ,EAEAD,EAAK,eAAiB,CAClB,gBAAiB,SAASkC,EAAgB,CACtC,IAAIC,EAAa,IAAI,OAAO,MAAQD,EAAiB,IAAI,EACrDE,EAAW,IAAI,OAAO,KAAOF,EAAiB,KAAK,EAEvD,OAAO,SAASG,EAAO,CAEnB,OAAI,OAAOA,EAAM,QAAW,WACjBA,EAAM,OAAO,SAAU,EAAG,CAC7B,OAAO,EACF,QAAQF,EAAY,EAAE,EACtB,QAAQC,EAAU,EAAE,CAC7B,CAAC,EAEMC,EACF,QAAQF,EAAY,EAAE,EACtB,QAAQC,EAAU,EAAE,CAEjC,CACJ,CACJ,CACJ,CACJ,CAAC,IC/SD,IAAAE,GAAAC,EAAA,CAAAC,EAAAC,KAAA,EAIE,SAAUC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEvC,OAAOA,CAAO,EACP,OAAOH,GAAY,SAM1BC,GAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAE3B,GAAEF,EAAM,UAAY,CAOhB,OAAO,SAASI,EAAM,CAMlB,SAASC,GAAgB,CACvB,IAAIC,EAAW,CACb,+FAAoB,IACpB,0CAAY,IACZ,kBAAQ,IACR,iDAAc,IACd,qCAAiB,IACjB,qBAAW,GACb,EACA,KAAK,UAAY,CAAC,EAClB,QAASC,KAAKD,EAAU,CACtB,IAAIE,EAAS,IAAI,OAAOD,CAAC,EACzB,KAAK,UAAU,KAAK,CAACC,EAAQF,EAASC,CAAC,CAAC,CAAC,CAC3C,CAEA,YAAK,OAAS,KACd,KAAK,MAAQ,CAAC,GAAK,EAAE,GAAK,KAAK,GAAK,IAAI,GAAK,KAAK,EAClD,KAAK,MAAQ,CAAC,GAAK,MAAM,GAAK,KAAK,GAAK,KAAK,GAAK,MAAM,GAAK,MAAM,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,MAAM,GAAK,MAAM,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,MAAM,GAAK,MAAM,GAAK,KAAK,GAAK,KAAK,EAC/L,KAAK,MAAQ,CAAC,GAAK,IAAI,GAAK,IAAI,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,MAAM,GAAK,GAAG,EAC1H,KAAK,MAAQ,CAAC,GAAK,IAAI,GAAK,IAAI,GAAK,KAAK,GAAK,GAAG,EAClD,KAAK,MAAQ,CAAC,GAAK,GAAG,GAAK,KAAK,EAChC,KAAK,MAAQ,CAAC,IAAM,KAAK,IAAM,KAAK,IAAM,MAAM,IAAM,IAAI,IAAM,KAAK,IAAM,IAAI,IAAM,IAAI,IAAM,MAAM,IAAM,IAAI,IAAM,KAAK,IAAM,KAAK,IAAM,MAAM,IAAM,IAAI,IAAM,IAAI,EACrK,KAAK,MAAQ,CAAC,IAAM,IAAI,IAAM,MAAM,IAAM,IAAI,IAAM,KAAK,IAAM,MAAM,IAAM,IAAI,IAAM,MAAM,IAAM,KAAK,IAAM,IAAI,IAAM,KAAK,EAC3H,KAAK,MAAQ,CAAC,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IAAI,IAAM,IAAI,IAAM,KAAK,IAAM,IAAI,IAAM,IAAI,IAAM,KAAK,IAAM,IAAI,IAAM,IAAI,IAAM,KAAK,IAAM,KAAK,IAAM,MAAM,IAAM,MAAM,IAAM,KAAK,EAC1L,KAAK,MAAQ,CAAC,IAAM,MAAM,IAAM,KAAK,IAAM,MAAM,IAAM,KAAK,IAAM,MAAM,IAAM,MAAM,IAAM,OAAO,IAAM,IAAI,IAAM,IAAI,IAAM,MAAM,IAAM,IAAI,EAC3I,KAAK,MAAQ,CAAC,UAAK,IAAI,UAAK,IAAI,SAAM,KAAK,SAAM,IAAI,eAAK,IAAI,eAAK,IAAI,eAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,IAAI,aAAK,MAAM,aAAK,IAAI,aAAK,KAAK,aAAK,IAAI,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,IAAI,aAAK,KAAK,aAAK,KAAK,aAAK,IAAI,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,IAAI,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,IAAI,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,IAAI,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,mBAAM,KAAK,mBAAM,IAAI,eAAK,IAAI,EAC1tB,KAAK,MAAQ,CAAC,KAAK,OAAO,GAAK,KAAK,eAAK,MAAM,eAAK,OAAO,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,IAAI,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,IAAI,aAAK,IAAI,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,IAAI,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,OAAO,aAAK,MAAM,aAAK,OAAO,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,OAAO,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,IAAI,aAAK,KAAK,aAAK,KAAK,aAAK,IAAI,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,OAAO,aAAK,IAAI,aAAK,MAAM,aAAK,IAAI,aAAK,MAAM,aAAK,IAAI,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,IAAI,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,eAAK,IAAI,EAC7vC,KAAK,MAAQ,CAAC,aAAK,MAAM,aAAK,IAAI,aAAK,KAAK,UAAK,MAAM,eAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,UAAK,KAAK,eAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,UAAK,KAAK,eAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,IAAI,UAAK,KAAK,eAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,UAAK,MAAM,eAAK,MAAM,aAAK,KAAK,UAAK,KAAK,eAAK,KAAK,aAAK,IAAI,aAAK,IAAI,UAAK,KAAK,eAAK,KAAK,aAAK,KAAK,aAAK,IAAI,aAAK,MAAM,aAAK,KAAK,UAAK,KAAK,eAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,IAAI,aAAK,KAAK,aAAK,IAAI,aAAK,IAAI,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,IAAI,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,UAAK,MAAM,eAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,UAAK,KAAK,eAAK,KAAK,aAAK,KAAM,UAAK,KAAK,eAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,UAAK,IAAI,eAAK,IAAI,aAAK,KAAK,aAAK,KAAK,aAAK,MAAM,aAAK,KAAK,aAAK,KAAK,aAAK,IAAI,aAAK,IAAI,aAAK,IAAI,aAAK,IAAI,aAAK,KAAK,aAAK,KAAK,aAAK,KAAK,OAAI,IAAI,aAAK,MAAM,UAAK,IAAI,eAAK,IAAI,aAAK,KAAK,aAAK,GAAG,EACroC,KAAK,MAAQ,CAAC,IAAM,KAAK,IAAM,KAAK,IAAM,IAAI,IAAM,IAAI,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,MAAM,IAAM,IAAI,IAAM,IAAI,IAAM,KAAK,EAC/I,KAAK,MAAQ,CAAC,IAAM,KAAK,IAAM,MAAM,IAAM,MAAM,IAAM,MAAM,IAAM,IAAI,IAAM,KAAK,EAClF,KAAK,MAAQ,CAAC,IAAM,KAAK,IAAM,IAAI,IAAM,KAAK,IAAM,MAAM,IAAM,IAAI,IAAM,MAAM,IAAM,IAAI,IAAM,MAAM,IAAM,MAAM,IAAM,KAAK,IAAM,MAAM,IAAM,KAAK,IAAM,MAAM,IAAM,IAAI,IAAM,MAAM,IAAM,MAAM,IAAM,MAAM,IAAM,KAAK,IAAM,IAAI,IAAM,KAAK,IAAM,MAAM,IAAM,IAAI,IAAM,KAAK,EAC/Q,KAAK,MAAQ,CAAC,IAAM,KAAK,IAAM,KAAK,IAAM,IAAI,IAAM,KAAK,IAAM,IAAI,IAAM,IAAI,IAAM,IAAI,IAAM,IAAI,IAAM,IAAI,IAAM,IAAI,IAAM,MAAM,IAAM,IAAI,IAAM,KAAK,IAAM,IAAI,IAAM,GAAG,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IAAI,IAAM,KAAK,IAAM,IAAI,IAAM,GAAG,EAC7P,KAAK,MAAQ,CAAC,KAAO,KAAK,KAAO,IAAI,KAAO,KAAK,KAAO,GAAG,KAAO,KAAK,KAAO,KAAK,KAAO,IAAI,KAAO,KAAK,KAAO,IAAI,KAAO,IAAI,KAAO,IAAI,KAAO,IAAI,KAAO,GAAG,EAChK,KAAK,MAAQ,CAAC,KAAO,MAAM,KAAO,MAAM,KAAO,KAAK,KAAO,KAAK,EAChE,KAAK,MAAQ,CAAC,KAAO,IAAI,KAAO,MAAM,KAAO,IAAI,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,IAAI,KAAO,IAAI,KAAO,KAAK,KAAO,IAAI,KAAO,IAAI,KAAO,IAAI,KAAO,KAAK,KAAO,KAAK,KAAO,IAAI,KAAO,IAAI,KAAO,IAAI,KAAO,IAAI,EACpP,KAAK,MAAQ,CAAC,KAAO,KAAK,KAAO,MAAM,KAAO,KAAK,KAAO,KAAK,KAAO,MAAM,KAAO,MAAM,KAAO,IAAI,KAAO,KAAK,KAAO,KAAK,KAAO,IAAI,KAAO,MAAM,KAAO,KAAK,KAAO,KAAK,KAAO,IAAI,KAAO,MAAM,KAAO,KAAK,EAChN,KAAK,MAAQ,CAAC,mBAAM,MAAM,mBAAM,IAAI,EACpC,KAAK,MAAQ,CAAC,mBAAM,MAAM,mBAAM,MAAM,mBAAM,MAAM,mBAAM,KAAK,mBAAM,MAAM,mBAAM,MAAM,mBAAM,KAAK,mBAAM,MAAM,mBAAM,MAAM,mBAAM,KAAK,mBAAM,KAAK,mBAAM,MAAM,mBAAM,MAAM,mBAAM,MAAM,mBAAM,MAAM,mBAAM,KAAK,EACzM,KAAK,MAAQ,CAAC,mBAAM,MAAM,mBAAM,KAAK,mBAAM,MAAM,mBAAM,MAAM,mBAAM,MAAM,mBAAM,MAAM,gBAAM,KAAK,qBAAM,KAAK,mBAAM,KAAK,mBAAM,MAAM,mBAAM,KAAK,EAC7I,KAAK,MAAQ,CAAC,gBAAM,KAAK,qBAAM,KAAK,mBAAM,MAAM,mBAAM,KAAK,gBAAM,KAAK,qBAAM,KAAK,mBAAM,KAAK,mBAAM,KAAK,mBAAM,KAAK,mBAAM,KAAK,mBAAM,MAAM,mBAAM,IAAI,EACnJ,KAAK,MAAQ,CAAC,EAAI,IAAI,EAAI,GAAG,EAAI,IAAI,EAAI,IAAI,EAC7C,KAAK,MAAQ,CAAC,EAAI,IAAI,EAAI,KAAK,EAAI,IAAI,EAAI,KAAK,EAAI,KAAK,EAAI,GAAG,EAChE,KAAK,MAAQ,CAAC,EAAI,MAAM,EAAI,IAAI,EAChC,KAAK,MAAQ,CAAC,EAAI,MAAM,EAAI,KAAK,EAAI,MAAM,EAAI,MAAM,EAAI,KAAK,EAAI,KAAK,EAAI,IAAI,EAC/E,KAAK,MAAQ,CAAC,EAAI,IAAI,EAAI,MAAM,EAAI,KAAK,EAAI,IAAI,EAAI,IAAI,EACzD,KAAK,MAAQ,CAAC,EAAI,KAAK,EAAI,KAAK,EAAI,GAAG,EAAI,IAAI,EAAI,IAAI,EACvD,KAAK,MAAQ,CAAC,EAAI,IAAI,EACtB,KAAK,MAAQ,CAAC,EAAI,GAAG,EAAI,GAAG,EAC5B,KAAK,MAAQ,CAAC,EAAI,GAAG,EACrB,KAAK,MAAQ,CAAC,GAAK,GAAG,GAAK,IAAI,GAAK,IAAI,GAAK,IAAI,GAAK,IAAI,GAAK,IAAI,GAAK,IAAI,GAAK,IAAI,GAAK,KAAK,EAC/F,KAAK,MAAQ,CAAC,GAAK,IAAI,GAAK,IAAI,GAAK,IAAI,EACzC,KAAK,MAAQ,CAAC,GAAK,KAAK,GAAK,GAAG,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,KAAK,EACxG,KAAK,MAAQ,CAAC,IAAI,IAAI,SAAI,IAAI,SAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,SAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,SAAI,KAAK,SAAI,IAAI,EACxV,KAAK,MAAQ,CAAC,IAAI,KAAK,SAAI,KAAK,SAAI,IAAI,SAAI,KAAK,SAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,MAAM,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,MAAM,OAAI,IAAI,OAAI,KAAK,OAAI,MAAM,OAAI,KAAK,OAAI,IAAI,OAAI,MAAM,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,MAAM,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,IAAI,OAAI,KAAK,OAAI,MAAM,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,IAAI,OAAI,MAAM,OAAI,MAAM,OAAI,MAAM,OAAI,KAAK,OAAI,MAAM,OAAI,KAAK,OAAI,KAAK,OAAI,MAAM,OAAI,IAAI,OAAI,KAAK,OAAI,MAAM,OAAI,KAAK,OAAI,MAAM,OAAI,KAAK,OAAI,MAAM,OAAI,MAAM,OAAI,KAAK,OAAI,MAAM,OAAI,MAAM,OAAI,MAAM,OAAI,MAAM,OAAI,KAAK,OAAI,MAAM,OAAI,MAAM,OAAI,KAAK,OAAI,KAAK,OAAI,MAAM,OAAI,KAAK,OAAI,MAAM,OAAI,IAAI,OAAI,IAAI,OAAI,MAAM,OAAI,KAAK,OAAI,MAAM,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,MAAM,OAAI,IAAI,OAAI,KAAK,OAAI,MAAM,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,MAAM,SAAI,KAAK,SAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,GAAG,EACl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aAAK,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,GAAG,EACx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aAAK,OAAO,SAAI,IAAI,OAAI,IAAI,OAAI,IAAI,OAAI,IAAI,EACn4B,KAAK,MAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,EAAI,KAAK,GAAK,IAAI,SAAI,IAAI,SAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,MAAM,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,IAAI,OAAI,KAAK,OAAI,MAAM,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,OAAI,KAAK,OAAI,IAAI,OAAI,KAAK,SAAI,KAAK,aAAK,IAAI,OAAI,KAAK,OAAI,IAAI,EAEhd,IACT,CACAF,EAAc,UAAU,OAAS,SAASI,EAAK,CAC7C,QAASF,KAAK,KAAK,UACjB,GAAIE,EAAI,MAAM,KAAK,UAAUF,CAAC,EAAE,CAAC,CAAC,EAChC,OAAO,KAAK,UAAUA,CAAC,EAAE,CAAC,EAG9B,MAAO,GACT,EAEAF,EAAc,UAAU,IAAM,SAASK,EAAG,CACxC,OAAIA,GACG,CACT,EAEAL,EAAc,UAAU,QAAU,SAASM,EAAO,CAChD,GAAIA,GAAS,MAAQA,GAAS,MAAaA,GAAS,GAClD,MAAO,CAAC,EAEV,IAAIC,EAAS,CAAC,EACVC,EAAM,CAAC,KAAK,KAAK,IAAI,EACrBC,EAAQ,CAAC,IAAI,IAAI,GAAG,EACpB,EAAIH,EAAM,MAAM,EAAE,EACtB,IAAKJ,EAAI,EAAGA,EAAI,EAAE,OAAQ,EAAEA,EAC1BM,EAAI,KAAK,EAAEN,CAAC,CAAC,EACbO,EAAM,KAAK,KAAK,OAAO,EAAEP,CAAC,CAAC,CAAC,EAE9BM,EAAI,KAAK,IAAI,EACbA,EAAI,KAAK,IAAI,EACbA,EAAI,KAAK,IAAI,EACbC,EAAM,KAAK,GAAG,EACdA,EAAM,KAAK,GAAG,EACdA,EAAM,KAAK,GAAG,EAKd,QAJIC,EAAOF,EAAI,CAAC,EACZG,EAAK,IACLC,EAAK,IACLC,EAAK,IACAX,EAAI,EAAGA,EAAIM,EAAI,OAAS,EAAG,EAAEN,EAAG,CACvC,IAAIY,EAAQ,KAAK,OACbC,EAAKP,EAAIN,EAAE,CAAC,EACZc,EAAKR,EAAIN,EAAE,CAAC,EACZe,EAAKT,EAAIN,EAAE,CAAC,EACZgB,EAAKV,EAAIN,CAAC,EACViB,EAAKX,EAAIN,EAAE,CAAC,EACZkB,EAAKZ,EAAIN,EAAE,CAAC,EACZmB,EAAKZ,EAAMP,EAAE,CAAC,EACdoB,EAAKb,EAAMP,EAAE,CAAC,EACdqB,EAAKd,EAAMP,EAAE,CAAC,EACdsB,EAAKf,EAAMP,CAAC,EACZuB,EAAKhB,EAAMP,EAAE,CAAC,EACdwB,EAAKjB,EAAMP,EAAE,CAAC,EAClBY,GAAS,KAAK,IAAI,KAAK,MAAMH,CAAE,CAAC,EAChCG,GAAS,KAAK,IAAI,KAAK,MAAMF,CAAE,CAAC,EAChCE,GAAS,KAAK,IAAI,KAAK,MAAMD,CAAE,CAAC,EAChCC,GAAS,KAAK,IAAI,KAAK,MAAMH,EAAKC,CAAE,CAAC,EACrCE,GAAS,KAAK,IAAI,KAAK,MAAMF,EAAKC,CAAE,CAAC,EACrCC,GAAS,KAAK,IAAI,KAAK,MAAMC,CAAE,CAAC,EAChCD,GAAS,KAAK,IAAI,KAAK,MAAME,CAAE,CAAC,EAChCF,GAAS,KAAK,IAAI,KAAK,MAAMG,CAAE,CAAC,EAChCH,GAAS,KAAK,IAAI,KAAK,MAAMI,CAAE,CAAC,EAChCJ,GAAS,KAAK,IAAI,KAAK,MAAMK,CAAE,CAAC,EAChCL,GAAS,KAAK,IAAI,KAAK,MAAMM,CAAE,CAAC,EAChCN,GAAS,KAAK,IAAI,KAAK,MAAME,EAAKC,CAAE,CAAC,EACrCH,GAAS,KAAK,IAAI,KAAK,MAAMG,EAAKC,CAAE,CAAC,EACrCJ,GAAS,KAAK,IAAI,KAAK,MAAMI,EAAKC,CAAE,CAAC,EACrCL,GAAS,KAAK,IAAI,KAAK,MAAMC,EAAKC,EAAKC,CAAE,CAAC,EAC1CH,GAAS,KAAK,IAAI,KAAK,MAAME,EAAKC,EAAKC,CAAE,CAAC,EAC1CJ,GAAS,KAAK,IAAI,KAAK,MAAMG,EAAKC,EAAKC,CAAE,CAAC,EAC1CL,GAAS,KAAK,IAAI,KAAK,MAAMI,EAAKC,EAAKC,CAAE,CAAC,EAC1CN,GAAS,KAAK,IAAI,KAAK,MAAMO,CAAE,CAAC,EAChCP,GAAS,KAAK,IAAI,KAAK,MAAMQ,CAAE,CAAC,EAChCR,GAAS,KAAK,IAAI,KAAK,MAAMS,CAAE,CAAC,EAChCT,GAAS,KAAK,IAAI,KAAK,MAAMU,CAAE,CAAC,EAChCV,GAAS,KAAK,IAAI,KAAK,MAAMW,CAAE,CAAC,EAChCX,GAAS,KAAK,IAAI,KAAK,MAAMY,CAAE,CAAC,EAChCZ,GAAS,KAAK,IAAI,KAAK,MAAMQ,EAAKC,CAAE,CAAC,EACrCT,GAAS,KAAK,IAAI,KAAK,MAAMS,EAAKC,CAAE,CAAC,EACrCV,GAAS,KAAK,IAAI,KAAK,MAAMU,EAAKC,CAAE,CAAC,EACrCX,GAAS,KAAK,IAAI,KAAK,MAAMO,EAAKC,EAAKC,CAAE,CAAC,EAC1CT,GAAS,KAAK,IAAI,KAAK,MAAMQ,EAAKC,EAAKC,CAAE,CAAC,EAC1CV,GAAS,KAAK,IAAI,KAAK,MAAMS,EAAKC,EAAKC,CAAE,CAAC,EAC1CX,GAAS,KAAK,IAAI,KAAK,MAAMU,EAAKC,EAAKC,CAAE,CAAC,EAE1CZ,GAAS,KAAK,IAAI,KAAK,MAAMH,EAAKU,CAAE,CAAC,EACrCP,GAAS,KAAK,IAAI,KAAK,MAAMF,EAAKU,CAAE,CAAC,EACrCR,GAAS,KAAK,IAAI,KAAK,MAAMD,EAAKU,CAAE,CAAC,EACrCT,GAAS,KAAK,IAAI,KAAK,MAAMF,EAAKU,EAAKC,CAAE,CAAC,EAC1CT,GAAS,KAAK,IAAI,KAAK,MAAMF,EAAKW,EAAKC,CAAE,CAAC,EAC1CV,GAAS,KAAK,IAAI,KAAK,MAAMD,EAAKS,EAAKC,CAAE,CAAC,EAC1CT,GAAS,KAAK,IAAI,KAAK,MAAMD,EAAKU,EAAKC,CAAE,CAAC,EAC1CV,GAAS,KAAK,IAAI,KAAK,MAAMF,EAAKS,EAAKC,EAAKC,CAAE,CAAC,EAC/CT,GAAS,KAAK,IAAI,KAAK,MAAMF,EAAKU,EAAKC,EAAKC,CAAE,CAAC,EAC/CV,GAAS,KAAK,IAAI,KAAK,MAAMD,EAAKQ,EAAKC,EAAKC,CAAE,CAAC,EAC/CT,GAAS,KAAK,IAAI,KAAK,MAAMD,EAAKS,EAAKC,EAAKC,CAAE,CAAC,EAC/C,IAAIG,EAAI,IACJb,EAAQ,IACVP,EAAO,KAAKG,CAAI,EAChBA,EAAO,GACPiB,EAAI,KAENhB,EAAKC,EACLA,EAAKC,EACLA,EAAKc,EACLjB,GAAQF,EAAIN,CAAC,CACf,CACA,OAAAK,EAAO,KAAKG,CAAI,EAETH,CACT,EAEAR,EAAK,cAAgBC,CACzB,CAEJ,CAAC,IC7MD,IAAA4B,GAAAC,EAAA,CAAAC,EAAAC,KAAA,EAIE,SAAUC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEvC,OAAOA,CAAO,EACP,OAAOH,GAAY,SAM1BC,GAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAE3B,GAAEF,EAAM,UAAY,CAMhB,OAAO,SAASI,EAAM,CAWlBA,EAAK,cAAgB,UAAkC,CAMnD,QALIC,EAAY,MAAM,UAAU,MAAM,KAAK,SAAS,EAChDC,EAAaD,EAAU,KAAK,GAAG,EAC/BE,EAAiB,GACjBC,EAAW,CAAC,EACZC,EAAiB,CAAC,EACbC,EAAI,EAAGA,EAAIL,EAAU,OAAQ,EAAEK,EAChCL,EAAUK,CAAC,GAAK,MAChBH,GAAkB,MAClBC,EAAS,QAAQJ,EAAK,cAAc,EACpCI,EAAS,KAAKJ,EAAK,OAAO,EAC1BK,EAAe,KAAKL,EAAK,OAAO,IAEhCG,GAAkBH,EAAKC,EAAUK,CAAC,CAAC,EAAE,eACjCN,EAAKC,EAAUK,CAAC,CAAC,EAAE,gBACnBF,EAAS,QAAQJ,EAAKC,EAAUK,CAAC,CAAC,EAAE,cAAc,EAElDN,EAAKC,EAAUK,CAAC,CAAC,EAAE,UACnBF,EAAS,KAAKJ,EAAKC,EAAUK,CAAC,CAAC,EAAE,OAAO,EACxCD,EAAe,KAAKL,EAAKC,EAAUK,CAAC,CAAC,EAAE,OAAO,IAI1D,IAAIC,EAAeP,EAAK,eAAe,gBAAgBG,CAAc,EACrE,OAAAH,EAAK,SAAS,iBAAiBO,EAAc,sBAAwBL,CAAU,EAC/EE,EAAS,QAAQG,CAAY,EAEtB,UAAW,CACd,KAAK,SAAS,MAAM,EAEpB,KAAK,SAAS,IAAI,MAAM,KAAK,SAAUH,CAAQ,EAK3C,KAAK,iBACL,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAI,MAAM,KAAK,eAAgBC,CAAc,EAEzE,CACJ,CACJ,CACJ,CAAC,IC3ED,IAAAG,EAAiB,QACjBC,GAAoB,QACpBC,GAAoB,QACpBF,GAAkB,QCNlB,SAASG,EAAiBC,EAAS,CAC/B,OAAO,IAAI,QAAQ,CAACC,EAASC,IAAW,CAEpCF,EAAQ,WAAaA,EAAQ,UAAY,IAAMC,EAAQD,EAAQ,MAAM,EAErEA,EAAQ,QAAUA,EAAQ,QAAU,IAAME,EAAOF,EAAQ,KAAK,CAClE,CAAC,CACL,CACA,SAASG,EAAYC,EAAQC,EAAW,CACpC,IAAML,EAAU,UAAU,KAAKI,CAAM,EACrCJ,EAAQ,gBAAkB,IAAMA,EAAQ,OAAO,kBAAkBK,CAAS,EAC1E,IAAMC,EAAMP,EAAiBC,CAAO,EACpC,MAAO,CAACO,EAAQC,IAAaF,EAAI,KAAMG,GAAOD,EAASC,EAAG,YAAYJ,EAAWE,CAAM,EAAE,YAAYF,CAAS,CAAC,CAAC,CACpH,CACA,IAAIK,EACJ,SAASC,IAAkB,CACvB,OAAKD,IACDA,EAAsBP,EAAY,eAAgB,QAAQ,GAEvDO,CACX,CAOA,SAASE,GAAIC,EAAKC,EAAcH,GAAgB,EAAG,CAC/C,OAAOG,EAAY,WAAaC,GAAUhB,EAAiBgB,EAAM,IAAIF,CAAG,CAAC,CAAC,CAC9E,CAQA,SAASG,GAAIH,EAAKI,EAAOH,EAAcH,GAAgB,EAAG,CACtD,OAAOG,EAAY,YAAcC,IAC7BA,EAAM,IAAIE,EAAOJ,CAAG,EACbd,EAAiBgB,EAAM,WAAW,EAC5C,CACL,CD1BA,IAAIG,EAEJ,eAAeC,GAAU,CAAE,cAAAC,CAAc,EAAiC,CACxE,GAAM,CAAE,MAAAC,EAAO,KAAAC,CAAK,EAAI,MAAMC,EAAc,EAC5CL,EAASM,GAAKH,EAAM,OAAOG,CAAC,EAAE,IAAI,CAAC,CAAE,IAAAC,CAAI,IAAMH,EAAKG,CAAG,CAAC,EACxD,YAAY,CAAE,EAAG,aAAc,CAAC,EAEhC,eAAeF,GAAgB,CAC7B,IAAMG,EAAM,MAAM,MAAM,eAAe,EACjCC,EAAOD,EAAI,QAAQ,IAAI,MAAM,EAC7BJ,EAAO,MAAMI,EAAI,KAAK,EACtBE,EAAQC,EAAY,QAAS,MAAM,EAWzC,GATIT,GAAiBA,EAAc,OAAS,OAC1C,GAAAU,SAAM,EAAAC,OAAI,KACV,GAAAC,SAAQ,EAAAD,OAAI,EACRX,EAAc,SAAS,IAAI,MAC7B,GAAAa,SAAQ,EAAAF,OAAI,EAEd,MAAM,QAAQ,IAAIX,EAAc,IAAIc,EAAY,CAAC,GAG/CP,EAAM,CACR,IAAMQ,EAAQ,KAAK,MAAM,MAAMC,GAAI,QAASR,CAAK,GAAK,IAAI,EAC1D,GAAIO,GAASA,EAAM,OAASR,EAC1B,MAAO,CAAE,MAAO,EAAAI,QAAK,MAAM,KAAKI,CAAK,EAAG,KAAAb,CAAK,CAEjD,CAEA,IAAMD,KAAQ,EAAAU,SAAK,UAAW,CAC5B,EAAAA,QAAK,UAAU,UAAY,aAE3B,KAAK,IAAI,MAAM,EACf,KAAK,MAAM,QAAS,CAAE,MAAO,EAAG,CAAC,EACjC,KAAK,MAAM,WAAY,CAAE,MAAO,EAAG,CAAC,EACpC,KAAK,MAAM,UAAW,CAAE,MAAO,EAAG,CAAC,EAE/BX,GAAiBA,EAAc,OAAS,GAC1C,KAAK,IAAI,EAAAW,QAAK,cAAc,GAAGX,CAAa,CAAC,EAG/C,QAAWiB,KAAOf,EAChB,KAAK,IAAIA,EAAKe,CAAG,CAAC,CAEtB,CAAC,EAED,OAAIV,GACF,MAAMW,GAAI,QAAS,KAAK,UAAU,OAAO,OAAOjB,EAAM,OAAO,EAAG,CAAE,KAAAM,CAAK,CAAC,CAAC,EAAGC,CAAK,EAG5E,CAAE,MAAAP,EAAO,KAAAC,CAAK,CACvB,CACF,CAEA,UAAY,SAASiB,EAAG,CAClBA,EAAE,KAAK,GAAKrB,EACd,YAAY,CAAE,EAAG,cAAe,EAAGA,EAAOqB,EAAE,KAAK,CAAC,CAAE,CAAC,EAC5CA,EAAE,KAAK,MAChBpB,GAAUoB,EAAE,KAAK,IAAI,EAAE,MAAM,QAAQ,KAAK,CAE9C,EAEA,IAAMC,GAAU,CACd,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,EAC5C,GAAI,IAAM,OAAO,2BAA2B,CAI9C,EAEA,eAAeN,GAAaO,EAAc,CACxC,GAAIA,IAAS,KAAM,CACjB,GAAM,CAAE,QAASC,CAAK,EAAI,MAAMF,GAAQC,CAAI,EAAE,EAC9CC,EAAK,EAAAX,OAAI,CACX,CACF", "names": ["require_lunr", "__commonJSMin", "exports", "module", "lunr", "config", "builder", "global", "message", "obj", "clone", "keys", "key", "val", "doc<PERSON>ef", "fieldName", "stringValue", "s", "n", "fieldRef", "elements", "i", "other", "object", "a", "b", "intersection", "element", "posting", "documentCount", "documentsWithTerm", "x", "str", "metadata", "fn", "t", "len", "tokens", "sliceEnd", "sliceStart", "char", "slice<PERSON><PERSON>th", "tokenMetadata", "label", "isRegistered", "serialised", "pipeline", "fnName", "fns", "existingFn", "newFn", "pos", "stack<PERSON>ength", "memo", "j", "result", "k", "token", "index", "start", "end", "pivotPoint", "pivotIndex", "insertIdx", "position", "sumOfSquares", "elements<PERSON>ength", "otherVector", "dotProduct", "aLen", "bLen", "aVal", "bVal", "output", "step2list", "step3list", "c", "v", "C", "V", "mgr0", "meq1", "mgr1", "s_v", "re_mgr0", "re_mgr1", "re_meq1", "re_s_v", "re_1a", "re2_1a", "re_1b", "re2_1b", "re_1b_2", "re2_1b_2", "re3_1b_2", "re4_1b_2", "re_1c", "re_2", "re_3", "re_4", "re2_4", "re_5", "re_5_1", "re3_5", "port<PERSON><PERSON><PERSON><PERSON>", "w", "stem", "suffix", "firstch", "re", "re2", "re3", "re4", "fp", "stopWords", "words", "stopWord", "arr", "clause", "editDistance", "root", "stack", "frame", "noEditNode", "insertionNode", "substitutionNode", "charA", "charB", "transposeNode", "node", "final", "next", "edges", "edge", "labels", "qEdges", "qLen", "nEdges", "nLen", "q", "qEdge", "nEdge", "qNode", "word", "commonPrefix", "nextNode", "downTo", "<PERSON><PERSON><PERSON>", "attrs", "queryString", "query", "parser", "matchingFields", "queryVectors", "termField<PERSON>ache", "requiredMatches", "prohibitedMatches", "terms", "clauseMatches", "m", "term", "termTokenSet", "expandedTerms", "field", "expandedTerm", "termIndex", "fieldPosting", "matchingDocumentRefs", "termField", "matchingDocumentsSet", "l", "matchingDocumentRef", "matchingFieldRef", "fieldMatch", "allRequiredMatches", "allProhibitedMatches", "matchingFieldRefs", "results", "matches", "fieldVector", "score", "docMatch", "match", "invertedIndex", "fieldVectors", "ref", "serializedIndex", "serializedVectors", "serializedInvertedIndex", "tokenSetBuilder", "tuple", "attributes", "number", "doc", "fields", "extractor", "fieldTerms", "metadataKey", "fieldRefs", "numberOfFields", "accumulator", "documentsWithField", "fieldRefsLength", "termIdfCache", "<PERSON><PERSON><PERSON><PERSON>", "termFrequencies", "termsLength", "fieldBoost", "docBoost", "tf", "idf", "scoreWithPrecision", "args", "clonedMetadata", "metadataKeys", "otherMatchData", "allFields", "options", "state", "subSlices", "type", "charCode", "lexer", "lexeme", "completed<PERSON><PERSON><PERSON>", "errorMessage", "nextLexeme", "possibleFields", "f", "boost", "factory", "require_lunr_stemmer_support", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "s", "substring_i", "result", "method", "s<PERSON><PERSON>th", "char<PERSON><PERSON>", "i", "current", "word", "min", "max", "ch", "s_size", "v", "v_size", "j", "c", "l", "common_i", "common_j", "first_key_inspected", "k", "diff", "common", "w", "i2", "res", "lb", "c_bra", "c_ket", "adjustment", "left", "right", "wordCharacters", "startRegex", "endRegex", "token", "require_tinyseg", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "TinySegmenter", "patterns", "i", "regexp", "str", "v", "input", "result", "seg", "ctype", "word", "p1", "p2", "p3", "score", "w1", "w2", "w3", "w4", "w5", "w6", "c1", "c2", "c3", "c4", "c5", "c6", "p", "require_lunr_multi", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "languages", "nameSuffix", "wordCharacters", "pipeline", "searchPipeline", "i", "multiTrimmer", "import_lunr", "import_lunr_stemmer", "import_tinyseg", "promisifyRequest", "request", "resolve", "reject", "createStore", "dbN<PERSON>", "storeName", "dbp", "txMode", "callback", "db", "defaultGetStoreFunc", "defaultGetStore", "get", "key", "customStore", "store", "set", "value", "search", "loadIndex", "lunrLanguages", "index", "data", "loadIndexCore", "q", "ref", "res", "etag", "cache", "createStore", "multi", "lunr", "stemmer", "tinyseg", "initLanguage", "value", "get", "key", "set", "e", "langMap", "lang", "init"]}