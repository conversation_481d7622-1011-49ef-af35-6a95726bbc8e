{"version": 3, "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/gitGraphDiagram-NRZ2UAAF.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-TMUBEWPD.mjs\";\nimport {\n  ImperativeState\n} from \"./chunk-KFBOBJHC.mjs\";\nimport {\n  cleanAndMerge,\n  random,\n  utils_default\n} from \"./chunk-7DKRZKHE.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getConfig2,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox2 as setupGraphViewbox\n} from \"./chunk-6DBFFHIP.mjs\";\n\n// src/diagrams/git/gitGraphParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/git/gitGraphTypes.ts\nvar commitType = {\n  NORMAL: 0,\n  REVERSE: 1,\n  HIGHLIGHT: 2,\n  MERGE: 3,\n  CHERRY_PICK: 4\n};\n\n// src/diagrams/git/gitGraphAst.ts\nvar DEFAULT_GITGRAPH_CONFIG = defaultConfig_default.gitGraph;\nvar getConfig3 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_GITGRAPH_CONFIG,\n    ...getConfig().gitGraph\n  });\n  return config;\n}, \"getConfig\");\nvar state = new ImperativeState(() => {\n  const config = getConfig3();\n  const mainBranchName = config.mainBranchName;\n  const mainBranchOrder = config.mainBranchOrder;\n  return {\n    mainBranchName,\n    commits: /* @__PURE__ */ new Map(),\n    head: null,\n    branchConfig: /* @__PURE__ */ new Map([[mainBranchName, { name: mainBranchName, order: mainBranchOrder }]]),\n    branches: /* @__PURE__ */ new Map([[mainBranchName, null]]),\n    currBranch: mainBranchName,\n    direction: \"LR\",\n    seq: 0,\n    options: {}\n  };\n});\nfunction getID() {\n  return random({ length: 7 });\n}\n__name(getID, \"getID\");\nfunction uniqBy(list, fn) {\n  const recordMap = /* @__PURE__ */ Object.create(null);\n  return list.reduce((out, item) => {\n    const key = fn(item);\n    if (!recordMap[key]) {\n      recordMap[key] = true;\n      out.push(item);\n    }\n    return out;\n  }, []);\n}\n__name(uniqBy, \"uniqBy\");\nvar setDirection = /* @__PURE__ */ __name(function(dir2) {\n  state.records.direction = dir2;\n}, \"setDirection\");\nvar setOptions = /* @__PURE__ */ __name(function(rawOptString) {\n  log.debug(\"options str\", rawOptString);\n  rawOptString = rawOptString?.trim();\n  rawOptString = rawOptString || \"{}\";\n  try {\n    state.records.options = JSON.parse(rawOptString);\n  } catch (e) {\n    log.error(\"error while parsing gitGraph options\", e.message);\n  }\n}, \"setOptions\");\nvar getOptions = /* @__PURE__ */ __name(function() {\n  return state.records.options;\n}, \"getOptions\");\nvar commit = /* @__PURE__ */ __name(function(commitDB) {\n  let msg = commitDB.msg;\n  let id = commitDB.id;\n  const type = commitDB.type;\n  let tags = commitDB.tags;\n  log.info(\"commit\", msg, id, type, tags);\n  log.debug(\"Entering commit:\", msg, id, type, tags);\n  const config = getConfig3();\n  id = common_default.sanitizeText(id, config);\n  msg = common_default.sanitizeText(msg, config);\n  tags = tags?.map((tag) => common_default.sanitizeText(tag, config));\n  const newCommit = {\n    id: id ? id : state.records.seq + \"-\" + getID(),\n    message: msg,\n    seq: state.records.seq++,\n    type: type ?? commitType.NORMAL,\n    tags: tags ?? [],\n    parents: state.records.head == null ? [] : [state.records.head.id],\n    branch: state.records.currBranch\n  };\n  state.records.head = newCommit;\n  log.info(\"main branch\", config.mainBranchName);\n  state.records.commits.set(newCommit.id, newCommit);\n  state.records.branches.set(state.records.currBranch, newCommit.id);\n  log.debug(\"in pushCommit \" + newCommit.id);\n}, \"commit\");\nvar branch = /* @__PURE__ */ __name(function(branchDB) {\n  let name = branchDB.name;\n  const order = branchDB.order;\n  name = common_default.sanitizeText(name, getConfig3());\n  if (state.records.branches.has(name)) {\n    throw new Error(\n      `Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using \"checkout ${name}\")`\n    );\n  }\n  state.records.branches.set(name, state.records.head != null ? state.records.head.id : null);\n  state.records.branchConfig.set(name, { name, order });\n  checkout(name);\n  log.debug(\"in createBranch\");\n}, \"branch\");\nvar merge = /* @__PURE__ */ __name((mergeDB) => {\n  let otherBranch = mergeDB.branch;\n  let customId = mergeDB.id;\n  const overrideType = mergeDB.type;\n  const customTags = mergeDB.tags;\n  const config = getConfig3();\n  otherBranch = common_default.sanitizeText(otherBranch, config);\n  if (customId) {\n    customId = common_default.sanitizeText(customId, config);\n  }\n  const currentBranchCheck = state.records.branches.get(state.records.currBranch);\n  const otherBranchCheck = state.records.branches.get(otherBranch);\n  const currentCommit = currentBranchCheck ? state.records.commits.get(currentBranchCheck) : void 0;\n  const otherCommit = otherBranchCheck ? state.records.commits.get(otherBranchCheck) : void 0;\n  if (currentCommit && otherCommit && currentCommit.branch === otherBranch) {\n    throw new Error(`Cannot merge branch '${otherBranch}' into itself.`);\n  }\n  if (state.records.currBranch === otherBranch) {\n    const error = new Error('Incorrect usage of \"merge\". Cannot merge a branch to itself');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  }\n  if (currentCommit === void 0 || !currentCommit) {\n    const error = new Error(\n      `Incorrect usage of \"merge\". Current branch (${state.records.currBranch})has no commits`\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"commit\"]\n    };\n    throw error;\n  }\n  if (!state.records.branches.has(otherBranch)) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") does not exist\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [`branch ${otherBranch}`]\n    };\n    throw error;\n  }\n  if (otherCommit === void 0 || !otherCommit) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") has no commits\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: ['\"commit\"']\n    };\n    throw error;\n  }\n  if (currentCommit === otherCommit) {\n    const error = new Error('Incorrect usage of \"merge\". Both branches have same head');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  }\n  if (customId && state.records.commits.has(customId)) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Commit with id:' + customId + \" already exists, use different custom Id\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(\" \")}`,\n      token: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(\" \")}`,\n      expected: [\n        `merge ${otherBranch} ${customId}_UNIQUE ${overrideType} ${customTags?.join(\" \")}`\n      ]\n    };\n    throw error;\n  }\n  const verifiedBranch = otherBranchCheck ? otherBranchCheck : \"\";\n  const commit2 = {\n    id: customId || `${state.records.seq}-${getID()}`,\n    message: `merged branch ${otherBranch} into ${state.records.currBranch}`,\n    seq: state.records.seq++,\n    parents: state.records.head == null ? [] : [state.records.head.id, verifiedBranch],\n    branch: state.records.currBranch,\n    type: commitType.MERGE,\n    customType: overrideType,\n    customId: customId ? true : false,\n    tags: customTags ?? []\n  };\n  state.records.head = commit2;\n  state.records.commits.set(commit2.id, commit2);\n  state.records.branches.set(state.records.currBranch, commit2.id);\n  log.debug(state.records.branches);\n  log.debug(\"in mergeBranch\");\n}, \"merge\");\nvar cherryPick = /* @__PURE__ */ __name(function(cherryPickDB) {\n  let sourceId = cherryPickDB.id;\n  let targetId = cherryPickDB.targetId;\n  let tags = cherryPickDB.tags;\n  let parentCommitId = cherryPickDB.parent;\n  log.debug(\"Entering cherryPick:\", sourceId, targetId, tags);\n  const config = getConfig3();\n  sourceId = common_default.sanitizeText(sourceId, config);\n  targetId = common_default.sanitizeText(targetId, config);\n  tags = tags?.map((tag) => common_default.sanitizeText(tag, config));\n  parentCommitId = common_default.sanitizeText(parentCommitId, config);\n  if (!sourceId || !state.records.commits.has(sourceId)) {\n    const error = new Error(\n      'Incorrect usage of \"cherryPick\". Source commit id should exist and provided'\n    );\n    error.hash = {\n      text: `cherryPick ${sourceId} ${targetId}`,\n      token: `cherryPick ${sourceId} ${targetId}`,\n      expected: [\"cherry-pick abc\"]\n    };\n    throw error;\n  }\n  const sourceCommit = state.records.commits.get(sourceId);\n  if (sourceCommit === void 0 || !sourceCommit) {\n    throw new Error('Incorrect usage of \"cherryPick\". Source commit id should exist and provided');\n  }\n  if (parentCommitId && !(Array.isArray(sourceCommit.parents) && sourceCommit.parents.includes(parentCommitId))) {\n    const error = new Error(\n      \"Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.\"\n    );\n    throw error;\n  }\n  const sourceCommitBranch = sourceCommit.branch;\n  if (sourceCommit.type === commitType.MERGE && !parentCommitId) {\n    const error = new Error(\n      \"Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.\"\n    );\n    throw error;\n  }\n  if (!targetId || !state.records.commits.has(targetId)) {\n    if (sourceCommitBranch === state.records.currBranch) {\n      const error = new Error(\n        'Incorrect usage of \"cherryPick\". Source commit is already on current branch'\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const currentCommitId = state.records.branches.get(state.records.currBranch);\n    if (currentCommitId === void 0 || !currentCommitId) {\n      const error = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const currentCommit = state.records.commits.get(currentCommitId);\n    if (currentCommit === void 0 || !currentCommit) {\n      const error = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const commit2 = {\n      id: state.records.seq + \"-\" + getID(),\n      message: `cherry-picked ${sourceCommit?.message} into ${state.records.currBranch}`,\n      seq: state.records.seq++,\n      parents: state.records.head == null ? [] : [state.records.head.id, sourceCommit.id],\n      branch: state.records.currBranch,\n      type: commitType.CHERRY_PICK,\n      tags: tags ? tags.filter(Boolean) : [\n        `cherry-pick:${sourceCommit.id}${sourceCommit.type === commitType.MERGE ? `|parent:${parentCommitId}` : \"\"}`\n      ]\n    };\n    state.records.head = commit2;\n    state.records.commits.set(commit2.id, commit2);\n    state.records.branches.set(state.records.currBranch, commit2.id);\n    log.debug(state.records.branches);\n    log.debug(\"in cherryPick\");\n  }\n}, \"cherryPick\");\nvar checkout = /* @__PURE__ */ __name(function(branch2) {\n  branch2 = common_default.sanitizeText(branch2, getConfig3());\n  if (!state.records.branches.has(branch2)) {\n    const error = new Error(\n      `Trying to checkout branch which is not yet created. (Help try using \"branch ${branch2}\")`\n    );\n    error.hash = {\n      text: `checkout ${branch2}`,\n      token: `checkout ${branch2}`,\n      expected: [`branch ${branch2}`]\n    };\n    throw error;\n  } else {\n    state.records.currBranch = branch2;\n    const id = state.records.branches.get(state.records.currBranch);\n    if (id === void 0 || !id) {\n      state.records.head = null;\n    } else {\n      state.records.head = state.records.commits.get(id) ?? null;\n    }\n  }\n}, \"checkout\");\nfunction upsert(arr, key, newVal) {\n  const index = arr.indexOf(key);\n  if (index === -1) {\n    arr.push(newVal);\n  } else {\n    arr.splice(index, 1, newVal);\n  }\n}\n__name(upsert, \"upsert\");\nfunction prettyPrintCommitHistory(commitArr) {\n  const commit2 = commitArr.reduce((out, commit3) => {\n    if (out.seq > commit3.seq) {\n      return out;\n    }\n    return commit3;\n  }, commitArr[0]);\n  let line = \"\";\n  commitArr.forEach(function(c) {\n    if (c === commit2) {\n      line += \"\t*\";\n    } else {\n      line += \"\t|\";\n    }\n  });\n  const label = [line, commit2.id, commit2.seq];\n  for (const branch2 in state.records.branches) {\n    if (state.records.branches.get(branch2) === commit2.id) {\n      label.push(branch2);\n    }\n  }\n  log.debug(label.join(\" \"));\n  if (commit2.parents && commit2.parents.length == 2 && commit2.parents[0] && commit2.parents[1]) {\n    const newCommit = state.records.commits.get(commit2.parents[0]);\n    upsert(commitArr, commit2, newCommit);\n    if (commit2.parents[1]) {\n      commitArr.push(state.records.commits.get(commit2.parents[1]));\n    }\n  } else if (commit2.parents.length == 0) {\n    return;\n  } else {\n    if (commit2.parents[0]) {\n      const newCommit = state.records.commits.get(commit2.parents[0]);\n      upsert(commitArr, commit2, newCommit);\n    }\n  }\n  commitArr = uniqBy(commitArr, (c) => c.id);\n  prettyPrintCommitHistory(commitArr);\n}\n__name(prettyPrintCommitHistory, \"prettyPrintCommitHistory\");\nvar prettyPrint = /* @__PURE__ */ __name(function() {\n  log.debug(state.records.commits);\n  const node = getCommitsArray()[0];\n  prettyPrintCommitHistory([node]);\n}, \"prettyPrint\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  state.reset();\n  clear();\n}, \"clear\");\nvar getBranchesAsObjArray = /* @__PURE__ */ __name(function() {\n  const branchesArray = [...state.records.branchConfig.values()].map((branchConfig, i) => {\n    if (branchConfig.order !== null && branchConfig.order !== void 0) {\n      return branchConfig;\n    }\n    return {\n      ...branchConfig,\n      order: parseFloat(`0.${i}`)\n    };\n  }).sort((a, b) => (a.order ?? 0) - (b.order ?? 0)).map(({ name }) => ({ name }));\n  return branchesArray;\n}, \"getBranchesAsObjArray\");\nvar getBranches = /* @__PURE__ */ __name(function() {\n  return state.records.branches;\n}, \"getBranches\");\nvar getCommits = /* @__PURE__ */ __name(function() {\n  return state.records.commits;\n}, \"getCommits\");\nvar getCommitsArray = /* @__PURE__ */ __name(function() {\n  const commitArr = [...state.records.commits.values()];\n  commitArr.forEach(function(o) {\n    log.debug(o.id);\n  });\n  commitArr.sort((a, b) => a.seq - b.seq);\n  return commitArr;\n}, \"getCommitsArray\");\nvar getCurrentBranch = /* @__PURE__ */ __name(function() {\n  return state.records.currBranch;\n}, \"getCurrentBranch\");\nvar getDirection = /* @__PURE__ */ __name(function() {\n  return state.records.direction;\n}, \"getDirection\");\nvar getHead = /* @__PURE__ */ __name(function() {\n  return state.records.head;\n}, \"getHead\");\nvar db = {\n  commitType,\n  getConfig: getConfig3,\n  setDirection,\n  setOptions,\n  getOptions,\n  commit,\n  branch,\n  merge,\n  cherryPick,\n  checkout,\n  //reset,\n  prettyPrint,\n  clear: clear2,\n  getBranchesAsObjArray,\n  getBranches,\n  getCommits,\n  getCommitsArray,\n  getCurrentBranch,\n  getDirection,\n  getHead,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  setDiagramTitle,\n  getDiagramTitle\n};\n\n// src/diagrams/git/gitGraphParser.ts\nvar populate = /* @__PURE__ */ __name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  if (ast.dir) {\n    db2.setDirection(ast.dir);\n  }\n  for (const statement of ast.statements) {\n    parseStatement(statement, db2);\n  }\n}, \"populate\");\nvar parseStatement = /* @__PURE__ */ __name((statement, db2) => {\n  const parsers = {\n    Commit: /* @__PURE__ */ __name((stmt) => db2.commit(parseCommit(stmt)), \"Commit\"),\n    Branch: /* @__PURE__ */ __name((stmt) => db2.branch(parseBranch(stmt)), \"Branch\"),\n    Merge: /* @__PURE__ */ __name((stmt) => db2.merge(parseMerge(stmt)), \"Merge\"),\n    Checkout: /* @__PURE__ */ __name((stmt) => db2.checkout(parseCheckout(stmt)), \"Checkout\"),\n    CherryPicking: /* @__PURE__ */ __name((stmt) => db2.cherryPick(parseCherryPicking(stmt)), \"CherryPicking\")\n  };\n  const parser2 = parsers[statement.$type];\n  if (parser2) {\n    parser2(statement);\n  } else {\n    log.error(`Unknown statement type: ${statement.$type}`);\n  }\n}, \"parseStatement\");\nvar parseCommit = /* @__PURE__ */ __name((commit2) => {\n  const commitDB = {\n    id: commit2.id,\n    msg: commit2.message ?? \"\",\n    type: commit2.type !== void 0 ? commitType[commit2.type] : commitType.NORMAL,\n    tags: commit2.tags ?? void 0\n  };\n  return commitDB;\n}, \"parseCommit\");\nvar parseBranch = /* @__PURE__ */ __name((branch2) => {\n  const branchDB = {\n    name: branch2.name,\n    order: branch2.order ?? 0\n  };\n  return branchDB;\n}, \"parseBranch\");\nvar parseMerge = /* @__PURE__ */ __name((merge2) => {\n  const mergeDB = {\n    branch: merge2.branch,\n    id: merge2.id ?? \"\",\n    type: merge2.type !== void 0 ? commitType[merge2.type] : void 0,\n    tags: merge2.tags ?? void 0\n  };\n  return mergeDB;\n}, \"parseMerge\");\nvar parseCheckout = /* @__PURE__ */ __name((checkout2) => {\n  const branch2 = checkout2.branch;\n  return branch2;\n}, \"parseCheckout\");\nvar parseCherryPicking = /* @__PURE__ */ __name((cherryPicking) => {\n  const cherryPickDB = {\n    id: cherryPicking.id,\n    targetId: \"\",\n    tags: cherryPicking.tags?.length === 0 ? void 0 : cherryPicking.tags,\n    parent: cherryPicking.parent\n  };\n  return cherryPickDB;\n}, \"parseCherryPicking\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"gitGraph\", input);\n    log.debug(ast);\n    populate(ast, db);\n  }, \"parse\")\n};\nif (void 0) {\n  const { it, expect, describe } = void 0;\n  const mockDB = {\n    commitType,\n    setDirection: vi.fn(),\n    commit: vi.fn(),\n    branch: vi.fn(),\n    merge: vi.fn(),\n    cherryPick: vi.fn(),\n    checkout: vi.fn()\n  };\n  describe(\"GitGraph Parser\", () => {\n    it(\"should parse a commit statement\", () => {\n      const commit2 = {\n        $type: \"Commit\",\n        id: \"1\",\n        message: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: \"NORMAL\"\n      };\n      parseStatement(commit2, mockDB);\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: \"1\",\n        msg: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n    });\n    it(\"should parse a branch statement\", () => {\n      const branch2 = {\n        $type: \"Branch\",\n        name: \"newBranch\",\n        order: 1\n      };\n      parseStatement(branch2, mockDB);\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: \"newBranch\", order: 1 });\n    });\n    it(\"should parse a checkout statement\", () => {\n      const checkout2 = {\n        $type: \"Checkout\",\n        branch: \"newBranch\"\n      };\n      parseStatement(checkout2, mockDB);\n      expect(mockDB.checkout).toHaveBeenCalledWith(\"newBranch\");\n    });\n    it(\"should parse a merge statement\", () => {\n      const merge2 = {\n        $type: \"Merge\",\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: \"NORMAL\"\n      };\n      parseStatement(merge2, mockDB);\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n    });\n    it(\"should parse a cherry picking statement\", () => {\n      const cherryPick2 = {\n        $type: \"CherryPicking\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        parent: \"2\"\n      };\n      parseStatement(cherryPick2, mockDB);\n      expect(mockDB.cherryPick).toHaveBeenCalledWith({\n        id: \"1\",\n        targetId: \"\",\n        parent: \"2\",\n        tags: [\"tag1\", \"tag2\"]\n      });\n    });\n    it(\"should parse a langium generated gitGraph ast\", () => {\n      const dummy = {\n        $type: \"GitGraph\",\n        statements: []\n      };\n      const gitGraphAst = {\n        $type: \"GitGraph\",\n        statements: [\n          {\n            $container: dummy,\n            $type: \"Commit\",\n            id: \"1\",\n            message: \"test\",\n            tags: [\"tag1\", \"tag2\"],\n            type: \"NORMAL\"\n          },\n          {\n            $container: dummy,\n            $type: \"Branch\",\n            name: \"newBranch\",\n            order: 1\n          },\n          {\n            $container: dummy,\n            $type: \"Merge\",\n            branch: \"newBranch\",\n            id: \"1\",\n            tags: [\"tag1\", \"tag2\"],\n            type: \"NORMAL\"\n          },\n          {\n            $container: dummy,\n            $type: \"Checkout\",\n            branch: \"newBranch\"\n          },\n          {\n            $container: dummy,\n            $type: \"CherryPicking\",\n            id: \"1\",\n            tags: [\"tag1\", \"tag2\"],\n            parent: \"2\"\n          }\n        ]\n      };\n      populate(gitGraphAst, mockDB);\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: \"1\",\n        msg: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: \"newBranch\", order: 1 });\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n      expect(mockDB.checkout).toHaveBeenCalledWith(\"newBranch\");\n    });\n  });\n}\n\n// src/diagrams/git/gitGraphRenderer.ts\nimport { select } from \"d3\";\nvar DEFAULT_CONFIG = getConfig2();\nvar DEFAULT_GITGRAPH_CONFIG2 = DEFAULT_CONFIG?.gitGraph;\nvar LAYOUT_OFFSET = 10;\nvar COMMIT_STEP = 40;\nvar PX = 4;\nvar PY = 2;\nvar THEME_COLOR_LIMIT = 8;\nvar branchPos = /* @__PURE__ */ new Map();\nvar commitPos = /* @__PURE__ */ new Map();\nvar defaultPos = 30;\nvar allCommitsDict = /* @__PURE__ */ new Map();\nvar lanes = [];\nvar maxPos = 0;\nvar dir = \"LR\";\nvar clear3 = /* @__PURE__ */ __name(() => {\n  branchPos.clear();\n  commitPos.clear();\n  allCommitsDict.clear();\n  maxPos = 0;\n  lanes = [];\n  dir = \"LR\";\n}, \"clear\");\nvar drawText = /* @__PURE__ */ __name((txt) => {\n  const svgLabel = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n  const rows = typeof txt === \"string\" ? txt.split(/\\\\n|\\n|<br\\s*\\/?>/gi) : txt;\n  rows.forEach((row) => {\n    const tspan = document.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n    tspan.setAttributeNS(\"http://www.w3.org/XML/1998/namespace\", \"xml:space\", \"preserve\");\n    tspan.setAttribute(\"dy\", \"1em\");\n    tspan.setAttribute(\"x\", \"0\");\n    tspan.setAttribute(\"class\", \"row\");\n    tspan.textContent = row.trim();\n    svgLabel.appendChild(tspan);\n  });\n  return svgLabel;\n}, \"drawText\");\nvar findClosestParent = /* @__PURE__ */ __name((parents) => {\n  let closestParent;\n  let comparisonFunc;\n  let targetPosition;\n  if (dir === \"BT\") {\n    comparisonFunc = /* @__PURE__ */ __name((a, b) => a <= b, \"comparisonFunc\");\n    targetPosition = Infinity;\n  } else {\n    comparisonFunc = /* @__PURE__ */ __name((a, b) => a >= b, \"comparisonFunc\");\n    targetPosition = 0;\n  }\n  parents.forEach((parent) => {\n    const parentPosition = dir === \"TB\" || dir == \"BT\" ? commitPos.get(parent)?.y : commitPos.get(parent)?.x;\n    if (parentPosition !== void 0 && comparisonFunc(parentPosition, targetPosition)) {\n      closestParent = parent;\n      targetPosition = parentPosition;\n    }\n  });\n  return closestParent;\n}, \"findClosestParent\");\nvar findClosestParentBT = /* @__PURE__ */ __name((parents) => {\n  let closestParent = \"\";\n  let maxPosition = Infinity;\n  parents.forEach((parent) => {\n    const parentPosition = commitPos.get(parent).y;\n    if (parentPosition <= maxPosition) {\n      closestParent = parent;\n      maxPosition = parentPosition;\n    }\n  });\n  return closestParent || void 0;\n}, \"findClosestParentBT\");\nvar setParallelBTPos = /* @__PURE__ */ __name((sortedKeys, commits, defaultPos2) => {\n  let curPos = defaultPos2;\n  let maxPosition = defaultPos2;\n  const roots = [];\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (!commit2) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n    if (commit2.parents.length) {\n      curPos = calculateCommitPosition(commit2);\n      maxPosition = Math.max(curPos, maxPosition);\n    } else {\n      roots.push(commit2);\n    }\n    setCommitPosition(commit2, curPos);\n  });\n  curPos = maxPosition;\n  roots.forEach((commit2) => {\n    setRootPosition(commit2, curPos, defaultPos2);\n  });\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (commit2?.parents.length) {\n      const closestParent = findClosestParentBT(commit2.parents);\n      curPos = commitPos.get(closestParent).y - COMMIT_STEP;\n      if (curPos <= maxPosition) {\n        maxPosition = curPos;\n      }\n      const x = branchPos.get(commit2.branch).pos;\n      const y = curPos - LAYOUT_OFFSET;\n      commitPos.set(commit2.id, { x, y });\n    }\n  });\n}, \"setParallelBTPos\");\nvar findClosestParentPos = /* @__PURE__ */ __name((commit2) => {\n  const closestParent = findClosestParent(commit2.parents.filter((p) => p !== null));\n  if (!closestParent) {\n    throw new Error(`Closest parent not found for commit ${commit2.id}`);\n  }\n  const closestParentPos = commitPos.get(closestParent)?.y;\n  if (closestParentPos === void 0) {\n    throw new Error(`Closest parent position not found for commit ${commit2.id}`);\n  }\n  return closestParentPos;\n}, \"findClosestParentPos\");\nvar calculateCommitPosition = /* @__PURE__ */ __name((commit2) => {\n  const closestParentPos = findClosestParentPos(commit2);\n  return closestParentPos + COMMIT_STEP;\n}, \"calculateCommitPosition\");\nvar setCommitPosition = /* @__PURE__ */ __name((commit2, curPos) => {\n  const branch2 = branchPos.get(commit2.branch);\n  if (!branch2) {\n    throw new Error(`Branch not found for commit ${commit2.id}`);\n  }\n  const x = branch2.pos;\n  const y = curPos + LAYOUT_OFFSET;\n  commitPos.set(commit2.id, { x, y });\n  return { x, y };\n}, \"setCommitPosition\");\nvar setRootPosition = /* @__PURE__ */ __name((commit2, curPos, defaultPos2) => {\n  const branch2 = branchPos.get(commit2.branch);\n  if (!branch2) {\n    throw new Error(`Branch not found for commit ${commit2.id}`);\n  }\n  const y = curPos + defaultPos2;\n  const x = branch2.pos;\n  commitPos.set(commit2.id, { x, y });\n}, \"setRootPosition\");\nvar drawCommitBullet = /* @__PURE__ */ __name((gBullets, commit2, commitPosition, typeClass, branchIndex, commitSymbolType) => {\n  if (commitSymbolType === commitType.HIGHLIGHT) {\n    gBullets.append(\"rect\").attr(\"x\", commitPosition.x - 10).attr(\"y\", commitPosition.y - 10).attr(\"width\", 20).attr(\"height\", 20).attr(\n      \"class\",\n      `commit ${commit2.id} commit-highlight${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-outer`\n    );\n    gBullets.append(\"rect\").attr(\"x\", commitPosition.x - 6).attr(\"y\", commitPosition.y - 6).attr(\"width\", 12).attr(\"height\", 12).attr(\n      \"class\",\n      `commit ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-inner`\n    );\n  } else if (commitSymbolType === commitType.CHERRY_PICK) {\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x).attr(\"cy\", commitPosition.y).attr(\"r\", 10).attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x - 3).attr(\"cy\", commitPosition.y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x + 3).attr(\"cy\", commitPosition.y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"line\").attr(\"x1\", commitPosition.x + 3).attr(\"y1\", commitPosition.y + 1).attr(\"x2\", commitPosition.x).attr(\"y2\", commitPosition.y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"line\").attr(\"x1\", commitPosition.x - 3).attr(\"y1\", commitPosition.y + 1).attr(\"x2\", commitPosition.x).attr(\"y2\", commitPosition.y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n  } else {\n    const circle = gBullets.append(\"circle\");\n    circle.attr(\"cx\", commitPosition.x);\n    circle.attr(\"cy\", commitPosition.y);\n    circle.attr(\"r\", commit2.type === commitType.MERGE ? 9 : 10);\n    circle.attr(\"class\", `commit ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    if (commitSymbolType === commitType.MERGE) {\n      const circle2 = gBullets.append(\"circle\");\n      circle2.attr(\"cx\", commitPosition.x);\n      circle2.attr(\"cy\", commitPosition.y);\n      circle2.attr(\"r\", 6);\n      circle2.attr(\n        \"class\",\n        `commit ${typeClass} ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`\n      );\n    }\n    if (commitSymbolType === commitType.REVERSE) {\n      const cross = gBullets.append(\"path\");\n      cross.attr(\n        \"d\",\n        `M ${commitPosition.x - 5},${commitPosition.y - 5}L${commitPosition.x + 5},${commitPosition.y + 5}M${commitPosition.x - 5},${commitPosition.y + 5}L${commitPosition.x + 5},${commitPosition.y - 5}`\n      ).attr(\"class\", `commit ${typeClass} ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    }\n  }\n}, \"drawCommitBullet\");\nvar drawCommitLabel = /* @__PURE__ */ __name((gLabels, commit2, commitPosition, pos) => {\n  if (commit2.type !== commitType.CHERRY_PICK && (commit2.customId && commit2.type === commitType.MERGE || commit2.type !== commitType.MERGE) && DEFAULT_GITGRAPH_CONFIG2?.showCommitLabel) {\n    const wrapper = gLabels.append(\"g\");\n    const labelBkg = wrapper.insert(\"rect\").attr(\"class\", \"commit-label-bkg\");\n    const text = wrapper.append(\"text\").attr(\"x\", pos).attr(\"y\", commitPosition.y + 25).attr(\"class\", \"commit-label\").text(commit2.id);\n    const bbox = text.node()?.getBBox();\n    if (bbox) {\n      labelBkg.attr(\"x\", commitPosition.posWithOffset - bbox.width / 2 - PY).attr(\"y\", commitPosition.y + 13.5).attr(\"width\", bbox.width + 2 * PY).attr(\"height\", bbox.height + 2 * PY);\n      if (dir === \"TB\" || dir === \"BT\") {\n        labelBkg.attr(\"x\", commitPosition.x - (bbox.width + 4 * PX + 5)).attr(\"y\", commitPosition.y - 12);\n        text.attr(\"x\", commitPosition.x - (bbox.width + 4 * PX)).attr(\"y\", commitPosition.y + bbox.height - 12);\n      } else {\n        text.attr(\"x\", commitPosition.posWithOffset - bbox.width / 2);\n      }\n      if (DEFAULT_GITGRAPH_CONFIG2.rotateCommitLabel) {\n        if (dir === \"TB\" || dir === \"BT\") {\n          text.attr(\n            \"transform\",\n            \"rotate(-45, \" + commitPosition.x + \", \" + commitPosition.y + \")\"\n          );\n          labelBkg.attr(\n            \"transform\",\n            \"rotate(-45, \" + commitPosition.x + \", \" + commitPosition.y + \")\"\n          );\n        } else {\n          const r_x = -7.5 - (bbox.width + 10) / 25 * 9.5;\n          const r_y = 10 + bbox.width / 25 * 8.5;\n          wrapper.attr(\n            \"transform\",\n            \"translate(\" + r_x + \", \" + r_y + \") rotate(-45, \" + pos + \", \" + commitPosition.y + \")\"\n          );\n        }\n      }\n    }\n  }\n}, \"drawCommitLabel\");\nvar drawCommitTags = /* @__PURE__ */ __name((gLabels, commit2, commitPosition, pos) => {\n  if (commit2.tags.length > 0) {\n    let yOffset = 0;\n    let maxTagBboxWidth = 0;\n    let maxTagBboxHeight = 0;\n    const tagElements = [];\n    for (const tagValue of commit2.tags.reverse()) {\n      const rect = gLabels.insert(\"polygon\");\n      const hole = gLabels.append(\"circle\");\n      const tag = gLabels.append(\"text\").attr(\"y\", commitPosition.y - 16 - yOffset).attr(\"class\", \"tag-label\").text(tagValue);\n      const tagBbox = tag.node()?.getBBox();\n      if (!tagBbox) {\n        throw new Error(\"Tag bbox not found\");\n      }\n      maxTagBboxWidth = Math.max(maxTagBboxWidth, tagBbox.width);\n      maxTagBboxHeight = Math.max(maxTagBboxHeight, tagBbox.height);\n      tag.attr(\"x\", commitPosition.posWithOffset - tagBbox.width / 2);\n      tagElements.push({\n        tag,\n        hole,\n        rect,\n        yOffset\n      });\n      yOffset += 20;\n    }\n    for (const { tag, hole, rect, yOffset: yOffset2 } of tagElements) {\n      const h2 = maxTagBboxHeight / 2;\n      const ly = commitPosition.y - 19.2 - yOffset2;\n      rect.attr(\"class\", \"tag-label-bkg\").attr(\n        \"points\",\n        `\n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly + PY}  \n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly - PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly + h2 + PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly + h2 + PY}`\n      );\n      hole.attr(\"cy\", ly).attr(\"cx\", pos - maxTagBboxWidth / 2 + PX / 2).attr(\"r\", 1.5).attr(\"class\", \"tag-hole\");\n      if (dir === \"TB\" || dir === \"BT\") {\n        const yOrigin = pos + yOffset2;\n        rect.attr(\"class\", \"tag-label-bkg\").attr(\n          \"points\",\n          `\n        ${commitPosition.x},${yOrigin + 2}\n        ${commitPosition.x},${yOrigin - 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin + h2 + 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin + h2 + 2}`\n        ).attr(\"transform\", \"translate(12,12) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n        hole.attr(\"cx\", commitPosition.x + PX / 2).attr(\"cy\", yOrigin).attr(\"transform\", \"translate(12,12) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n        tag.attr(\"x\", commitPosition.x + 5).attr(\"y\", yOrigin + 3).attr(\"transform\", \"translate(14,14) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n      }\n    }\n  }\n}, \"drawCommitTags\");\nvar getCommitClassType = /* @__PURE__ */ __name((commit2) => {\n  const commitSymbolType = commit2.customType ?? commit2.type;\n  switch (commitSymbolType) {\n    case commitType.NORMAL:\n      return \"commit-normal\";\n    case commitType.REVERSE:\n      return \"commit-reverse\";\n    case commitType.HIGHLIGHT:\n      return \"commit-highlight\";\n    case commitType.MERGE:\n      return \"commit-merge\";\n    case commitType.CHERRY_PICK:\n      return \"commit-cherry-pick\";\n    default:\n      return \"commit-normal\";\n  }\n}, \"getCommitClassType\");\nvar calculatePosition = /* @__PURE__ */ __name((commit2, dir2, pos, commitPos2) => {\n  const defaultCommitPosition = { x: 0, y: 0 };\n  if (commit2.parents.length > 0) {\n    const closestParent = findClosestParent(commit2.parents);\n    if (closestParent) {\n      const parentPosition = commitPos2.get(closestParent) ?? defaultCommitPosition;\n      if (dir2 === \"TB\") {\n        return parentPosition.y + COMMIT_STEP;\n      } else if (dir2 === \"BT\") {\n        const currentPosition = commitPos2.get(commit2.id) ?? defaultCommitPosition;\n        return currentPosition.y - COMMIT_STEP;\n      } else {\n        return parentPosition.x + COMMIT_STEP;\n      }\n    }\n  } else {\n    if (dir2 === \"TB\") {\n      return defaultPos;\n    } else if (dir2 === \"BT\") {\n      const currentPosition = commitPos2.get(commit2.id) ?? defaultCommitPosition;\n      return currentPosition.y - COMMIT_STEP;\n    } else {\n      return 0;\n    }\n  }\n  return 0;\n}, \"calculatePosition\");\nvar getCommitPosition = /* @__PURE__ */ __name((commit2, pos, isParallelCommits) => {\n  const posWithOffset = dir === \"BT\" && isParallelCommits ? pos : pos + LAYOUT_OFFSET;\n  const y = dir === \"TB\" || dir === \"BT\" ? posWithOffset : branchPos.get(commit2.branch)?.pos;\n  const x = dir === \"TB\" || dir === \"BT\" ? branchPos.get(commit2.branch)?.pos : posWithOffset;\n  if (x === void 0 || y === void 0) {\n    throw new Error(`Position were undefined for commit ${commit2.id}`);\n  }\n  return { x, y, posWithOffset };\n}, \"getCommitPosition\");\nvar drawCommits = /* @__PURE__ */ __name((svg, commits, modifyGraph) => {\n  if (!DEFAULT_GITGRAPH_CONFIG2) {\n    throw new Error(\"GitGraph config not found\");\n  }\n  const gBullets = svg.append(\"g\").attr(\"class\", \"commit-bullets\");\n  const gLabels = svg.append(\"g\").attr(\"class\", \"commit-labels\");\n  let pos = dir === \"TB\" || dir === \"BT\" ? defaultPos : 0;\n  const keys = [...commits.keys()];\n  const isParallelCommits = DEFAULT_GITGRAPH_CONFIG2?.parallelCommits ?? false;\n  const sortKeys = /* @__PURE__ */ __name((a, b) => {\n    const seqA = commits.get(a)?.seq;\n    const seqB = commits.get(b)?.seq;\n    return seqA !== void 0 && seqB !== void 0 ? seqA - seqB : 0;\n  }, \"sortKeys\");\n  let sortedKeys = keys.sort(sortKeys);\n  if (dir === \"BT\") {\n    if (isParallelCommits) {\n      setParallelBTPos(sortedKeys, commits, pos);\n    }\n    sortedKeys = sortedKeys.reverse();\n  }\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (!commit2) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n    if (isParallelCommits) {\n      pos = calculatePosition(commit2, dir, pos, commitPos);\n    }\n    const commitPosition = getCommitPosition(commit2, pos, isParallelCommits);\n    if (modifyGraph) {\n      const typeClass = getCommitClassType(commit2);\n      const commitSymbolType = commit2.customType ?? commit2.type;\n      const branchIndex = branchPos.get(commit2.branch)?.index ?? 0;\n      drawCommitBullet(gBullets, commit2, commitPosition, typeClass, branchIndex, commitSymbolType);\n      drawCommitLabel(gLabels, commit2, commitPosition, pos);\n      drawCommitTags(gLabels, commit2, commitPosition, pos);\n    }\n    if (dir === \"TB\" || dir === \"BT\") {\n      commitPos.set(commit2.id, { x: commitPosition.x, y: commitPosition.posWithOffset });\n    } else {\n      commitPos.set(commit2.id, { x: commitPosition.posWithOffset, y: commitPosition.y });\n    }\n    pos = dir === \"BT\" && isParallelCommits ? pos + COMMIT_STEP : pos + COMMIT_STEP + LAYOUT_OFFSET;\n    if (pos > maxPos) {\n      maxPos = pos;\n    }\n  });\n}, \"drawCommits\");\nvar shouldRerouteArrow = /* @__PURE__ */ __name((commitA, commitB, p1, p2, allCommits) => {\n  const commitBIsFurthest = dir === \"TB\" || dir === \"BT\" ? p1.x < p2.x : p1.y < p2.y;\n  const branchToGetCurve = commitBIsFurthest ? commitB.branch : commitA.branch;\n  const isOnBranchToGetCurve = /* @__PURE__ */ __name((x) => x.branch === branchToGetCurve, \"isOnBranchToGetCurve\");\n  const isBetweenCommits = /* @__PURE__ */ __name((x) => x.seq > commitA.seq && x.seq < commitB.seq, \"isBetweenCommits\");\n  return [...allCommits.values()].some((commitX) => {\n    return isBetweenCommits(commitX) && isOnBranchToGetCurve(commitX);\n  });\n}, \"shouldRerouteArrow\");\nvar findLane = /* @__PURE__ */ __name((y1, y2, depth = 0) => {\n  const candidate = y1 + Math.abs(y1 - y2) / 2;\n  if (depth > 5) {\n    return candidate;\n  }\n  const ok = lanes.every((lane) => Math.abs(lane - candidate) >= 10);\n  if (ok) {\n    lanes.push(candidate);\n    return candidate;\n  }\n  const diff = Math.abs(y1 - y2);\n  return findLane(y1, y2 - diff / 5, depth + 1);\n}, \"findLane\");\nvar drawArrow = /* @__PURE__ */ __name((svg, commitA, commitB, allCommits) => {\n  const p1 = commitPos.get(commitA.id);\n  const p2 = commitPos.get(commitB.id);\n  if (p1 === void 0 || p2 === void 0) {\n    throw new Error(`Commit positions not found for commits ${commitA.id} and ${commitB.id}`);\n  }\n  const arrowNeedsRerouting = shouldRerouteArrow(commitA, commitB, p1, p2, allCommits);\n  let arc = \"\";\n  let arc2 = \"\";\n  let radius = 0;\n  let offset = 0;\n  let colorClassNum = branchPos.get(commitB.branch)?.index;\n  if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n    colorClassNum = branchPos.get(commitA.branch)?.index;\n  }\n  let lineDef;\n  if (arrowNeedsRerouting) {\n    arc = \"A 10 10, 0, 0, 0,\";\n    arc2 = \"A 10 10, 0, 0, 1,\";\n    radius = 10;\n    offset = 10;\n    const lineY = p1.y < p2.y ? findLane(p1.y, p2.y) : findLane(p2.y, p1.y);\n    const lineX = p1.x < p2.x ? findLane(p1.x, p2.x) : findLane(p2.x, p1.x);\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc2} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc2} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === \"BT\") {\n      if (p1.x < p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc2} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc2} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY - radius} ${arc} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc2} ${p2.x} ${lineY + offset} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY + radius} ${arc2} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc} ${p2.x} ${lineY - offset} L ${p2.x} ${p2.y}`;\n      }\n    }\n  } else {\n    arc = \"A 20 20, 0, 0, 0,\";\n    arc2 = \"A 20 20, 0, 0, 1,\";\n    radius = 20;\n    offset = 20;\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = \"A 20 20, 0, 0, 0,\";\n        arc2 = \"A 20 20, 0, 0, 1,\";\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc2} ${p1.x - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x + radius} ${p1.y} ${arc} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === \"BT\") {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = \"A 20 20, 0, 0, 0,\";\n        arc2 = \"A 20 20, 0, 0, 1,\";\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc} ${p1.x - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y > p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y === p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    }\n  }\n  if (lineDef === void 0) {\n    throw new Error(\"Line definition not found\");\n  }\n  svg.append(\"path\").attr(\"d\", lineDef).attr(\"class\", \"arrow arrow\" + colorClassNum % THEME_COLOR_LIMIT);\n}, \"drawArrow\");\nvar drawArrows = /* @__PURE__ */ __name((svg, commits) => {\n  const gArrows = svg.append(\"g\").attr(\"class\", \"commit-arrows\");\n  [...commits.keys()].forEach((key) => {\n    const commit2 = commits.get(key);\n    if (commit2.parents && commit2.parents.length > 0) {\n      commit2.parents.forEach((parent) => {\n        drawArrow(gArrows, commits.get(parent), commit2, commits);\n      });\n    }\n  });\n}, \"drawArrows\");\nvar drawBranches = /* @__PURE__ */ __name((svg, branches) => {\n  const g = svg.append(\"g\");\n  branches.forEach((branch2, index) => {\n    const adjustIndexForTheme = index % THEME_COLOR_LIMIT;\n    const pos = branchPos.get(branch2.name)?.pos;\n    if (pos === void 0) {\n      throw new Error(`Position not found for branch ${branch2.name}`);\n    }\n    const line = g.append(\"line\");\n    line.attr(\"x1\", 0);\n    line.attr(\"y1\", pos);\n    line.attr(\"x2\", maxPos);\n    line.attr(\"y2\", pos);\n    line.attr(\"class\", \"branch branch\" + adjustIndexForTheme);\n    if (dir === \"TB\") {\n      line.attr(\"y1\", defaultPos);\n      line.attr(\"x1\", pos);\n      line.attr(\"y2\", maxPos);\n      line.attr(\"x2\", pos);\n    } else if (dir === \"BT\") {\n      line.attr(\"y1\", maxPos);\n      line.attr(\"x1\", pos);\n      line.attr(\"y2\", defaultPos);\n      line.attr(\"x2\", pos);\n    }\n    lanes.push(pos);\n    const name = branch2.name;\n    const labelElement = drawText(name);\n    const bkg = g.insert(\"rect\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\" + adjustIndexForTheme);\n    label.node().appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n    bkg.attr(\"class\", \"branchLabelBkg label\" + adjustIndexForTheme).attr(\"rx\", 4).attr(\"ry\", 4).attr(\"x\", -bbox.width - 4 - (DEFAULT_GITGRAPH_CONFIG2?.rotateCommitLabel === true ? 30 : 0)).attr(\"y\", -bbox.height / 2 + 8).attr(\"width\", bbox.width + 18).attr(\"height\", bbox.height + 4);\n    label.attr(\n      \"transform\",\n      \"translate(\" + (-bbox.width - 14 - (DEFAULT_GITGRAPH_CONFIG2?.rotateCommitLabel === true ? 30 : 0)) + \", \" + (pos - bbox.height / 2 - 1) + \")\"\n    );\n    if (dir === \"TB\") {\n      bkg.attr(\"x\", pos - bbox.width / 2 - 10).attr(\"y\", 0);\n      label.attr(\"transform\", \"translate(\" + (pos - bbox.width / 2 - 5) + \", 0)\");\n    } else if (dir === \"BT\") {\n      bkg.attr(\"x\", pos - bbox.width / 2 - 10).attr(\"y\", maxPos);\n      label.attr(\"transform\", \"translate(\" + (pos - bbox.width / 2 - 5) + \", \" + maxPos + \")\");\n    } else {\n      bkg.attr(\"transform\", \"translate(-19, \" + (pos - bbox.height / 2) + \")\");\n    }\n  });\n}, \"drawBranches\");\nvar setBranchPosition = /* @__PURE__ */ __name(function(name, pos, index, bbox, rotateCommitLabel) {\n  branchPos.set(name, { pos, index });\n  pos += 50 + (rotateCommitLabel ? 40 : 0) + (dir === \"TB\" || dir === \"BT\" ? bbox.width / 2 : 0);\n  return pos;\n}, \"setBranchPosition\");\nvar draw = /* @__PURE__ */ __name(function(txt, id, ver, diagObj) {\n  clear3();\n  log.debug(\"in gitgraph renderer\", txt + \"\\n\", \"id:\", id, ver);\n  if (!DEFAULT_GITGRAPH_CONFIG2) {\n    throw new Error(\"GitGraph config not found\");\n  }\n  const rotateCommitLabel = DEFAULT_GITGRAPH_CONFIG2.rotateCommitLabel ?? false;\n  const db2 = diagObj.db;\n  allCommitsDict = db2.getCommits();\n  const branches = db2.getBranchesAsObjArray();\n  dir = db2.getDirection();\n  const diagram2 = select(`[id=\"${id}\"]`);\n  let pos = 0;\n  branches.forEach((branch2, index) => {\n    const labelElement = drawText(branch2.name);\n    const g = diagram2.append(\"g\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\");\n    label.node()?.appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n    pos = setBranchPosition(branch2.name, pos, index, bbox, rotateCommitLabel);\n    label.remove();\n    branchLabel.remove();\n    g.remove();\n  });\n  drawCommits(diagram2, allCommitsDict, false);\n  if (DEFAULT_GITGRAPH_CONFIG2.showBranches) {\n    drawBranches(diagram2, branches);\n  }\n  drawArrows(diagram2, allCommitsDict);\n  drawCommits(diagram2, allCommitsDict, true);\n  utils_default.insertTitle(\n    diagram2,\n    \"gitTitleText\",\n    DEFAULT_GITGRAPH_CONFIG2.titleTopMargin ?? 0,\n    db2.getDiagramTitle()\n  );\n  setupGraphViewbox(\n    void 0,\n    diagram2,\n    DEFAULT_GITGRAPH_CONFIG2.diagramPadding,\n    DEFAULT_GITGRAPH_CONFIG2.useMaxWidth\n  );\n}, \"draw\");\nvar gitGraphRenderer_default = {\n  draw\n};\nif (void 0) {\n  const { it, expect, describe } = void 0;\n  describe(\"drawText\", () => {\n    it(\"should drawText\", () => {\n      const svgLabel = drawText(\"main\");\n      expect(svgLabel).toBeDefined();\n      expect(svgLabel.children[0].innerHTML).toBe(\"main\");\n    });\n  });\n  describe(\"branchPosition\", () => {\n    const bbox = {\n      x: 0,\n      y: 0,\n      width: 10,\n      height: 10,\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      toJSON: /* @__PURE__ */ __name(() => \"\", \"toJSON\")\n    };\n    it(\"should setBranchPositions LR with two branches\", () => {\n      dir = \"LR\";\n      const pos = setBranchPosition(\"main\", 0, 0, bbox, true);\n      expect(pos).toBe(90);\n      expect(branchPos.get(\"main\")).toEqual({ pos: 0, index: 0 });\n      const posNext = setBranchPosition(\"develop\", pos, 1, bbox, true);\n      expect(posNext).toBe(180);\n      expect(branchPos.get(\"develop\")).toEqual({ pos, index: 1 });\n    });\n    it(\"should setBranchPositions TB with two branches\", () => {\n      dir = \"TB\";\n      bbox.width = 34.9921875;\n      const pos = setBranchPosition(\"main\", 0, 0, bbox, true);\n      expect(pos).toBe(107.49609375);\n      expect(branchPos.get(\"main\")).toEqual({ pos: 0, index: 0 });\n      bbox.width = 56.421875;\n      const posNext = setBranchPosition(\"develop\", pos, 1, bbox, true);\n      expect(posNext).toBe(225.70703125);\n      expect(branchPos.get(\"develop\")).toEqual({ pos, index: 1 });\n    });\n  });\n  describe(\"commitPosition\", () => {\n    const commits = /* @__PURE__ */ new Map([\n      [\n        \"commitZero\",\n        {\n          id: \"ZERO\",\n          message: \"\",\n          seq: 0,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"commitA\",\n        {\n          id: \"A\",\n          message: \"\",\n          seq: 1,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"ZERO\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"commitB\",\n        {\n          id: \"B\",\n          message: \"\",\n          seq: 2,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"A\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"commitM\",\n        {\n          id: \"M\",\n          message: \"merged branch feature into main\",\n          seq: 3,\n          type: commitType.MERGE,\n          tags: [],\n          parents: [\"ZERO\", \"B\"],\n          branch: \"main\",\n          customId: true\n        }\n      ],\n      [\n        \"commitC\",\n        {\n          id: \"C\",\n          message: \"\",\n          seq: 4,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"ZERO\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commit5_8928ea0\",\n        {\n          id: \"5-8928ea0\",\n          message: \"cherry-picked [object Object] into release\",\n          seq: 5,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: [\"C\", \"M\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commitD\",\n        {\n          id: \"D\",\n          message: \"\",\n          seq: 6,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"5-8928ea0\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commit7_ed848ba\",\n        {\n          id: \"7-ed848ba\",\n          message: \"cherry-picked [object Object] into release\",\n          seq: 7,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: [\"D\", \"M\"],\n          branch: \"release\"\n        }\n      ]\n    ]);\n    let pos = 0;\n    branchPos.set(\"main\", { pos: 0, index: 0 });\n    branchPos.set(\"feature\", { pos: 107.49609375, index: 1 });\n    branchPos.set(\"release\", { pos: 224.03515625, index: 2 });\n    describe(\"TB\", () => {\n      pos = 30;\n      dir = \"TB\";\n      const expectedCommitPositionTB = /* @__PURE__ */ new Map([\n        [\"commitZero\", { x: 0, y: 40, posWithOffset: 40 }],\n        [\"commitA\", { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        [\"commitB\", { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        [\"commitM\", { x: 0, y: 190, posWithOffset: 190 }],\n        [\"commitC\", { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        [\"commit5_8928ea0\", { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        [\"commitD\", { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        [\"commit7_ed848ba\", { x: 224.03515625, y: 390, posWithOffset: 390 }]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit2, pos, false);\n          expect(position).toEqual(expectedCommitPositionTB.get(key));\n          pos += 50;\n        });\n      });\n    });\n    describe(\"LR\", () => {\n      let pos2 = 30;\n      dir = \"LR\";\n      const expectedCommitPositionLR = /* @__PURE__ */ new Map([\n        [\"commitZero\", { x: 0, y: 40, posWithOffset: 40 }],\n        [\"commitA\", { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        [\"commitB\", { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        [\"commitM\", { x: 0, y: 190, posWithOffset: 190 }],\n        [\"commitC\", { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        [\"commit5_8928ea0\", { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        [\"commitD\", { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        [\"commit7_ed848ba\", { x: 224.03515625, y: 390, posWithOffset: 390 }]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit2, pos2, false);\n          expect(position).toEqual(expectedCommitPositionLR.get(key));\n          pos2 += 50;\n        });\n      });\n    });\n    describe(\"getCommitClassType\", () => {\n      const expectedCommitClassType = /* @__PURE__ */ new Map([\n        [\"commitZero\", \"commit-normal\"],\n        [\"commitA\", \"commit-normal\"],\n        [\"commitB\", \"commit-normal\"],\n        [\"commitM\", \"commit-merge\"],\n        [\"commitC\", \"commit-normal\"],\n        [\"commit5_8928ea0\", \"commit-cherry-pick\"],\n        [\"commitD\", \"commit-normal\"],\n        [\"commit7_ed848ba\", \"commit-cherry-pick\"]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct class type for commit ${key}`, () => {\n          const classType = getCommitClassType(commit2);\n          expect(classType).toBe(expectedCommitClassType.get(key));\n        });\n      });\n    });\n  });\n  describe(\"building BT parallel commit diagram\", () => {\n    const commits = /* @__PURE__ */ new Map([\n      [\n        \"1-abcdefg\",\n        {\n          id: \"1-abcdefg\",\n          message: \"\",\n          seq: 0,\n          type: 0,\n          tags: [],\n          parents: [],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"2-abcdefg\",\n        {\n          id: \"2-abcdefg\",\n          message: \"\",\n          seq: 1,\n          type: 0,\n          tags: [],\n          parents: [\"1-abcdefg\"],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"3-abcdefg\",\n        {\n          id: \"3-abcdefg\",\n          message: \"\",\n          seq: 2,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"develop\"\n        }\n      ],\n      [\n        \"4-abcdefg\",\n        {\n          id: \"4-abcdefg\",\n          message: \"\",\n          seq: 3,\n          type: 0,\n          tags: [],\n          parents: [\"3-abcdefg\"],\n          branch: \"develop\"\n        }\n      ],\n      [\n        \"5-abcdefg\",\n        {\n          id: \"5-abcdefg\",\n          message: \"\",\n          seq: 4,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"6-abcdefg\",\n        {\n          id: \"6-abcdefg\",\n          message: \"\",\n          seq: 5,\n          type: 0,\n          tags: [],\n          parents: [\"5-abcdefg\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"7-abcdefg\",\n        {\n          id: \"7-abcdefg\",\n          message: \"\",\n          seq: 6,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"8-abcdefg\",\n        {\n          id: \"8-abcdefg\",\n          message: \"\",\n          seq: 7,\n          type: 0,\n          tags: [],\n          parents: [\"7-abcdefg\"],\n          branch: \"main\"\n        }\n      ]\n    ]);\n    const expectedCommitPosition = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", { x: 0, y: 40 }],\n      [\"2-abcdefg\", { x: 0, y: 90 }],\n      [\"3-abcdefg\", { x: 107.49609375, y: 140 }],\n      [\"4-abcdefg\", { x: 107.49609375, y: 190 }],\n      [\"5-abcdefg\", { x: 225.70703125, y: 140 }],\n      [\"6-abcdefg\", { x: 225.70703125, y: 190 }],\n      [\"7-abcdefg\", { x: 0, y: 140 }],\n      [\"8-abcdefg\", { x: 0, y: 190 }]\n    ]);\n    const expectedCommitPositionAfterParallel = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", { x: 0, y: 210 }],\n      [\"2-abcdefg\", { x: 0, y: 160 }],\n      [\"3-abcdefg\", { x: 107.49609375, y: 110 }],\n      [\"4-abcdefg\", { x: 107.49609375, y: 60 }],\n      [\"5-abcdefg\", { x: 225.70703125, y: 110 }],\n      [\"6-abcdefg\", { x: 225.70703125, y: 60 }],\n      [\"7-abcdefg\", { x: 0, y: 110 }],\n      [\"8-abcdefg\", { x: 0, y: 60 }]\n    ]);\n    const expectedCommitCurrentPosition = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", 30],\n      [\"2-abcdefg\", 80],\n      [\"3-abcdefg\", 130],\n      [\"4-abcdefg\", 180],\n      [\"5-abcdefg\", 130],\n      [\"6-abcdefg\", 180],\n      [\"7-abcdefg\", 130],\n      [\"8-abcdefg\", 180]\n    ]);\n    const sortedKeys = [...expectedCommitPosition.keys()];\n    it(\"should get the correct commit position and current position\", () => {\n      dir = \"BT\";\n      let curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set(\"main\", { pos: 0, index: 0 });\n      branchPos.set(\"develop\", { pos: 107.49609375, index: 1 });\n      branchPos.set(\"feature\", { pos: 225.70703125, index: 2 });\n      DEFAULT_GITGRAPH_CONFIG2.parallelCommits = true;\n      commits.forEach((commit2, key) => {\n        if (commit2.parents.length > 0) {\n          curPos = calculateCommitPosition(commit2);\n        }\n        const position = setCommitPosition(commit2, curPos);\n        expect(position).toEqual(expectedCommitPosition.get(key));\n        expect(curPos).toEqual(expectedCommitCurrentPosition.get(key));\n      });\n    });\n    it(\"should get the correct commit position after parallel commits\", () => {\n      commitPos.clear();\n      branchPos.clear();\n      dir = \"BT\";\n      const curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set(\"main\", { pos: 0, index: 0 });\n      branchPos.set(\"develop\", { pos: 107.49609375, index: 1 });\n      branchPos.set(\"feature\", { pos: 225.70703125, index: 2 });\n      setParallelBTPos(sortedKeys, commits, curPos);\n      sortedKeys.forEach((commit2) => {\n        const position = commitPos.get(commit2);\n        expect(position).toEqual(expectedCommitPositionAfterParallel.get(commit2));\n      });\n    });\n  });\n  DEFAULT_GITGRAPH_CONFIG2.parallelCommits = false;\n  it(\"add\", () => {\n    commitPos.set(\"parent1\", { x: 1, y: 1 });\n    commitPos.set(\"parent2\", { x: 2, y: 2 });\n    commitPos.set(\"parent3\", { x: 3, y: 3 });\n    dir = \"LR\";\n    const parents = [\"parent1\", \"parent2\", \"parent3\"];\n    const closestParent = findClosestParent(parents);\n    expect(closestParent).toBe(\"parent3\");\n    commitPos.clear();\n  });\n}\n\n// src/diagrams/git/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .commit-id,\n  .commit-msg,\n  .branch-label {\n    fill: lightgrey;\n    color: lightgrey;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n  }\n  ${[0, 1, 2, 3, 4, 5, 6, 7].map(\n  (i) => `\n        .branch-label${i} { fill: ${options[\"gitBranchLabel\" + i]}; }\n        .commit${i} { stroke: ${options[\"git\" + i]}; fill: ${options[\"git\" + i]}; }\n        .commit-highlight${i} { stroke: ${options[\"gitInv\" + i]}; fill: ${options[\"gitInv\" + i]}; }\n        .label${i}  { fill: ${options[\"git\" + i]}; }\n        .arrow${i} { stroke: ${options[\"git\" + i]}; }\n        `\n).join(\"\\n\")}\n\n  .branch {\n    stroke-width: 1;\n    stroke: ${options.lineColor};\n    stroke-dasharray: 2;\n  }\n  .commit-label { font-size: ${options.commitLabelFontSize}; fill: ${options.commitLabelColor};}\n  .commit-label-bkg { font-size: ${options.commitLabelFontSize}; fill: ${options.commitLabelBackground}; opacity: 0.5; }\n  .tag-label { font-size: ${options.tagLabelFontSize}; fill: ${options.tagLabelColor};}\n  .tag-label-bkg { fill: ${options.tagLabelBackground}; stroke: ${options.tagLabelBorder}; }\n  .tag-hole { fill: ${options.textColor}; }\n\n  .commit-merge {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n  .commit-reverse {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n    stroke-width: 3;\n  }\n  .commit-highlight-outer {\n  }\n  .commit-highlight-inner {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n\n  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}\n  .gitTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/git/gitGraphDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer: gitGraphRenderer_default,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "mappings": "uqBAgCA,IAAIA,EAAa,CACf,OAAQ,EACR,QAAS,EACT,UAAW,EACX,MAAO,EACP,YAAa,CACf,EAGIC,GAA0BC,EAAsB,SAChDC,EAA6BC,EAAO,IACvBC,EAAc,CAC3B,GAAGJ,GACH,GAAGK,EAAU,EAAE,QACjB,CAAC,EAEA,WAAW,EACVC,EAAQ,IAAIC,GAAgB,IAAM,CACpC,IAAMC,EAASN,EAAW,EACpBO,EAAiBD,EAAO,eACxBE,EAAkBF,EAAO,gBAC/B,MAAO,CACL,eAAAC,EACA,QAAyB,IAAI,IAC7B,KAAM,KACN,aAA8B,IAAI,IAAI,CAAC,CAACA,EAAgB,CAAE,KAAMA,EAAgB,MAAOC,CAAgB,CAAC,CAAC,CAAC,EAC1G,SAA0B,IAAI,IAAI,CAAC,CAACD,EAAgB,IAAI,CAAC,CAAC,EAC1D,WAAYA,EACZ,UAAW,KACX,IAAK,EACL,QAAS,CAAC,CACZ,CACF,CAAC,EACD,SAASE,GAAQ,CACf,OAAOC,EAAO,CAAE,OAAQ,CAAE,CAAC,CAC7B,CACAT,EAAOQ,EAAO,OAAO,EACrB,SAASE,GAAOC,EAAMC,EAAI,CACxB,IAAMC,EAA4B,OAAO,OAAO,IAAI,EACpD,OAAOF,EAAK,OAAO,CAACG,EAAKC,IAAS,CAChC,IAAMC,EAAMJ,EAAGG,CAAI,EACnB,OAAKF,EAAUG,CAAG,IAChBH,EAAUG,CAAG,EAAI,GACjBF,EAAI,KAAKC,CAAI,GAERD,CACT,EAAG,CAAC,CAAC,CACP,CACAd,EAAOU,GAAQ,QAAQ,EACvB,IAAIO,GAA+BjB,EAAO,SAASkB,EAAM,CACvDf,EAAM,QAAQ,UAAYe,CAC5B,EAAG,cAAc,EACbC,GAA6BnB,EAAO,SAASoB,EAAc,CAC7DC,EAAI,MAAM,cAAeD,CAAY,EACrCA,EAAeA,GAAc,KAAK,EAClCA,EAAeA,GAAgB,KAC/B,GAAI,CACFjB,EAAM,QAAQ,QAAU,KAAK,MAAMiB,CAAY,CACjD,OAAS,EAAG,CACVC,EAAI,MAAM,uCAAwC,EAAE,OAAO,CAC7D,CACF,EAAG,YAAY,EACXC,GAA6BtB,EAAO,UAAW,CACjD,OAAOG,EAAM,QAAQ,OACvB,EAAG,YAAY,EACXoB,GAAyBvB,EAAO,SAASwB,EAAU,CACrD,IAAIC,EAAMD,EAAS,IACfE,EAAKF,EAAS,GACZG,EAAOH,EAAS,KAClBI,EAAOJ,EAAS,KACpBH,EAAI,KAAK,SAAUI,EAAKC,EAAIC,EAAMC,CAAI,EACtCP,EAAI,MAAM,mBAAoBI,EAAKC,EAAIC,EAAMC,CAAI,EACjD,IAAMvB,EAASN,EAAW,EAC1B2B,EAAKG,EAAe,aAAaH,EAAIrB,CAAM,EAC3CoB,EAAMI,EAAe,aAAaJ,EAAKpB,CAAM,EAC7CuB,EAAOA,GAAM,IAAKE,GAAQD,EAAe,aAAaC,EAAKzB,CAAM,CAAC,EAClE,IAAM0B,EAAY,CAChB,GAAIL,GAAUvB,EAAM,QAAQ,IAAM,IAAMK,EAAM,EAC9C,QAASiB,EACT,IAAKtB,EAAM,QAAQ,MACnB,KAAMwB,GAAQ/B,EAAW,OACzB,KAAMgC,GAAQ,CAAC,EACf,QAASzB,EAAM,QAAQ,MAAQ,KAAO,CAAC,EAAI,CAACA,EAAM,QAAQ,KAAK,EAAE,EACjE,OAAQA,EAAM,QAAQ,UACxB,EACAA,EAAM,QAAQ,KAAO4B,EACrBV,EAAI,KAAK,cAAehB,EAAO,cAAc,EAC7CF,EAAM,QAAQ,QAAQ,IAAI4B,EAAU,GAAIA,CAAS,EACjD5B,EAAM,QAAQ,SAAS,IAAIA,EAAM,QAAQ,WAAY4B,EAAU,EAAE,EACjEV,EAAI,MAAM,iBAAmBU,EAAU,EAAE,CAC3C,EAAG,QAAQ,EACPC,GAAyBhC,EAAO,SAASiC,EAAU,CACrD,IAAIC,EAAOD,EAAS,KACdE,EAAQF,EAAS,MAEvB,GADAC,EAAOL,EAAe,aAAaK,EAAMnC,EAAW,CAAC,EACjDI,EAAM,QAAQ,SAAS,IAAI+B,CAAI,EACjC,MAAM,IAAI,MACR,4HAA4HA,CAAI,IAClI,EAEF/B,EAAM,QAAQ,SAAS,IAAI+B,EAAM/B,EAAM,QAAQ,MAAQ,KAAOA,EAAM,QAAQ,KAAK,GAAK,IAAI,EAC1FA,EAAM,QAAQ,aAAa,IAAI+B,EAAM,CAAE,KAAAA,EAAM,MAAAC,CAAM,CAAC,EACpDC,GAASF,CAAI,EACbb,EAAI,MAAM,iBAAiB,CAC7B,EAAG,QAAQ,EACPgB,GAAwBrC,EAAQsC,GAAY,CAC9C,IAAIC,EAAcD,EAAQ,OACtBE,EAAWF,EAAQ,GACjBG,EAAeH,EAAQ,KACvBI,EAAaJ,EAAQ,KACrBjC,EAASN,EAAW,EAC1BwC,EAAcV,EAAe,aAAaU,EAAalC,CAAM,EACzDmC,IACFA,EAAWX,EAAe,aAAaW,EAAUnC,CAAM,GAEzD,IAAMsC,EAAqBxC,EAAM,QAAQ,SAAS,IAAIA,EAAM,QAAQ,UAAU,EACxEyC,EAAmBzC,EAAM,QAAQ,SAAS,IAAIoC,CAAW,EACzDM,EAAgBF,EAAqBxC,EAAM,QAAQ,QAAQ,IAAIwC,CAAkB,EAAI,OACrFG,EAAcF,EAAmBzC,EAAM,QAAQ,QAAQ,IAAIyC,CAAgB,EAAI,OACrF,GAAIC,GAAiBC,GAAeD,EAAc,SAAWN,EAC3D,MAAM,IAAI,MAAM,wBAAwBA,CAAW,gBAAgB,EAErE,GAAIpC,EAAM,QAAQ,aAAeoC,EAAa,CAC5C,IAAMQ,EAAQ,IAAI,MAAM,6DAA6D,EACrF,MAAAA,EAAM,KAAO,CACX,KAAM,SAASR,CAAW,GAC1B,MAAO,SAASA,CAAW,GAC3B,SAAU,CAAC,YAAY,CACzB,EACMQ,CACR,CACA,GAAIF,IAAkB,QAAU,CAACA,EAAe,CAC9C,IAAME,EAAQ,IAAI,MAChB,+CAA+C5C,EAAM,QAAQ,UAAU,iBACzE,EACA,MAAA4C,EAAM,KAAO,CACX,KAAM,SAASR,CAAW,GAC1B,MAAO,SAASA,CAAW,GAC3B,SAAU,CAAC,QAAQ,CACrB,EACMQ,CACR,CACA,GAAI,CAAC5C,EAAM,QAAQ,SAAS,IAAIoC,CAAW,EAAG,CAC5C,IAAMQ,EAAQ,IAAI,MAChB,oDAAsDR,EAAc,kBACtE,EACA,MAAAQ,EAAM,KAAO,CACX,KAAM,SAASR,CAAW,GAC1B,MAAO,SAASA,CAAW,GAC3B,SAAU,CAAC,UAAUA,CAAW,EAAE,CACpC,EACMQ,CACR,CACA,GAAID,IAAgB,QAAU,CAACA,EAAa,CAC1C,IAAMC,EAAQ,IAAI,MAChB,oDAAsDR,EAAc,kBACtE,EACA,MAAAQ,EAAM,KAAO,CACX,KAAM,SAASR,CAAW,GAC1B,MAAO,SAASA,CAAW,GAC3B,SAAU,CAAC,UAAU,CACvB,EACMQ,CACR,CACA,GAAIF,IAAkBC,EAAa,CACjC,IAAMC,EAAQ,IAAI,MAAM,0DAA0D,EAClF,MAAAA,EAAM,KAAO,CACX,KAAM,SAASR,CAAW,GAC1B,MAAO,SAASA,CAAW,GAC3B,SAAU,CAAC,YAAY,CACzB,EACMQ,CACR,CACA,GAAIP,GAAYrC,EAAM,QAAQ,QAAQ,IAAIqC,CAAQ,EAAG,CACnD,IAAMO,EAAQ,IAAI,MAChB,8CAAgDP,EAAW,0CAC7D,EACA,MAAAO,EAAM,KAAO,CACX,KAAM,SAASR,CAAW,IAAIC,CAAQ,IAAIC,CAAY,IAAIC,GAAY,KAAK,GAAG,CAAC,GAC/E,MAAO,SAASH,CAAW,IAAIC,CAAQ,IAAIC,CAAY,IAAIC,GAAY,KAAK,GAAG,CAAC,GAChF,SAAU,CACR,SAASH,CAAW,IAAIC,CAAQ,WAAWC,CAAY,IAAIC,GAAY,KAAK,GAAG,CAAC,EAClF,CACF,EACMK,CACR,CACA,IAAMC,EAAiBJ,GAAsC,GACvDK,EAAU,CACd,GAAIT,GAAY,GAAGrC,EAAM,QAAQ,GAAG,IAAIK,EAAM,CAAC,GAC/C,QAAS,iBAAiB+B,CAAW,SAASpC,EAAM,QAAQ,UAAU,GACtE,IAAKA,EAAM,QAAQ,MACnB,QAASA,EAAM,QAAQ,MAAQ,KAAO,CAAC,EAAI,CAACA,EAAM,QAAQ,KAAK,GAAI6C,CAAc,EACjF,OAAQ7C,EAAM,QAAQ,WACtB,KAAMP,EAAW,MACjB,WAAY6C,EACZ,SAAU,EAAAD,EACV,KAAME,GAAc,CAAC,CACvB,EACAvC,EAAM,QAAQ,KAAO8C,EACrB9C,EAAM,QAAQ,QAAQ,IAAI8C,EAAQ,GAAIA,CAAO,EAC7C9C,EAAM,QAAQ,SAAS,IAAIA,EAAM,QAAQ,WAAY8C,EAAQ,EAAE,EAC/D5B,EAAI,MAAMlB,EAAM,QAAQ,QAAQ,EAChCkB,EAAI,MAAM,gBAAgB,CAC5B,EAAG,OAAO,EACN6B,GAA6BlD,EAAO,SAASmD,EAAc,CAC7D,IAAIC,EAAWD,EAAa,GACxBE,EAAWF,EAAa,SACxBvB,EAAOuB,EAAa,KACpBG,EAAiBH,EAAa,OAClC9B,EAAI,MAAM,uBAAwB+B,EAAUC,EAAUzB,CAAI,EAC1D,IAAMvB,EAASN,EAAW,EAK1B,GAJAqD,EAAWvB,EAAe,aAAauB,EAAU/C,CAAM,EACvDgD,EAAWxB,EAAe,aAAawB,EAAUhD,CAAM,EACvDuB,EAAOA,GAAM,IAAKE,GAAQD,EAAe,aAAaC,EAAKzB,CAAM,CAAC,EAClEiD,EAAiBzB,EAAe,aAAayB,EAAgBjD,CAAM,EAC/D,CAAC+C,GAAY,CAACjD,EAAM,QAAQ,QAAQ,IAAIiD,CAAQ,EAAG,CACrD,IAAML,EAAQ,IAAI,MAChB,6EACF,EACA,MAAAA,EAAM,KAAO,CACX,KAAM,cAAcK,CAAQ,IAAIC,CAAQ,GACxC,MAAO,cAAcD,CAAQ,IAAIC,CAAQ,GACzC,SAAU,CAAC,iBAAiB,CAC9B,EACMN,CACR,CACA,IAAMQ,EAAepD,EAAM,QAAQ,QAAQ,IAAIiD,CAAQ,EACvD,GAAIG,IAAiB,QAAU,CAACA,EAC9B,MAAM,IAAI,MAAM,6EAA6E,EAE/F,GAAID,GAAkB,EAAE,MAAM,QAAQC,EAAa,OAAO,GAAKA,EAAa,QAAQ,SAASD,CAAc,GAIzG,MAHc,IAAI,MAChB,wGACF,EAGF,IAAME,EAAqBD,EAAa,OACxC,GAAIA,EAAa,OAAS3D,EAAW,OAAS,CAAC0D,EAI7C,MAHc,IAAI,MAChB,uHACF,EAGF,GAAI,CAACD,GAAY,CAAClD,EAAM,QAAQ,QAAQ,IAAIkD,CAAQ,EAAG,CACrD,GAAIG,IAAuBrD,EAAM,QAAQ,WAAY,CACnD,IAAM4C,EAAQ,IAAI,MAChB,6EACF,EACA,MAAAA,EAAM,KAAO,CACX,KAAM,cAAcK,CAAQ,IAAIC,CAAQ,GACxC,MAAO,cAAcD,CAAQ,IAAIC,CAAQ,GACzC,SAAU,CAAC,iBAAiB,CAC9B,EACMN,CACR,CACA,IAAMU,EAAkBtD,EAAM,QAAQ,SAAS,IAAIA,EAAM,QAAQ,UAAU,EAC3E,GAAIsD,IAAoB,QAAU,CAACA,EAAiB,CAClD,IAAMV,EAAQ,IAAI,MAChB,qDAAqD5C,EAAM,QAAQ,UAAU,iBAC/E,EACA,MAAA4C,EAAM,KAAO,CACX,KAAM,cAAcK,CAAQ,IAAIC,CAAQ,GACxC,MAAO,cAAcD,CAAQ,IAAIC,CAAQ,GACzC,SAAU,CAAC,iBAAiB,CAC9B,EACMN,CACR,CACA,IAAMF,EAAgB1C,EAAM,QAAQ,QAAQ,IAAIsD,CAAe,EAC/D,GAAIZ,IAAkB,QAAU,CAACA,EAAe,CAC9C,IAAME,EAAQ,IAAI,MAChB,qDAAqD5C,EAAM,QAAQ,UAAU,iBAC/E,EACA,MAAA4C,EAAM,KAAO,CACX,KAAM,cAAcK,CAAQ,IAAIC,CAAQ,GACxC,MAAO,cAAcD,CAAQ,IAAIC,CAAQ,GACzC,SAAU,CAAC,iBAAiB,CAC9B,EACMN,CACR,CACA,IAAME,EAAU,CACd,GAAI9C,EAAM,QAAQ,IAAM,IAAMK,EAAM,EACpC,QAAS,iBAAiB+C,GAAc,OAAO,SAASpD,EAAM,QAAQ,UAAU,GAChF,IAAKA,EAAM,QAAQ,MACnB,QAASA,EAAM,QAAQ,MAAQ,KAAO,CAAC,EAAI,CAACA,EAAM,QAAQ,KAAK,GAAIoD,EAAa,EAAE,EAClF,OAAQpD,EAAM,QAAQ,WACtB,KAAMP,EAAW,YACjB,KAAMgC,EAAOA,EAAK,OAAO,OAAO,EAAI,CAClC,eAAe2B,EAAa,EAAE,GAAGA,EAAa,OAAS3D,EAAW,MAAQ,WAAW0D,CAAc,GAAK,EAAE,EAC5G,CACF,EACAnD,EAAM,QAAQ,KAAO8C,EACrB9C,EAAM,QAAQ,QAAQ,IAAI8C,EAAQ,GAAIA,CAAO,EAC7C9C,EAAM,QAAQ,SAAS,IAAIA,EAAM,QAAQ,WAAY8C,EAAQ,EAAE,EAC/D5B,EAAI,MAAMlB,EAAM,QAAQ,QAAQ,EAChCkB,EAAI,MAAM,eAAe,CAC3B,CACF,EAAG,YAAY,EACXe,GAA2BpC,EAAO,SAAS0D,EAAS,CAEtD,GADAA,EAAU7B,EAAe,aAAa6B,EAAS3D,EAAW,CAAC,EACtDI,EAAM,QAAQ,SAAS,IAAIuD,CAAO,EAUhC,CACLvD,EAAM,QAAQ,WAAauD,EAC3B,IAAMhC,EAAKvB,EAAM,QAAQ,SAAS,IAAIA,EAAM,QAAQ,UAAU,EAC1DuB,IAAO,QAAU,CAACA,EACpBvB,EAAM,QAAQ,KAAO,KAErBA,EAAM,QAAQ,KAAOA,EAAM,QAAQ,QAAQ,IAAIuB,CAAE,GAAK,IAE1D,KAlB0C,CACxC,IAAMqB,EAAQ,IAAI,MAChB,+EAA+EW,CAAO,IACxF,EACA,MAAAX,EAAM,KAAO,CACX,KAAM,YAAYW,CAAO,GACzB,MAAO,YAAYA,CAAO,GAC1B,SAAU,CAAC,UAAUA,CAAO,EAAE,CAChC,EACMX,CACR,CASF,EAAG,UAAU,EACb,SAASY,EAAOC,EAAK5C,EAAK6C,EAAQ,CAChC,IAAMC,EAAQF,EAAI,QAAQ5C,CAAG,EACzB8C,IAAU,GACZF,EAAI,KAAKC,CAAM,EAEfD,EAAI,OAAOE,EAAO,EAAGD,CAAM,CAE/B,CACA7D,EAAO2D,EAAQ,QAAQ,EACvB,SAASI,EAAyBC,EAAW,CAC3C,IAAMf,EAAUe,EAAU,OAAO,CAAClD,EAAKmD,IACjCnD,EAAI,IAAMmD,EAAQ,IACbnD,EAEFmD,EACND,EAAU,CAAC,CAAC,EACXE,EAAO,GACXF,EAAU,QAAQ,SAASG,EAAG,CACxBA,IAAMlB,EACRiB,GAAQ,KAERA,GAAQ,IAEZ,CAAC,EACD,IAAME,EAAQ,CAACF,EAAMjB,EAAQ,GAAIA,EAAQ,GAAG,EAC5C,QAAWS,KAAWvD,EAAM,QAAQ,SAC9BA,EAAM,QAAQ,SAAS,IAAIuD,CAAO,IAAMT,EAAQ,IAClDmB,EAAM,KAAKV,CAAO,EAItB,GADArC,EAAI,MAAM+C,EAAM,KAAK,GAAG,CAAC,EACrBnB,EAAQ,SAAWA,EAAQ,QAAQ,QAAU,GAAKA,EAAQ,QAAQ,CAAC,GAAKA,EAAQ,QAAQ,CAAC,EAAG,CAC9F,IAAMlB,EAAY5B,EAAM,QAAQ,QAAQ,IAAI8C,EAAQ,QAAQ,CAAC,CAAC,EAC9DU,EAAOK,EAAWf,EAASlB,CAAS,EAChCkB,EAAQ,QAAQ,CAAC,GACnBe,EAAU,KAAK7D,EAAM,QAAQ,QAAQ,IAAI8C,EAAQ,QAAQ,CAAC,CAAC,CAAC,CAEhE,KAAO,IAAIA,EAAQ,QAAQ,QAAU,EACnC,OAEA,GAAIA,EAAQ,QAAQ,CAAC,EAAG,CACtB,IAAMlB,EAAY5B,EAAM,QAAQ,QAAQ,IAAI8C,EAAQ,QAAQ,CAAC,CAAC,EAC9DU,EAAOK,EAAWf,EAASlB,CAAS,CACtC,EAEFiC,EAAYtD,GAAOsD,EAAYG,GAAMA,EAAE,EAAE,EACzCJ,EAAyBC,CAAS,CACpC,CACAhE,EAAO+D,EAA0B,0BAA0B,EAC3D,IAAIM,GAA8BrE,EAAO,UAAW,CAClDqB,EAAI,MAAMlB,EAAM,QAAQ,OAAO,EAC/B,IAAMmE,EAAOC,GAAgB,EAAE,CAAC,EAChCR,EAAyB,CAACO,CAAI,CAAC,CACjC,EAAG,aAAa,EACZE,GAAyBxE,EAAO,UAAW,CAC7CG,EAAM,MAAM,EACZsE,EAAM,CACR,EAAG,OAAO,EACNC,GAAwC1E,EAAO,UAAW,CAU5D,MATsB,CAAC,GAAGG,EAAM,QAAQ,aAAa,OAAO,CAAC,EAAE,IAAI,CAACwE,EAAcC,IAC5ED,EAAa,QAAU,MAAQA,EAAa,QAAU,OACjDA,EAEF,CACL,GAAGA,EACH,MAAO,WAAW,KAAKC,CAAC,EAAE,CAC5B,CACD,EAAE,KAAK,CAACC,EAAGC,KAAOD,EAAE,OAAS,IAAMC,EAAE,OAAS,EAAE,EAAE,IAAI,CAAC,CAAE,KAAA5C,CAAK,KAAO,CAAE,KAAAA,CAAK,EAAE,CAEjF,EAAG,uBAAuB,EACtB6C,GAA8B/E,EAAO,UAAW,CAClD,OAAOG,EAAM,QAAQ,QACvB,EAAG,aAAa,EACZ6E,GAA6BhF,EAAO,UAAW,CACjD,OAAOG,EAAM,QAAQ,OACvB,EAAG,YAAY,EACXoE,GAAkCvE,EAAO,UAAW,CACtD,IAAMgE,EAAY,CAAC,GAAG7D,EAAM,QAAQ,QAAQ,OAAO,CAAC,EACpD,OAAA6D,EAAU,QAAQ,SAASiB,EAAG,CAC5B5D,EAAI,MAAM4D,EAAE,EAAE,CAChB,CAAC,EACDjB,EAAU,KAAK,CAACa,EAAGC,IAAMD,EAAE,IAAMC,EAAE,GAAG,EAC/Bd,CACT,EAAG,iBAAiB,EAChBkB,GAAmClF,EAAO,UAAW,CACvD,OAAOG,EAAM,QAAQ,UACvB,EAAG,kBAAkB,EACjBgF,GAA+BnF,EAAO,UAAW,CACnD,OAAOG,EAAM,QAAQ,SACvB,EAAG,cAAc,EACbiF,GAA0BpF,EAAO,UAAW,CAC9C,OAAOG,EAAM,QAAQ,IACvB,EAAG,SAAS,EACRkF,GAAK,CACP,WAAAzF,EACA,UAAWG,EACX,aAAAkB,GACA,WAAAE,GACA,WAAAG,GACA,OAAAC,GACA,OAAAS,GACA,MAAAK,GACA,WAAAa,GACA,SAAAd,GAEA,YAAAiC,GACA,MAAOG,GACP,sBAAAE,GACA,YAAAK,GACA,WAAAC,GACA,gBAAAT,GACA,iBAAAW,GACA,aAAAC,GACA,QAAAC,GACA,YAAAE,EACA,YAAAC,EACA,kBAAAC,EACA,kBAAAC,EACA,gBAAAC,EACA,gBAAAC,CACF,EAGIC,GAA2B5F,EAAO,CAAC6F,EAAKC,IAAQ,CAClDC,GAAiBF,EAAKC,CAAG,EACrBD,EAAI,KACNC,EAAI,aAAaD,EAAI,GAAG,EAE1B,QAAWG,KAAaH,EAAI,WAC1BI,GAAeD,EAAWF,CAAG,CAEjC,EAAG,UAAU,EACTG,GAAiCjG,EAAO,CAACgG,EAAWF,IAAQ,CAQ9D,IAAMI,EAPU,CACd,OAAwBlG,EAAQmG,GAASL,EAAI,OAAOM,GAAYD,CAAI,CAAC,EAAG,QAAQ,EAChF,OAAwBnG,EAAQmG,GAASL,EAAI,OAAOO,GAAYF,CAAI,CAAC,EAAG,QAAQ,EAChF,MAAuBnG,EAAQmG,GAASL,EAAI,MAAMQ,GAAWH,CAAI,CAAC,EAAG,OAAO,EAC5E,SAA0BnG,EAAQmG,GAASL,EAAI,SAASS,GAAcJ,CAAI,CAAC,EAAG,UAAU,EACxF,cAA+BnG,EAAQmG,GAASL,EAAI,WAAWU,GAAmBL,CAAI,CAAC,EAAG,eAAe,CAC3G,EACwBH,EAAU,KAAK,EACnCE,EACFA,EAAQF,CAAS,EAEjB3E,EAAI,MAAM,2BAA2B2E,EAAU,KAAK,EAAE,CAE1D,EAAG,gBAAgB,EACfI,GAA8BpG,EAAQiD,IACvB,CACf,GAAIA,EAAQ,GACZ,IAAKA,EAAQ,SAAW,GACxB,KAAMA,EAAQ,OAAS,OAASrD,EAAWqD,EAAQ,IAAI,EAAIrD,EAAW,OACtE,KAAMqD,EAAQ,MAAQ,MACxB,GAEC,aAAa,EACZoD,GAA8BrG,EAAQ0D,IACvB,CACf,KAAMA,EAAQ,KACd,MAAOA,EAAQ,OAAS,CAC1B,GAEC,aAAa,EACZ4C,GAA6BtG,EAAQyG,IACvB,CACd,OAAQA,EAAO,OACf,GAAIA,EAAO,IAAM,GACjB,KAAMA,EAAO,OAAS,OAAS7G,EAAW6G,EAAO,IAAI,EAAI,OACzD,KAAMA,EAAO,MAAQ,MACvB,GAEC,YAAY,EACXF,GAAgCvG,EAAQ0G,GAC1BA,EAAU,OAEzB,eAAe,EACdF,GAAqCxG,EAAQ2G,IAC1B,CACnB,GAAIA,EAAc,GAClB,SAAU,GACV,KAAMA,EAAc,MAAM,SAAW,EAAI,OAASA,EAAc,KAChE,OAAQA,EAAc,MACxB,GAEC,oBAAoB,EACnBC,GAAS,CACX,MAAuB5G,EAAO,MAAO6G,GAAU,CAC7C,IAAMhB,EAAM,MAAMiB,GAAM,WAAYD,CAAK,EACzCxF,EAAI,MAAMwE,CAAG,EACbD,GAASC,EAAKR,EAAE,CAClB,EAAG,OAAO,CACZ,EA8II0B,GAAiBC,EAAW,EAC5BC,EAA2BF,IAAgB,SAC3CG,EAAgB,GAChBC,EAAc,GACdC,EAAK,EACLC,EAAK,EACLC,EAAoB,EACpBC,EAA4B,IAAI,IAChCC,EAA4B,IAAI,IAChCC,EAAa,GACbC,EAAiC,IAAI,IACrCC,EAAQ,CAAC,EACTC,EAAS,EACTC,EAAM,KACNC,GAAyB9H,EAAO,IAAM,CACxCuH,EAAU,MAAM,EAChBC,EAAU,MAAM,EAChBE,EAAe,MAAM,EACrBE,EAAS,EACTD,EAAQ,CAAC,EACTE,EAAM,IACR,EAAG,OAAO,EACNE,GAA2B/H,EAAQgI,GAAQ,CAC7C,IAAMC,EAAW,SAAS,gBAAgB,6BAA8B,MAAM,EAE9E,OADa,OAAOD,GAAQ,SAAWA,EAAI,MAAM,qBAAqB,EAAIA,GACrE,QAASE,GAAQ,CACpB,IAAMC,EAAQ,SAAS,gBAAgB,6BAA8B,OAAO,EAC5EA,EAAM,eAAe,uCAAwC,YAAa,UAAU,EACpFA,EAAM,aAAa,KAAM,KAAK,EAC9BA,EAAM,aAAa,IAAK,GAAG,EAC3BA,EAAM,aAAa,QAAS,KAAK,EACjCA,EAAM,YAAcD,EAAI,KAAK,EAC7BD,EAAS,YAAYE,CAAK,CAC5B,CAAC,EACMF,CACT,EAAG,UAAU,EACTG,GAAoCpI,EAAQqI,GAAY,CAC1D,IAAIC,EACAC,EACAC,EACJ,OAAIX,IAAQ,MACVU,EAAiCvI,EAAO,CAAC6E,EAAGC,IAAMD,GAAKC,EAAG,gBAAgB,EAC1E0D,EAAiB,MAEjBD,EAAiCvI,EAAO,CAAC6E,EAAGC,IAAMD,GAAKC,EAAG,gBAAgB,EAC1E0D,EAAiB,GAEnBH,EAAQ,QAASI,GAAW,CAC1B,IAAMC,EAAiBb,IAAQ,MAAQA,GAAO,KAAOL,EAAU,IAAIiB,CAAM,GAAG,EAAIjB,EAAU,IAAIiB,CAAM,GAAG,EACnGC,IAAmB,QAAUH,EAAeG,EAAgBF,CAAc,IAC5EF,EAAgBG,EAChBD,EAAiBE,EAErB,CAAC,EACMJ,CACT,EAAG,mBAAmB,EAClBK,GAAsC3I,EAAQqI,GAAY,CAC5D,IAAIC,EAAgB,GAChBM,EAAc,IAClB,OAAAP,EAAQ,QAASI,GAAW,CAC1B,IAAMC,EAAiBlB,EAAU,IAAIiB,CAAM,EAAE,EACzCC,GAAkBE,IACpBN,EAAgBG,EAChBG,EAAcF,EAElB,CAAC,EACMJ,GAAiB,MAC1B,EAAG,qBAAqB,EACpBO,GAAmC7I,EAAO,CAAC8I,EAAYC,EAASC,IAAgB,CAClF,IAAIC,EAASD,EACTJ,EAAcI,EACZE,EAAQ,CAAC,EACfJ,EAAW,QAAS9H,GAAQ,CAC1B,IAAMiC,EAAU8F,EAAQ,IAAI/H,CAAG,EAC/B,GAAI,CAACiC,EACH,MAAM,IAAI,MAAM,4BAA4BjC,CAAG,EAAE,EAE/CiC,EAAQ,QAAQ,QAClBgG,EAASE,GAAwBlG,CAAO,EACxC2F,EAAc,KAAK,IAAIK,EAAQL,CAAW,GAE1CM,EAAM,KAAKjG,CAAO,EAEpBmG,GAAkBnG,EAASgG,CAAM,CACnC,CAAC,EACDA,EAASL,EACTM,EAAM,QAASjG,GAAY,CACzBoG,GAAgBpG,EAASgG,EAAQD,CAAW,CAC9C,CAAC,EACDF,EAAW,QAAS9H,GAAQ,CAC1B,IAAMiC,EAAU8F,EAAQ,IAAI/H,CAAG,EAC/B,GAAIiC,GAAS,QAAQ,OAAQ,CAC3B,IAAMqF,EAAgBK,GAAoB1F,EAAQ,OAAO,EACzDgG,EAASzB,EAAU,IAAIc,CAAa,EAAE,EAAInB,EACtC8B,GAAUL,IACZA,EAAcK,GAEhB,IAAMK,EAAI/B,EAAU,IAAItE,EAAQ,MAAM,EAAE,IAClCsG,EAAIN,EAAS/B,EACnBM,EAAU,IAAIvE,EAAQ,GAAI,CAAE,EAAAqG,EAAG,EAAAC,CAAE,CAAC,CACpC,CACF,CAAC,CACH,EAAG,kBAAkB,EACjBC,GAAuCxJ,EAAQiD,GAAY,CAC7D,IAAMqF,EAAgBF,GAAkBnF,EAAQ,QAAQ,OAAQwG,GAAMA,IAAM,IAAI,CAAC,EACjF,GAAI,CAACnB,EACH,MAAM,IAAI,MAAM,uCAAuCrF,EAAQ,EAAE,EAAE,EAErE,IAAMyG,EAAmBlC,EAAU,IAAIc,CAAa,GAAG,EACvD,GAAIoB,IAAqB,OACvB,MAAM,IAAI,MAAM,gDAAgDzG,EAAQ,EAAE,EAAE,EAE9E,OAAOyG,CACT,EAAG,sBAAsB,EACrBP,GAA0CnJ,EAAQiD,GAC3BuG,GAAqBvG,CAAO,EAC3BkE,EACzB,yBAAyB,EACxBiC,GAAoCpJ,EAAO,CAACiD,EAASgG,IAAW,CAClE,IAAMvF,EAAU6D,EAAU,IAAItE,EAAQ,MAAM,EAC5C,GAAI,CAACS,EACH,MAAM,IAAI,MAAM,+BAA+BT,EAAQ,EAAE,EAAE,EAE7D,IAAMqG,EAAI5F,EAAQ,IACZ6F,EAAIN,EAAS/B,EACnB,OAAAM,EAAU,IAAIvE,EAAQ,GAAI,CAAE,EAAAqG,EAAG,EAAAC,CAAE,CAAC,EAC3B,CAAE,EAAAD,EAAG,EAAAC,CAAE,CAChB,EAAG,mBAAmB,EAClBF,GAAkCrJ,EAAO,CAACiD,EAASgG,EAAQD,IAAgB,CAC7E,IAAMtF,EAAU6D,EAAU,IAAItE,EAAQ,MAAM,EAC5C,GAAI,CAACS,EACH,MAAM,IAAI,MAAM,+BAA+BT,EAAQ,EAAE,EAAE,EAE7D,IAAMsG,EAAIN,EAASD,EACbM,EAAI5F,EAAQ,IAClB8D,EAAU,IAAIvE,EAAQ,GAAI,CAAE,EAAAqG,EAAG,EAAAC,CAAE,CAAC,CACpC,EAAG,iBAAiB,EAChBI,GAAmC3J,EAAO,CAAC4J,EAAU3G,EAAS4G,EAAgBC,EAAWC,EAAaC,IAAqB,CAC7H,GAAIA,IAAqBpK,EAAW,UAClCgK,EAAS,OAAO,MAAM,EAAE,KAAK,IAAKC,EAAe,EAAI,EAAE,EAAE,KAAK,IAAKA,EAAe,EAAI,EAAE,EAAE,KAAK,QAAS,EAAE,EAAE,KAAK,SAAU,EAAE,EAAE,KAC7H,QACA,UAAU5G,EAAQ,EAAE,oBAAoB8G,EAAczC,CAAiB,IAAIwC,CAAS,QACtF,EACAF,EAAS,OAAO,MAAM,EAAE,KAAK,IAAKC,EAAe,EAAI,CAAC,EAAE,KAAK,IAAKA,EAAe,EAAI,CAAC,EAAE,KAAK,QAAS,EAAE,EAAE,KAAK,SAAU,EAAE,EAAE,KAC3H,QACA,UAAU5G,EAAQ,EAAE,UAAU8G,EAAczC,CAAiB,IAAIwC,CAAS,QAC5E,UACSE,IAAqBpK,EAAW,YACzCgK,EAAS,OAAO,QAAQ,EAAE,KAAK,KAAMC,EAAe,CAAC,EAAE,KAAK,KAAMA,EAAe,CAAC,EAAE,KAAK,IAAK,EAAE,EAAE,KAAK,QAAS,UAAU5G,EAAQ,EAAE,IAAI6G,CAAS,EAAE,EACnJF,EAAS,OAAO,QAAQ,EAAE,KAAK,KAAMC,EAAe,EAAI,CAAC,EAAE,KAAK,KAAMA,EAAe,EAAI,CAAC,EAAE,KAAK,IAAK,IAAI,EAAE,KAAK,OAAQ,MAAM,EAAE,KAAK,QAAS,UAAU5G,EAAQ,EAAE,IAAI6G,CAAS,EAAE,EAClLF,EAAS,OAAO,QAAQ,EAAE,KAAK,KAAMC,EAAe,EAAI,CAAC,EAAE,KAAK,KAAMA,EAAe,EAAI,CAAC,EAAE,KAAK,IAAK,IAAI,EAAE,KAAK,OAAQ,MAAM,EAAE,KAAK,QAAS,UAAU5G,EAAQ,EAAE,IAAI6G,CAAS,EAAE,EAClLF,EAAS,OAAO,MAAM,EAAE,KAAK,KAAMC,EAAe,EAAI,CAAC,EAAE,KAAK,KAAMA,EAAe,EAAI,CAAC,EAAE,KAAK,KAAMA,EAAe,CAAC,EAAE,KAAK,KAAMA,EAAe,EAAI,CAAC,EAAE,KAAK,SAAU,MAAM,EAAE,KAAK,QAAS,UAAU5G,EAAQ,EAAE,IAAI6G,CAAS,EAAE,EAChOF,EAAS,OAAO,MAAM,EAAE,KAAK,KAAMC,EAAe,EAAI,CAAC,EAAE,KAAK,KAAMA,EAAe,EAAI,CAAC,EAAE,KAAK,KAAMA,EAAe,CAAC,EAAE,KAAK,KAAMA,EAAe,EAAI,CAAC,EAAE,KAAK,SAAU,MAAM,EAAE,KAAK,QAAS,UAAU5G,EAAQ,EAAE,IAAI6G,CAAS,EAAE,MAC3N,CACL,IAAMG,EAASL,EAAS,OAAO,QAAQ,EAKvC,GAJAK,EAAO,KAAK,KAAMJ,EAAe,CAAC,EAClCI,EAAO,KAAK,KAAMJ,EAAe,CAAC,EAClCI,EAAO,KAAK,IAAKhH,EAAQ,OAASrD,EAAW,MAAQ,EAAI,EAAE,EAC3DqK,EAAO,KAAK,QAAS,UAAUhH,EAAQ,EAAE,UAAU8G,EAAczC,CAAiB,EAAE,EAChF0C,IAAqBpK,EAAW,MAAO,CACzC,IAAMsK,EAAUN,EAAS,OAAO,QAAQ,EACxCM,EAAQ,KAAK,KAAML,EAAe,CAAC,EACnCK,EAAQ,KAAK,KAAML,EAAe,CAAC,EACnCK,EAAQ,KAAK,IAAK,CAAC,EACnBA,EAAQ,KACN,QACA,UAAUJ,CAAS,IAAI7G,EAAQ,EAAE,UAAU8G,EAAczC,CAAiB,EAC5E,CACF,CACI0C,IAAqBpK,EAAW,SACpBgK,EAAS,OAAO,MAAM,EAC9B,KACJ,IACA,KAAKC,EAAe,EAAI,CAAC,IAAIA,EAAe,EAAI,CAAC,IAAIA,EAAe,EAAI,CAAC,IAAIA,EAAe,EAAI,CAAC,IAAIA,EAAe,EAAI,CAAC,IAAIA,EAAe,EAAI,CAAC,IAAIA,EAAe,EAAI,CAAC,IAAIA,EAAe,EAAI,CAAC,EACnM,EAAE,KAAK,QAAS,UAAUC,CAAS,IAAI7G,EAAQ,EAAE,UAAU8G,EAAczC,CAAiB,EAAE,CAEhG,CACF,EAAG,kBAAkB,EACjB6C,GAAkCnK,EAAO,CAACoK,EAASnH,EAAS4G,EAAgBQ,IAAQ,CACtF,GAAIpH,EAAQ,OAASrD,EAAW,cAAgBqD,EAAQ,UAAYA,EAAQ,OAASrD,EAAW,OAASqD,EAAQ,OAASrD,EAAW,QAAUqH,GAA0B,gBAAiB,CACxL,IAAMqD,EAAUF,EAAQ,OAAO,GAAG,EAC5BG,EAAWD,EAAQ,OAAO,MAAM,EAAE,KAAK,QAAS,kBAAkB,EAClEE,EAAOF,EAAQ,OAAO,MAAM,EAAE,KAAK,IAAKD,CAAG,EAAE,KAAK,IAAKR,EAAe,EAAI,EAAE,EAAE,KAAK,QAAS,cAAc,EAAE,KAAK5G,EAAQ,EAAE,EAC3HwH,EAAOD,EAAK,KAAK,GAAG,QAAQ,EAClC,GAAIC,IACFF,EAAS,KAAK,IAAKV,EAAe,cAAgBY,EAAK,MAAQ,EAAIpD,CAAE,EAAE,KAAK,IAAKwC,EAAe,EAAI,IAAI,EAAE,KAAK,QAASY,EAAK,MAAQ,EAAIpD,CAAE,EAAE,KAAK,SAAUoD,EAAK,OAAS,EAAIpD,CAAE,EAC5KQ,IAAQ,MAAQA,IAAQ,MAC1B0C,EAAS,KAAK,IAAKV,EAAe,GAAKY,EAAK,MAAQ,EAAIrD,EAAK,EAAE,EAAE,KAAK,IAAKyC,EAAe,EAAI,EAAE,EAChGW,EAAK,KAAK,IAAKX,EAAe,GAAKY,EAAK,MAAQ,EAAIrD,EAAG,EAAE,KAAK,IAAKyC,EAAe,EAAIY,EAAK,OAAS,EAAE,GAEtGD,EAAK,KAAK,IAAKX,EAAe,cAAgBY,EAAK,MAAQ,CAAC,EAE1DxD,EAAyB,mBAC3B,GAAIY,IAAQ,MAAQA,IAAQ,KAC1B2C,EAAK,KACH,YACA,eAAiBX,EAAe,EAAI,KAAOA,EAAe,EAAI,GAChE,EACAU,EAAS,KACP,YACA,eAAiBV,EAAe,EAAI,KAAOA,EAAe,EAAI,GAChE,MACK,CACL,IAAMa,EAAM,MAAQD,EAAK,MAAQ,IAAM,GAAK,IACtCE,EAAM,GAAKF,EAAK,MAAQ,GAAK,IACnCH,EAAQ,KACN,YACA,aAAeI,EAAM,KAAOC,EAAM,iBAAmBN,EAAM,KAAOR,EAAe,EAAI,GACvF,CACF,CAGN,CACF,EAAG,iBAAiB,EAChBe,GAAiC5K,EAAO,CAACoK,EAASnH,EAAS4G,EAAgBQ,IAAQ,CACrF,GAAIpH,EAAQ,KAAK,OAAS,EAAG,CAC3B,IAAI4H,EAAU,EACVC,EAAkB,EAClBC,EAAmB,EACjBC,EAAc,CAAC,EACrB,QAAWC,KAAYhI,EAAQ,KAAK,QAAQ,EAAG,CAC7C,IAAMiI,EAAOd,EAAQ,OAAO,SAAS,EAC/Be,EAAOf,EAAQ,OAAO,QAAQ,EAC9BtI,EAAMsI,EAAQ,OAAO,MAAM,EAAE,KAAK,IAAKP,EAAe,EAAI,GAAKgB,CAAO,EAAE,KAAK,QAAS,WAAW,EAAE,KAAKI,CAAQ,EAChHG,EAAUtJ,EAAI,KAAK,GAAG,QAAQ,EACpC,GAAI,CAACsJ,EACH,MAAM,IAAI,MAAM,oBAAoB,EAEtCN,EAAkB,KAAK,IAAIA,EAAiBM,EAAQ,KAAK,EACzDL,EAAmB,KAAK,IAAIA,EAAkBK,EAAQ,MAAM,EAC5DtJ,EAAI,KAAK,IAAK+H,EAAe,cAAgBuB,EAAQ,MAAQ,CAAC,EAC9DJ,EAAY,KAAK,CACf,IAAAlJ,EACA,KAAAqJ,EACA,KAAAD,EACA,QAAAL,CACF,CAAC,EACDA,GAAW,EACb,CACA,OAAW,CAAE,IAAA/I,EAAK,KAAAqJ,EAAM,KAAAD,EAAM,QAASG,CAAS,IAAKL,EAAa,CAChE,IAAMM,EAAKP,EAAmB,EACxBQ,EAAK1B,EAAe,EAAI,KAAOwB,EAYrC,GAXAH,EAAK,KAAK,QAAS,eAAe,EAAE,KAClC,SACA;AAAA,QACAb,EAAMS,EAAkB,EAAI1D,EAAK,CAAC,IAAImE,EAAKlE,CAAE;AAAA,QAC7CgD,EAAMS,EAAkB,EAAI1D,EAAK,CAAC,IAAImE,EAAKlE,CAAE;AAAA,QAC7CwC,EAAe,cAAgBiB,EAAkB,EAAI1D,CAAE,IAAImE,EAAKD,EAAKjE,CAAE;AAAA,QACvEwC,EAAe,cAAgBiB,EAAkB,EAAI1D,CAAE,IAAImE,EAAKD,EAAKjE,CAAE;AAAA,QACvEwC,EAAe,cAAgBiB,EAAkB,EAAI1D,CAAE,IAAImE,EAAKD,EAAKjE,CAAE;AAAA,QACvEwC,EAAe,cAAgBiB,EAAkB,EAAI1D,CAAE,IAAImE,EAAKD,EAAKjE,CAAE,EACzE,EACA8D,EAAK,KAAK,KAAMI,CAAE,EAAE,KAAK,KAAMlB,EAAMS,EAAkB,EAAI1D,EAAK,CAAC,EAAE,KAAK,IAAK,GAAG,EAAE,KAAK,QAAS,UAAU,EACtGS,IAAQ,MAAQA,IAAQ,KAAM,CAChC,IAAM2D,EAAUnB,EAAMgB,EACtBH,EAAK,KAAK,QAAS,eAAe,EAAE,KAClC,SACA;AAAA,UACArB,EAAe,CAAC,IAAI2B,EAAU,CAAC;AAAA,UAC/B3B,EAAe,CAAC,IAAI2B,EAAU,CAAC;AAAA,UAC/B3B,EAAe,EAAI3C,CAAa,IAAIsE,EAAUF,EAAK,CAAC;AAAA,UACpDzB,EAAe,EAAI3C,EAAgB4D,EAAkB,CAAC,IAAIU,EAAUF,EAAK,CAAC;AAAA,UAC1EzB,EAAe,EAAI3C,EAAgB4D,EAAkB,CAAC,IAAIU,EAAUF,EAAK,CAAC;AAAA,UAC1EzB,EAAe,EAAI3C,CAAa,IAAIsE,EAAUF,EAAK,CAAC,EACtD,EAAE,KAAK,YAAa,+BAAiCzB,EAAe,EAAI,IAAMQ,EAAM,GAAG,EACvFc,EAAK,KAAK,KAAMtB,EAAe,EAAIzC,EAAK,CAAC,EAAE,KAAK,KAAMoE,CAAO,EAAE,KAAK,YAAa,+BAAiC3B,EAAe,EAAI,IAAMQ,EAAM,GAAG,EACpJvI,EAAI,KAAK,IAAK+H,EAAe,EAAI,CAAC,EAAE,KAAK,IAAK2B,EAAU,CAAC,EAAE,KAAK,YAAa,+BAAiC3B,EAAe,EAAI,IAAMQ,EAAM,GAAG,CAClJ,CACF,CACF,CACF,EAAG,gBAAgB,EACfoB,GAAqCzL,EAAQiD,GAAY,CAE3D,OADyBA,EAAQ,YAAcA,EAAQ,KAC7B,CACxB,KAAKrD,EAAW,OACd,MAAO,gBACT,KAAKA,EAAW,QACd,MAAO,iBACT,KAAKA,EAAW,UACd,MAAO,mBACT,KAAKA,EAAW,MACd,MAAO,eACT,KAAKA,EAAW,YACd,MAAO,qBACT,QACE,MAAO,eACX,CACF,EAAG,oBAAoB,EACnB8L,GAAoC1L,EAAO,CAACiD,EAAS/B,EAAMmJ,EAAKsB,IAAe,CACjF,IAAMC,EAAwB,CAAE,EAAG,EAAG,EAAG,CAAE,EAC3C,GAAI3I,EAAQ,QAAQ,OAAS,EAAG,CAC9B,IAAMqF,EAAgBF,GAAkBnF,EAAQ,OAAO,EACvD,GAAIqF,EAAe,CACjB,IAAMI,EAAiBiD,EAAW,IAAIrD,CAAa,GAAKsD,EACxD,OAAI1K,IAAS,KACJwH,EAAe,EAAIvB,EACjBjG,IAAS,MACMyK,EAAW,IAAI1I,EAAQ,EAAE,GAAK2I,GAC/B,EAAIzE,EAEpBuB,EAAe,EAAIvB,CAE9B,CACF,KACE,QAAIjG,IAAS,KACJuG,EACEvG,IAAS,MACMyK,EAAW,IAAI1I,EAAQ,EAAE,GAAK2I,GAC/B,EAAIzE,EAEpB,EAGX,MAAO,EACT,EAAG,mBAAmB,EAClB0E,GAAoC7L,EAAO,CAACiD,EAASoH,EAAKyB,IAAsB,CAClF,IAAMC,EAAgBlE,IAAQ,MAAQiE,EAAoBzB,EAAMA,EAAMnD,EAChEqC,EAAI1B,IAAQ,MAAQA,IAAQ,KAAOkE,EAAgBxE,EAAU,IAAItE,EAAQ,MAAM,GAAG,IAClFqG,EAAIzB,IAAQ,MAAQA,IAAQ,KAAON,EAAU,IAAItE,EAAQ,MAAM,GAAG,IAAM8I,EAC9E,GAAIzC,IAAM,QAAUC,IAAM,OACxB,MAAM,IAAI,MAAM,sCAAsCtG,EAAQ,EAAE,EAAE,EAEpE,MAAO,CAAE,EAAAqG,EAAG,EAAAC,EAAG,cAAAwC,CAAc,CAC/B,EAAG,mBAAmB,EAClBC,GAA8BhM,EAAO,CAACiM,EAAKlD,EAASmD,IAAgB,CACtE,GAAI,CAACjF,EACH,MAAM,IAAI,MAAM,2BAA2B,EAE7C,IAAM2C,EAAWqC,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,gBAAgB,EACzD7B,EAAU6B,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EACzD5B,EAAMxC,IAAQ,MAAQA,IAAQ,KAAOJ,EAAa,EAChD0E,EAAO,CAAC,GAAGpD,EAAQ,KAAK,CAAC,EACzB+C,EAAoB7E,GAA0B,iBAAmB,GACjEmF,EAA2BpM,EAAO,CAAC6E,EAAGC,IAAM,CAChD,IAAMuH,EAAOtD,EAAQ,IAAIlE,CAAC,GAAG,IACvByH,EAAOvD,EAAQ,IAAIjE,CAAC,GAAG,IAC7B,OAAOuH,IAAS,QAAUC,IAAS,OAASD,EAAOC,EAAO,CAC5D,EAAG,UAAU,EACTxD,EAAaqD,EAAK,KAAKC,CAAQ,EAC/BvE,IAAQ,OACNiE,GACFjD,GAAiBC,EAAYC,EAASsB,CAAG,EAE3CvB,EAAaA,EAAW,QAAQ,GAElCA,EAAW,QAAS9H,GAAQ,CAC1B,IAAMiC,EAAU8F,EAAQ,IAAI/H,CAAG,EAC/B,GAAI,CAACiC,EACH,MAAM,IAAI,MAAM,4BAA4BjC,CAAG,EAAE,EAE/C8K,IACFzB,EAAMqB,GAAkBzI,EAAS4E,EAAKwC,EAAK7C,CAAS,GAEtD,IAAMqC,EAAiBgC,GAAkB5I,EAASoH,EAAKyB,CAAiB,EACxE,GAAII,EAAa,CACf,IAAMpC,EAAY2B,GAAmBxI,CAAO,EACtC+G,EAAmB/G,EAAQ,YAAcA,EAAQ,KACjD8G,EAAcxC,EAAU,IAAItE,EAAQ,MAAM,GAAG,OAAS,EAC5D0G,GAAiBC,EAAU3G,EAAS4G,EAAgBC,EAAWC,EAAaC,CAAgB,EAC5FG,GAAgBC,EAASnH,EAAS4G,EAAgBQ,CAAG,EACrDO,GAAeR,EAASnH,EAAS4G,EAAgBQ,CAAG,CACtD,CACIxC,IAAQ,MAAQA,IAAQ,KAC1BL,EAAU,IAAIvE,EAAQ,GAAI,CAAE,EAAG4G,EAAe,EAAG,EAAGA,EAAe,aAAc,CAAC,EAElFrC,EAAU,IAAIvE,EAAQ,GAAI,CAAE,EAAG4G,EAAe,cAAe,EAAGA,EAAe,CAAE,CAAC,EAEpFQ,EAAMxC,IAAQ,MAAQiE,EAAoBzB,EAAMlD,EAAckD,EAAMlD,EAAcD,EAC9EmD,EAAMzC,IACRA,EAASyC,EAEb,CAAC,CACH,EAAG,aAAa,EACZkC,GAAqCvM,EAAO,CAACwM,EAASC,EAASC,EAAIC,EAAIC,IAAe,CAExF,IAAMC,GADoBhF,IAAQ,MAAQA,IAAQ,KAAO6E,EAAG,EAAIC,EAAG,EAAID,EAAG,EAAIC,EAAG,GACpCF,EAAQ,OAASD,EAAQ,OAChEM,EAAuC9M,EAAQsJ,GAAMA,EAAE,SAAWuD,EAAkB,sBAAsB,EAC1GE,EAAmC/M,EAAQsJ,GAAMA,EAAE,IAAMkD,EAAQ,KAAOlD,EAAE,IAAMmD,EAAQ,IAAK,kBAAkB,EACrH,MAAO,CAAC,GAAGG,EAAW,OAAO,CAAC,EAAE,KAAMI,GAC7BD,EAAiBC,CAAO,GAAKF,EAAqBE,CAAO,CACjE,CACH,EAAG,oBAAoB,EACnBC,EAA2BjN,EAAO,CAACkN,EAAIC,EAAIC,EAAQ,IAAM,CAC3D,IAAMC,EAAYH,EAAK,KAAK,IAAIA,EAAKC,CAAE,EAAI,EAC3C,GAAIC,EAAQ,EACV,OAAOC,EAGT,GADW1F,EAAM,MAAO2F,GAAS,KAAK,IAAIA,EAAOD,CAAS,GAAK,EAAE,EAE/D,OAAA1F,EAAM,KAAK0F,CAAS,EACbA,EAET,IAAME,EAAO,KAAK,IAAIL,EAAKC,CAAE,EAC7B,OAAOF,EAASC,EAAIC,EAAKI,EAAO,EAAGH,EAAQ,CAAC,CAC9C,EAAG,UAAU,EACTI,GAA4BxN,EAAO,CAACiM,EAAKO,EAASC,EAASG,IAAe,CAC5E,IAAMF,EAAKlF,EAAU,IAAIgF,EAAQ,EAAE,EAC7BG,EAAKnF,EAAU,IAAIiF,EAAQ,EAAE,EACnC,GAAIC,IAAO,QAAUC,IAAO,OAC1B,MAAM,IAAI,MAAM,0CAA0CH,EAAQ,EAAE,QAAQC,EAAQ,EAAE,EAAE,EAE1F,IAAMgB,EAAsBlB,GAAmBC,EAASC,EAASC,EAAIC,EAAIC,CAAU,EAC/Ec,EAAM,GACNC,EAAO,GACPC,EAAS,EACTC,EAAS,EACTC,EAAgBvG,EAAU,IAAIkF,EAAQ,MAAM,GAAG,MAC/CA,EAAQ,OAAS7M,EAAW,OAAS4M,EAAQ,KAAOC,EAAQ,QAAQ,CAAC,IACvEqB,EAAgBvG,EAAU,IAAIiF,EAAQ,MAAM,GAAG,OAEjD,IAAIuB,EACJ,GAAIN,EAAqB,CACvBC,EAAM,oBACNC,EAAO,oBACPC,EAAS,GACTC,EAAS,GACT,IAAMG,EAAQtB,EAAG,EAAIC,EAAG,EAAIM,EAASP,EAAG,EAAGC,EAAG,CAAC,EAAIM,EAASN,EAAG,EAAGD,EAAG,CAAC,EAChEuB,EAAQvB,EAAG,EAAIC,EAAG,EAAIM,EAASP,EAAG,EAAGC,EAAG,CAAC,EAAIM,EAASN,EAAG,EAAGD,EAAG,CAAC,EAClE7E,IAAQ,KACN6E,EAAG,EAAIC,EAAG,EACZoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMuB,EAAQL,CAAM,IAAIlB,EAAG,CAAC,IAAIiB,CAAI,IAAIM,CAAK,IAAIvB,EAAG,EAAImB,CAAM,MAAMI,CAAK,IAAItB,EAAG,EAAIiB,CAAM,IAAIF,CAAG,IAAIO,EAAQJ,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,IAExKmB,EAAgBvG,EAAU,IAAIiF,EAAQ,MAAM,GAAG,MAC/CuB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMuB,EAAQL,CAAM,IAAIlB,EAAG,CAAC,IAAIgB,CAAG,IAAIO,CAAK,IAAIvB,EAAG,EAAImB,CAAM,MAAMI,CAAK,IAAItB,EAAG,EAAIiB,CAAM,IAAID,CAAI,IAAIM,EAAQJ,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,IAEjK9E,IAAQ,KACb6E,EAAG,EAAIC,EAAG,EACZoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMuB,EAAQL,CAAM,IAAIlB,EAAG,CAAC,IAAIgB,CAAG,IAAIO,CAAK,IAAIvB,EAAG,EAAImB,CAAM,MAAMI,CAAK,IAAItB,EAAG,EAAIiB,CAAM,IAAID,CAAI,IAAIM,EAAQJ,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,IAExKmB,EAAgBvG,EAAU,IAAIiF,EAAQ,MAAM,GAAG,MAC/CuB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMuB,EAAQL,CAAM,IAAIlB,EAAG,CAAC,IAAIiB,CAAI,IAAIM,CAAK,IAAIvB,EAAG,EAAImB,CAAM,MAAMI,CAAK,IAAItB,EAAG,EAAIiB,CAAM,IAAIF,CAAG,IAAIO,EAAQJ,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,IAGtKD,EAAG,EAAIC,EAAG,EACZoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIsB,EAAQJ,CAAM,IAAIF,CAAG,IAAIhB,EAAG,EAAImB,CAAM,IAAIG,CAAK,MAAMrB,EAAG,EAAIiB,CAAM,IAAII,CAAK,IAAIL,CAAI,IAAIhB,EAAG,CAAC,IAAIqB,EAAQH,CAAM,MAAMlB,EAAG,CAAC,IAAIA,EAAG,CAAC,IAExKmB,EAAgBvG,EAAU,IAAIiF,EAAQ,MAAM,GAAG,MAC/CuB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIsB,EAAQJ,CAAM,IAAID,CAAI,IAAIjB,EAAG,EAAImB,CAAM,IAAIG,CAAK,MAAMrB,EAAG,EAAIiB,CAAM,IAAII,CAAK,IAAIN,CAAG,IAAIf,EAAG,CAAC,IAAIqB,EAAQH,CAAM,MAAMlB,EAAG,CAAC,IAAIA,EAAG,CAAC,GAG9K,MACEe,EAAM,oBACNC,EAAO,oBACPC,EAAS,GACTC,EAAS,GACLhG,IAAQ,MACN6E,EAAG,EAAIC,EAAG,IACRF,EAAQ,OAAS7M,EAAW,OAAS4M,EAAQ,KAAOC,EAAQ,QAAQ,CAAC,EACvEsB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIC,EAAG,EAAIiB,CAAM,IAAIF,CAAG,IAAIhB,EAAG,EAAImB,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,GAExGoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMC,EAAG,EAAIiB,CAAM,IAAIlB,EAAG,CAAC,IAAIiB,CAAI,IAAIhB,EAAG,CAAC,IAAID,EAAG,EAAImB,CAAM,MAAMlB,EAAG,CAAC,IAAIA,EAAG,CAAC,IAGzGD,EAAG,EAAIC,EAAG,IACZe,EAAM,oBACNC,EAAO,oBACPC,EAAS,GACTC,EAAS,GACLpB,EAAQ,OAAS7M,EAAW,OAAS4M,EAAQ,KAAOC,EAAQ,QAAQ,CAAC,EACvEsB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIC,EAAG,EAAIiB,CAAM,IAAID,CAAI,IAAIjB,EAAG,EAAImB,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,GAEzGoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMC,EAAG,EAAIiB,CAAM,IAAIlB,EAAG,CAAC,IAAIgB,CAAG,IAAIf,EAAG,CAAC,IAAID,EAAG,EAAImB,CAAM,MAAMlB,EAAG,CAAC,IAAIA,EAAG,CAAC,IAGxGD,EAAG,IAAMC,EAAG,IACdoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMC,EAAG,CAAC,IAAIA,EAAG,CAAC,KAEtC9E,IAAQ,MACb6E,EAAG,EAAIC,EAAG,IACRF,EAAQ,OAAS7M,EAAW,OAAS4M,EAAQ,KAAOC,EAAQ,QAAQ,CAAC,EACvEsB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIC,EAAG,EAAIiB,CAAM,IAAID,CAAI,IAAIjB,EAAG,EAAImB,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,GAEzGoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMC,EAAG,EAAIiB,CAAM,IAAIlB,EAAG,CAAC,IAAIgB,CAAG,IAAIf,EAAG,CAAC,IAAID,EAAG,EAAImB,CAAM,MAAMlB,EAAG,CAAC,IAAIA,EAAG,CAAC,IAGxGD,EAAG,EAAIC,EAAG,IACZe,EAAM,oBACNC,EAAO,oBACPC,EAAS,GACTC,EAAS,GACLpB,EAAQ,OAAS7M,EAAW,OAAS4M,EAAQ,KAAOC,EAAQ,QAAQ,CAAC,EACvEsB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIC,EAAG,EAAIiB,CAAM,IAAIF,CAAG,IAAIhB,EAAG,EAAImB,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,GAExGoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMC,EAAG,EAAIiB,CAAM,IAAIlB,EAAG,CAAC,IAAIgB,CAAG,IAAIf,EAAG,CAAC,IAAID,EAAG,EAAImB,CAAM,MAAMlB,EAAG,CAAC,IAAIA,EAAG,CAAC,IAGxGD,EAAG,IAAMC,EAAG,IACdoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMC,EAAG,CAAC,IAAIA,EAAG,CAAC,MAG3CD,EAAG,EAAIC,EAAG,IACRF,EAAQ,OAAS7M,EAAW,OAAS4M,EAAQ,KAAOC,EAAQ,QAAQ,CAAC,EACvEsB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMC,EAAG,EAAIiB,CAAM,IAAIlB,EAAG,CAAC,IAAIiB,CAAI,IAAIhB,EAAG,CAAC,IAAID,EAAG,EAAImB,CAAM,MAAMlB,EAAG,CAAC,IAAIA,EAAG,CAAC,GAEzGoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIC,EAAG,EAAIiB,CAAM,IAAIF,CAAG,IAAIhB,EAAG,EAAImB,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,IAGxGD,EAAG,EAAIC,EAAG,IACRF,EAAQ,OAAS7M,EAAW,OAAS4M,EAAQ,KAAOC,EAAQ,QAAQ,CAAC,EACvEsB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMC,EAAG,EAAIiB,CAAM,IAAIlB,EAAG,CAAC,IAAIgB,CAAG,IAAIf,EAAG,CAAC,IAAID,EAAG,EAAImB,CAAM,MAAMlB,EAAG,CAAC,IAAIA,EAAG,CAAC,GAExGoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIC,EAAG,EAAIiB,CAAM,IAAID,CAAI,IAAIjB,EAAG,EAAImB,CAAM,IAAIlB,EAAG,CAAC,MAAMA,EAAG,CAAC,IAAIA,EAAG,CAAC,IAGzGD,EAAG,IAAMC,EAAG,IACdoB,EAAU,KAAKrB,EAAG,CAAC,IAAIA,EAAG,CAAC,MAAMC,EAAG,CAAC,IAAIA,EAAG,CAAC,KAInD,GAAIoB,IAAY,OACd,MAAM,IAAI,MAAM,2BAA2B,EAE7C9B,EAAI,OAAO,MAAM,EAAE,KAAK,IAAK8B,CAAO,EAAE,KAAK,QAAS,cAAgBD,EAAgBxG,CAAiB,CACvG,EAAG,WAAW,EACV4G,GAA6BlO,EAAO,CAACiM,EAAKlD,IAAY,CACxD,IAAMoF,EAAUlC,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EAC7D,CAAC,GAAGlD,EAAQ,KAAK,CAAC,EAAE,QAAS/H,GAAQ,CACnC,IAAMiC,EAAU8F,EAAQ,IAAI/H,CAAG,EAC3BiC,EAAQ,SAAWA,EAAQ,QAAQ,OAAS,GAC9CA,EAAQ,QAAQ,QAASwF,GAAW,CAClC+E,GAAUW,EAASpF,EAAQ,IAAIN,CAAM,EAAGxF,EAAS8F,CAAO,CAC1D,CAAC,CAEL,CAAC,CACH,EAAG,YAAY,EACXqF,GAA+BpO,EAAO,CAACiM,EAAKoC,IAAa,CAC3D,IAAMC,EAAIrC,EAAI,OAAO,GAAG,EACxBoC,EAAS,QAAQ,CAAC3K,EAASI,IAAU,CACnC,IAAMyK,EAAsBzK,EAAQwD,EAC9B+C,EAAM9C,EAAU,IAAI7D,EAAQ,IAAI,GAAG,IACzC,GAAI2G,IAAQ,OACV,MAAM,IAAI,MAAM,iCAAiC3G,EAAQ,IAAI,EAAE,EAEjE,IAAMQ,EAAOoK,EAAE,OAAO,MAAM,EAC5BpK,EAAK,KAAK,KAAM,CAAC,EACjBA,EAAK,KAAK,KAAMmG,CAAG,EACnBnG,EAAK,KAAK,KAAM0D,CAAM,EACtB1D,EAAK,KAAK,KAAMmG,CAAG,EACnBnG,EAAK,KAAK,QAAS,gBAAkBqK,CAAmB,EACpD1G,IAAQ,MACV3D,EAAK,KAAK,KAAMuD,CAAU,EAC1BvD,EAAK,KAAK,KAAMmG,CAAG,EACnBnG,EAAK,KAAK,KAAM0D,CAAM,EACtB1D,EAAK,KAAK,KAAMmG,CAAG,GACVxC,IAAQ,OACjB3D,EAAK,KAAK,KAAM0D,CAAM,EACtB1D,EAAK,KAAK,KAAMmG,CAAG,EACnBnG,EAAK,KAAK,KAAMuD,CAAU,EAC1BvD,EAAK,KAAK,KAAMmG,CAAG,GAErB1C,EAAM,KAAK0C,CAAG,EACd,IAAMnI,EAAOwB,EAAQ,KACf8K,EAAezG,GAAS7F,CAAI,EAC5BuM,EAAMH,EAAE,OAAO,MAAM,EAErBlK,EADckK,EAAE,OAAO,GAAG,EAAE,KAAK,QAAS,aAAa,EACnC,OAAO,GAAG,EAAE,KAAK,QAAS,qBAAuBC,CAAmB,EAC9FnK,EAAM,KAAK,EAAE,YAAYoK,CAAY,EACrC,IAAM/D,EAAO+D,EAAa,QAAQ,EAClCC,EAAI,KAAK,QAAS,uBAAyBF,CAAmB,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,IAAK,CAAC9D,EAAK,MAAQ,GAAKxD,GAA0B,oBAAsB,GAAO,GAAK,EAAE,EAAE,KAAK,IAAK,CAACwD,EAAK,OAAS,EAAI,CAAC,EAAE,KAAK,QAASA,EAAK,MAAQ,EAAE,EAAE,KAAK,SAAUA,EAAK,OAAS,CAAC,EACtRrG,EAAM,KACJ,YACA,cAAgB,CAACqG,EAAK,MAAQ,IAAMxD,GAA0B,oBAAsB,GAAO,GAAK,IAAM,MAAQoD,EAAMI,EAAK,OAAS,EAAI,GAAK,GAC7I,EACI5C,IAAQ,MACV4G,EAAI,KAAK,IAAKpE,EAAMI,EAAK,MAAQ,EAAI,EAAE,EAAE,KAAK,IAAK,CAAC,EACpDrG,EAAM,KAAK,YAAa,cAAgBiG,EAAMI,EAAK,MAAQ,EAAI,GAAK,MAAM,GACjE5C,IAAQ,MACjB4G,EAAI,KAAK,IAAKpE,EAAMI,EAAK,MAAQ,EAAI,EAAE,EAAE,KAAK,IAAK7C,CAAM,EACzDxD,EAAM,KAAK,YAAa,cAAgBiG,EAAMI,EAAK,MAAQ,EAAI,GAAK,KAAO7C,EAAS,GAAG,GAEvF6G,EAAI,KAAK,YAAa,mBAAqBpE,EAAMI,EAAK,OAAS,GAAK,GAAG,CAE3E,CAAC,CACH,EAAG,cAAc,EACbiE,GAAoC1O,EAAO,SAASkC,EAAMmI,EAAKvG,EAAO2G,EAAMkE,EAAmB,CACjG,OAAApH,EAAU,IAAIrF,EAAM,CAAE,IAAAmI,EAAK,MAAAvG,CAAM,CAAC,EAClCuG,GAAO,IAAMsE,EAAoB,GAAK,IAAM9G,IAAQ,MAAQA,IAAQ,KAAO4C,EAAK,MAAQ,EAAI,GACrFJ,CACT,EAAG,mBAAmB,EAClBuE,GAAuB5O,EAAO,SAASgI,EAAKtG,EAAImN,EAAKC,EAAS,CAGhE,GAFAhH,GAAO,EACPzG,EAAI,MAAM,uBAAwB2G,EAAM;AAAA,EAAM,MAAOtG,EAAImN,CAAG,EACxD,CAAC5H,EACH,MAAM,IAAI,MAAM,2BAA2B,EAE7C,IAAM0H,EAAoB1H,EAAyB,mBAAqB,GAClEnB,EAAMgJ,EAAQ,GACpBpH,EAAiB5B,EAAI,WAAW,EAChC,IAAMuI,EAAWvI,EAAI,sBAAsB,EAC3C+B,EAAM/B,EAAI,aAAa,EACvB,IAAMiJ,EAAWC,EAAO,QAAQtN,CAAE,IAAI,EAClC2I,EAAM,EACVgE,EAAS,QAAQ,CAAC3K,EAASI,IAAU,CACnC,IAAM0K,EAAezG,GAASrE,EAAQ,IAAI,EACpC4K,EAAIS,EAAS,OAAO,GAAG,EACvBE,EAAcX,EAAE,OAAO,GAAG,EAAE,KAAK,QAAS,aAAa,EACvDlK,EAAQ6K,EAAY,OAAO,GAAG,EAAE,KAAK,QAAS,oBAAoB,EACxE7K,EAAM,KAAK,GAAG,YAAYoK,CAAY,EACtC,IAAM/D,EAAO+D,EAAa,QAAQ,EAClCnE,EAAMqE,GAAkBhL,EAAQ,KAAM2G,EAAKvG,EAAO2G,EAAMkE,CAAiB,EACzEvK,EAAM,OAAO,EACb6K,EAAY,OAAO,EACnBX,EAAE,OAAO,CACX,CAAC,EACDtC,GAAY+C,EAAUrH,EAAgB,EAAK,EACvCT,EAAyB,cAC3BmH,GAAaW,EAAUV,CAAQ,EAEjCH,GAAWa,EAAUrH,CAAc,EACnCsE,GAAY+C,EAAUrH,EAAgB,EAAI,EAC1CwH,EAAc,YACZH,EACA,eACA9H,EAAyB,gBAAkB,EAC3CnB,EAAI,gBAAgB,CACtB,EACAqJ,EACE,OACAJ,EACA9H,EAAyB,eACzBA,EAAyB,WAC3B,CACF,EAAG,MAAM,EACLmI,GAA2B,CAC7B,KAAAR,EACF,EAmYIS,GAA4BrP,EAAQsP,GAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAShD,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAAE,IAC1B1K,GAAM;AAAA,uBACcA,CAAC,YAAY0K,EAAQ,iBAAmB1K,CAAC,CAAC;AAAA,iBAChDA,CAAC,cAAc0K,EAAQ,MAAQ1K,CAAC,CAAC,WAAW0K,EAAQ,MAAQ1K,CAAC,CAAC;AAAA,2BACpDA,CAAC,cAAc0K,EAAQ,SAAW1K,CAAC,CAAC,WAAW0K,EAAQ,SAAW1K,CAAC,CAAC;AAAA,gBAC/EA,CAAC,aAAa0K,EAAQ,MAAQ1K,CAAC,CAAC;AAAA,gBAChCA,CAAC,cAAc0K,EAAQ,MAAQ1K,CAAC,CAAC;AAAA,SAEjD,EAAE,KAAK;AAAA,CAAI,CAAC;AAAA;AAAA;AAAA;AAAA,cAIE0K,EAAQ,SAAS;AAAA;AAAA;AAAA,+BAGAA,EAAQ,mBAAmB,WAAWA,EAAQ,gBAAgB;AAAA,mCAC1DA,EAAQ,mBAAmB,WAAWA,EAAQ,qBAAqB;AAAA,4BAC1EA,EAAQ,gBAAgB,WAAWA,EAAQ,aAAa;AAAA,2BACzDA,EAAQ,kBAAkB,aAAaA,EAAQ,cAAc;AAAA,sBAClEA,EAAQ,SAAS;AAAA;AAAA;AAAA,cAGzBA,EAAQ,YAAY;AAAA,YACtBA,EAAQ,YAAY;AAAA;AAAA;AAAA,cAGlBA,EAAQ,YAAY;AAAA,YACtBA,EAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMlBA,EAAQ,YAAY;AAAA,YACtBA,EAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOpBA,EAAQ,SAAS;AAAA;AAAA,EAE1B,WAAW,EACVC,GAAiBF,GAGjBG,GAAU,CACZ,OAAA5I,GACA,GAAAvB,GACA,SAAU+J,GACV,OAAQG,EACV", "names": ["commitType", "DEFAULT_GITGRAPH_CONFIG", "defaultConfig_default", "getConfig3", "__name", "cleanAndMerge", "getConfig", "state", "ImperativeState", "config", "mainBranchName", "mainBranchOrder", "getID", "random", "uniqBy", "list", "fn", "recordMap", "out", "item", "key", "setDirection", "dir2", "setOptions", "rawOptString", "log", "getOptions", "commit", "commitDB", "msg", "id", "type", "tags", "common_default", "tag", "newCommit", "branch", "branchDB", "name", "order", "checkout", "merge", "mergeDB", "otherBranch", "customId", "overrideType", "customTags", "currentBranchCheck", "otherBranchCheck", "currentCommit", "otherCommit", "error", "verifiedBranch", "commit2", "cherryPick", "cherryPickDB", "sourceId", "targetId", "parentCommitId", "sourceCommit", "sourceCommitBranch", "currentCommitId", "branch2", "upsert", "arr", "newVal", "index", "prettyPrintCommitHistory", "commitArr", "commit3", "line", "c", "label", "<PERSON><PERSON><PERSON><PERSON>", "node", "getCommitsArray", "clear2", "clear", "getBranchesAsObjArray", "branchConfig", "i", "a", "b", "getBranches", "getCommits", "o", "getCurrentBranch", "getDirection", "getHead", "db", "setAccTitle", "getAccTitle", "getAccDescription", "setAccDescription", "setDiagramTitle", "getDiagramTitle", "populate", "ast", "db2", "populateCommonDb", "statement", "parseStatement", "parser2", "stmt", "parseCommit", "parseBranch", "parseMerge", "parseCheckout", "parseCherryPicking", "merge2", "checkout2", "cherryPicking", "parser", "input", "parse", "DEFAULT_CONFIG", "getConfig2", "DEFAULT_GITGRAPH_CONFIG2", "LAYOUT_OFFSET", "COMMIT_STEP", "PX", "PY", "THEME_COLOR_LIMIT", "branchPos", "commitPos", "defaultPos", "allCommitsDict", "lanes", "maxPos", "dir", "clear3", "drawText", "txt", "svgLabel", "row", "tspan", "findClosestParent", "parents", "closestParent", "comparisonFunc", "targetPosition", "parent", "parentPosition", "findClosestParentBT", "maxPosition", "setParallelBTPos", "sortedKeys", "commits", "defaultPos2", "curPos", "roots", "calculateCommitPosition", "setCommitPosition", "setRootPosition", "x", "y", "findClosestParentPos", "p", "closestParentPos", "drawCommitBullet", "gBullets", "commitPosition", "typeClass", "branchIndex", "commitSymbolType", "circle", "circle2", "drawCommitLabel", "g<PERSON><PERSON><PERSON>", "pos", "wrapper", "labelBkg", "text", "bbox", "r_x", "r_y", "drawCommitTags", "yOffset", "maxTagBboxWidth", "maxTagBboxHeight", "tagElements", "tagValue", "rect", "hole", "tagBbox", "yOffset2", "h2", "ly", "y<PERSON><PERSON><PERSON>", "getCommitClassType", "calculatePosition", "commitPos2", "defaultCommitPosition", "getCommitPosition", "isParallelCommits", "posWithOffset", "drawCommits", "svg", "modifyGraph", "keys", "sortKeys", "seqA", "seqB", "shouldRerouteArrow", "commitA", "commitB", "p1", "p2", "allCommits", "branchToGetCurve", "isOnBranchToGetCurve", "isBetweenCommits", "commitX", "find<PERSON><PERSON>", "y1", "y2", "depth", "candidate", "lane", "diff", "drawArrow", "arrowNeedsRerouting", "arc", "arc2", "radius", "offset", "colorClassNum", "lineDef", "lineY", "lineX", "drawArrows", "gArrows", "drawBranches", "branches", "g", "adjustIndexForTheme", "labelElement", "bkg", "setBranchPosition", "rotateCommitLabel", "draw", "ver", "diagObj", "diagram2", "select_default", "branchLabel", "utils_default", "setupGraphViewbox2", "gitGraphRenderer_default", "getStyles", "options", "styles_default", "diagram"]}