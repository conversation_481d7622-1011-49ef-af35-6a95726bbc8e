# API Documentation

Welcome to the DrawnUi.Maui API documentation. This section contains detailed reference information for all public classes, interfaces, and members in the DrawnUi.Maui library.

## Main Namespaces

### DrawnUi.Draw
Core drawing and control classes including:
- Base control classes (SkiaControl, SkiaLayout)
- Drawing contexts and rendering
- Animation and effects
- Gesture handling

### DrawnUi.Controls
Specialized controls including:
- SkiaShell for navigation
- SkiaCarousel for carousels
- SkiaDrawer for slide-out panels
- Various input controls

### DrawnUi.Views
Platform integration views:
- DrawnView for hosting Skia content
- Canvas for direct drawing
- Platform-specific implementations

### DrawnUi.Infrastructure
Supporting infrastructure:
- Helpers and utilities
- Models and data structures
- Platform abstractions

## Getting Started with the API

For practical examples and usage patterns, see the [Articles section](../articles/) which provides:
- [Getting Started Guide](../articles/getting-started.md)
- [Control Documentation](../articles/controls/)
- [Fluent Extensions](../articles/fluent-extensions.md)

## Browse the API

Use the navigation on the left to explore specific namespaces and types, or use the search functionality to find specific classes or members.
