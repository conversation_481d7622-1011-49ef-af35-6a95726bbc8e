import{a as I}from"./chunk-OSRY5VT3.min.js";var R=I((l,f)=>{(function(i,e){typeof define=="function"&&define.amd?define(e):typeof l=="object"?f.exports=e():e()(i.lunr)})(l,function(){return function(i){if(typeof i>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof i.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");i.hu=function(){this.pipeline.reset(),this.pipeline.add(i.hu.trimmer,i.hu.stopWordFilter,i.hu.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(i.hu.stemmer))},i.hu.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",i.hu.trimmer=i.trimmerSupport.generateTrimmer(i.hu.wordCharacters),i.Pipeline.registerFunction(i.hu.trimmer,"trimmer-hu"),i.hu.stemmer=function(){var e=i.stemmerSupport.Among,_=i.stemmerSupport.SnowballProgram,u=new function(){var w=[new e("cs",-1,-1),new e("dzs",-1,-1),new e("gy",-1,-1),new e("ly",-1,-1),new e("ny",-1,-1),new e("sz",-1,-1),new e("ty",-1,-1),new e("zs",-1,-1)],b=[new e("\xE1",-1,1),new e("\xE9",-1,2)],g=[new e("bb",-1,-1),new e("cc",-1,-1),new e("dd",-1,-1),new e("ff",-1,-1),new e("gg",-1,-1),new e("jj",-1,-1),new e("kk",-1,-1),new e("ll",-1,-1),new e("mm",-1,-1),new e("nn",-1,-1),new e("pp",-1,-1),new e("rr",-1,-1),new e("ccs",-1,-1),new e("ss",-1,-1),new e("zzs",-1,-1),new e("tt",-1,-1),new e("vv",-1,-1),new e("ggy",-1,-1),new e("lly",-1,-1),new e("nny",-1,-1),new e("tty",-1,-1),new e("ssz",-1,-1),new e("zz",-1,-1)],d=[new e("al",-1,1),new e("el",-1,2)],E=[new e("ba",-1,-1),new e("ra",-1,-1),new e("be",-1,-1),new e("re",-1,-1),new e("ig",-1,-1),new e("nak",-1,-1),new e("nek",-1,-1),new e("val",-1,-1),new e("vel",-1,-1),new e("ul",-1,-1),new e("n\xE1l",-1,-1),new e("n\xE9l",-1,-1),new e("b\xF3l",-1,-1),new e("r\xF3l",-1,-1),new e("t\xF3l",-1,-1),new e("b\xF5l",-1,-1),new e("r\xF5l",-1,-1),new e("t\xF5l",-1,-1),new e("\xFCl",-1,-1),new e("n",-1,-1),new e("an",19,-1),new e("ban",20,-1),new e("en",19,-1),new e("ben",22,-1),new e("k\xE9ppen",22,-1),new e("on",19,-1),new e("\xF6n",19,-1),new e("k\xE9pp",-1,-1),new e("kor",-1,-1),new e("t",-1,-1),new e("at",29,-1),new e("et",29,-1),new e("k\xE9nt",29,-1),new e("ank\xE9nt",32,-1),new e("enk\xE9nt",32,-1),new e("onk\xE9nt",32,-1),new e("ot",29,-1),new e("\xE9rt",29,-1),new e("\xF6t",29,-1),new e("hez",-1,-1),new e("hoz",-1,-1),new e("h\xF6z",-1,-1),new e("v\xE1",-1,-1),new e("v\xE9",-1,-1)],h=[new e("\xE1n",-1,2),new e("\xE9n",-1,1),new e("\xE1nk\xE9nt",-1,3)],v=[new e("stul",-1,2),new e("astul",0,1),new e("\xE1stul",0,3),new e("st\xFCl",-1,2),new e("est\xFCl",3,1),new e("\xE9st\xFCl",3,4)],p=[new e("\xE1",-1,1),new e("\xE9",-1,2)],F=[new e("k",-1,7),new e("ak",0,4),new e("ek",0,6),new e("ok",0,5),new e("\xE1k",0,1),new e("\xE9k",0,2),new e("\xF6k",0,3)],z=[new e("\xE9i",-1,7),new e("\xE1\xE9i",0,6),new e("\xE9\xE9i",0,5),new e("\xE9",-1,9),new e("k\xE9",3,4),new e("ak\xE9",4,1),new e("ek\xE9",4,1),new e("ok\xE9",4,1),new e("\xE1k\xE9",4,3),new e("\xE9k\xE9",4,2),new e("\xF6k\xE9",4,1),new e("\xE9\xE9",3,8)],y=[new e("a",-1,18),new e("ja",0,17),new e("d",-1,16),new e("ad",2,13),new e("ed",2,13),new e("od",2,13),new e("\xE1d",2,14),new e("\xE9d",2,15),new e("\xF6d",2,13),new e("e",-1,18),new e("je",9,17),new e("nk",-1,4),new e("unk",11,1),new e("\xE1nk",11,2),new e("\xE9nk",11,3),new e("\xFCnk",11,1),new e("uk",-1,8),new e("juk",16,7),new e("\xE1juk",17,5),new e("\xFCk",-1,8),new e("j\xFCk",19,7),new e("\xE9j\xFCk",20,6),new e("m",-1,12),new e("am",22,9),new e("em",22,9),new e("om",22,9),new e("\xE1m",22,10),new e("\xE9m",22,11),new e("o",-1,18),new e("\xE1",-1,19),new e("\xE9",-1,20)],j=[new e("id",-1,10),new e("aid",0,9),new e("jaid",1,6),new e("eid",0,9),new e("jeid",3,6),new e("\xE1id",0,7),new e("\xE9id",0,8),new e("i",-1,15),new e("ai",7,14),new e("jai",8,11),new e("ei",7,14),new e("jei",10,11),new e("\xE1i",7,12),new e("\xE9i",7,13),new e("itek",-1,24),new e("eitek",14,21),new e("jeitek",15,20),new e("\xE9itek",14,23),new e("ik",-1,29),new e("aik",18,26),new e("jaik",19,25),new e("eik",18,26),new e("jeik",21,25),new e("\xE1ik",18,27),new e("\xE9ik",18,28),new e("ink",-1,20),new e("aink",25,17),new e("jaink",26,16),new e("eink",25,17),new e("jeink",28,16),new e("\xE1ink",25,18),new e("\xE9ink",25,19),new e("aitok",-1,21),new e("jaitok",32,20),new e("\xE1itok",-1,22),new e("im",-1,5),new e("aim",35,4),new e("jaim",36,1),new e("eim",35,4),new e("jeim",38,1),new e("\xE1im",35,2),new e("\xE9im",35,3)],c=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,0,1,17,52,14],t,n=new _;this.setCurrent=function(r){n.setCurrent(r)},this.getCurrent=function(){return n.getCurrent()};function C(){var r=n.cursor,s;if(t=n.limit,n.in_grouping(c,97,252))for(;;){if(s=n.cursor,n.out_grouping(c,97,252)){n.cursor=s,n.find_among(w,8)||(n.cursor=s,s<n.limit&&n.cursor++),t=n.cursor;return}if(n.cursor=s,s>=n.limit){t=s;return}n.cursor++}if(n.cursor=r,n.out_grouping(c,97,252)){for(;!n.in_grouping(c,97,252);){if(n.cursor>=n.limit)return;n.cursor++}t=n.cursor}}function a(){return t<=n.cursor}function A(){var r;if(n.ket=n.cursor,r=n.find_among_b(b,2),r&&(n.bra=n.cursor,a()))switch(r){case 1:n.slice_from("a");break;case 2:n.slice_from("e");break}}function m(){var r=n.limit-n.cursor;return n.find_among_b(g,23)?(n.cursor=n.limit-r,!0):!1}function k(){if(n.cursor>n.limit_backward){n.cursor--,n.ket=n.cursor;var r=n.cursor-1;n.limit_backward<=r&&r<=n.limit&&(n.cursor=r,n.bra=r,n.slice_del())}}function B(){var r;if(n.ket=n.cursor,r=n.find_among_b(d,2),r&&(n.bra=n.cursor,a())){if((r==1||r==2)&&!m())return;n.slice_del(),k()}}function D(){n.ket=n.cursor,n.find_among_b(E,44)&&(n.bra=n.cursor,a()&&(n.slice_del(),A()))}function P(){var r;if(n.ket=n.cursor,r=n.find_among_b(h,3),r&&(n.bra=n.cursor,a()))switch(r){case 1:n.slice_from("e");break;case 2:case 3:n.slice_from("a");break}}function S(){var r;if(n.ket=n.cursor,r=n.find_among_b(v,6),r&&(n.bra=n.cursor,a()))switch(r){case 1:case 2:n.slice_del();break;case 3:n.slice_from("a");break;case 4:n.slice_from("e");break}}function x(){var r;if(n.ket=n.cursor,r=n.find_among_b(p,2),r&&(n.bra=n.cursor,a())){if((r==1||r==2)&&!m())return;n.slice_del(),k()}}function W(){var r;if(n.ket=n.cursor,r=n.find_among_b(F,7),r&&(n.bra=n.cursor,a()))switch(r){case 1:n.slice_from("a");break;case 2:n.slice_from("e");break;case 3:case 4:case 5:case 6:case 7:n.slice_del();break}}function L(){var r;if(n.ket=n.cursor,r=n.find_among_b(z,12),r&&(n.bra=n.cursor,a()))switch(r){case 1:case 4:case 7:case 9:n.slice_del();break;case 2:case 5:case 8:n.slice_from("e");break;case 3:case 6:n.slice_from("a");break}}function q(){var r;if(n.ket=n.cursor,r=n.find_among_b(y,31),r&&(n.bra=n.cursor,a()))switch(r){case 1:case 4:case 7:case 8:case 9:case 12:case 13:case 16:case 17:case 18:n.slice_del();break;case 2:case 5:case 10:case 14:case 19:n.slice_from("a");break;case 3:case 6:case 11:case 15:case 20:n.slice_from("e");break}}function H(){var r;if(n.ket=n.cursor,r=n.find_among_b(j,42),r&&(n.bra=n.cursor,a()))switch(r){case 1:case 4:case 5:case 6:case 9:case 10:case 11:case 14:case 15:case 16:case 17:case 20:case 21:case 24:case 25:case 26:case 29:n.slice_del();break;case 2:case 7:case 12:case 18:case 22:case 27:n.slice_from("a");break;case 3:case 8:case 13:case 19:case 23:case 28:n.slice_from("e");break}}this.stem=function(){var r=n.cursor;return C(),n.limit_backward=r,n.cursor=n.limit,B(),n.cursor=n.limit,D(),n.cursor=n.limit,P(),n.cursor=n.limit,S(),n.cursor=n.limit,x(),n.cursor=n.limit,L(),n.cursor=n.limit,q(),n.cursor=n.limit,H(),n.cursor=n.limit,W(),!0}};return function(o){return typeof o.update=="function"?o.update(function(w){return u.setCurrent(w),u.stem(),u.getCurrent()}):(u.setCurrent(o),u.stem(),u.getCurrent())}}(),i.Pipeline.registerFunction(i.hu.stemmer,"stemmer-hu"),i.hu.stopWordFilter=i.generateStopWordFilter("a abban ahhoz ahogy ahol aki akik akkor alatt amely amelyek amelyekben amelyeket amelyet amelynek ami amikor amit amolyan am\xEDg annak arra arr\xF3l az azok azon azonban azt azt\xE1n azut\xE1n azzal az\xE9rt be bel\xFCl benne b\xE1r cikk cikkek cikkeket csak de e ebben eddig egy egyes egyetlen egyik egyre egy\xE9b eg\xE9sz ehhez ekkor el ellen els\xF5 el\xE9g el\xF5 el\xF5sz\xF6r el\xF5tt emilyen ennek erre ez ezek ezen ezt ezzel ez\xE9rt fel fel\xE9 hanem hiszen hogy hogyan igen ill ill. illetve ilyen ilyenkor ism\xE9t ison itt jobban j\xF3 j\xF3l kell kellett keress\xFCnk kereszt\xFCl ki k\xEDv\xFCl k\xF6z\xF6tt k\xF6z\xFCl legal\xE1bb legyen lehet lehetett lenne lenni lesz lett maga mag\xE1t majd majd meg mellett mely melyek mert mi mikor milyen minden mindenki mindent mindig mint mintha mit mivel mi\xE9rt most m\xE1r m\xE1s m\xE1sik m\xE9g m\xEDg nagy nagyobb nagyon ne nekem neki nem nincs n\xE9ha n\xE9h\xE1ny n\xE9lk\xFCl olyan ott pedig persze r\xE1 s saj\xE1t sem semmi sok sokat sokkal szemben szerint szinte sz\xE1m\xE1ra tal\xE1n teh\xE1t teljes tov\xE1bb tov\xE1bb\xE1 t\xF6bb ugyanis utols\xF3 ut\xE1n ut\xE1na vagy vagyis vagyok valaki valami valamint val\xF3 van vannak vele vissza viszont volna volt voltak voltam voltunk \xE1ltal \xE1ltal\xE1ban \xE1t \xE9n \xE9ppen \xE9s \xEDgy \xF5 \xF5k \xF5ket \xF6ssze \xFAgy \xFAj \xFAjabb \xFAjra".split(" ")),i.Pipeline.registerFunction(i.hu.stopWordFilter,"stopWordFilter-hu")}})});export default R();
/*! Bundled license information:

lunr-languages/lunr.hu.js:
  (*!
   * Lunr languages, `Hungarian` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.hu-DLG2DSVM.min.js.map
