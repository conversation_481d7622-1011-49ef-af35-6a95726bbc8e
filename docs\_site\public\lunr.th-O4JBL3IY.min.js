import{a as h}from"./chunk-OSRY5VT3.min.js";var p=h((r,o)=>{(function(e,t){typeof define=="function"&&define.amd?define(t):typeof r=="object"?o.exports=t():t()(e.lunr)})(r,function(){return function(e){if(typeof e>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof e.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");var t=e.version[0]=="2";e.th=function(){this.pipeline.reset(),this.pipeline.add(e.th.trimmer),t?this.tokenizer=e.th.tokenizer:(e.tokenizer&&(e.tokenizer=e.th.tokenizer),this.tokenizerFn&&(this.tokenizerFn=e.th.tokenizer))},e.th.wordCharacters="[\u0E00-\u0E7F]",e.th.trimmer=e.trimmerSupport.generateTrimmer(e.th.wordCharacters),e.Pipeline.registerFunction(e.th.trimmer,"trimmer-th");var n=e.wordcut;n.init(),e.th.tokenizer=function(i){if(!arguments.length||i==null||i==null)return[];if(Array.isArray(i))return i.map(function(s){return t?new e.Token(s):s});var f=i.toString().replace(/^\s+/,"");return n.cut(f).split("|")}}})});export default p();
/*! Bundled license information:

lunr-languages/lunr.th.js:
  (*!
   * Lunr languages, `Thai` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2017, Keerati Thiwanruk
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.th-O4JBL3IY.min.js.map
