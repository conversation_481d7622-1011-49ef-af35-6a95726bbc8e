{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.de.js"], "sourcesContent": ["/*!\n * Lunr languages, `German` language\n * https://github.com/Mihai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.de = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.de.trimmer,\n        lunr.de.stopWordFilter,\n        lunr.de.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.de.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.de.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.de.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.de.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.de.trimmer, 'trimmer-de');\n\n    /* lunr stemmer function */\n    lunr.de.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function GermanStemmer() {\n          var a_0 = [new Among(\"\", -1, 6), new Among(\"U\", 0, 2),\n              new Among(\"Y\", 0, 1), new Among(\"\\u00E4\", 0, 3),\n              new Among(\"\\u00F6\", 0, 4), new Among(\"\\u00FC\", 0, 5)\n            ],\n            a_1 = [\n              new Among(\"e\", -1, 2), new Among(\"em\", -1, 1),\n              new Among(\"en\", -1, 2), new Among(\"ern\", -1, 1),\n              new Among(\"er\", -1, 1), new Among(\"s\", -1, 3),\n              new Among(\"es\", 5, 2)\n            ],\n            a_2 = [new Among(\"en\", -1, 1),\n              new Among(\"er\", -1, 1), new Among(\"st\", -1, 2),\n              new Among(\"est\", 2, 1)\n            ],\n            a_3 = [new Among(\"ig\", -1, 1),\n              new Among(\"lich\", -1, 1)\n            ],\n            a_4 = [new Among(\"end\", -1, 1),\n              new Among(\"ig\", -1, 2), new Among(\"ung\", -1, 1),\n              new Among(\"lich\", -1, 3), new Among(\"isch\", -1, 2),\n              new Among(\"ik\", -1, 2), new Among(\"heit\", -1, 3),\n              new Among(\"keit\", -1, 4)\n            ],\n            g_v = [17, 65, 16, 1, 0, 0, 0, 0, 0, 0,\n              0, 0, 0, 0, 0, 0, 8, 0, 32, 8\n            ],\n            g_s_ending = [117, 30, 5],\n            g_st_ending = [\n              117, 30, 4\n            ],\n            I_x, I_p2, I_p1, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function habr1(c1, c2, v_1) {\n            if (sbp.eq_s(1, c1)) {\n              sbp.ket = sbp.cursor;\n              if (sbp.in_grouping(g_v, 97, 252)) {\n                sbp.slice_from(c2);\n                sbp.cursor = v_1;\n                return true;\n              }\n            }\n            return false;\n          }\n\n          function r_prelude() {\n            var v_1 = sbp.cursor,\n              v_2, v_3, v_4, v_5;\n            while (true) {\n              v_2 = sbp.cursor;\n              sbp.bra = v_2;\n              if (sbp.eq_s(1, \"\\u00DF\")) {\n                sbp.ket = sbp.cursor;\n                sbp.slice_from(\"ss\");\n              } else {\n                if (v_2 >= sbp.limit)\n                  break;\n                sbp.cursor = v_2 + 1;\n              }\n            }\n            sbp.cursor = v_1;\n            while (true) {\n              v_3 = sbp.cursor;\n              while (true) {\n                v_4 = sbp.cursor;\n                if (sbp.in_grouping(g_v, 97, 252)) {\n                  v_5 = sbp.cursor;\n                  sbp.bra = v_5;\n                  if (habr1(\"u\", \"U\", v_4))\n                    break;\n                  sbp.cursor = v_5;\n                  if (habr1(\"y\", \"Y\", v_4))\n                    break;\n                }\n                if (v_4 >= sbp.limit) {\n                  sbp.cursor = v_3;\n                  return;\n                }\n                sbp.cursor = v_4 + 1;\n              }\n            }\n          }\n\n          function habr2() {\n            while (!sbp.in_grouping(g_v, 97, 252)) {\n              if (sbp.cursor >= sbp.limit)\n                return true;\n              sbp.cursor++;\n            }\n            while (!sbp.out_grouping(g_v, 97, 252)) {\n              if (sbp.cursor >= sbp.limit)\n                return true;\n              sbp.cursor++;\n            }\n            return false;\n          }\n\n          function r_mark_regions() {\n            I_p1 = sbp.limit;\n            I_p2 = I_p1;\n            var c = sbp.cursor + 3;\n            if (0 <= c && c <= sbp.limit) {\n              I_x = c;\n              if (!habr2()) {\n                I_p1 = sbp.cursor;\n                if (I_p1 < I_x)\n                  I_p1 = I_x;\n                if (!habr2())\n                  I_p2 = sbp.cursor;\n              }\n            }\n          }\n\n          function r_postlude() {\n            var among_var, v_1;\n            while (true) {\n              v_1 = sbp.cursor;\n              sbp.bra = v_1;\n              among_var = sbp.find_among(a_0, 6);\n              if (!among_var)\n                return;\n              sbp.ket = sbp.cursor;\n              switch (among_var) {\n                case 1:\n                  sbp.slice_from(\"y\");\n                  break;\n                case 2:\n                case 5:\n                  sbp.slice_from(\"u\");\n                  break;\n                case 3:\n                  sbp.slice_from(\"a\");\n                  break;\n                case 4:\n                  sbp.slice_from(\"o\");\n                  break;\n                case 6:\n                  if (sbp.cursor >= sbp.limit)\n                    return;\n                  sbp.cursor++;\n                  break;\n              }\n            }\n          }\n\n          function r_R1() {\n            return I_p1 <= sbp.cursor;\n          }\n\n          function r_R2() {\n            return I_p2 <= sbp.cursor;\n          }\n\n          function r_standard_suffix() {\n            var among_var, v_1 = sbp.limit - sbp.cursor,\n              v_2, v_3, v_4;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_1, 7);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                    sbp.slice_del();\n                    sbp.ket = sbp.cursor;\n                    if (sbp.eq_s_b(1, \"s\")) {\n                      sbp.bra = sbp.cursor;\n                      if (sbp.eq_s_b(3, \"nis\"))\n                        sbp.slice_del();\n                    }\n                    break;\n                  case 3:\n                    if (sbp.in_grouping_b(g_s_ending, 98, 116))\n                      sbp.slice_del();\n                    break;\n                }\n              }\n            }\n            sbp.cursor = sbp.limit - v_1;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_2, 4);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                    if (sbp.in_grouping_b(g_st_ending, 98, 116)) {\n                      var c = sbp.cursor - 3;\n                      if (sbp.limit_backward <= c && c <= sbp.limit) {\n                        sbp.cursor = c;\n                        sbp.slice_del();\n                      }\n                    }\n                    break;\n                }\n              }\n            }\n            sbp.cursor = sbp.limit - v_1;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_4, 8);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R2()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    sbp.ket = sbp.cursor;\n                    if (sbp.eq_s_b(2, \"ig\")) {\n                      sbp.bra = sbp.cursor;\n                      v_2 = sbp.limit - sbp.cursor;\n                      if (!sbp.eq_s_b(1, \"e\")) {\n                        sbp.cursor = sbp.limit - v_2;\n                        if (r_R2())\n                          sbp.slice_del();\n                      }\n                    }\n                    break;\n                  case 2:\n                    v_3 = sbp.limit - sbp.cursor;\n                    if (!sbp.eq_s_b(1, \"e\")) {\n                      sbp.cursor = sbp.limit - v_3;\n                      sbp.slice_del();\n                    }\n                    break;\n                  case 3:\n                    sbp.slice_del();\n                    sbp.ket = sbp.cursor;\n                    v_4 = sbp.limit - sbp.cursor;\n                    if (!sbp.eq_s_b(2, \"er\")) {\n                      sbp.cursor = sbp.limit - v_4;\n                      if (!sbp.eq_s_b(2, \"en\"))\n                        break;\n                    }\n                    sbp.bra = sbp.cursor;\n                    if (r_R1())\n                      sbp.slice_del();\n                    break;\n                  case 4:\n                    sbp.slice_del();\n                    sbp.ket = sbp.cursor;\n                    among_var = sbp.find_among_b(a_3, 2);\n                    if (among_var) {\n                      sbp.bra = sbp.cursor;\n                      if (r_R2() && among_var == 1)\n                        sbp.slice_del();\n                    }\n                    break;\n                }\n              }\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_prelude();\n            sbp.cursor = v_1;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            r_standard_suffix();\n            sbp.cursor = sbp.limit_backward;\n            r_postlude();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.de.stemmer, 'stemmer-de');\n\n    lunr.de.stopWordFilter = lunr.generateStopWordFilter('aber alle allem allen aller alles als also am an ander andere anderem anderen anderer anderes anderm andern anderr anders auch auf aus bei bin bis bist da damit dann das dasselbe dazu daß dein deine deinem deinen deiner deines dem demselben den denn denselben der derer derselbe derselben des desselben dessen dich die dies diese dieselbe dieselben diesem diesen dieser dieses dir doch dort du durch ein eine einem einen einer eines einig einige einigem einigen einiger einiges einmal er es etwas euch euer eure eurem euren eurer eures für gegen gewesen hab habe haben hat hatte hatten hier hin hinter ich ihm ihn ihnen ihr ihre ihrem ihren ihrer ihres im in indem ins ist jede jedem jeden jeder jedes jene jenem jenen jener jenes jetzt kann kein keine keinem keinen keiner keines können könnte machen man manche manchem manchen mancher manches mein meine meinem meinen meiner meines mich mir mit muss musste nach nicht nichts noch nun nur ob oder ohne sehr sein seine seinem seinen seiner seines selbst sich sie sind so solche solchem solchen solcher solches soll sollte sondern sonst um und uns unse unsem unsen unser unses unter viel vom von vor war waren warst was weg weil weiter welche welchem welchen welcher welches wenn werde werden wie wieder will wir wird wirst wo wollen wollte während würde würden zu zum zur zwar zwischen über'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.de.stopWordFilter, 'stopWordFilter-de');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAAyB,CAChC,IAAIC,EAAM,CAAC,IAAIH,EAAM,GAAI,GAAI,CAAC,EAAG,IAAIA,EAAM,IAAK,EAAG,CAAC,EAChD,IAAIA,EAAM,IAAK,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,EAC9C,IAAIA,EAAM,OAAU,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,CACrD,EACAI,EAAM,CACJ,IAAIJ,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC5C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC9C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,IAAK,GAAI,CAAC,EAC5C,IAAIA,EAAM,KAAM,EAAG,CAAC,CACtB,EACAK,EAAM,CAAC,IAAIL,EAAM,KAAM,GAAI,CAAC,EAC1B,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC7C,IAAIA,EAAM,MAAO,EAAG,CAAC,CACvB,EACAM,EAAM,CAAC,IAAIN,EAAM,KAAM,GAAI,CAAC,EAC1B,IAAIA,EAAM,OAAQ,GAAI,CAAC,CACzB,EACAO,EAAM,CAAC,IAAIP,EAAM,MAAO,GAAI,CAAC,EAC3B,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC9C,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,OAAQ,GAAI,CAAC,CACzB,EACAQ,EAAM,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EACnC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,CAC9B,EACAC,EAAa,CAAC,IAAK,GAAI,CAAC,EACxBC,EAAc,CACZ,IAAK,GAAI,CACX,EACAC,EAAKC,EAAMC,EAAMC,EAAM,IAAIb,EAC7B,KAAK,WAAa,SAASc,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,EAAMC,EAAIC,EAAIC,EAAK,CAC1B,OAAIL,EAAI,KAAK,EAAGG,CAAE,IAChBH,EAAI,IAAMA,EAAI,OACVA,EAAI,YAAYN,EAAK,GAAI,GAAG,IAC9BM,EAAI,WAAWI,CAAE,EACjBJ,EAAI,OAASK,EACN,IAGJ,EACT,CAEA,SAASC,GAAY,CAGnB,QAFID,EAAML,EAAI,OACZO,EAAKC,EAAKC,EAAKC,IAIf,GAFAH,EAAMP,EAAI,OACVA,EAAI,IAAMO,EACNP,EAAI,KAAK,EAAG,MAAQ,EACtBA,EAAI,IAAMA,EAAI,OACdA,EAAI,WAAW,IAAI,MACd,CACL,GAAIO,GAAOP,EAAI,MACb,MACFA,EAAI,OAASO,EAAM,CACrB,CAGF,IADAP,EAAI,OAASK,IAGX,IADAG,EAAMR,EAAI,OAERS,EAAMT,EAAI,OACN,EAAAA,EAAI,YAAYN,EAAK,GAAI,GAAG,IAC9BgB,EAAMV,EAAI,OACVA,EAAI,IAAMU,EACNR,EAAM,IAAK,IAAKO,CAAG,IAEvBT,EAAI,OAASU,EACTR,EAAM,IAAK,IAAKO,CAAG,MARd,CAWX,GAAIA,GAAOT,EAAI,MAAO,CACpBA,EAAI,OAASQ,EACb,MACF,CACAR,EAAI,OAASS,EAAM,CACrB,CAEJ,CAEA,SAASE,GAAQ,CACf,KAAO,CAACX,EAAI,YAAYN,EAAK,GAAI,GAAG,GAAG,CACrC,GAAIM,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,KAAO,CAACA,EAAI,aAAaN,EAAK,GAAI,GAAG,GAAG,CACtC,GAAIM,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASY,GAAiB,CACxBb,EAAOC,EAAI,MACXF,EAAOC,EACP,IAAIc,EAAIb,EAAI,OAAS,EACjB,GAAKa,GAAKA,GAAKb,EAAI,QACrBH,EAAMgB,EACDF,EAAM,IACTZ,EAAOC,EAAI,OACPD,EAAOF,IACTE,EAAOF,GACJc,EAAM,IACTb,EAAOE,EAAI,SAGnB,CAEA,SAASc,GAAa,CAEpB,QADIC,EAAWV,IACF,CAIX,GAHAA,EAAML,EAAI,OACVA,EAAI,IAAMK,EACVU,EAAYf,EAAI,WAAWX,EAAK,CAAC,EAC7B,CAAC0B,EACH,OAEF,OADAf,EAAI,IAAMA,EAAI,OACNe,EAAW,CACjB,IAAK,GACHf,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACL,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACH,GAAIA,EAAI,QAAUA,EAAI,MACpB,OACFA,EAAI,SACJ,KACJ,CACF,CACF,CAEA,SAASgB,GAAO,CACd,OAAOjB,GAAQC,EAAI,MACrB,CAEA,SAASiB,GAAO,CACd,OAAOnB,GAAQE,EAAI,MACrB,CAEA,SAASkB,GAAoB,CAC3B,IAAIH,EAAWV,EAAML,EAAI,MAAQA,EAAI,OACnCO,EAAKC,EAAKC,EAGZ,GAFAT,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,aAAaV,EAAK,CAAC,EAC/ByB,IACFf,EAAI,IAAMA,EAAI,OACVgB,EAAK,GACP,OAAQD,EAAW,CACjB,IAAK,GACHf,EAAI,UAAU,EACd,MACF,IAAK,GACHA,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,IACnBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,KAAK,GACrBA,EAAI,UAAU,GAElB,MACF,IAAK,GACCA,EAAI,cAAcL,EAAY,GAAI,GAAG,GACvCK,EAAI,UAAU,EAChB,KACJ,CAMJ,GAHAA,EAAI,OAASA,EAAI,MAAQK,EACzBL,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,aAAaT,EAAK,CAAC,EAC/BwB,IACFf,EAAI,IAAMA,EAAI,OACVgB,EAAK,GACP,OAAQD,EAAW,CACjB,IAAK,GACHf,EAAI,UAAU,EACd,MACF,IAAK,GACH,GAAIA,EAAI,cAAcJ,EAAa,GAAI,GAAG,EAAG,CAC3C,IAAIiB,EAAIb,EAAI,OAAS,EACjBA,EAAI,gBAAkBa,GAAKA,GAAKb,EAAI,QACtCA,EAAI,OAASa,EACbb,EAAI,UAAU,EAElB,CACA,KACJ,CAMJ,GAHAA,EAAI,OAASA,EAAI,MAAQK,EACzBL,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,aAAaP,EAAK,CAAC,EAC/BsB,IACFf,EAAI,IAAMA,EAAI,OACViB,EAAK,GACP,OAAQF,EAAW,CACjB,IAAK,GACHf,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACdO,EAAMP,EAAI,MAAQA,EAAI,OACjBA,EAAI,OAAO,EAAG,GAAG,IACpBA,EAAI,OAASA,EAAI,MAAQO,EACrBU,EAAK,GACPjB,EAAI,UAAU,IAGpB,MACF,IAAK,GACHQ,EAAMR,EAAI,MAAQA,EAAI,OACjBA,EAAI,OAAO,EAAG,GAAG,IACpBA,EAAI,OAASA,EAAI,MAAQQ,EACzBR,EAAI,UAAU,GAEhB,MACF,IAAK,GAIH,GAHAA,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACdS,EAAMT,EAAI,MAAQA,EAAI,OAClB,CAACA,EAAI,OAAO,EAAG,IAAI,IACrBA,EAAI,OAASA,EAAI,MAAQS,EACrB,CAACT,EAAI,OAAO,EAAG,IAAI,GACrB,MAEJA,EAAI,IAAMA,EAAI,OACVgB,EAAK,GACPhB,EAAI,UAAU,EAChB,MACF,IAAK,GACHA,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,aAAaR,EAAK,CAAC,EAC/BuB,IACFf,EAAI,IAAMA,EAAI,OACViB,EAAK,GAAKF,GAAa,GACzBf,EAAI,UAAU,GAElB,KACJ,CAGN,CACA,KAAK,KAAO,UAAW,CACrB,IAAIK,EAAML,EAAI,OACd,OAAAM,EAAU,EACVN,EAAI,OAASK,EACbO,EAAe,EACfZ,EAAI,eAAiBK,EACrBL,EAAI,OAASA,EAAI,MACjBkB,EAAkB,EAClBlB,EAAI,OAASA,EAAI,eACjBc,EAAW,EACJ,EACT,CACF,EAGF,OAAO,SAASK,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASlB,EAAM,CACjC,OAAAb,EAAG,WAAWa,CAAI,EAClBb,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAW+B,CAAK,EACnB/B,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,u1CAA+zC,MAAM,GAAG,CAAC,EAE93CA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_de", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "a_3", "a_4", "g_v", "g_s_ending", "g_st_ending", "I_x", "I_p2", "I_p1", "sbp", "word", "habr1", "c1", "c2", "v_1", "r_prelude", "v_2", "v_3", "v_4", "v_5", "habr2", "r_mark_regions", "c", "r_postlude", "among_var", "r_R1", "r_R2", "r_standard_suffix", "token"]}