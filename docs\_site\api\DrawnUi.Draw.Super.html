<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class Super | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class Super | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Super.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Super%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.Super">



  <h1 id="DrawnUi_Draw_Super" data-uid="DrawnUi.Draw.Super" class="text-break">
Class Super  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class Super</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">Super</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Draw_Super_CanUseHardwareAcceleration" data-uid="DrawnUi.Draw.Super.CanUseHardwareAcceleration">
  CanUseHardwareAcceleration
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L52"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Can optionally disable hardware-acceleration with this flag, for example on iOS you would want to avoid creating many metal views.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool CanUseHardwareAcceleration</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_CapMicroSecs" data-uid="DrawnUi.Draw.Super.CapMicroSecs">
  CapMicroSecs
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L164"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Capping FPS,at 120</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float CapMicroSecs</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_CreateHttpClient" data-uid="DrawnUi.Draw.Super.CreateHttpClient">
  CreateHttpClient
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L19"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Since we removed IHttpClientFactory for faster app startup on mobile
one can set this delegate to be used to create a custom client that would be used for loading internet sources.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Func&lt;IServiceProvider, HttpClient&gt; CreateHttpClient</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-2">Func</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.iserviceprovider">IServiceProvider</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.net.http.httpclient">HttpClient</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_InitialWindowSize" data-uid="DrawnUi.Draw.Super.InitialWindowSize">
  InitialWindowSize
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L215"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Size InitialWindowSize</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.size">Size</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_InsetsChanged" data-uid="DrawnUi.Draw.Super.InsetsChanged">
  InsetsChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L145"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Subscribe your navigation bar to react</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static EventHandler InsetsChanged</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_Multithreaded" data-uid="DrawnUi.Draw.Super.Multithreaded">
  Multithreaded
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L30"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Experimental for dev use. Set OffscreenRenderingAtCanvasLevel to true when this is true.
Default is False</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool Multithreaded</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_OnMauiAppCreated" data-uid="DrawnUi.Draw.Super.OnMauiAppCreated">
  OnMauiAppCreated
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L244"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Maui App was launched and UI is ready to be consumed</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Action OnMauiAppCreated</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_PreloadRegisteredFonts" data-uid="DrawnUi.Draw.Super.PreloadRegisteredFonts">
  PreloadRegisteredFonts
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L32"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool PreloadRegisteredFonts</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_SendTapsOnMainThread" data-uid="DrawnUi.Draw.Super.SendTapsOnMainThread">
  SendTapsOnMainThread
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L24"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Used by Tapped event handler, default is false</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool SendTapsOnMainThread</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_SkiaGeneration" data-uid="DrawnUi.Draw.Super.SkiaGeneration">
  SkiaGeneration
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L398"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int SkiaGeneration</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Draw_Super_UseFrozenVisualLayers" data-uid="DrawnUi.Draw.Super.UseFrozenVisualLayers">
  UseFrozenVisualLayers
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool UseFrozenVisualLayers</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_Super_App_" data-uid="DrawnUi.Draw.Super.App*"></a>

  <h3 id="DrawnUi_Draw_Super_App" data-uid="DrawnUi.Draw.Super.App">
  App
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Super.Maui.cs/#L59"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IApplication App { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iapplication">IApplication</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_AppContext_" data-uid="DrawnUi.Draw.Super.AppContext*"></a>

  <h3 id="DrawnUi_Draw_Super_AppContext" data-uid="DrawnUi.Draw.Super.AppContext">
  AppContext
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Super.Maui.cs/#L173"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IMauiContext AppContext { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.imauicontext">IMauiContext</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_BottomTabsHeight_" data-uid="DrawnUi.Draw.Super.BottomTabsHeight*"></a>

  <h3 id="DrawnUi_Draw_Super_BottomTabsHeight" data-uid="DrawnUi.Draw.Super.BottomTabsHeight">
  BottomTabsHeight
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L198"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>In DP</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double BottomTabsHeight { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_CacheEnabled_" data-uid="DrawnUi.Draw.Super.CacheEnabled*"></a>

  <h3 id="DrawnUi_Draw_Super_CacheEnabled" data-uid="DrawnUi.Draw.Super.CacheEnabled">
  CacheEnabled
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L392"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool CacheEnabled { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_ColorAccent_" data-uid="DrawnUi.Draw.Super.ColorAccent*"></a>

  <h3 id="DrawnUi_Draw_Super_ColorAccent" data-uid="DrawnUi.Draw.Super.ColorAccent">
  ColorAccent
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Super.Maui.cs/#L55"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color ColorAccent { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_ColorPrimary_" data-uid="DrawnUi.Draw.Super.ColorPrimary*"></a>

  <h3 id="DrawnUi_Draw_Super_ColorPrimary" data-uid="DrawnUi.Draw.Super.ColorPrimary">
  ColorPrimary
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Super.Maui.cs/#L57"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color ColorPrimary { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_EnableRendering_" data-uid="DrawnUi.Draw.Super.EnableRendering*"></a>

  <h3 id="DrawnUi_Draw_Super_EnableRendering" data-uid="DrawnUi.Draw.Super.EnableRendering">
  EnableRendering
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L94"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool EnableRendering { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_FontSubPixelRendering_" data-uid="DrawnUi.Draw.Super.FontSubPixelRendering*"></a>

  <h3 id="DrawnUi_Draw_Super_FontSubPixelRendering" data-uid="DrawnUi.Draw.Super.FontSubPixelRendering">
  FontSubPixelRendering
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L126"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Enables sub-pixel font rendering, might provide better antialiasing on some platforms. Default is True;</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool FontSubPixelRendering { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_GpuCacheEnabled_" data-uid="DrawnUi.Draw.Super.GpuCacheEnabled*"></a>

  <h3 id="DrawnUi_Draw_Super_GpuCacheEnabled" data-uid="DrawnUi.Draw.Super.GpuCacheEnabled">
  GpuCacheEnabled
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L394"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GpuCacheEnabled { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_InBackground_" data-uid="DrawnUi.Draw.Super.InBackground*"></a>

  <h3 id="DrawnUi_Draw_Super_InBackground" data-uid="DrawnUi.Draw.Super.InBackground">
  InBackground
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L267"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool InBackground { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_Initialized_" data-uid="DrawnUi.Draw.Super.Initialized*"></a>

  <h3 id="DrawnUi_Draw_Super_Initialized" data-uid="DrawnUi.Draw.Super.Initialized">
  Initialized
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L74"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool Initialized { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_IsRtl_" data-uid="DrawnUi.Draw.Super.IsRtl*"></a>

  <h3 id="DrawnUi_Draw_Super_IsRtl" data-uid="DrawnUi.Draw.Super.IsRtl">
  IsRtl
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L109"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>RTL support UNDER CONSTRUCTION</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsRtl { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_MaxFrameLengthMs_" data-uid="DrawnUi.Draw.Super.MaxFrameLengthMs*"></a>

  <h3 id="DrawnUi_Draw_Super_MaxFrameLengthMs" data-uid="DrawnUi.Draw.Super.MaxFrameLengthMs">
  MaxFrameLengthMs
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L202"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double MaxFrameLengthMs { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_NavBarHeight_" data-uid="DrawnUi.Draw.Super.NavBarHeight*"></a>

  <h3 id="DrawnUi_Draw_Super_NavBarHeight" data-uid="DrawnUi.Draw.Super.NavBarHeight">
  NavBarHeight
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L183"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>In DP</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double NavBarHeight { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_NavigationBarHeight_" data-uid="DrawnUi.Draw.Super.NavigationBarHeight*"></a>

  <h3 id="DrawnUi_Draw_Super_NavigationBarHeight" data-uid="DrawnUi.Draw.Super.NavigationBarHeight">
  NavigationBarHeight
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L193"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>In DP, actually Android only</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double NavigationBarHeight { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_OffscreenRenderingAtCanvasLevel_" data-uid="DrawnUi.Draw.Super.OffscreenRenderingAtCanvasLevel*"></a>

  <h3 id="DrawnUi_Draw_Super_OffscreenRenderingAtCanvasLevel" data-uid="DrawnUi.Draw.Super.OffscreenRenderingAtCanvasLevel">
  OffscreenRenderingAtCanvasLevel
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L38"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>If set to True will process all ofscreen rendering in one background thread at canvas level, otherwise every control will launch its own background processing thread.
Default is False</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool OffscreenRenderingAtCanvasLevel { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_Screen_" data-uid="DrawnUi.Draw.Super.Screen*"></a>

  <h3 id="DrawnUi_Draw_Super_Screen" data-uid="DrawnUi.Draw.Super.Screen">
  Screen
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L151"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Screen Screen { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Models.Screen.html">Screen</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_Services_" data-uid="DrawnUi.Draw.Super.Services*"></a>

  <h3 id="DrawnUi_Draw_Super_Services" data-uid="DrawnUi.Draw.Super.Services">
  Services
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Super.Maui.cs/#L151"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IServiceProvider Services { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.iserviceprovider">IServiceProvider</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_StatusBarHeight_" data-uid="DrawnUi.Draw.Super.StatusBarHeight*"></a>

  <h3 id="DrawnUi_Draw_Super_StatusBarHeight" data-uid="DrawnUi.Draw.Super.StatusBarHeight">
  StatusBarHeight
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L188"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>In DP</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double StatusBarHeight { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_StopRenderingInBackground_" data-uid="DrawnUi.Draw.Super.StopRenderingInBackground*"></a>

  <h3 id="DrawnUi_Draw_Super_StopRenderingInBackground" data-uid="DrawnUi.Draw.Super.StopRenderingInBackground">
  StopRenderingInBackground
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L307"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool StopRenderingInBackground { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_Super_UserAgent_" data-uid="DrawnUi.Draw.Super.UserAgent*"></a>

  <h3 id="DrawnUi_Draw_Super_UserAgent" data-uid="DrawnUi.Draw.Super.UserAgent">
  UserAgent
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L396"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string UserAgent { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_Super_DisplayException_" data-uid="DrawnUi.Draw.Super.DisplayException*"></a>

  <h3 id="DrawnUi_Draw_Super_DisplayException_Microsoft_Maui_Controls_VisualElement_System_Exception_" data-uid="DrawnUi.Draw.Super.DisplayException(Microsoft.Maui.Controls.VisualElement,System.Exception)">
  DisplayException(VisualElement, Exception)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Super.Maui.cs/#L66"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Display xaml page creation exception</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DisplayException(VisualElement view, Exception e)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement">VisualElement</a></dt>
    <dd></dd>
    <dt><code>e</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.exception">Exception</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Super_EnqueueBackgroundTask_" data-uid="DrawnUi.Draw.Super.EnqueueBackgroundTask*"></a>

  <h3 id="DrawnUi_Draw_Super_EnqueueBackgroundTask_System_Func_System_Threading_Tasks_Task__" data-uid="DrawnUi.Draw.Super.EnqueueBackgroundTask(System.Func{System.Threading.Tasks.Task})">
  EnqueueBackgroundTask(Func&lt;Task&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L403"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void EnqueueBackgroundTask(Func&lt;Task&gt; asyncAction)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>asyncAction</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-1">Func</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Super_GetCurrentTimeMs_" data-uid="DrawnUi.Draw.Super.GetCurrentTimeMs*"></a>

  <h3 id="DrawnUi_Draw_Super_GetCurrentTimeMs" data-uid="DrawnUi.Draw.Super.GetCurrentTimeMs">
  GetCurrentTimeMs()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L166"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static long GetCurrentTimeMs()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_Super_GetCurrentTimeNanos_" data-uid="DrawnUi.Draw.Super.GetCurrentTimeNanos*"></a>

  <h3 id="DrawnUi_Draw_Super_GetCurrentTimeNanos" data-uid="DrawnUi.Draw.Super.GetCurrentTimeNanos">
  GetCurrentTimeNanos()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L173"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static long GetCurrentTimeNanos()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_Super_InitShared_" data-uid="DrawnUi.Draw.Super.InitShared*"></a>

  <h3 id="DrawnUi_Draw_Super_InitShared" data-uid="DrawnUi.Draw.Super.InitShared">
  InitShared()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L84"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected static void InitShared()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Super_Log_" data-uid="DrawnUi.Draw.Super.Log*"></a>

  <h3 id="DrawnUi_Draw_Super_Log_System_Exception_System_String_" data-uid="DrawnUi.Draw.Super.Log(System.Exception,System.String)">
  Log(Exception, string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L54"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Log(Exception e, string caller = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>e</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.exception">Exception</a></dt>
    <dd></dd>
    <dt><code>caller</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Super_Log_" data-uid="DrawnUi.Draw.Super.Log*"></a>

  <h3 id="DrawnUi_Draw_Super_Log_System_String_Microsoft_Extensions_Logging_LogLevel_System_String_" data-uid="DrawnUi.Draw.Super.Log(System.String,Microsoft.Extensions.Logging.LogLevel,System.String)">
  Log(string, LogLevel, string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Super.Maui.cs/#L136"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Log(string message, LogLevel logLevel = LogLevel.Error, string caller = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>message</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>logLevel</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.extensions.logging.loglevel">LogLevel</a></dt>
    <dd></dd>
    <dt><code>caller</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Super_NeedGlobalUpdate_" data-uid="DrawnUi.Draw.Super.NeedGlobalUpdate*"></a>

  <h3 id="DrawnUi_Draw_Super_NeedGlobalUpdate" data-uid="DrawnUi.Draw.Super.NeedGlobalUpdate">
  NeedGlobalUpdate()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L256"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This will force recalculate canvas visibility in ViewTree and update those visible. Also when GRContext changes, all references must be cleared to avoid crash on next draw</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void NeedGlobalUpdate()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Super_OnCreated_" data-uid="DrawnUi.Draw.Super.OnCreated*"></a>

  <h3 id="DrawnUi_Draw_Super_OnCreated" data-uid="DrawnUi.Draw.Super.OnCreated">
  OnCreated()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L221"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void OnCreated()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Super_OnWentBackground_" data-uid="DrawnUi.Draw.Super.OnWentBackground*"></a>

  <h3 id="DrawnUi_Draw_Super_OnWentBackground" data-uid="DrawnUi.Draw.Super.OnWentBackground">
  OnWentBackground()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L228"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void OnWentBackground()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Super_OnWentForeground_" data-uid="DrawnUi.Draw.Super.OnWentForeground*"></a>

  <h3 id="DrawnUi_Draw_Super_OnWentForeground" data-uid="DrawnUi.Draw.Super.OnWentForeground">
  OnWentForeground()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L234"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void OnWentForeground()</code></pre>
  </div>













  <a id="DrawnUi_Draw_Super_ProcessBackgroundQueue_" data-uid="DrawnUi.Draw.Super.ProcessBackgroundQueue*"></a>

  <h3 id="DrawnUi_Draw_Super_ProcessBackgroundQueue" data-uid="DrawnUi.Draw.Super.ProcessBackgroundQueue">
  ProcessBackgroundQueue()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L408"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected static Task ProcessBackgroundQueue()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_Super_ResizeWindow_" data-uid="DrawnUi.Draw.Super.ResizeWindow*"></a>

  <h3 id="DrawnUi_Draw_Super_ResizeWindow_Microsoft_Maui_Controls_Window_System_Int32_System_Int32_System_Boolean_" data-uid="DrawnUi.Draw.Super.ResizeWindow(Microsoft.Maui.Controls.Window,System.Int32,System.Int32,System.Boolean)">
  ResizeWindow(Window, int, int, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Super.Maui.cs/#L208"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>For desktop platforms, will resize app window and eventually lock it from being resized.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ResizeWindow(Window window, int width, int height, bool isFixed)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>window</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.window">Window</a></dt>
    <dd></dd>
    <dt><code>width</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>height</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>isFixed</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Super_RunOnMainThreadAndWait_" data-uid="DrawnUi.Draw.Super.RunOnMainThreadAndWait*"></a>

  <h3 id="DrawnUi_Draw_Super_RunOnMainThreadAndWait_System_Action_System_Threading_CancellationToken_" data-uid="DrawnUi.Draw.Super.RunOnMainThreadAndWait(System.Action,System.Threading.CancellationToken)">
  RunOnMainThreadAndWait(Action, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L330"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RunOnMainThreadAndWait(Action action, CancellationToken cancellationToken = default)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>action</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a></dt>
    <dd></dd>
    <dt><code>cancellationToken</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Super_RunOnMainThreadAndWaitAsync_" data-uid="DrawnUi.Draw.Super.RunOnMainThreadAndWaitAsync*"></a>

  <h3 id="DrawnUi_Draw_Super_RunOnMainThreadAndWaitAsync_System_Func_System_Threading_Tasks_Task__System_Threading_CancellationToken_" data-uid="DrawnUi.Draw.Super.RunOnMainThreadAndWaitAsync(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
  RunOnMainThreadAndWaitAsync(Func&lt;Task&gt;, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L354"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Task RunOnMainThreadAndWaitAsync(Func&lt;Task&gt; asyncAction, CancellationToken cancellationToken = default)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>asyncAction</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-1">Func</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a>&gt;</dt>
    <dd></dd>
    <dt><code>cancellationToken</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_Super_SetLocale_" data-uid="DrawnUi.Draw.Super.SetLocale*"></a>

  <h3 id="DrawnUi_Draw_Super_SetLocale_System_String_" data-uid="DrawnUi.Draw.Super.SetLocale(System.String)">
  SetLocale(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L66"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetLocale(string lang)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>lang</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_Super_SetupFrameLooper_" data-uid="DrawnUi.Draw.Super.SetupFrameLooper*"></a>

  <h3 id="DrawnUi_Draw_Super_SetupFrameLooper" data-uid="DrawnUi.Draw.Super.SetupFrameLooper">
  SetupFrameLooper()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L42"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected static void SetupFrameLooper()</code></pre>
  </div>













  <h2 class="section" id="events">Events
</h2>



  <h3 id="DrawnUi_Draw_Super_HotReload" data-uid="DrawnUi.Draw.Super.HotReload">
  HotReload
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Super.Maui.cs/#L47"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Will be triggered only if debugger is attached</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static event Action&lt;Type[]?&gt;? HotReload</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-1">Action</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.type">Type</a>[]&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Draw_Super_NeedGlobalRefresh" data-uid="DrawnUi.Draw.Super.NeedGlobalRefresh">
  NeedGlobalRefresh
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L250"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This will force recalculate canvas visibility in ViewTree and update those visible. Also when GRContext changes, all references must be cleared to avoid crash on next draw</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static event EventHandler NeedGlobalRefresh</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Draw_Super_OnNativeAppCreated" data-uid="DrawnUi.Draw.Super.OnNativeAppCreated">
  OnNativeAppCreated
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L207"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>App was launched and UI is ready to be created</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static event EventHandler OnNativeAppCreated</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Draw_Super_OnNativeAppDestroyed" data-uid="DrawnUi.Draw.Super.OnNativeAppDestroyed">
  OnNativeAppDestroyed
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L213"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static event EventHandler OnNativeAppDestroyed</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Draw_Super_OnNativeAppPaused" data-uid="DrawnUi.Draw.Super.OnNativeAppPaused">
  OnNativeAppPaused
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L209"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static event EventHandler OnNativeAppPaused</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Draw_Super_OnNativeAppResumed" data-uid="DrawnUi.Draw.Super.OnNativeAppResumed">
  OnNativeAppResumed
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L211"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static event EventHandler OnNativeAppResumed</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>








</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Super.cs/#L8" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
