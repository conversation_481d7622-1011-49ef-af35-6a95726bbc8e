import{a as j}from"./chunk-OSRY5VT3.min.js";var U=j((_,g)=>{(function(n,e){typeof define=="function"&&define.amd?define(e):typeof _=="object"?g.exports=e():e()(n.lunr)})(_,function(){return function(n){if(typeof n>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof n.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");n.ro=function(){this.pipeline.reset(),this.pipeline.add(n.ro.trimmer,n.ro.stopWordFilter,n.ro.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(n.ro.stemmer))},n.ro.wordCharacters="A-Za-z\xAA\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A",n.ro.trimmer=n.trimmerSupport.generateTrimmer(n.ro.wordCharacters),n.Pipeline.registerFunction(n.ro.trimmer,"trimmer-ro"),n.ro.stemmer=function(){var e=n.stemmerSupport.Among,F=n.stemmerSupport.SnowballProgram,s=new function(){var m=[new e("",-1,3),new e("I",0,1),new e("U",0,2)],h=[new e("ea",-1,3),new e("a\u0163ia",-1,7),new e("aua",-1,2),new e("iua",-1,4),new e("a\u0163ie",-1,7),new e("ele",-1,3),new e("ile",-1,5),new e("iile",6,4),new e("iei",-1,4),new e("atei",-1,6),new e("ii",-1,4),new e("ului",-1,1),new e("ul",-1,1),new e("elor",-1,3),new e("ilor",-1,4),new e("iilor",14,4)],k=[new e("icala",-1,4),new e("iciva",-1,4),new e("ativa",-1,5),new e("itiva",-1,6),new e("icale",-1,4),new e("a\u0163iune",-1,5),new e("i\u0163iune",-1,6),new e("atoare",-1,5),new e("itoare",-1,6),new e("\u0103toare",-1,5),new e("icitate",-1,4),new e("abilitate",-1,1),new e("ibilitate",-1,2),new e("ivitate",-1,3),new e("icive",-1,4),new e("ative",-1,5),new e("itive",-1,6),new e("icali",-1,4),new e("atori",-1,5),new e("icatori",18,4),new e("itori",-1,6),new e("\u0103tori",-1,5),new e("icitati",-1,4),new e("abilitati",-1,1),new e("ivitati",-1,3),new e("icivi",-1,4),new e("ativi",-1,5),new e("itivi",-1,6),new e("icit\u0103i",-1,4),new e("abilit\u0103i",-1,1),new e("ivit\u0103i",-1,3),new e("icit\u0103\u0163i",-1,4),new e("abilit\u0103\u0163i",-1,1),new e("ivit\u0103\u0163i",-1,3),new e("ical",-1,4),new e("ator",-1,5),new e("icator",35,4),new e("itor",-1,6),new e("\u0103tor",-1,5),new e("iciv",-1,4),new e("ativ",-1,5),new e("itiv",-1,6),new e("ical\u0103",-1,4),new e("iciv\u0103",-1,4),new e("ativ\u0103",-1,5),new e("itiv\u0103",-1,6)],E=[new e("ica",-1,1),new e("abila",-1,1),new e("ibila",-1,1),new e("oasa",-1,1),new e("ata",-1,1),new e("ita",-1,1),new e("anta",-1,1),new e("ista",-1,3),new e("uta",-1,1),new e("iva",-1,1),new e("ic",-1,1),new e("ice",-1,1),new e("abile",-1,1),new e("ibile",-1,1),new e("isme",-1,3),new e("iune",-1,2),new e("oase",-1,1),new e("ate",-1,1),new e("itate",17,1),new e("ite",-1,1),new e("ante",-1,1),new e("iste",-1,3),new e("ute",-1,1),new e("ive",-1,1),new e("ici",-1,1),new e("abili",-1,1),new e("ibili",-1,1),new e("iuni",-1,2),new e("atori",-1,1),new e("osi",-1,1),new e("ati",-1,1),new e("itati",30,1),new e("iti",-1,1),new e("anti",-1,1),new e("isti",-1,3),new e("uti",-1,1),new e("i\u015Fti",-1,3),new e("ivi",-1,1),new e("it\u0103i",-1,1),new e("o\u015Fi",-1,1),new e("it\u0103\u0163i",-1,1),new e("abil",-1,1),new e("ibil",-1,1),new e("ism",-1,3),new e("ator",-1,1),new e("os",-1,1),new e("at",-1,1),new e("it",-1,1),new e("ant",-1,1),new e("ist",-1,3),new e("ut",-1,1),new e("iv",-1,1),new e("ic\u0103",-1,1),new e("abil\u0103",-1,1),new e("ibil\u0103",-1,1),new e("oas\u0103",-1,1),new e("at\u0103",-1,1),new e("it\u0103",-1,1),new e("ant\u0103",-1,1),new e("ist\u0103",-1,3),new e("ut\u0103",-1,1),new e("iv\u0103",-1,1)],A=[new e("ea",-1,1),new e("ia",-1,1),new e("esc",-1,1),new e("\u0103sc",-1,1),new e("ind",-1,1),new e("\xE2nd",-1,1),new e("are",-1,1),new e("ere",-1,1),new e("ire",-1,1),new e("\xE2re",-1,1),new e("se",-1,2),new e("ase",10,1),new e("sese",10,2),new e("ise",10,1),new e("use",10,1),new e("\xE2se",10,1),new e("e\u015Fte",-1,1),new e("\u0103\u015Fte",-1,1),new e("eze",-1,1),new e("ai",-1,1),new e("eai",19,1),new e("iai",19,1),new e("sei",-1,2),new e("e\u015Fti",-1,1),new e("\u0103\u015Fti",-1,1),new e("ui",-1,1),new e("ezi",-1,1),new e("\xE2i",-1,1),new e("a\u015Fi",-1,1),new e("se\u015Fi",-1,2),new e("ase\u015Fi",29,1),new e("sese\u015Fi",29,2),new e("ise\u015Fi",29,1),new e("use\u015Fi",29,1),new e("\xE2se\u015Fi",29,1),new e("i\u015Fi",-1,1),new e("u\u015Fi",-1,1),new e("\xE2\u015Fi",-1,1),new e("a\u0163i",-1,2),new e("ea\u0163i",38,1),new e("ia\u0163i",38,1),new e("e\u0163i",-1,2),new e("i\u0163i",-1,2),new e("\xE2\u0163i",-1,2),new e("ar\u0103\u0163i",-1,1),new e("ser\u0103\u0163i",-1,2),new e("aser\u0103\u0163i",45,1),new e("seser\u0103\u0163i",45,2),new e("iser\u0103\u0163i",45,1),new e("user\u0103\u0163i",45,1),new e("\xE2ser\u0103\u0163i",45,1),new e("ir\u0103\u0163i",-1,1),new e("ur\u0103\u0163i",-1,1),new e("\xE2r\u0103\u0163i",-1,1),new e("am",-1,1),new e("eam",54,1),new e("iam",54,1),new e("em",-1,2),new e("asem",57,1),new e("sesem",57,2),new e("isem",57,1),new e("usem",57,1),new e("\xE2sem",57,1),new e("im",-1,2),new e("\xE2m",-1,2),new e("\u0103m",-1,2),new e("ar\u0103m",65,1),new e("ser\u0103m",65,2),new e("aser\u0103m",67,1),new e("seser\u0103m",67,2),new e("iser\u0103m",67,1),new e("user\u0103m",67,1),new e("\xE2ser\u0103m",67,1),new e("ir\u0103m",65,1),new e("ur\u0103m",65,1),new e("\xE2r\u0103m",65,1),new e("au",-1,1),new e("eau",76,1),new e("iau",76,1),new e("indu",-1,1),new e("\xE2ndu",-1,1),new e("ez",-1,1),new e("easc\u0103",-1,1),new e("ar\u0103",-1,1),new e("ser\u0103",-1,2),new e("aser\u0103",84,1),new e("seser\u0103",84,2),new e("iser\u0103",84,1),new e("user\u0103",84,1),new e("\xE2ser\u0103",84,1),new e("ir\u0103",-1,1),new e("ur\u0103",-1,1),new e("\xE2r\u0103",-1,1),new e("eaz\u0103",-1,1)],C=[new e("a",-1,1),new e("e",-1,1),new e("ie",1,1),new e("i",-1,1),new e("\u0103",-1,1)],t=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,0,2,32,0,0,4],w,l,f,a,i=new F;this.setCurrent=function(r){i.setCurrent(r)},this.getCurrent=function(){return i.getCurrent()};function p(r,u){i.eq_s(1,r)&&(i.ket=i.cursor,i.in_grouping(t,97,259)&&i.slice_from(u))}function B(){for(var r,u;r=i.cursor,i.in_grouping(t,97,259)&&(u=i.cursor,i.bra=u,p("u","U"),i.cursor=u,p("i","I")),i.cursor=r,!(i.cursor>=i.limit);)i.cursor++}function v(){if(i.out_grouping(t,97,259)){for(;!i.in_grouping(t,97,259);){if(i.cursor>=i.limit)return!0;i.cursor++}return!1}return!0}function D(){if(i.in_grouping(t,97,259))for(;!i.out_grouping(t,97,259);){if(i.cursor>=i.limit)return!0;i.cursor++}return!1}function x(){var r=i.cursor,u,o;if(i.in_grouping(t,97,259))if(u=i.cursor,v()){if(i.cursor=u,!D()){a=i.cursor;return}}else{a=i.cursor;return}i.cursor=r,i.out_grouping(t,97,259)&&(o=i.cursor,v()&&(i.cursor=o,i.in_grouping(t,97,259)&&i.cursor<i.limit&&i.cursor++),a=i.cursor)}function d(){for(;!i.in_grouping(t,97,259);){if(i.cursor>=i.limit)return!1;i.cursor++}for(;!i.out_grouping(t,97,259);){if(i.cursor>=i.limit)return!1;i.cursor++}return!0}function z(){var r=i.cursor;a=i.limit,f=a,l=a,x(),i.cursor=r,d()&&(f=i.cursor,d()&&(l=i.cursor))}function P(){for(var r;;){if(i.bra=i.cursor,r=i.find_among(m,3),r)switch(i.ket=i.cursor,r){case 1:i.slice_from("i");continue;case 2:i.slice_from("u");continue;case 3:if(i.cursor>=i.limit)break;i.cursor++;continue}break}}function S(){return a<=i.cursor}function b(){return f<=i.cursor}function q(){return l<=i.cursor}function I(){var r,u;if(i.ket=i.cursor,r=i.find_among_b(h,16),r&&(i.bra=i.cursor,b()))switch(r){case 1:i.slice_del();break;case 2:i.slice_from("a");break;case 3:i.slice_from("e");break;case 4:i.slice_from("i");break;case 5:u=i.limit-i.cursor,i.eq_s_b(2,"ab")||(i.cursor=i.limit-u,i.slice_from("i"));break;case 6:i.slice_from("at");break;case 7:i.slice_from("a\u0163i");break}}function W(){var r,u=i.limit-i.cursor;if(i.ket=i.cursor,r=i.find_among_b(k,46),r&&(i.bra=i.cursor,b())){switch(r){case 1:i.slice_from("abil");break;case 2:i.slice_from("ibil");break;case 3:i.slice_from("iv");break;case 4:i.slice_from("ic");break;case 5:i.slice_from("at");break;case 6:i.slice_from("it");break}return w=!0,i.cursor=i.limit-u,!0}return!1}function L(){var r,u;for(w=!1;;)if(u=i.limit-i.cursor,!W()){i.cursor=i.limit-u;break}if(i.ket=i.cursor,r=i.find_among_b(E,62),r&&(i.bra=i.cursor,q())){switch(r){case 1:i.slice_del();break;case 2:i.eq_s_b(1,"\u0163")&&(i.bra=i.cursor,i.slice_from("t"));break;case 3:i.slice_from("ist");break}w=!0}}function R(){var r,u,o;if(i.cursor>=a){if(u=i.limit_backward,i.limit_backward=a,i.ket=i.cursor,r=i.find_among_b(A,94),r)switch(i.bra=i.cursor,r){case 1:if(o=i.limit-i.cursor,!i.out_grouping_b(t,97,259)&&(i.cursor=i.limit-o,!i.eq_s_b(1,"u")))break;case 2:i.slice_del();break}i.limit_backward=u}}function y(){var r;i.ket=i.cursor,r=i.find_among_b(C,5),r&&(i.bra=i.cursor,S()&&r==1&&i.slice_del())}this.stem=function(){var r=i.cursor;return B(),i.cursor=r,z(),i.limit_backward=r,i.cursor=i.limit,I(),i.cursor=i.limit,L(),i.cursor=i.limit,w||(i.cursor=i.limit,R(),i.cursor=i.limit),y(),i.cursor=i.limit_backward,P(),!0}};return function(c){return typeof c.update=="function"?c.update(function(m){return s.setCurrent(m),s.stem(),s.getCurrent()}):(s.setCurrent(c),s.stem(),s.getCurrent())}}(),n.Pipeline.registerFunction(n.ro.stemmer,"stemmer-ro"),n.ro.stopWordFilter=n.generateStopWordFilter("acea aceasta aceast\u0103 aceea acei aceia acel acela acele acelea acest acesta aceste acestea ace\u015Fti ace\u015Ftia acolo acord acum ai aia aib\u0103 aici al ale alea altceva altcineva am ar are asemenea asta astea ast\u0103zi asupra au avea avem ave\u0163i azi a\u015F a\u015Fadar a\u0163i bine bucur bun\u0103 ca care caut ce cel ceva chiar cinci cine cineva contra cu cum cumva cur\xE2nd cur\xEEnd c\xE2nd c\xE2t c\xE2te c\xE2tva c\xE2\u0163i c\xEEnd c\xEEt c\xEEte c\xEEtva c\xEE\u0163i c\u0103 c\u0103ci c\u0103rei c\u0103ror c\u0103rui c\u0103tre da dac\u0103 dar datorit\u0103 dat\u0103 dau de deci deja deoarece departe de\u015Fi din dinaintea dintr- dintre doi doilea dou\u0103 drept dup\u0103 d\u0103 ea ei el ele eram este eu e\u015Fti face fata fi fie fiecare fii fim fiu fi\u0163i frumos f\u0103r\u0103 gra\u0163ie halb\u0103 iar ieri la le li lor lui l\xE2ng\u0103 l\xEEng\u0103 mai mea mei mele mereu meu mi mie mine mult mult\u0103 mul\u0163i mul\u0163umesc m\xE2ine m\xEEine m\u0103 ne nevoie nici nic\u0103ieri nimeni nimeri nimic ni\u015Fte noastre noastr\u0103 noi noroc nostru nou\u0103 no\u015Ftri nu opt ori oricare orice oricine oricum oric\xE2nd oric\xE2t oric\xEEnd oric\xEEt oriunde patra patru patrulea pe pentru peste pic poate pot prea prima primul prin pu\u0163in pu\u0163ina pu\u0163in\u0103 p\xE2n\u0103 p\xEEn\u0103 rog sa sale sau se spate spre sub sunt suntem sunte\u0163i sut\u0103 s\xEEnt s\xEEntem s\xEEnte\u0163i s\u0103 s\u0103i s\u0103u ta tale te timp tine toate toat\u0103 tot totu\u015Fi to\u0163i trei treia treilea tu t\u0103i t\u0103u un una unde undeva unei uneia unele uneori unii unor unora unu unui unuia unul vi voastre voastr\u0103 voi vostru vou\u0103 vo\u015Ftri vreme vreo vreun v\u0103 zece zero zi zice \xEEi \xEEl \xEEmi \xEEmpotriva \xEEn  \xEEnainte \xEEnaintea \xEEncotro \xEEnc\xE2t \xEEnc\xEEt \xEEntre \xEEntruc\xE2t \xEEntruc\xEEt \xEE\u0163i \u0103la \u0103lea \u0103sta \u0103stea \u0103\u015Ftia \u015Fapte \u015Fase \u015Fi \u015Ftiu \u0163i \u0163ie".split(" ")),n.Pipeline.registerFunction(n.ro.stopWordFilter,"stopWordFilter-ro")}})});export default U();
/*! Bundled license information:

lunr-languages/lunr.ro.js:
  (*!
   * Lunr languages, `Romanian` language
   * https://github.com/MihaiValentin/lunr-languages
   *
   * Copyright 2014, Mihai Valentin
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.ro-O76266FJ.min.js.map
