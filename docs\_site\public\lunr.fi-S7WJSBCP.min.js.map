{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.fi.js"], "sourcesContent": ["/*!\n * Lunr languages, `Finnish` language\n * https://github.com/MihaiValentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.fi = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.fi.trimmer,\n        lunr.fi.stopWordFilter,\n        lunr.fi.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.fi.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.fi.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.fi.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.fi.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.fi.trimmer, 'trimmer-fi');\n\n    /* lunr stemmer function */\n    lunr.fi.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function FinnishStemmer() {\n          var a_0 = [new Among(\"pa\", -1, 1), new Among(\"sti\", -1, 2),\n              new Among(\"kaan\", -1, 1), new Among(\"han\", -1, 1),\n              new Among(\"kin\", -1, 1), new Among(\"h\\u00E4n\", -1, 1),\n              new Among(\"k\\u00E4\\u00E4n\", -1, 1), new Among(\"ko\", -1, 1),\n              new Among(\"p\\u00E4\", -1, 1), new Among(\"k\\u00F6\", -1, 1)\n            ],\n            a_1 = [\n              new Among(\"lla\", -1, -1), new Among(\"na\", -1, -1),\n              new Among(\"ssa\", -1, -1), new Among(\"ta\", -1, -1),\n              new Among(\"lta\", 3, -1), new Among(\"sta\", 3, -1)\n            ],\n            a_2 = [\n              new Among(\"ll\\u00E4\", -1, -1), new Among(\"n\\u00E4\", -1, -1),\n              new Among(\"ss\\u00E4\", -1, -1), new Among(\"t\\u00E4\", -1, -1),\n              new Among(\"lt\\u00E4\", 3, -1), new Among(\"st\\u00E4\", 3, -1)\n            ],\n            a_3 = [\n              new Among(\"lle\", -1, -1), new Among(\"ine\", -1, -1)\n            ],\n            a_4 = [\n              new Among(\"nsa\", -1, 3), new Among(\"mme\", -1, 3),\n              new Among(\"nne\", -1, 3), new Among(\"ni\", -1, 2),\n              new Among(\"si\", -1, 1), new Among(\"an\", -1, 4),\n              new Among(\"en\", -1, 6), new Among(\"\\u00E4n\", -1, 5),\n              new Among(\"ns\\u00E4\", -1, 3)\n            ],\n            a_5 = [new Among(\"aa\", -1, -1),\n              new Among(\"ee\", -1, -1), new Among(\"ii\", -1, -1),\n              new Among(\"oo\", -1, -1), new Among(\"uu\", -1, -1),\n              new Among(\"\\u00E4\\u00E4\", -1, -1),\n              new Among(\"\\u00F6\\u00F6\", -1, -1)\n            ],\n            a_6 = [new Among(\"a\", -1, 8),\n              new Among(\"lla\", 0, -1), new Among(\"na\", 0, -1),\n              new Among(\"ssa\", 0, -1), new Among(\"ta\", 0, -1),\n              new Among(\"lta\", 4, -1), new Among(\"sta\", 4, -1),\n              new Among(\"tta\", 4, 9), new Among(\"lle\", -1, -1),\n              new Among(\"ine\", -1, -1), new Among(\"ksi\", -1, -1),\n              new Among(\"n\", -1, 7), new Among(\"han\", 11, 1),\n              new Among(\"den\", 11, -1, r_VI), new Among(\"seen\", 11, -1, r_LONG),\n              new Among(\"hen\", 11, 2), new Among(\"tten\", 11, -1, r_VI),\n              new Among(\"hin\", 11, 3), new Among(\"siin\", 11, -1, r_VI),\n              new Among(\"hon\", 11, 4), new Among(\"h\\u00E4n\", 11, 5),\n              new Among(\"h\\u00F6n\", 11, 6), new Among(\"\\u00E4\", -1, 8),\n              new Among(\"ll\\u00E4\", 22, -1), new Among(\"n\\u00E4\", 22, -1),\n              new Among(\"ss\\u00E4\", 22, -1), new Among(\"t\\u00E4\", 22, -1),\n              new Among(\"lt\\u00E4\", 26, -1), new Among(\"st\\u00E4\", 26, -1),\n              new Among(\"tt\\u00E4\", 26, 9)\n            ],\n            a_7 = [new Among(\"eja\", -1, -1),\n              new Among(\"mma\", -1, 1), new Among(\"imma\", 1, -1),\n              new Among(\"mpa\", -1, 1), new Among(\"impa\", 3, -1),\n              new Among(\"mmi\", -1, 1), new Among(\"immi\", 5, -1),\n              new Among(\"mpi\", -1, 1), new Among(\"impi\", 7, -1),\n              new Among(\"ej\\u00E4\", -1, -1), new Among(\"mm\\u00E4\", -1, 1),\n              new Among(\"imm\\u00E4\", 10, -1), new Among(\"mp\\u00E4\", -1, 1),\n              new Among(\"imp\\u00E4\", 12, -1)\n            ],\n            a_8 = [new Among(\"i\", -1, -1),\n              new Among(\"j\", -1, -1)\n            ],\n            a_9 = [new Among(\"mma\", -1, 1),\n              new Among(\"imma\", 0, -1)\n            ],\n            g_AEI = [17, 1, 0, 0, 0, 0, 0, 0, 0, 0,\n              0, 0, 0, 0, 0, 0, 8\n            ],\n            g_V1 = [17, 65, 16, 1, 0, 0, 0, 0, 0, 0, 0,\n              0, 0, 0, 0, 0, 8, 0, 32\n            ],\n            g_V2 = [17, 65, 16, 0, 0, 0, 0, 0, 0, 0,\n              0, 0, 0, 0, 0, 0, 8, 0, 32\n            ],\n            g_particle_end = [17, 97, 24, 1, 0, 0,\n              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 32\n            ],\n            B_ending_removed, S_x, I_p2, I_p1, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function r_mark_regions() {\n            I_p1 = sbp.limit;\n            I_p2 = I_p1;\n            if (!habr1()) {\n              I_p1 = sbp.cursor;\n              if (!habr1())\n                I_p2 = sbp.cursor;\n            }\n          }\n\n          function habr1() {\n            var v_1;\n            while (true) {\n              v_1 = sbp.cursor;\n              if (sbp.in_grouping(g_V1, 97, 246))\n                break;\n              sbp.cursor = v_1;\n              if (v_1 >= sbp.limit)\n                return true;\n              sbp.cursor++;\n            }\n            sbp.cursor = v_1;\n            while (!sbp.out_grouping(g_V1, 97, 246)) {\n              if (sbp.cursor >= sbp.limit)\n                return true;\n              sbp.cursor++;\n            }\n            return false;\n          }\n\n          function r_R2() {\n            return I_p2 <= sbp.cursor;\n          }\n\n          function r_particle_etc() {\n            var among_var, v_1;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_0, 10);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                sbp.limit_backward = v_1;\n                switch (among_var) {\n                  case 1:\n                    if (!sbp.in_grouping_b(g_particle_end, 97, 246))\n                      return;\n                    break;\n                  case 2:\n                    if (!r_R2())\n                      return;\n                    break;\n                }\n                sbp.slice_del();\n              } else\n                sbp.limit_backward = v_1;\n            }\n          }\n\n          function r_possessive() {\n            var among_var, v_1, v_2;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_4, 9);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                sbp.limit_backward = v_1;\n                switch (among_var) {\n                  case 1:\n                    v_2 = sbp.limit - sbp.cursor;\n                    if (!sbp.eq_s_b(1, \"k\")) {\n                      sbp.cursor = sbp.limit - v_2;\n                      sbp.slice_del();\n                    }\n                    break;\n                  case 2:\n                    sbp.slice_del();\n                    sbp.ket = sbp.cursor;\n                    if (sbp.eq_s_b(3, \"kse\")) {\n                      sbp.bra = sbp.cursor;\n                      sbp.slice_from(\"ksi\");\n                    }\n                    break;\n                  case 3:\n                    sbp.slice_del();\n                    break;\n                  case 4:\n                    if (sbp.find_among_b(a_1, 6))\n                      sbp.slice_del();\n                    break;\n                  case 5:\n                    if (sbp.find_among_b(a_2, 6))\n                      sbp.slice_del();\n                    break;\n                  case 6:\n                    if (sbp.find_among_b(a_3, 2))\n                      sbp.slice_del();\n                    break;\n                }\n              } else\n                sbp.limit_backward = v_1;\n            }\n          }\n\n          function r_LONG() {\n            return sbp.find_among_b(a_5, 7);\n          }\n\n          function r_VI() {\n            return sbp.eq_s_b(1, \"i\") && sbp.in_grouping_b(g_V2, 97, 246);\n          }\n\n          function r_case_ending() {\n            var among_var, v_1, v_2;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_6, 30);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                sbp.limit_backward = v_1;\n                switch (among_var) {\n                  case 1:\n                    if (!sbp.eq_s_b(1, \"a\"))\n                      return;\n                    break;\n                  case 2:\n                  case 9:\n                    if (!sbp.eq_s_b(1, \"e\"))\n                      return;\n                    break;\n                  case 3:\n                    if (!sbp.eq_s_b(1, \"i\"))\n                      return;\n                    break;\n                  case 4:\n                    if (!sbp.eq_s_b(1, \"o\"))\n                      return;\n                    break;\n                  case 5:\n                    if (!sbp.eq_s_b(1, \"\\u00E4\"))\n                      return;\n                    break;\n                  case 6:\n                    if (!sbp.eq_s_b(1, \"\\u00F6\"))\n                      return;\n                    break;\n                  case 7:\n                    v_2 = sbp.limit - sbp.cursor;\n                    if (!r_LONG()) {\n                      sbp.cursor = sbp.limit - v_2;\n                      if (!sbp.eq_s_b(2, \"ie\")) {\n                        sbp.cursor = sbp.limit - v_2;\n                        break;\n                      }\n                    }\n                    sbp.cursor = sbp.limit - v_2;\n                    if (sbp.cursor <= sbp.limit_backward) {\n                      sbp.cursor = sbp.limit - v_2;\n                      break;\n                    }\n                    sbp.cursor--;\n                    sbp.bra = sbp.cursor;\n                    break;\n                  case 8:\n                    if (!sbp.in_grouping_b(g_V1, 97, 246) ||\n                      !sbp.out_grouping_b(g_V1, 97, 246))\n                      return;\n                    break;\n                }\n                sbp.slice_del();\n                B_ending_removed = true;\n              } else\n                sbp.limit_backward = v_1;\n            }\n          }\n\n          function r_other_endings() {\n            var among_var, v_1, v_2;\n            if (sbp.cursor >= I_p2) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p2;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_7, 14);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                sbp.limit_backward = v_1;\n                if (among_var == 1) {\n                  v_2 = sbp.limit - sbp.cursor;\n                  if (sbp.eq_s_b(2, \"po\"))\n                    return;\n                  sbp.cursor = sbp.limit - v_2;\n                }\n                sbp.slice_del();\n              } else\n                sbp.limit_backward = v_1;\n            }\n          }\n\n          function r_i_plural() {\n            var v_1;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              if (sbp.find_among_b(a_8, 2)) {\n                sbp.bra = sbp.cursor;\n                sbp.limit_backward = v_1;\n                sbp.slice_del();\n              } else\n                sbp.limit_backward = v_1;\n            }\n          }\n\n          function r_t_plural() {\n            var among_var, v_1, v_2, v_3, v_4, v_5;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              if (sbp.eq_s_b(1, \"t\")) {\n                sbp.bra = sbp.cursor;\n                v_2 = sbp.limit - sbp.cursor;\n                if (sbp.in_grouping_b(g_V1, 97, 246)) {\n                  sbp.cursor = sbp.limit - v_2;\n                  sbp.slice_del();\n                  sbp.limit_backward = v_1;\n                  v_3 = sbp.limit - sbp.cursor;\n                  if (sbp.cursor >= I_p2) {\n                    sbp.cursor = I_p2;\n                    v_4 = sbp.limit_backward;\n                    sbp.limit_backward = sbp.cursor;\n                    sbp.cursor = sbp.limit - v_3;\n                    sbp.ket = sbp.cursor;\n                    among_var = sbp.find_among_b(a_9, 2);\n                    if (among_var) {\n                      sbp.bra = sbp.cursor;\n                      sbp.limit_backward = v_4;\n                      if (among_var == 1) {\n                        v_5 = sbp.limit - sbp.cursor;\n                        if (sbp.eq_s_b(2, \"po\"))\n                          return;\n                        sbp.cursor = sbp.limit - v_5;\n                      }\n                      sbp.slice_del();\n                      return;\n                    }\n                  }\n                }\n              }\n              sbp.limit_backward = v_1;\n            }\n          }\n\n          function r_tidy() {\n            var v_1, v_2, v_3, v_4;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              v_2 = sbp.limit - sbp.cursor;\n              if (r_LONG()) {\n                sbp.cursor = sbp.limit - v_2;\n                sbp.ket = sbp.cursor;\n                if (sbp.cursor > sbp.limit_backward) {\n                  sbp.cursor--;\n                  sbp.bra = sbp.cursor;\n                  sbp.slice_del();\n                }\n              }\n              sbp.cursor = sbp.limit - v_2;\n              sbp.ket = sbp.cursor;\n              if (sbp.in_grouping_b(g_AEI, 97, 228)) {\n                sbp.bra = sbp.cursor;\n                if (sbp.out_grouping_b(g_V1, 97, 246))\n                  sbp.slice_del();\n              }\n              sbp.cursor = sbp.limit - v_2;\n              sbp.ket = sbp.cursor;\n              if (sbp.eq_s_b(1, \"j\")) {\n                sbp.bra = sbp.cursor;\n                v_3 = sbp.limit - sbp.cursor;\n                if (!sbp.eq_s_b(1, \"o\")) {\n                  sbp.cursor = sbp.limit - v_3;\n                  if (sbp.eq_s_b(1, \"u\"))\n                    sbp.slice_del();\n                } else\n                  sbp.slice_del();\n              }\n              sbp.cursor = sbp.limit - v_2;\n              sbp.ket = sbp.cursor;\n              if (sbp.eq_s_b(1, \"o\")) {\n                sbp.bra = sbp.cursor;\n                if (sbp.eq_s_b(1, \"j\"))\n                  sbp.slice_del();\n              }\n              sbp.cursor = sbp.limit - v_2;\n              sbp.limit_backward = v_1;\n              while (true) {\n                v_4 = sbp.limit - sbp.cursor;\n                if (sbp.out_grouping_b(g_V1, 97, 246)) {\n                  sbp.cursor = sbp.limit - v_4;\n                  break;\n                }\n                sbp.cursor = sbp.limit - v_4;\n                if (sbp.cursor <= sbp.limit_backward)\n                  return;\n                sbp.cursor--;\n              }\n              sbp.ket = sbp.cursor;\n              if (sbp.cursor > sbp.limit_backward) {\n                sbp.cursor--;\n                sbp.bra = sbp.cursor;\n                S_x = sbp.slice_to();\n                if (sbp.eq_v_b(S_x))\n                  sbp.slice_del();\n              }\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_mark_regions();\n            B_ending_removed = false;\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            r_particle_etc();\n            sbp.cursor = sbp.limit;\n            r_possessive();\n            sbp.cursor = sbp.limit;\n            r_case_ending();\n            sbp.cursor = sbp.limit;\n            r_other_endings();\n            sbp.cursor = sbp.limit;\n            if (B_ending_removed) {\n              r_i_plural();\n              sbp.cursor = sbp.limit;\n            } else {\n              sbp.cursor = sbp.limit;\n              r_t_plural();\n              sbp.cursor = sbp.limit;\n            }\n            r_tidy();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.fi.stemmer, 'stemmer-fi');\n\n    lunr.fi.stopWordFilter = lunr.generateStopWordFilter('ei eivät emme en et ette että he heidän heidät heihin heille heillä heiltä heissä heistä heitä hän häneen hänelle hänellä häneltä hänen hänessä hänestä hänet häntä itse ja johon joiden joihin joiksi joilla joille joilta joina joissa joista joita joka joksi jolla jolle jolta jona jonka jos jossa josta jota jotka kanssa keiden keihin keiksi keille keillä keiltä keinä keissä keistä keitä keneen keneksi kenelle kenellä keneltä kenen kenenä kenessä kenestä kenet ketkä ketkä ketä koska kuin kuka kun me meidän meidät meihin meille meillä meiltä meissä meistä meitä mihin miksi mikä mille millä miltä minkä minkä minua minulla minulle minulta minun minussa minusta minut minuun minä minä missä mistä mitkä mitä mukaan mutta ne niiden niihin niiksi niille niillä niiltä niin niin niinä niissä niistä niitä noiden noihin noiksi noilla noille noilta noin noina noissa noista noita nuo nyt näiden näihin näiksi näille näillä näiltä näinä näissä näistä näitä nämä ole olemme olen olet olette oli olimme olin olisi olisimme olisin olisit olisitte olisivat olit olitte olivat olla olleet ollut on ovat poikki se sekä sen siihen siinä siitä siksi sille sillä sillä siltä sinua sinulla sinulle sinulta sinun sinussa sinusta sinut sinuun sinä sinä sitä tai te teidän teidät teihin teille teillä teiltä teissä teistä teitä tuo tuohon tuoksi tuolla tuolle tuolta tuon tuona tuossa tuosta tuota tähän täksi tälle tällä tältä tämä tämän tänä tässä tästä tätä vaan vai vaikka yli'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.fi.stopWordFilter, 'stopWordFilter-fi');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAA0B,CACjC,IAAIC,EAAM,CAAC,IAAIH,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACrD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EACpD,IAAIA,EAAM,aAAkB,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EACzD,IAAIA,EAAM,QAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAW,GAAI,CAAC,CACzD,EACAI,EAAM,CACJ,IAAIJ,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAChD,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAChD,IAAIA,EAAM,MAAO,EAAG,EAAE,EAAG,IAAIA,EAAM,MAAO,EAAG,EAAE,CACjD,EACAK,EAAM,CACJ,IAAIL,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAW,GAAI,EAAE,EAC1D,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAW,GAAI,EAAE,EAC1D,IAAIA,EAAM,SAAY,EAAG,EAAE,EAAG,IAAIA,EAAM,SAAY,EAAG,EAAE,CAC3D,EACAM,EAAM,CACJ,IAAIN,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,CACnD,EACAO,EAAM,CACJ,IAAIP,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC7C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAW,GAAI,CAAC,EAClD,IAAIA,EAAM,SAAY,GAAI,CAAC,CAC7B,EACAQ,EAAM,CAAC,IAAIR,EAAM,KAAM,GAAI,EAAE,EAC3B,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,WAAgB,GAAI,EAAE,EAChC,IAAIA,EAAM,WAAgB,GAAI,EAAE,CAClC,EACAS,EAAM,CAAC,IAAIT,EAAM,IAAK,GAAI,CAAC,EACzB,IAAIA,EAAM,MAAO,EAAG,EAAE,EAAG,IAAIA,EAAM,KAAM,EAAG,EAAE,EAC9C,IAAIA,EAAM,MAAO,EAAG,EAAE,EAAG,IAAIA,EAAM,KAAM,EAAG,EAAE,EAC9C,IAAIA,EAAM,MAAO,EAAG,EAAE,EAAG,IAAIA,EAAM,MAAO,EAAG,EAAE,EAC/C,IAAIA,EAAM,MAAO,EAAG,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EAC/C,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACjD,IAAIA,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC7C,IAAIA,EAAM,MAAO,GAAI,GAAIU,CAAI,EAAG,IAAIV,EAAM,OAAQ,GAAI,GAAIW,CAAM,EAChE,IAAIX,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,GAAIU,CAAI,EACvD,IAAIV,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,GAAIU,CAAI,EACvD,IAAIV,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EACpD,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,GAAI,CAAC,EACvD,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAW,GAAI,EAAE,EAC1D,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAW,GAAI,EAAE,EAC1D,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAY,GAAI,EAAE,EAC3D,IAAIA,EAAM,SAAY,GAAI,CAAC,CAC7B,EACAY,EAAM,CAAC,IAAIZ,EAAM,MAAO,GAAI,EAAE,EAC5B,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,EAAE,EAChD,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EAC1D,IAAIA,EAAM,UAAa,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EAC3D,IAAIA,EAAM,UAAa,GAAI,EAAE,CAC/B,EACAa,EAAM,CAAC,IAAIb,EAAM,IAAK,GAAI,EAAE,EAC1B,IAAIA,EAAM,IAAK,GAAI,EAAE,CACvB,EACAc,EAAM,CAAC,IAAId,EAAM,MAAO,GAAI,CAAC,EAC3B,IAAIA,EAAM,OAAQ,EAAG,EAAE,CACzB,EACAe,EAAQ,CAAC,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EACnC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CACpB,EACAC,EAAO,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EACvC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EACvB,EACAC,EAAO,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EACpC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAC1B,EACAC,EAAiB,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAClC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EACtC,EACAC,EAAkBC,EAAKC,EAAMC,EAAMC,EAAM,IAAItB,EAC/C,KAAK,WAAa,SAASuB,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,GAAiB,CACxBH,EAAOC,EAAI,MACXF,EAAOC,EACFI,EAAM,IACTJ,EAAOC,EAAI,OACNG,EAAM,IACTL,EAAOE,EAAI,QAEjB,CAEA,SAASG,GAAQ,CAEf,QADIC,EAEFA,EAAMJ,EAAI,OACN,CAAAA,EAAI,YAAYP,EAAM,GAAI,GAAG,GAFtB,CAKX,GADAO,EAAI,OAASI,EACTA,GAAOJ,EAAI,MACb,MAAO,GACTA,EAAI,QACN,CAEA,IADAA,EAAI,OAASI,EACN,CAACJ,EAAI,aAAaP,EAAM,GAAI,GAAG,GAAG,CACvC,GAAIO,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASK,GAAO,CACd,OAAOP,GAAQE,EAAI,MACrB,CAEA,SAASM,GAAiB,CACxB,IAAIC,EAAWH,EACf,GAAIJ,EAAI,QAAUD,EAKhB,GAJAK,EAAMJ,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAapB,EAAK,EAAE,EAChC2B,EAAW,CAGb,OAFAP,EAAI,IAAMA,EAAI,OACdA,EAAI,eAAiBI,EACbG,EAAW,CACjB,IAAK,GACH,GAAI,CAACP,EAAI,cAAcL,EAAgB,GAAI,GAAG,EAC5C,OACF,MACF,IAAK,GACH,GAAI,CAACU,EAAK,EACR,OACF,KACJ,CACAL,EAAI,UAAU,CAChB,MACEA,EAAI,eAAiBI,CAE3B,CAEA,SAASI,GAAe,CACtB,IAAID,EAAWH,EAAKK,EACpB,GAAIT,EAAI,QAAUD,EAKhB,GAJAK,EAAMJ,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAahB,EAAK,CAAC,EAC/BuB,EAGF,OAFAP,EAAI,IAAMA,EAAI,OACdA,EAAI,eAAiBI,EACbG,EAAW,CACjB,IAAK,GACHE,EAAMT,EAAI,MAAQA,EAAI,OACjBA,EAAI,OAAO,EAAG,GAAG,IACpBA,EAAI,OAASA,EAAI,MAAQS,EACzBT,EAAI,UAAU,GAEhB,MACF,IAAK,GACHA,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,KAAK,IACrBA,EAAI,IAAMA,EAAI,OACdA,EAAI,WAAW,KAAK,GAEtB,MACF,IAAK,GACHA,EAAI,UAAU,EACd,MACF,IAAK,GACCA,EAAI,aAAanB,EAAK,CAAC,GACzBmB,EAAI,UAAU,EAChB,MACF,IAAK,GACCA,EAAI,aAAalB,EAAK,CAAC,GACzBkB,EAAI,UAAU,EAChB,MACF,IAAK,GACCA,EAAI,aAAajB,EAAK,CAAC,GACzBiB,EAAI,UAAU,EAChB,KACJ,MAEAA,EAAI,eAAiBI,CAE3B,CAEA,SAAShB,GAAS,CAChB,OAAOY,EAAI,aAAaf,EAAK,CAAC,CAChC,CAEA,SAASE,GAAO,CACd,OAAOa,EAAI,OAAO,EAAG,GAAG,GAAKA,EAAI,cAAcN,EAAM,GAAI,GAAG,CAC9D,CAEA,SAASgB,GAAgB,CACvB,IAAIH,EAAWH,EAAKK,EACpB,GAAIT,EAAI,QAAUD,EAKhB,GAJAK,EAAMJ,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAad,EAAK,EAAE,EAChCqB,EAAW,CAGb,OAFAP,EAAI,IAAMA,EAAI,OACdA,EAAI,eAAiBI,EACbG,EAAW,CACjB,IAAK,GACH,GAAI,CAACP,EAAI,OAAO,EAAG,GAAG,EACpB,OACF,MACF,IAAK,GACL,IAAK,GACH,GAAI,CAACA,EAAI,OAAO,EAAG,GAAG,EACpB,OACF,MACF,IAAK,GACH,GAAI,CAACA,EAAI,OAAO,EAAG,GAAG,EACpB,OACF,MACF,IAAK,GACH,GAAI,CAACA,EAAI,OAAO,EAAG,GAAG,EACpB,OACF,MACF,IAAK,GACH,GAAI,CAACA,EAAI,OAAO,EAAG,MAAQ,EACzB,OACF,MACF,IAAK,GACH,GAAI,CAACA,EAAI,OAAO,EAAG,MAAQ,EACzB,OACF,MACF,IAAK,GAEH,GADAS,EAAMT,EAAI,MAAQA,EAAI,OAClB,CAACZ,EAAO,IACVY,EAAI,OAASA,EAAI,MAAQS,EACrB,CAACT,EAAI,OAAO,EAAG,IAAI,GAAG,CACxBA,EAAI,OAASA,EAAI,MAAQS,EACzB,KACF,CAGF,GADAT,EAAI,OAASA,EAAI,MAAQS,EACrBT,EAAI,QAAUA,EAAI,eAAgB,CACpCA,EAAI,OAASA,EAAI,MAAQS,EACzB,KACF,CACAT,EAAI,SACJA,EAAI,IAAMA,EAAI,OACd,MACF,IAAK,GACH,GAAI,CAACA,EAAI,cAAcP,EAAM,GAAI,GAAG,GAClC,CAACO,EAAI,eAAeP,EAAM,GAAI,GAAG,EACjC,OACF,KACJ,CACAO,EAAI,UAAU,EACdJ,EAAmB,EACrB,MACEI,EAAI,eAAiBI,CAE3B,CAEA,SAASO,GAAkB,CACzB,IAAIJ,EAAWH,EAAKK,EACpB,GAAIT,EAAI,QAAUF,EAKhB,GAJAM,EAAMJ,EAAI,eACVA,EAAI,eAAiBF,EACrBE,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaX,EAAK,EAAE,EAChCkB,EAAW,CAGb,GAFAP,EAAI,IAAMA,EAAI,OACdA,EAAI,eAAiBI,EACjBG,GAAa,EAAG,CAElB,GADAE,EAAMT,EAAI,MAAQA,EAAI,OAClBA,EAAI,OAAO,EAAG,IAAI,EACpB,OACFA,EAAI,OAASA,EAAI,MAAQS,CAC3B,CACAT,EAAI,UAAU,CAChB,MACEA,EAAI,eAAiBI,CAE3B,CAEA,SAASQ,GAAa,CACpB,IAAIR,EACAJ,EAAI,QAAUD,IAChBK,EAAMJ,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACVA,EAAI,aAAaV,EAAK,CAAC,GACzBU,EAAI,IAAMA,EAAI,OACdA,EAAI,eAAiBI,EACrBJ,EAAI,UAAU,GAEdA,EAAI,eAAiBI,EAE3B,CAEA,SAASS,GAAa,CACpB,IAAIN,EAAWH,EAAKK,EAAKK,EAAKC,EAAKC,EACnC,GAAIhB,EAAI,QAAUD,EAAM,CAItB,GAHAK,EAAMJ,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,IACnBA,EAAI,IAAMA,EAAI,OACdS,EAAMT,EAAI,MAAQA,EAAI,OAClBA,EAAI,cAAcP,EAAM,GAAI,GAAG,IACjCO,EAAI,OAASA,EAAI,MAAQS,EACzBT,EAAI,UAAU,EACdA,EAAI,eAAiBI,EACrBU,EAAMd,EAAI,MAAQA,EAAI,OAClBA,EAAI,QAAUF,IAChBE,EAAI,OAASF,EACbiB,EAAMf,EAAI,eACVA,EAAI,eAAiBA,EAAI,OACzBA,EAAI,OAASA,EAAI,MAAQc,EACzBd,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaT,EAAK,CAAC,EAC/BgB,KAAW,CAGb,GAFAP,EAAI,IAAMA,EAAI,OACdA,EAAI,eAAiBe,EACjBR,GAAa,EAAG,CAElB,GADAS,EAAMhB,EAAI,MAAQA,EAAI,OAClBA,EAAI,OAAO,EAAG,IAAI,EACpB,OACFA,EAAI,OAASA,EAAI,MAAQgB,CAC3B,CACAhB,EAAI,UAAU,EACd,MACF,CAINA,EAAI,eAAiBI,CACvB,CACF,CAEA,SAASa,GAAS,CAChB,IAAIb,EAAKK,EAAKK,EAAKC,EACnB,GAAIf,EAAI,QAAUD,EAAM,CAyCtB,IAxCAK,EAAMJ,EAAI,eACVA,EAAI,eAAiBD,EACrBU,EAAMT,EAAI,MAAQA,EAAI,OAClBZ,EAAO,IACTY,EAAI,OAASA,EAAI,MAAQS,EACzBT,EAAI,IAAMA,EAAI,OACVA,EAAI,OAASA,EAAI,iBACnBA,EAAI,SACJA,EAAI,IAAMA,EAAI,OACdA,EAAI,UAAU,IAGlBA,EAAI,OAASA,EAAI,MAAQS,EACzBT,EAAI,IAAMA,EAAI,OACVA,EAAI,cAAcR,EAAO,GAAI,GAAG,IAClCQ,EAAI,IAAMA,EAAI,OACVA,EAAI,eAAeP,EAAM,GAAI,GAAG,GAClCO,EAAI,UAAU,GAElBA,EAAI,OAASA,EAAI,MAAQS,EACzBT,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,IACnBA,EAAI,IAAMA,EAAI,OACdc,EAAMd,EAAI,MAAQA,EAAI,OACjBA,EAAI,OAAO,EAAG,GAAG,EAKpBA,EAAI,UAAU,GAJdA,EAAI,OAASA,EAAI,MAAQc,EACrBd,EAAI,OAAO,EAAG,GAAG,GACnBA,EAAI,UAAU,IAIpBA,EAAI,OAASA,EAAI,MAAQS,EACzBT,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,IACnBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,GACnBA,EAAI,UAAU,GAElBA,EAAI,OAASA,EAAI,MAAQS,EACzBT,EAAI,eAAiBI,IACR,CAEX,GADAW,EAAMf,EAAI,MAAQA,EAAI,OAClBA,EAAI,eAAeP,EAAM,GAAI,GAAG,EAAG,CACrCO,EAAI,OAASA,EAAI,MAAQe,EACzB,KACF,CAEA,GADAf,EAAI,OAASA,EAAI,MAAQe,EACrBf,EAAI,QAAUA,EAAI,eACpB,OACFA,EAAI,QACN,CACAA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAASA,EAAI,iBACnBA,EAAI,SACJA,EAAI,IAAMA,EAAI,OACdH,EAAMG,EAAI,SAAS,EACfA,EAAI,OAAOH,CAAG,GAChBG,EAAI,UAAU,EAEpB,CACF,CACA,KAAK,KAAO,UAAW,CACrB,IAAII,EAAMJ,EAAI,OACd,OAAAE,EAAe,EACfN,EAAmB,GACnBI,EAAI,eAAiBI,EACrBJ,EAAI,OAASA,EAAI,MACjBM,EAAe,EACfN,EAAI,OAASA,EAAI,MACjBQ,EAAa,EACbR,EAAI,OAASA,EAAI,MACjBU,EAAc,EACdV,EAAI,OAASA,EAAI,MACjBW,EAAgB,EAChBX,EAAI,OAASA,EAAI,MACbJ,GACFgB,EAAW,EACXZ,EAAI,OAASA,EAAI,QAEjBA,EAAI,OAASA,EAAI,MACjBa,EAAW,EACXb,EAAI,OAASA,EAAI,OAEnBiB,EAAO,EACA,EACT,CACF,EAGF,OAAO,SAASC,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASjB,EAAM,CACjC,OAAAtB,EAAG,WAAWsB,CAAI,EAClBtB,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWuC,CAAK,EACnBvC,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,qxDAAy7C,MAAM,GAAG,CAAC,EAEx/CA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_fi", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6", "r_VI", "r_LONG", "a_7", "a_8", "a_9", "g_AEI", "g_V1", "g_V2", "g_particle_end", "B_ending_removed", "S_x", "I_p2", "I_p1", "sbp", "word", "r_mark_regions", "habr1", "v_1", "r_R2", "r_particle_etc", "among_var", "r_possessive", "v_2", "r_case_ending", "r_other_endings", "r_i_plural", "r_t_plural", "v_3", "v_4", "v_5", "r_tidy", "token"]}