import{a as o}from"./chunk-OSRY5VT3.min.js";var p=o((n,u)=>{(function(e,r){typeof define=="function"&&define.amd?define(r):typeof n=="object"?u.exports=r():r()(e.lunr)})(n,function(){return function(e){if(typeof e>"u")throw new Error("Lunr is not present. Please include / require Lunr before this script.");if(typeof e.stemmerSupport>"u")throw new Error("Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.");e.te=function(){this.pipeline.reset(),this.pipeline.add(e.te.trimmer,e.te.stopWordFilter,e.te.stemmer),this.searchPipeline&&(this.searchPipeline.reset(),this.searchPipeline.add(e.te.stemmer))},e.te.wordCharacters="\u0C00-\u0C04\u0C05-\u0C14\u0C15-\u0C39\u0C3E-\u0C4C\u0C55-\u0C56\u0C58-\u0C5A\u0C60-\u0C61\u0C62-\u0C63\u0C66-\u0C6F\u0C78-\u0C7F\u0C3C\u0C3D\u0C4D\u0C5D\u0C77\u0C64\u0C65",e.te.trimmer=e.trimmerSupport.generateTrimmer(e.te.wordCharacters),e.Pipeline.registerFunction(e.te.trimmer,"trimmer-te"),e.te.stopWordFilter=e.generateStopWordFilter("\u0C05\u0C02\u0C26\u0C30\u0C42 \u0C05\u0C02\u0C26\u0C41\u0C2C\u0C3E\u0C1F\u0C41\u0C32\u0C4B \u0C05\u0C21\u0C17\u0C02\u0C21\u0C3F \u0C05\u0C21\u0C17\u0C21\u0C02 \u0C05\u0C21\u0C4D\u0C21\u0C02\u0C17\u0C3E \u0C05\u0C28\u0C41\u0C17\u0C41\u0C23\u0C02\u0C17\u0C3E \u0C05\u0C28\u0C41\u0C2E\u0C24\u0C3F\u0C02\u0C1A\u0C41 \u0C05\u0C28\u0C41\u0C2E\u0C24\u0C3F\u0C38\u0C4D\u0C24\u0C41\u0C02\u0C26\u0C3F \u0C05\u0C2F\u0C3F\u0C24\u0C47 \u0C07\u0C2A\u0C4D\u0C2A\u0C1F\u0C3F\u0C15\u0C47 \u0C09\u0C28\u0C4D\u0C28\u0C3E\u0C30\u0C41 \u0C0E\u0C15\u0C4D\u0C15\u0C21\u0C48\u0C28\u0C3E \u0C0E\u0C2A\u0C4D\u0C2A\u0C41\u0C21\u0C41 \u0C0E\u0C35\u0C30\u0C48\u0C28\u0C3E \u0C0E\u0C35\u0C30\u0C4B \u0C0F \u0C0F\u0C26\u0C48\u0C28\u0C3E \u0C0F\u0C2E\u0C48\u0C28\u0C2A\u0C4D\u0C2A\u0C1F\u0C3F\u0C15\u0C3F \u0C12\u0C15 \u0C12\u0C15\u0C30\u0C41 \u0C15\u0C28\u0C3F\u0C2A\u0C3F\u0C38\u0C4D\u0C24\u0C3E\u0C2F\u0C3F \u0C15\u0C3E\u0C26\u0C41 \u0C15\u0C42\u0C21\u0C3E \u0C17\u0C3E \u0C17\u0C41\u0C30\u0C3F\u0C02\u0C1A\u0C3F \u0C1A\u0C41\u0C1F\u0C4D\u0C1F\u0C42 \u0C1A\u0C47\u0C2F\u0C17\u0C32\u0C3F\u0C17\u0C3F\u0C02\u0C26\u0C3F \u0C24\u0C17\u0C3F\u0C28 \u0C24\u0C30\u0C4D\u0C35\u0C3E\u0C24 \u0C26\u0C3E\u0C26\u0C3E\u0C2A\u0C41 \u0C26\u0C42\u0C30\u0C02\u0C17\u0C3E \u0C28\u0C3F\u0C1C\u0C02\u0C17\u0C3E \u0C2A\u0C48 \u0C2A\u0C4D\u0C30\u0C15\u0C3E\u0C30\u0C02 \u0C2A\u0C4D\u0C30\u0C15\u0C4D\u0C15\u0C28 \u0C2E\u0C27\u0C4D\u0C2F \u0C2E\u0C30\u0C3F\u0C2F\u0C41 \u0C2E\u0C30\u0C4A\u0C15 \u0C2E\u0C33\u0C4D\u0C33\u0C40 \u0C2E\u0C3E\u0C24\u0C4D\u0C30\u0C2E\u0C47 \u0C2E\u0C46\u0C1A\u0C4D\u0C1A\u0C41\u0C15\u0C4B \u0C35\u0C26\u0C4D\u0C26 \u0C35\u0C46\u0C02\u0C1F \u0C35\u0C47\u0C30\u0C41\u0C17\u0C3E \u0C35\u0C4D\u0C2F\u0C24\u0C3F\u0C30\u0C47\u0C15\u0C02\u0C17\u0C3E \u0C38\u0C02\u0C2C\u0C02\u0C27\u0C02".split(" ")),e.te.stemmer=function(){return function(t){return typeof t.update=="function"?t.update(function(i){return i}):t}}();var r=e.wordcut;r.init(),e.te.tokenizer=function(t){if(!arguments.length||t==null||t==null)return[];if(Array.isArray(t))return t.map(function(s){return isLunr2?new e.Token(s.toLowerCase()):s.toLowerCase()});var i=t.toString().toLowerCase().replace(/^\s+/,"");return r.cut(i).split("|")},e.Pipeline.registerFunction(e.te.stemmer,"stemmer-te"),e.Pipeline.registerFunction(e.te.stopWordFilter,"stopWordFilter-te")}})});export default p();
/*! Bundled license information:

lunr-languages/lunr.te.js:
  (*!
   * Lunr languages, `Hindi` language
   * https://github.com/MiKr13/lunr-languages
   *
   * Copyright 2023, India
   * http://www.mozilla.org/MPL/
   *)
  (*!
   * based on
   * Snowball JavaScript Library v0.3
   * http://code.google.com/p/urim/
   * http://snowball.tartarus.org/
   *
   * Copyright 2010, Oleg Mazko
   * http://www.mozilla.org/MPL/
   *)
*/
//# sourceMappingURL=lunr.te-JGGL3BFP.min.js.map
