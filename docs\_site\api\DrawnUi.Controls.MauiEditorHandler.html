<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class MauiEditorHandler | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class MauiEditorHandler | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_MauiEditorHandler.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.MauiEditorHandler%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Controls.MauiEditorHandler">



  <h1 id="DrawnUi_Controls_MauiEditorHandler" data-uid="DrawnUi.Controls.MauiEditorHandler" class="text-break">
Class MauiEditorHandler  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/EditText/MauiEditorHandler.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Controls.html">Controls</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MauiEditorHandler : EditorHandler, IEditorHandler, IViewHandler, IElementHandler</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.elementhandler">ElementHandler</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler">ViewHandler</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2">ViewHandler</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ieditor">IEditor</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler">EditorHandler</a></div>
      <div><span class="xref">MauiEditorHandler</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.ieditorhandler">IEditorHandler</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler">IViewHandler</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler">IElementHandler</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapper">EditorHandler.Mapper</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.commandmapper">EditorHandler.CommandMapper</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.createplatformview">EditorHandler.CreatePlatformView()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.maptext">EditorHandler.MapText(IViewHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.maptextcolor#microsoft-maui-handlers-editorhandler-maptextcolor(microsoft-maui-iviewhandler-microsoft-maui-ieditor)">EditorHandler.MapTextColor(IViewHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapplaceholder">EditorHandler.MapPlaceholder(IViewHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapplaceholdercolor">EditorHandler.MapPlaceholderColor(IViewHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapcharacterspacing">EditorHandler.MapCharacterSpacing(IViewHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapmaxlength">EditorHandler.MapMaxLength(IViewHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapistextpredictionenabled">EditorHandler.MapIsTextPredictionEnabled(IEditorHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapisspellcheckenabled">EditorHandler.MapIsSpellCheckEnabled(IEditorHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapfont">EditorHandler.MapFont(IViewHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapisreadonly">EditorHandler.MapIsReadOnly(IViewHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.maptextcolor#microsoft-maui-handlers-editorhandler-maptextcolor(microsoft-maui-handlers-ieditorhandler-microsoft-maui-ieditor)">EditorHandler.MapTextColor(IEditorHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.maphorizontaltextalignment">EditorHandler.MapHorizontalTextAlignment(IEditorHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapverticaltextalignment">EditorHandler.MapVerticalTextAlignment(IEditorHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapkeyboard">EditorHandler.MapKeyboard(IEditorHandler, IEditor)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapcursorposition">EditorHandler.MapCursorPosition(IEditorHandler, ITextInput)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.editorhandler.mapselectionlength">EditorHandler.MapSelectionLength(IEditorHandler, ITextInput)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.setvirtualview#microsoft-maui-handlers-viewhandler-2-setvirtualview(microsoft-maui-iview)">ViewHandler&lt;IEditor, object&gt;.SetVirtualView(IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.setvirtualview#microsoft-maui-handlers-viewhandler-2-setvirtualview(microsoft-maui-ielement)">ViewHandler&lt;IEditor, object&gt;.SetVirtualView(IElement)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.createplatformview">ViewHandler&lt;IEditor, object&gt;.CreatePlatformView()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.connecthandler">ViewHandler&lt;IEditor, object&gt;.ConnectHandler(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.disconnecthandler">ViewHandler&lt;IEditor, object&gt;.DisconnectHandler(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.platformarrange">ViewHandler&lt;IEditor, object&gt;.PlatformArrange(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.getdesiredsize">ViewHandler&lt;IEditor, object&gt;.GetDesiredSize(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.setupcontainer">ViewHandler&lt;IEditor, object&gt;.SetupContainer()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.removecontainer">ViewHandler&lt;IEditor, object&gt;.RemoveContainer()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.platformview">ViewHandler&lt;IEditor, object&gt;.PlatformView</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.virtualview">ViewHandler&lt;IEditor, object&gt;.VirtualView</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler-2.platformviewfactory">ViewHandler&lt;IEditor, object&gt;.PlatformViewFactory</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.viewmapper">ViewHandler.ViewMapper</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.viewcommandmapper">ViewHandler.ViewCommandMapper</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.setupcontainer">ViewHandler.SetupContainer()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.removecontainer">ViewHandler.RemoveContainer()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.getdesiredsize">ViewHandler.GetDesiredSize(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.platformarrange">ViewHandler.PlatformArrange(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapwidth">ViewHandler.MapWidth(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapheight">ViewHandler.MapHeight(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapminimumheight">ViewHandler.MapMinimumHeight(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapmaximumheight">ViewHandler.MapMaximumHeight(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapminimumwidth">ViewHandler.MapMinimumWidth(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapmaximumwidth">ViewHandler.MapMaximumWidth(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapisenabled">ViewHandler.MapIsEnabled(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapvisibility">ViewHandler.MapVisibility(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapbackground">ViewHandler.MapBackground(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapflowdirection">ViewHandler.MapFlowDirection(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapopacity">ViewHandler.MapOpacity(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapautomationid">ViewHandler.MapAutomationId(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapclip">ViewHandler.MapClip(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapshadow">ViewHandler.MapShadow(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapsemantics">ViewHandler.MapSemantics(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapinvalidatemeasure">ViewHandler.MapInvalidateMeasure(IViewHandler, IView, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapcontainerview">ViewHandler.MapContainerView(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapborderview">ViewHandler.MapBorderView(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapframe">ViewHandler.MapFrame(IViewHandler, IView, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapzindex">ViewHandler.MapZIndex(IViewHandler, IView, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapfocus">ViewHandler.MapFocus(IViewHandler, IView, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapinputtransparent">ViewHandler.MapInputTransparent(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapunfocus">ViewHandler.MapUnfocus(IViewHandler, IView, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.maptooltip">ViewHandler.MapToolTip(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.maptranslationx">ViewHandler.MapTranslationX(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.maptranslationy">ViewHandler.MapTranslationY(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapscale">ViewHandler.MapScale(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapscalex">ViewHandler.MapScaleX(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapscaley">ViewHandler.MapScaleY(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.maprotation">ViewHandler.MapRotation(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.maprotationx">ViewHandler.MapRotationX(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.maprotationy">ViewHandler.MapRotationY(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapanchorx">ViewHandler.MapAnchorX(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapanchory">ViewHandler.MapAnchorY(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.mapcontextflyout">ViewHandler.MapContextFlyout(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.hascontainer">ViewHandler.HasContainer</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.needscontainer">ViewHandler.NeedsContainer</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.containerview">ViewHandler.ContainerView</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.platformview">ViewHandler.PlatformView</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.viewhandler.virtualview">ViewHandler.VirtualView</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.elementhandler.elementmapper">ElementHandler.ElementMapper</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.elementhandler.elementcommandmapper">ElementHandler.ElementCommandMapper</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.elementhandler.setmauicontext">ElementHandler.SetMauiContext(IMauiContext)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.elementhandler.setvirtualview">ElementHandler.SetVirtualView(IElement)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.elementhandler.updatevalue">ElementHandler.UpdateValue(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.elementhandler.invoke">ElementHandler.Invoke(string, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.elementhandler.mauicontext">ElementHandler.MauiContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.elementhandler.services">ElementHandler.Services</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>






</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/EditText/MauiEditorHandler.cs/#L5" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
