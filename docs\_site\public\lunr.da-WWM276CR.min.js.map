{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.da.js"], "sourcesContent": ["/*!\n * Lunr languages, `Danish` language\n * https://github.com/Mihai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.da = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.da.trimmer,\n        lunr.da.stopWordFilter,\n        lunr.da.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.da.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.da.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.da.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.da.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.da.trimmer, 'trimmer-da');\n\n    /* lunr stemmer function */\n    lunr.da.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function DanishStemmer() {\n          var a_0 = [new Among(\"hed\", -1, 1), new Among(\"ethed\", 0, 1),\n              new Among(\"ered\", -1, 1), new Among(\"e\", -1, 1),\n              new Among(\"erede\", 3, 1), new Among(\"ende\", 3, 1),\n              new Among(\"erende\", 5, 1), new Among(\"ene\", 3, 1),\n              new Among(\"erne\", 3, 1), new Among(\"ere\", 3, 1),\n              new Among(\"en\", -1, 1), new Among(\"heden\", 10, 1),\n              new Among(\"eren\", 10, 1), new Among(\"er\", -1, 1),\n              new Among(\"heder\", 13, 1), new Among(\"erer\", 13, 1),\n              new Among(\"s\", -1, 2), new Among(\"heds\", 16, 1),\n              new Among(\"es\", 16, 1), new Among(\"endes\", 18, 1),\n              new Among(\"erendes\", 19, 1), new Among(\"enes\", 18, 1),\n              new Among(\"ernes\", 18, 1), new Among(\"eres\", 18, 1),\n              new Among(\"ens\", 16, 1), new Among(\"hedens\", 24, 1),\n              new Among(\"erens\", 24, 1), new Among(\"ers\", 16, 1),\n              new Among(\"ets\", 16, 1), new Among(\"erets\", 28, 1),\n              new Among(\"et\", -1, 1), new Among(\"eret\", 30, 1)\n            ],\n            a_1 = [\n              new Among(\"gd\", -1, -1), new Among(\"dt\", -1, -1),\n              new Among(\"gt\", -1, -1), new Among(\"kt\", -1, -1)\n            ],\n            a_2 = [\n              new Among(\"ig\", -1, 1), new Among(\"lig\", 0, 1),\n              new Among(\"elig\", 1, 1), new Among(\"els\", -1, 1),\n              new Among(\"l\\u00F8st\", -1, 2)\n            ],\n            g_v = [17, 65, 16, 1, 0, 0, 0, 0,\n              0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 128\n            ],\n            g_s_ending = [239, 254, 42, 3,\n              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16\n            ],\n            I_x, I_p1, S_ch, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function r_mark_regions() {\n            var v_1, c = sbp.cursor + 3;\n            I_p1 = sbp.limit;\n            if (0 <= c && c <= sbp.limit) {\n              I_x = c;\n              while (true) {\n                v_1 = sbp.cursor;\n                if (sbp.in_grouping(g_v, 97, 248)) {\n                  sbp.cursor = v_1;\n                  break;\n                }\n                sbp.cursor = v_1;\n                if (v_1 >= sbp.limit)\n                  return;\n                sbp.cursor++;\n              }\n              while (!sbp.out_grouping(g_v, 97, 248)) {\n                if (sbp.cursor >= sbp.limit)\n                  return;\n                sbp.cursor++;\n              }\n              I_p1 = sbp.cursor;\n              if (I_p1 < I_x)\n                I_p1 = I_x;\n            }\n          }\n\n          function r_main_suffix() {\n            var among_var, v_1;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_0, 32);\n              sbp.limit_backward = v_1;\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                    if (sbp.in_grouping_b(g_s_ending, 97, 229))\n                      sbp.slice_del();\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_consonant_pair() {\n            var v_1 = sbp.limit - sbp.cursor,\n              v_2;\n            if (sbp.cursor >= I_p1) {\n              v_2 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              if (sbp.find_among_b(a_1, 4)) {\n                sbp.bra = sbp.cursor;\n                sbp.limit_backward = v_2;\n                sbp.cursor = sbp.limit - v_1;\n                if (sbp.cursor > sbp.limit_backward) {\n                  sbp.cursor--;\n                  sbp.bra = sbp.cursor;\n                  sbp.slice_del();\n                }\n              } else\n                sbp.limit_backward = v_2;\n            }\n          }\n\n          function r_other_suffix() {\n            var among_var, v_1 = sbp.limit - sbp.cursor,\n              v_2, v_3;\n            sbp.ket = sbp.cursor;\n            if (sbp.eq_s_b(2, \"st\")) {\n              sbp.bra = sbp.cursor;\n              if (sbp.eq_s_b(2, \"ig\"))\n                sbp.slice_del();\n            }\n            sbp.cursor = sbp.limit - v_1;\n            if (sbp.cursor >= I_p1) {\n              v_2 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_2, 5);\n              sbp.limit_backward = v_2;\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    v_3 = sbp.limit - sbp.cursor;\n                    r_consonant_pair();\n                    sbp.cursor = sbp.limit - v_3;\n                    break;\n                  case 2:\n                    sbp.slice_from(\"l\\u00F8s\");\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_undouble() {\n            var v_1;\n            if (sbp.cursor >= I_p1) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_p1;\n              sbp.ket = sbp.cursor;\n              if (sbp.out_grouping_b(g_v, 97, 248)) {\n                sbp.bra = sbp.cursor;\n                S_ch = sbp.slice_to(S_ch);\n                sbp.limit_backward = v_1;\n                if (sbp.eq_v_b(S_ch))\n                  sbp.slice_del();\n              } else\n                sbp.limit_backward = v_1;\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            r_main_suffix();\n            sbp.cursor = sbp.limit;\n            r_consonant_pair();\n            sbp.cursor = sbp.limit;\n            r_other_suffix();\n            sbp.cursor = sbp.limit;\n            r_undouble();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.da.stemmer, 'stemmer-da');\n\n    lunr.da.stopWordFilter = lunr.generateStopWordFilter('ad af alle alt anden at blev blive bliver da de dem den denne der deres det dette dig din disse dog du efter eller en end er et for fra ham han hans har havde have hende hendes her hos hun hvad hvis hvor i ikke ind jeg jer jo kunne man mange med meget men mig min mine mit mod ned noget nogle nu når og også om op os over på selv sig sin sine sit skal skulle som sådan thi til ud under var vi vil ville vor være været'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.da.stopWordFilter, 'stopWordFilter-da');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAAyB,CAChC,IAAIC,EAAM,CAAC,IAAIH,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,EAAG,CAAC,EACvD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,IAAK,GAAI,CAAC,EAC9C,IAAIA,EAAM,QAAS,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAChD,IAAIA,EAAM,SAAU,EAAG,CAAC,EAAG,IAAIA,EAAM,MAAO,EAAG,CAAC,EAChD,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,MAAO,EAAG,CAAC,EAC9C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC/C,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC9C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAChD,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACpD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EAClD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,CACjD,EACAI,EAAM,CACJ,IAAIJ,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,CACjD,EACAK,EAAM,CACJ,IAAIL,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,EAAG,CAAC,EAC7C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,UAAa,GAAI,CAAC,CAC9B,EACAM,EAAM,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAC7B,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,GACjC,EACAC,EAAa,CAAC,IAAK,IAAK,GAAI,EAC1B,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EACtC,EACAC,EAAKC,EAAMC,EAAMC,EAAM,IAAIV,EAC7B,KAAK,WAAa,SAASW,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,GAAiB,CACxB,IAAIC,EAAKC,EAAIJ,EAAI,OAAS,EAE1B,GADAF,EAAOE,EAAI,MACP,GAAKI,GAAKA,GAAKJ,EAAI,MAAO,CAE5B,IADAH,EAAMO,IACO,CAEX,GADAD,EAAMH,EAAI,OACNA,EAAI,YAAYL,EAAK,GAAI,GAAG,EAAG,CACjCK,EAAI,OAASG,EACb,KACF,CAEA,GADAH,EAAI,OAASG,EACTA,GAAOH,EAAI,MACb,OACFA,EAAI,QACN,CACA,KAAO,CAACA,EAAI,aAAaL,EAAK,GAAI,GAAG,GAAG,CACtC,GAAIK,EAAI,QAAUA,EAAI,MACpB,OACFA,EAAI,QACN,CACAF,EAAOE,EAAI,OACPF,EAAOD,IACTC,EAAOD,EACX,CACF,CAEA,SAASQ,GAAgB,CACvB,IAAIC,EAAWH,EACf,GAAIH,EAAI,QAAUF,IAChBK,EAAMH,EAAI,eACVA,EAAI,eAAiBF,EACrBE,EAAI,IAAMA,EAAI,OACdM,EAAYN,EAAI,aAAaR,EAAK,EAAE,EACpCQ,EAAI,eAAiBG,EACjBG,GAEF,OADAN,EAAI,IAAMA,EAAI,OACNM,EAAW,CACjB,IAAK,GACHN,EAAI,UAAU,EACd,MACF,IAAK,GACCA,EAAI,cAAcJ,EAAY,GAAI,GAAG,GACvCI,EAAI,UAAU,EAChB,KACJ,CAGN,CAEA,SAASO,GAAmB,CAC1B,IAAIJ,EAAMH,EAAI,MAAQA,EAAI,OACxBQ,EACER,EAAI,QAAUF,IAChBU,EAAMR,EAAI,eACVA,EAAI,eAAiBF,EACrBE,EAAI,IAAMA,EAAI,OACVA,EAAI,aAAaP,EAAK,CAAC,GACzBO,EAAI,IAAMA,EAAI,OACdA,EAAI,eAAiBQ,EACrBR,EAAI,OAASA,EAAI,MAAQG,EACrBH,EAAI,OAASA,EAAI,iBACnBA,EAAI,SACJA,EAAI,IAAMA,EAAI,OACdA,EAAI,UAAU,IAGhBA,EAAI,eAAiBQ,EAE3B,CAEA,SAASC,GAAiB,CACxB,IAAIH,EAAWH,EAAMH,EAAI,MAAQA,EAAI,OACnCQ,EAAKE,EAQP,GAPAV,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,GACpBA,EAAI,UAAU,GAElBA,EAAI,OAASA,EAAI,MAAQG,EACrBH,EAAI,QAAUF,IAChBU,EAAMR,EAAI,eACVA,EAAI,eAAiBF,EACrBE,EAAI,IAAMA,EAAI,OACdM,EAAYN,EAAI,aAAaN,EAAK,CAAC,EACnCM,EAAI,eAAiBQ,EACjBF,GAEF,OADAN,EAAI,IAAMA,EAAI,OACNM,EAAW,CACjB,IAAK,GACHN,EAAI,UAAU,EACdU,EAAMV,EAAI,MAAQA,EAAI,OACtBO,EAAiB,EACjBP,EAAI,OAASA,EAAI,MAAQU,EACzB,MACF,IAAK,GACHV,EAAI,WAAW,QAAU,EACzB,KACJ,CAGN,CAEA,SAASW,GAAa,CACpB,IAAIR,EACAH,EAAI,QAAUF,IAChBK,EAAMH,EAAI,eACVA,EAAI,eAAiBF,EACrBE,EAAI,IAAMA,EAAI,OACVA,EAAI,eAAeL,EAAK,GAAI,GAAG,GACjCK,EAAI,IAAMA,EAAI,OACdD,EAAOC,EAAI,SAASD,CAAI,EACxBC,EAAI,eAAiBG,EACjBH,EAAI,OAAOD,CAAI,GACjBC,EAAI,UAAU,GAEhBA,EAAI,eAAiBG,EAE3B,CACA,KAAK,KAAO,UAAW,CACrB,IAAIA,EAAMH,EAAI,OACd,OAAAE,EAAe,EACfF,EAAI,eAAiBG,EACrBH,EAAI,OAASA,EAAI,MACjBK,EAAc,EACdL,EAAI,OAASA,EAAI,MACjBO,EAAiB,EACjBP,EAAI,OAASA,EAAI,MACjBS,EAAe,EACfT,EAAI,OAASA,EAAI,MACjBW,EAAW,EACJ,EACT,CACF,EAGF,OAAO,SAASC,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASX,EAAM,CACjC,OAAAV,EAAG,WAAWU,CAAI,EAClBV,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWqB,CAAK,EACnBrB,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,sbAAoa,MAAM,GAAG,CAAC,EAEneA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_da", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "g_v", "g_s_ending", "I_x", "I_p1", "S_ch", "sbp", "word", "r_mark_regions", "v_1", "c", "r_main_suffix", "among_var", "r_consonant_pair", "v_2", "r_other_suffix", "v_3", "r_undouble", "token"]}