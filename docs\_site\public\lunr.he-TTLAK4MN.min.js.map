{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.he.js"], "sourcesContent": ["/*!\n * Lunr languages, `Hebrew` language\n * https://github.com/avisaradir/lunr-languages-he\n *\n * Copyright 2023, Adir Avisar\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (2005)\n * <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> (2012)\n *\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.he = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.he.trimmer,\n        lunr.he.stopWordFilter,\n        lunr.he.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.he.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.he.wordCharacters = \"\\u0590-\\u05FF\\u05D0-\\u05EAa-zA-Zａ-ｚＡ-Ｚ0-9０-９\";\n    lunr.he.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.he.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.he.trimmer, 'trimmer-he');\n\n    /* lunr stemmer function */\n    lunr.he.stemmer = (function() {\n      var self = this;\n      var word = '';\n      self.result = false;\n\n      self.execArray = [\n        'cleanWord'\n      ];\n\n      self.stem = function() {\n        var counter = 0;\n        self.result = false;\n        while (counter < self.execArray.length && self.result != true) {\n          self.result = self[self.execArray[counter]]();\n          counter++;\n        }\n      }\n\n      self.setCurrent = function(word) {\n        self.word = word;\n      }\n\n      self.getCurrent = function() {\n        return self.word\n      }\n\n      /*remove elongating character and test that the word does not contain non-hebrew characters.\n      If the word contains special characters, don't stem. */\n      self.cleanWord = function() {\n        var wordCharacters = \"\\u0591-\\u05F4\\u05D0-\\u05EA\";\n        var testRegex = new RegExp(\"[^\" + wordCharacters + \"]\");\n        if (testRegex.test(word)) {\n          return true;\n        }\n        return false;\n      }\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            self.setCurrent(word);\n            self.stem();\n            return self.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          self.setCurrent(token);\n          self.stem();\n          return self.getCurrent();\n        }\n\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.he.stemmer, 'stemmer-he');\n\n    lunr.he.stopWordFilter = lunr.generateStopWordFilter('אבל או אולי אותו אותי אותך אותם אותן אותנו אז אחר אחרות אחרי אחריכן אחרים אחרת אי איזה איך אין איפה אל אלה אלו אם אנחנו אני אף אפשר את אתה אתכם אתכן אתם אתן באיזה באיזו בגלל בין בלבד בעבור בעזרת בכל בכן בלי במידה במקום שבו ברוב בשביל בשעה ש בתוך גם דרך הוא היא היה היי היכן היתה היתי הם הן הנה הסיבה שבגללה הרי ואילו ואת זאת זה זות יהיה יוכל יוכלו יותר מדי יכול יכולה יכולות יכולים יכל יכלה יכלו יש כאן כאשר כולם כולן כזה כי כיצד כך כל כלל כמו כן כפי כש לא לאו לאיזותך לאן לבין לה להיות להם להן לו לזה לזות לי לך לכם לכן למה למעלה למעלה מ למטה למטה מ למעט למקום שבו למרות לנו לעבר לעיכן לפיכך לפני מאד מאחורי מאיזו סיבה מאין מאיפה מבלי מבעד מדוע מה מהיכן מול מחוץ מי מידע מכאן מכל מכן מלבד מן מנין מסוגל מעט מעטים מעל מצד מקום בו מתחת מתי נגד נגר נו עד עז על עלי עליו עליה עליהם עליך עלינו עם עצמה עצמהם עצמהן עצמו עצמי עצמם עצמן עצמנו פה רק שוב של שלה שלהם שלהן שלו שלי שלך שלכה שלכם שלכן שלנו שם תהיה תחת'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.he.stopWordFilter, 'stopWordFilter-he');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAyBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,6EACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAC5B,IAAIC,EAAO,KACPC,EAAO,GACX,OAAAD,EAAK,OAAS,GAEdA,EAAK,UAAY,CACf,WACF,EAEAA,EAAK,KAAO,UAAW,CACrB,IAAIE,EAAU,EAEd,IADAF,EAAK,OAAS,GACPE,EAAUF,EAAK,UAAU,QAAUA,EAAK,QAAU,IACvDA,EAAK,OAASA,EAAKA,EAAK,UAAUE,CAAO,CAAC,EAAE,EAC5CA,GAEJ,EAEAF,EAAK,WAAa,SAASC,EAAM,CAC/BD,EAAK,KAAOC,CACd,EAEAD,EAAK,WAAa,UAAW,CAC3B,OAAOA,EAAK,IACd,EAIAA,EAAK,UAAY,UAAW,CAC1B,IAAIG,EAAiB,6BACjBC,EAAY,IAAI,OAAO,KAAOD,EAAiB,GAAG,EACtD,MAAI,EAAAC,EAAU,KAAKH,CAAI,CAIzB,EAGO,SAASI,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASJ,EAAM,CACjC,OAAAD,EAAK,WAAWC,CAAI,EACpBD,EAAK,KAAK,EACHA,EAAK,WAAW,CACzB,CAAC,GAEDA,EAAK,WAAWK,CAAK,EACrBL,EAAK,KAAK,EACHA,EAAK,WAAW,EAG3B,CACF,EAAG,EAEHD,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,41IAA64B,MAAM,GAAG,CAAC,EAE58BA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_he", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "self", "word", "counter", "wordCharacters", "testRegex", "token"]}