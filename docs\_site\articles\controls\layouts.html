<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Layout Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Layout Controls | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="../toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/layouts.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="layout-controls">Layout Controls</h1>

<p>DrawnUi.Maui provides a powerful and flexible layout system for arranging controls with high performance. The system is similar to MAUI's native layout system but optimized for direct rendering with SkiaSharp.</p>
<h2 id="core-layout-types">Core Layout Types</h2>
<p>DrawnUi.Maui offers several core layout types:</p>
<h3 id="skialayout">SkiaLayout</h3>
<p>The base layout control for measurement, arrangement, and rendering of child elements. It supports different layout strategies via the <code>Type</code> property:</p>
<ul>
<li>Managing child controls</li>
<li>Performance optimizations (see below)</li>
</ul>
<pre><code class="lang-xml">&lt;draw:SkiaLayout
    Type=&quot;Absolute&quot;
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;300&quot;&gt;
    &lt;!-- Child controls here --&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h2 id="layout-types">Layout Types</h2>
<p>The <code>Type</code> property on <code>SkiaLayout</code> supports:</p>
<ul>
<li><strong>Absolute</strong>: Free positioning, default. Think of it like a MAUI Grid with a single column and a single row.</li>
<li><strong>Grid</strong>: Row and column-based layout, classic MAUI Grid.</li>
<li><strong>Column</strong>: Vertical stacking (like MAUI VerticalStackLayout)</li>
<li><strong>Row</strong>: Horizontal stacking (like MAUI HorizontalStackLayout)</li>
<li><strong>Wrap</strong>: Items wrap to new lines when space runs out (similar to WPF Stackpanel)</li>
</ul>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Wrap&quot; Spacing=&quot;5&quot;&gt;
    &lt;draw:SkiaLabel Text=&quot;Item 1&quot; /&gt;
    &lt;draw:SkiaLabel Text=&quot;Item 2&quot; /&gt;
    &lt;!-- More items --&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<blockquote>
<p><strong>Note</strong>: The <code>Spacing</code> property takes a single double value that applies to both horizontal and vertical spacing between items.</p>
</blockquote>
<h2 id="specialized-layout-controls">Specialized Layout Controls</h2>
<h3 id="contentlayout">ContentLayout</h3>
<p>A specialized layout for hosting a single content element is <code>ContentLayout</code>, <code>SkiaShape</code> is subclassing it to be able to contain a single child inside a <code>Content</code> property, instead of using <code>Children</code>.</p>
<h3 id="snappinglayout">SnappingLayout</h3>
<p>Supports snap points for controlled scrolling, ideal for carousels or paginated interfaces. <code>SkiaDrawer</code>, <code>SkiaCarousel</code> are deriving from it.</p>
<h3 id="skiadecoratedgrid">SkiaDecoratedGrid</h3>
<p><code>SkiaDecoratedGrid</code> is a specialized grid that can draw shapes between rows and columns, providing visual separation and decoration.</p>
<pre><code class="lang-xml">&lt;draw:SkiaDecoratedGrid
    ColumnDefinitions=&quot;*,*,*&quot;
    RowDefinitions=&quot;Auto,Auto,Auto&quot;
    RowSpacing=&quot;1&quot;
    ColumnSpacing=&quot;1&quot;
    SeparatorColor=&quot;Gray&quot;
    SeparatorWidth=&quot;1&quot;&gt;

    &lt;draw:SkiaLabel Text=&quot;Cell 1,1&quot; Column=&quot;0&quot; Row=&quot;0&quot; /&gt;
    &lt;draw:SkiaLabel Text=&quot;Cell 1,2&quot; Column=&quot;1&quot; Row=&quot;0&quot; /&gt;
    &lt;draw:SkiaLabel Text=&quot;Cell 1,3&quot; Column=&quot;2&quot; Row=&quot;0&quot; /&gt;
    &lt;draw:SkiaLabel Text=&quot;Cell 2,1&quot; Column=&quot;0&quot; Row=&quot;1&quot; /&gt;
    &lt;draw:SkiaLabel Text=&quot;Cell 2,2&quot; Column=&quot;1&quot; Row=&quot;1&quot; /&gt;
    &lt;draw:SkiaLabel Text=&quot;Cell 2,3&quot; Column=&quot;2&quot; Row=&quot;1&quot; /&gt;
&lt;/draw:SkiaDecoratedGrid&gt;
</code></pre>
<h3 id="skiahotspot">SkiaHotspot</h3>
<p><code>SkiaHotspot</code> provides a way to handle gestures in a lazy way, creating invisible touch-sensitive areas.</p>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Absolute&quot;&gt;
    &lt;draw:SkiaImage Source=&quot;background.png&quot; /&gt;

    &lt;draw:SkiaHotspot
        X=&quot;100&quot; Y=&quot;100&quot;
        WidthRequest=&quot;50&quot; HeightRequest=&quot;50&quot;
        Tapped=&quot;OnHotspotTapped&quot; /&gt;

    &lt;draw:SkiaHotspot
        X=&quot;200&quot; Y=&quot;150&quot;
        WidthRequest=&quot;80&quot; HeightRequest=&quot;30&quot;
        Tapped=&quot;OnAnotherHotspotTapped&quot; /&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h3 id="skiabackdrop">SkiaBackdrop</h3>
<p><code>SkiaBackdrop</code> applies effects to the background below, like blur and other visual effects.</p>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Absolute&quot;&gt;
    &lt;draw:SkiaImage Source=&quot;background.png&quot; /&gt;

    &lt;draw:SkiaBackdrop
        X=&quot;50&quot; Y=&quot;50&quot;
        WidthRequest=&quot;200&quot; HeightRequest=&quot;150&quot;
        BlurRadius=&quot;10&quot;
        BackgroundColor=&quot;#80FFFFFF&quot; /&gt;

    &lt;draw:SkiaLabel
        Text=&quot;Text over blurred background&quot;
        X=&quot;60&quot; Y=&quot;100&quot;
        TextColor=&quot;Black&quot; /&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h2 id="example-creating-a-grid-layout">Example: Creating a Grid Layout</h2>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Grid&quot;
    ColumnDefinitions=&quot;Auto,*,100&quot;
    RowDefinitions=&quot;Auto,*,50&quot;&gt;
    &lt;!-- Header spanning all columns --&gt;
    &lt;draw:SkiaLabel 
        Text=&quot;Grid Header&quot; 
        Column=&quot;0&quot; 
        ColumnSpan=&quot;3&quot;
        Row=&quot;0&quot; 
        HorizontalOptions=&quot;Center&quot; /&gt;
    &lt;!-- Sidebar --&gt;
    &lt;draw:SkiaLayout
        Type=&quot;Column&quot;
        Column=&quot;0&quot;
        Row=&quot;1&quot;
        RowSpan=&quot;2&quot;
        BackgroundColor=&quot;LightGray&quot;
        Padding=&quot;10&quot;&gt;
        &lt;draw:SkiaLabel Text=&quot;Menu Item 1&quot; /&gt;
        &lt;draw:SkiaLabel Text=&quot;Menu Item 2&quot; /&gt;
        &lt;draw:SkiaLabel Text=&quot;Menu Item 3&quot; /&gt;
    &lt;/draw:SkiaLayout&gt;
    &lt;!-- Main content --&gt;
    &lt;draw:ContentLayout 
        Column=&quot;1&quot; 
        Row=&quot;1&quot;&gt;
        &lt;draw:SkiaLabel 
            Text=&quot;Main Content Area&quot; 
            HorizontalOptions=&quot;Center&quot; 
            VerticalOptions=&quot;Center&quot; /&gt;
    &lt;/draw:ContentLayout&gt;
    &lt;!-- Right panel --&gt;
    &lt;draw:SkiaLayout
        Type=&quot;Column&quot;
        Column=&quot;2&quot;
        Row=&quot;1&quot;
        BackgroundColor=&quot;LightBlue&quot;
        Padding=&quot;5&quot;&gt;
        &lt;draw:SkiaLabel Text=&quot;Panel Info&quot; /&gt;
    &lt;/draw:SkiaLayout&gt;
    &lt;!-- Footer spanning columns 1-2 --&gt;
    &lt;draw:SkiaLabel 
        Text=&quot;Footer&quot; 
        Column=&quot;1&quot; 
        ColumnSpan=&quot;2&quot;
        Row=&quot;2&quot; 
        HorizontalOptions=&quot;Center&quot;
        VerticalOptions=&quot;Center&quot; /&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<p>All grid functionality is handled by SkiaLayout with <code>Type=&quot;Grid&quot;</code>.</p>
<h2 id="under-the-hood">Under the Hood</h2>
<p>The layout system is built on top of the <code>SkiaControl</code> base class. Layout controls extend this for child management, measurement, and arrangement. Internally, structures like <code>LayoutStructure</code> and <code>GridStructure</code> efficiently track and manage layout information.</p>
<h3 id="caching">Caching</h3>
<p>Caching options help balance CPU and memory usage:</p>
<ul>
<li><strong>None</strong>: No caching, recalculated every frame</li>
<li><strong>Operations</strong>: Caches drawing commands</li>
<li><strong>Image</strong>: Caches as bitmap (more memory, less CPU)</li>
<li><strong>GPU</strong>: Uses hardware acceleration where available</li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/layouts.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
