<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class VelocitySkiaAnimator | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class VelocitySkiaAnimator | DrawnUi Documentation ">
      
      <meta name="description" content="Basically a modified port of Android FlingAnimation">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Animate_Animators_VelocitySkiaAnimator.md&amp;value=---%0Auid%3A%20DrawnUi.Animate.Animators.VelocitySkiaAnimator%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator">



  <h1 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator" class="text-break">
Class VelocitySkiaAnimator  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L28"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Animate.html">Animate</a>.<a class="xref" href="DrawnUi.Animate.Animators.html">Animators</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>Basically a modified port of Android FlingAnimation</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class VelocitySkiaAnimator : SkiaValueAnimator, ISkiaAnimator, IDisposable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="DrawnUi.Draw.AnimatorBase.html">AnimatorBase</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html">SkiaValueAnimator</a></div>
      <div><span class="xref">VelocitySkiaAnimator</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose">SkiaValueAnimator.Dispose()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_">SkiaValueAnimator.RunAsync(Action, CancellationToken)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_">SkiaValueAnimator.OnRunningStateChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_">SkiaValueAnimator.Seek(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_CycleFInished">SkiaValueAnimator.CycleFInished</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Finished">SkiaValueAnimator.Finished</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning">SkiaValueAnimator.FinishedRunning()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FrameTimeInterpolator">SkiaValueAnimator.FrameTimeInterpolator</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_">SkiaValueAnimator.TickFrame(long)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Repeat">SkiaValueAnimator.Repeat</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mValue">SkiaValueAnimator.mValue</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mStartValueIsSet">SkiaValueAnimator.mStartValueIsSet</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMaxValue">SkiaValueAnimator.mMaxValue</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMinValue">SkiaValueAnimator.mMinValue</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Easing">SkiaValueAnimator.Easing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Speed">SkiaValueAnimator.Speed</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TransformReportedValue_System_Int64_">SkiaValueAnimator.TransformReportedValue(long)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Debug">SkiaValueAnimator.Debug</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds">SkiaValueAnimator.GetNanoseconds()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ElapsedMs">SkiaValueAnimator.ElapsedMs</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Progress">SkiaValueAnimator.Progress</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnUpdated">SkiaValueAnimator.OnUpdated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_">SkiaValueAnimator.Start(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_">SkiaValueAnimator.SetValue(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_">SkiaValueAnimator.SetSpeed(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPostAnimator">AnimatorBase.IsPostAnimator</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsHiddenInViewTree">AnimatorBase.IsHiddenInViewTree</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_">AnimatorBase.Radians(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_runDelayMs">AnimatorBase.runDelayMs</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register">AnimatorBase.Register()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister">AnimatorBase.Unregister()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel">AnimatorBase.Cancel()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause">AnimatorBase.Pause()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume">AnimatorBase.Resume()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPaused">AnimatorBase.IsPaused</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStop">AnimatorBase.OnStop</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStart">AnimatorBase.OnStart</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Parent">AnimatorBase.Parent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsDeactivated">AnimatorBase.IsDeactivated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_LastFrameTimeNanos">AnimatorBase.LastFrameTimeNanos</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_StartFrameTimeNanos">AnimatorBase.StartFrameTimeNanos</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Uid">AnimatorBase.Uid</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsRunning">AnimatorBase.IsRunning</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_WasStarted">AnimatorBase.WasStarted</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator__ctor_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.#ctor*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator__ctor_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.#ctor(DrawnUi.Draw.SkiaControl)">
  VelocitySkiaAnimator(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L457"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public VelocitySkiaAnimator(SkiaControl parent)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>parent</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_MIN_VISIBLE_CHANGE_ALPHA" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ALPHA">
  MIN_VISIBLE_CHANGE_ALPHA
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L466"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const float MIN_VISIBLE_CHANGE_ALPHA = 0</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_MIN_VISIBLE_CHANGE_PIXELS" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_PIXELS">
  MIN_VISIBLE_CHANGE_PIXELS
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L464"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const float MIN_VISIBLE_CHANGE_PIXELS = 1</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_MIN_VISIBLE_CHANGE_ROTATION_DEGREES" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ROTATION_DEGREES">
  MIN_VISIBLE_CHANGE_ROTATION_DEGREES
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L465"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const float MIN_VISIBLE_CHANGE_ROTATION_DEGREES = 0</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_MIN_VISIBLE_CHANGE_SCALE" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_SCALE">
  MIN_VISIBLE_CHANGE_SCALE
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L467"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const float MIN_VISIBLE_CHANGE_SCALE = 0</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_THRESHOLD_MULTIPLIER" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.THRESHOLD_MULTIPLIER">
  THRESHOLD_MULTIPLIER
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L469"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const float THRESHOLD_MULTIPLIER = 0.75</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_UNSET" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.UNSET">
  UNSET
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L468"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const float UNSET = 3.4028235E+38</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mFlingForce" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mFlingForce">
  mFlingForce
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L317"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected VelocitySkiaAnimator.DragForce mFlingForce</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Animate.Animators.VelocitySkiaAnimator.html">VelocitySkiaAnimator</a>.<a class="xref" href="DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html">DragForce</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMassState" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMassState">
  mMassState
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L311"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected VelocitySkiaAnimator.MassState mMassState</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Animate.Animators.VelocitySkiaAnimator.html">VelocitySkiaAnimator</a>.<a class="xref" href="DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState.html">MassState</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_Friction_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_Friction" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction">
  Friction
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L290"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The bigger the sooner animation will slow down, default is 1.0</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Friction { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_InverseK_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_InverseK" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK">
  InverseK
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L91"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float InverseK { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_InvertOnLimits_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_InvertOnLimits" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits">
  InvertOnLimits
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L58"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool InvertOnLimits { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_MaxLimit_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_MaxLimit" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit">
  MaxLimit
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L48"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float MaxLimit { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_MinLimit_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_MinLimit" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit">
  MinLimit
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L40"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float MinLimit { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_Preset_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_Preset" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset">
  Preset
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L408"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public VelocitySkiaAnimator.PresetType Preset { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Animate.Animators.VelocitySkiaAnimator.html">VelocitySkiaAnimator</a>.<a class="xref" href="DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType.html">PresetType</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_RemainingVelocity_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_RemainingVelocity" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity">
  RemainingVelocity
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L309"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This is set after we are done so we will know at OnStop if we have some energy left</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RemainingVelocity { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_Scale_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_Scale" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale">
  Scale
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L425"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Scale { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_SnapBouncingIfVelocityLessThan_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_SnapBouncingIfVelocityLessThan" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan">
  SnapBouncingIfVelocityLessThan
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L57"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SnapBouncingIfVelocityLessThan { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMaxOverscrollValue_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMaxOverscrollValue" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue">
  mMaxOverscrollValue
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L38"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Must be over 0</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float mMaxOverscrollValue { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMaxVelocity_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMaxVelocity" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity">
  mMaxVelocity
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L61"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float mMaxVelocity { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMinOverscrollValue_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMinOverscrollValue" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue">
  mMinOverscrollValue
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L33"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Must be over 0</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float mMinOverscrollValue { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMinVelocity_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMinVelocity" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity">
  mMinVelocity
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L63"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float mMinVelocity { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMinVisibleChange_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMinVisibleChange" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange">
  mMinVisibleChange
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L256"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float mMinVisibleChange { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mVelocity_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mVelocity" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity">
  mVelocity
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L59"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float mVelocity { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mVerticalVelocityChange_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_mVerticalVelocityChange" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange">
  mVerticalVelocityChange
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L65"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float mVerticalVelocityChange { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_ClampOnStart_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_ClampOnStart" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart">
  ClampOnStart()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L78"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void ClampOnStart()</code></pre>
  </div>













  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_SetFriction_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_SetFriction_System_Single_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction(System.Single)">
  SetFriction(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L249"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public VelocitySkiaAnimator SetFriction(float value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Animate.Animators.VelocitySkiaAnimator.html">VelocitySkiaAnimator</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_SetVelocity_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_SetVelocity_System_Single_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity(System.Single)">
  SetVelocity(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L243"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public VelocitySkiaAnimator SetVelocity(float value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Animate.Animators.VelocitySkiaAnimator.html">VelocitySkiaAnimator</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_Stop_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_Stop" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop">
  Stop()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L71"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Stop()</code></pre>
  </div>













  <a id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_UpdateValue_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue*"></a>

  <h3 id="DrawnUi_Animate_Animators_VelocitySkiaAnimator_UpdateValue_System_Int64_System_Int64_" data-uid="DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue(System.Int64,System.Int64)">
  UpdateValue(long, long)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L93"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Update mValue using time distance between rendered frames.
Return true if anims is finished.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override bool UpdateValue(long deltaT, long deltaFromStart)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>deltaT</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
    <dt><code>deltaFromStart</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int64">long</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animators/VelocitySkiaAnimator.cs/#L28" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
