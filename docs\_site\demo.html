<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Demo | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Demo | DrawnUi Documentation ">
      
      
      <link rel="icon" href="images/favicon.ico">
      <link rel="stylesheet" href="public/docfx.min.css">
      <link rel="stylesheet" href="public/main.css">
      <meta name="docfx:navrel" content="toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/demo.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="index.html">
            <img id="logo" class="svg" src="images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">

      <div class="content">
        <div class="actionbar">

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="demo">Demo</h1>

<p>This section is dedicated to showcasing demo applications and interactive examples built with DrawnUI for .NET MAUI.</p>
<ul>
<li>Explore real-world usage of DrawnUI controls and layouts.</li>
<li>See how to implement custom controls and advanced UI scenarios.</li>
<li>Find inspiration for your own projects.</li>
</ul>
<ul>
<li><p>DrawnUI repository contains a Sandbox project with some custom controls, playground examples, maps etc</p>
</li>
<li><p>The main demo app is the <a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo">Engine Demo</a> 🤩</p>
</li>
<li><p><a href="https://github.com/taublast/ShadersCarousel/">Shaders Carousel Demo</a> featuring SkiaSharp v3 capabilities</p>
</li>
<li><p>A <a href="https://github.com/taublast/SurfAppCompareDrawn">drawn CollectionView demo</a> where you could see how simple and profitable it is to convert an existing recycled cells list into a drawn one</p>
</li>
<li><p>A <a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter">dynamic arcade game</a> drawn with this engine, uses preview nuget with SkiaSharp v3.</p>
</li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/demo.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
