# Samples

Explore real-world examples and code snippets to help you get started and master DrawnUi.Maui.

## Example Apps
- **[Engine Demo](https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo)** - A totally drawn app demo with recycled cells, camera etc
- **[Space Shooter Game](https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter)** - Arcade game etude built with DrawnUi
- **[Shaders Carousel](https://github.com/taublast/ShadersCarousel/)** - A totally drawn app with making use of SkiaSharp v3 shaders
- **[Sandbox project](https://github.com/taublast/DrawnUi.Maui/tree/main/src/Maui/Samples/Sandbox)** - Experiment with pre-built drawn 

## Code Snippets
- [Game UI & Interactive Games](advanced/game-ui.md)
- [Gradients](advanced/gradients.md)
- [SkiaScroll & Virtualization](advanced/skiascroll.md)
- [Gestures & Touch Input](advanced/gestures.md)

## Contributing <PERSON>ples
Have a cool UI or feature? Submit a PR or open an issue to share your sample with the community!
