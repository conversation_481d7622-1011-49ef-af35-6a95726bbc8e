{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.hy.js"], "sourcesContent": ["/*!\n * Lunr languages, `Armenian` language\n * https://github.com/turbobit/lunr-languages\n *\n * Copyright 2021, Manikandan Venkatasubban\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.hy = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.hy.trimmer,\n        lunr.hy.stopWordFilter\n      );\n    };\n\n    /* lunr trimmer function */\n    // http://www.unicode.org/charts/\n    lunr.hy.wordCharacters = \"[\" +\n      \"A-Za-z\" +\n      \"\\u0530-\\u058F\" + // armenian alphabet\n      \"\\uFB00-\\uFB4F\" + // armenian ligatures\n      \"]\";\n    lunr.hy.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.hy.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.hy.trimmer, 'trimmer-hy');\n\n\n    /* lunr stop word filter */\n    // https://www.ranks.nl/stopwords/armenian\n    lunr.hy.stopWordFilter = lunr.generateStopWordFilter('դու և եք էիր էիք հետո նաև նրանք որը վրա է որ պիտի են այս մեջ ն իր ու ի այդ որոնք այն կամ էր մի ես համար այլ իսկ էին ենք հետ ին թ էինք մենք նրա նա դուք եմ էի ըստ որպես ում'.split(' '));\n    lunr.Pipeline.registerFunction(lunr.hy.stopWordFilter, 'stopWordFilter-hy');\n\n    /* lunr stemmer function */\n    lunr.hy.stemmer = (function() {\n\n      return function(word) {\n        // for lunr version 2\n        if (typeof word.update === \"function\") {\n          return word.update(function(word) {\n            return word;\n          })\n        } else { // for lunr version <= 1\n          return word;\n        }\n\n      }\n    })();\n    lunr.Pipeline.registerFunction(lunr.hy.stemmer, 'stemmer-hy');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,cACV,CACF,EAIAA,EAAK,GAAG,eAAiB,qCAKzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAK5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,myBAA6K,MAAM,GAAG,CAAC,EAC5OA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,EAG1EA,EAAK,GAAG,QAAW,UAAW,CAE5B,OAAO,SAASC,EAAM,CAEpB,OAAI,OAAOA,EAAK,QAAW,WAClBA,EAAK,OAAO,SAASA,EAAM,CAChC,OAAOA,CACT,CAAC,EAEMA,CAGX,CACF,EAAG,EACHD,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,CAC9D,CACF,CAAC", "names": ["require_lunr_hy", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "word"]}