{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.hu.js"], "sourcesContent": ["/*!\n * Lunr languages, `Hungarian` language\n * https://github.com/Mihai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.hu = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.hu.trimmer,\n        lunr.hu.stopWordFilter,\n        lunr.hu.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.hu.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.hu.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.hu.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.hu.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.hu.trimmer, 'trimmer-hu');\n\n    /* lunr stemmer function */\n    lunr.hu.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function HungarianStemmer() {\n          var a_0 = [new Among(\"cs\", -1, -1), new Among(\"dzs\", -1, -1),\n              new Among(\"gy\", -1, -1), new Among(\"ly\", -1, -1),\n              new Among(\"ny\", -1, -1), new Among(\"sz\", -1, -1),\n              new Among(\"ty\", -1, -1), new Among(\"zs\", -1, -1)\n            ],\n            a_1 = [\n              new Among(\"\\u00E1\", -1, 1), new Among(\"\\u00E9\", -1, 2)\n            ],\n            a_2 = [\n              new Among(\"bb\", -1, -1), new Among(\"cc\", -1, -1),\n              new Among(\"dd\", -1, -1), new Among(\"ff\", -1, -1),\n              new Among(\"gg\", -1, -1), new Among(\"jj\", -1, -1),\n              new Among(\"kk\", -1, -1), new Among(\"ll\", -1, -1),\n              new Among(\"mm\", -1, -1), new Among(\"nn\", -1, -1),\n              new Among(\"pp\", -1, -1), new Among(\"rr\", -1, -1),\n              new Among(\"ccs\", -1, -1), new Among(\"ss\", -1, -1),\n              new Among(\"zzs\", -1, -1), new Among(\"tt\", -1, -1),\n              new Among(\"vv\", -1, -1), new Among(\"ggy\", -1, -1),\n              new Among(\"lly\", -1, -1), new Among(\"nny\", -1, -1),\n              new Among(\"tty\", -1, -1), new Among(\"ssz\", -1, -1),\n              new Among(\"zz\", -1, -1)\n            ],\n            a_3 = [new Among(\"al\", -1, 1),\n              new Among(\"el\", -1, 2)\n            ],\n            a_4 = [new Among(\"ba\", -1, -1),\n              new Among(\"ra\", -1, -1), new Among(\"be\", -1, -1),\n              new Among(\"re\", -1, -1), new Among(\"ig\", -1, -1),\n              new Among(\"nak\", -1, -1), new Among(\"nek\", -1, -1),\n              new Among(\"val\", -1, -1), new Among(\"vel\", -1, -1),\n              new Among(\"ul\", -1, -1), new Among(\"n\\u00E1l\", -1, -1),\n              new Among(\"n\\u00E9l\", -1, -1), new Among(\"b\\u00F3l\", -1, -1),\n              new Among(\"r\\u00F3l\", -1, -1), new Among(\"t\\u00F3l\", -1, -1),\n              new Among(\"b\\u00F5l\", -1, -1), new Among(\"r\\u00F5l\", -1, -1),\n              new Among(\"t\\u00F5l\", -1, -1), new Among(\"\\u00FCl\", -1, -1),\n              new Among(\"n\", -1, -1), new Among(\"an\", 19, -1),\n              new Among(\"ban\", 20, -1), new Among(\"en\", 19, -1),\n              new Among(\"ben\", 22, -1), new Among(\"k\\u00E9ppen\", 22, -1),\n              new Among(\"on\", 19, -1), new Among(\"\\u00F6n\", 19, -1),\n              new Among(\"k\\u00E9pp\", -1, -1), new Among(\"kor\", -1, -1),\n              new Among(\"t\", -1, -1), new Among(\"at\", 29, -1),\n              new Among(\"et\", 29, -1), new Among(\"k\\u00E9nt\", 29, -1),\n              new Among(\"ank\\u00E9nt\", 32, -1), new Among(\"enk\\u00E9nt\", 32, -1),\n              new Among(\"onk\\u00E9nt\", 32, -1), new Among(\"ot\", 29, -1),\n              new Among(\"\\u00E9rt\", 29, -1), new Among(\"\\u00F6t\", 29, -1),\n              new Among(\"hez\", -1, -1), new Among(\"hoz\", -1, -1),\n              new Among(\"h\\u00F6z\", -1, -1), new Among(\"v\\u00E1\", -1, -1),\n              new Among(\"v\\u00E9\", -1, -1)\n            ],\n            a_5 = [new Among(\"\\u00E1n\", -1, 2),\n              new Among(\"\\u00E9n\", -1, 1), new Among(\"\\u00E1nk\\u00E9nt\", -1, 3)\n            ],\n            a_6 = [\n              new Among(\"stul\", -1, 2), new Among(\"astul\", 0, 1),\n              new Among(\"\\u00E1stul\", 0, 3), new Among(\"st\\u00FCl\", -1, 2),\n              new Among(\"est\\u00FCl\", 3, 1), new Among(\"\\u00E9st\\u00FCl\", 3, 4)\n            ],\n            a_7 = [\n              new Among(\"\\u00E1\", -1, 1), new Among(\"\\u00E9\", -1, 2)\n            ],\n            a_8 = [\n              new Among(\"k\", -1, 7), new Among(\"ak\", 0, 4),\n              new Among(\"ek\", 0, 6), new Among(\"ok\", 0, 5),\n              new Among(\"\\u00E1k\", 0, 1), new Among(\"\\u00E9k\", 0, 2),\n              new Among(\"\\u00F6k\", 0, 3)\n            ],\n            a_9 = [new Among(\"\\u00E9i\", -1, 7),\n              new Among(\"\\u00E1\\u00E9i\", 0, 6), new Among(\"\\u00E9\\u00E9i\", 0, 5),\n              new Among(\"\\u00E9\", -1, 9), new Among(\"k\\u00E9\", 3, 4),\n              new Among(\"ak\\u00E9\", 4, 1), new Among(\"ek\\u00E9\", 4, 1),\n              new Among(\"ok\\u00E9\", 4, 1), new Among(\"\\u00E1k\\u00E9\", 4, 3),\n              new Among(\"\\u00E9k\\u00E9\", 4, 2), new Among(\"\\u00F6k\\u00E9\", 4, 1),\n              new Among(\"\\u00E9\\u00E9\", 3, 8)\n            ],\n            a_10 = [new Among(\"a\", -1, 18),\n              new Among(\"ja\", 0, 17), new Among(\"d\", -1, 16),\n              new Among(\"ad\", 2, 13), new Among(\"ed\", 2, 13),\n              new Among(\"od\", 2, 13), new Among(\"\\u00E1d\", 2, 14),\n              new Among(\"\\u00E9d\", 2, 15), new Among(\"\\u00F6d\", 2, 13),\n              new Among(\"e\", -1, 18), new Among(\"je\", 9, 17),\n              new Among(\"nk\", -1, 4), new Among(\"unk\", 11, 1),\n              new Among(\"\\u00E1nk\", 11, 2), new Among(\"\\u00E9nk\", 11, 3),\n              new Among(\"\\u00FCnk\", 11, 1), new Among(\"uk\", -1, 8),\n              new Among(\"juk\", 16, 7), new Among(\"\\u00E1juk\", 17, 5),\n              new Among(\"\\u00FCk\", -1, 8), new Among(\"j\\u00FCk\", 19, 7),\n              new Among(\"\\u00E9j\\u00FCk\", 20, 6), new Among(\"m\", -1, 12),\n              new Among(\"am\", 22, 9), new Among(\"em\", 22, 9),\n              new Among(\"om\", 22, 9), new Among(\"\\u00E1m\", 22, 10),\n              new Among(\"\\u00E9m\", 22, 11), new Among(\"o\", -1, 18),\n              new Among(\"\\u00E1\", -1, 19), new Among(\"\\u00E9\", -1, 20)\n            ],\n            a_11 = [\n              new Among(\"id\", -1, 10), new Among(\"aid\", 0, 9),\n              new Among(\"jaid\", 1, 6), new Among(\"eid\", 0, 9),\n              new Among(\"jeid\", 3, 6), new Among(\"\\u00E1id\", 0, 7),\n              new Among(\"\\u00E9id\", 0, 8), new Among(\"i\", -1, 15),\n              new Among(\"ai\", 7, 14), new Among(\"jai\", 8, 11),\n              new Among(\"ei\", 7, 14), new Among(\"jei\", 10, 11),\n              new Among(\"\\u00E1i\", 7, 12), new Among(\"\\u00E9i\", 7, 13),\n              new Among(\"itek\", -1, 24), new Among(\"eitek\", 14, 21),\n              new Among(\"jeitek\", 15, 20), new Among(\"\\u00E9itek\", 14, 23),\n              new Among(\"ik\", -1, 29), new Among(\"aik\", 18, 26),\n              new Among(\"jaik\", 19, 25), new Among(\"eik\", 18, 26),\n              new Among(\"jeik\", 21, 25), new Among(\"\\u00E1ik\", 18, 27),\n              new Among(\"\\u00E9ik\", 18, 28), new Among(\"ink\", -1, 20),\n              new Among(\"aink\", 25, 17), new Among(\"jaink\", 26, 16),\n              new Among(\"eink\", 25, 17), new Among(\"jeink\", 28, 16),\n              new Among(\"\\u00E1ink\", 25, 18), new Among(\"\\u00E9ink\", 25, 19),\n              new Among(\"aitok\", -1, 21), new Among(\"jaitok\", 32, 20),\n              new Among(\"\\u00E1itok\", -1, 22), new Among(\"im\", -1, 5),\n              new Among(\"aim\", 35, 4), new Among(\"jaim\", 36, 1),\n              new Among(\"eim\", 35, 4), new Among(\"jeim\", 38, 1),\n              new Among(\"\\u00E1im\", 35, 2), new Among(\"\\u00E9im\", 35, 3)\n            ],\n            g_v = [\n              17, 65, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17, 52, 14\n            ],\n            I_p1, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function r_mark_regions() {\n            var v_1 = sbp.cursor,\n              v_2;\n            I_p1 = sbp.limit;\n            if (sbp.in_grouping(g_v, 97, 252)) {\n              while (true) {\n                v_2 = sbp.cursor;\n                if (sbp.out_grouping(g_v, 97, 252)) {\n                  sbp.cursor = v_2;\n                  if (!sbp.find_among(a_0, 8)) {\n                    sbp.cursor = v_2;\n                    if (v_2 < sbp.limit)\n                      sbp.cursor++;\n                  }\n                  I_p1 = sbp.cursor;\n                  return;\n                }\n                sbp.cursor = v_2;\n                if (v_2 >= sbp.limit) {\n                  I_p1 = v_2;\n                  return;\n                }\n                sbp.cursor++;\n              }\n            }\n            sbp.cursor = v_1;\n            if (sbp.out_grouping(g_v, 97, 252)) {\n              while (!sbp.in_grouping(g_v, 97, 252)) {\n                if (sbp.cursor >= sbp.limit)\n                  return;\n                sbp.cursor++;\n              }\n              I_p1 = sbp.cursor;\n            }\n          }\n\n          function r_R1() {\n            return I_p1 <= sbp.cursor;\n          }\n\n          function r_v_ending() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_1, 2);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"a\");\n                    break;\n                  case 2:\n                    sbp.slice_from(\"e\");\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_double() {\n            var v_1 = sbp.limit - sbp.cursor;\n            if (!sbp.find_among_b(a_2, 23))\n              return false;\n            sbp.cursor = sbp.limit - v_1;\n            return true;\n          }\n\n          function r_undouble() {\n            if (sbp.cursor > sbp.limit_backward) {\n              sbp.cursor--;\n              sbp.ket = sbp.cursor;\n              var c = sbp.cursor - 1;\n              if (sbp.limit_backward <= c && c <= sbp.limit) {\n                sbp.cursor = c;\n                sbp.bra = c;\n                sbp.slice_del();\n              }\n            }\n          }\n\n          function r_instrum() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_3, 2);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                if (among_var == 1 || among_var == 2)\n                  if (!r_double())\n                    return;\n                sbp.slice_del();\n                r_undouble();\n              }\n            }\n          }\n\n          function r_case() {\n            sbp.ket = sbp.cursor;\n            if (sbp.find_among_b(a_4, 44)) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                sbp.slice_del();\n                r_v_ending();\n              }\n            }\n          }\n\n          function r_case_special() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_5, 3);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"e\");\n                    break;\n                  case 2:\n                  case 3:\n                    sbp.slice_from(\"a\");\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_case_other() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_6, 6);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                  case 2:\n                    sbp.slice_del();\n                    break;\n                  case 3:\n                    sbp.slice_from(\"a\");\n                    break;\n                  case 4:\n                    sbp.slice_from(\"e\");\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_factive() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_7, 2);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                if (among_var == 1 || among_var == 2)\n                  if (!r_double())\n                    return;\n                sbp.slice_del();\n                r_undouble()\n              }\n            }\n          }\n\n          function r_plural() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_8, 7);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"a\");\n                    break;\n                  case 2:\n                    sbp.slice_from(\"e\");\n                    break;\n                  case 3:\n                  case 4:\n                  case 5:\n                  case 6:\n                  case 7:\n                    sbp.slice_del();\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_owned() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_9, 12);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                  case 4:\n                  case 7:\n                  case 9:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                  case 5:\n                  case 8:\n                    sbp.slice_from(\"e\");\n                    break;\n                  case 3:\n                  case 6:\n                    sbp.slice_from(\"a\");\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_sing_owner() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_10, 31);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                  case 4:\n                  case 7:\n                  case 8:\n                  case 9:\n                  case 12:\n                  case 13:\n                  case 16:\n                  case 17:\n                  case 18:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                  case 5:\n                  case 10:\n                  case 14:\n                  case 19:\n                    sbp.slice_from(\"a\");\n                    break;\n                  case 3:\n                  case 6:\n                  case 11:\n                  case 15:\n                  case 20:\n                    sbp.slice_from(\"e\");\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_plur_owner() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_11, 42);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                  case 4:\n                  case 5:\n                  case 6:\n                  case 9:\n                  case 10:\n                  case 11:\n                  case 14:\n                  case 15:\n                  case 16:\n                  case 17:\n                  case 20:\n                  case 21:\n                  case 24:\n                  case 25:\n                  case 26:\n                  case 29:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                  case 7:\n                  case 12:\n                  case 18:\n                  case 22:\n                  case 27:\n                    sbp.slice_from(\"a\");\n                    break;\n                  case 3:\n                  case 8:\n                  case 13:\n                  case 19:\n                  case 23:\n                  case 28:\n                    sbp.slice_from(\"e\");\n                    break;\n                }\n              }\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            r_instrum();\n            sbp.cursor = sbp.limit;\n            r_case();\n            sbp.cursor = sbp.limit;\n            r_case_special();\n            sbp.cursor = sbp.limit;\n            r_case_other();\n            sbp.cursor = sbp.limit;\n            r_factive();\n            sbp.cursor = sbp.limit;\n            r_owned();\n            sbp.cursor = sbp.limit;\n            r_sing_owner();\n            sbp.cursor = sbp.limit;\n            r_plur_owner();\n            sbp.cursor = sbp.limit;\n            r_plural();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.hu.stemmer, 'stemmer-hu');\n\n    lunr.hu.stopWordFilter = lunr.generateStopWordFilter('a abban ahhoz ahogy ahol aki akik akkor alatt amely amelyek amelyekben amelyeket amelyet amelynek ami amikor amit amolyan amíg annak arra arról az azok azon azonban azt aztán azután azzal azért be belül benne bár cikk cikkek cikkeket csak de e ebben eddig egy egyes egyetlen egyik egyre egyéb egész ehhez ekkor el ellen elsõ elég elõ elõször elõtt emilyen ennek erre ez ezek ezen ezt ezzel ezért fel felé hanem hiszen hogy hogyan igen ill ill. illetve ilyen ilyenkor ismét ison itt jobban jó jól kell kellett keressünk keresztül ki kívül között közül legalább legyen lehet lehetett lenne lenni lesz lett maga magát majd majd meg mellett mely melyek mert mi mikor milyen minden mindenki mindent mindig mint mintha mit mivel miért most már más másik még míg nagy nagyobb nagyon ne nekem neki nem nincs néha néhány nélkül olyan ott pedig persze rá s saját sem semmi sok sokat sokkal szemben szerint szinte számára talán tehát teljes tovább továbbá több ugyanis utolsó után utána vagy vagyis vagyok valaki valami valamint való van vannak vele vissza viszont volna volt voltak voltam voltunk által általában át én éppen és így õ õk õket össze úgy új újabb újra'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.hu.stopWordFilter, 'stopWordFilter-hu');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAA4B,CACnC,IAAIC,EAAM,CAAC,IAAIH,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACvD,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,CACjD,EACAI,EAAM,CACJ,IAAIJ,EAAM,OAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,GAAI,CAAC,CACvD,EACAK,EAAM,CACJ,IAAIL,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAChD,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAChD,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EAChD,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACjD,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACjD,IAAIA,EAAM,KAAM,GAAI,EAAE,CACxB,EACAM,EAAM,CAAC,IAAIN,EAAM,KAAM,GAAI,CAAC,EAC1B,IAAIA,EAAM,KAAM,GAAI,CAAC,CACvB,EACAO,EAAM,CAAC,IAAIP,EAAM,KAAM,GAAI,EAAE,EAC3B,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACjD,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACjD,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAY,GAAI,EAAE,EACrD,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAY,GAAI,EAAE,EAC3D,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAY,GAAI,EAAE,EAC3D,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAY,GAAI,EAAE,EAC3D,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAW,GAAI,EAAE,EAC1D,IAAIA,EAAM,IAAK,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC9C,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAChD,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,YAAe,GAAI,EAAE,EACzD,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAW,GAAI,EAAE,EACpD,IAAIA,EAAM,UAAa,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACvD,IAAIA,EAAM,IAAK,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC9C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,UAAa,GAAI,EAAE,EACtD,IAAIA,EAAM,YAAe,GAAI,EAAE,EAAG,IAAIA,EAAM,YAAe,GAAI,EAAE,EACjE,IAAIA,EAAM,YAAe,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EACxD,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAW,GAAI,EAAE,EAC1D,IAAIA,EAAM,MAAO,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACjD,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAW,GAAI,EAAE,EAC1D,IAAIA,EAAM,QAAW,GAAI,EAAE,CAC7B,EACAQ,EAAM,CAAC,IAAIR,EAAM,QAAW,GAAI,CAAC,EAC/B,IAAIA,EAAM,QAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,eAAoB,GAAI,CAAC,CAClE,EACAS,EAAM,CACJ,IAAIT,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,EAAG,CAAC,EACjD,IAAIA,EAAM,WAAc,EAAG,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EAC3D,IAAIA,EAAM,WAAc,EAAG,CAAC,EAAG,IAAIA,EAAM,cAAmB,EAAG,CAAC,CAClE,EACAU,EAAM,CACJ,IAAIV,EAAM,OAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,GAAI,CAAC,CACvD,EACAW,EAAM,CACJ,IAAIX,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,EAAG,CAAC,EAC3C,IAAIA,EAAM,KAAM,EAAG,CAAC,EAAG,IAAIA,EAAM,KAAM,EAAG,CAAC,EAC3C,IAAIA,EAAM,QAAW,EAAG,CAAC,EAAG,IAAIA,EAAM,QAAW,EAAG,CAAC,EACrD,IAAIA,EAAM,QAAW,EAAG,CAAC,CAC3B,EACAY,EAAM,CAAC,IAAIZ,EAAM,QAAW,GAAI,CAAC,EAC/B,IAAIA,EAAM,YAAiB,EAAG,CAAC,EAAG,IAAIA,EAAM,YAAiB,EAAG,CAAC,EACjE,IAAIA,EAAM,OAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAW,EAAG,CAAC,EACrD,IAAIA,EAAM,SAAY,EAAG,CAAC,EAAG,IAAIA,EAAM,SAAY,EAAG,CAAC,EACvD,IAAIA,EAAM,SAAY,EAAG,CAAC,EAAG,IAAIA,EAAM,YAAiB,EAAG,CAAC,EAC5D,IAAIA,EAAM,YAAiB,EAAG,CAAC,EAAG,IAAIA,EAAM,YAAiB,EAAG,CAAC,EACjE,IAAIA,EAAM,WAAgB,EAAG,CAAC,CAChC,EACAa,EAAO,CAAC,IAAIb,EAAM,IAAK,GAAI,EAAE,EAC3B,IAAIA,EAAM,KAAM,EAAG,EAAE,EAAG,IAAIA,EAAM,IAAK,GAAI,EAAE,EAC7C,IAAIA,EAAM,KAAM,EAAG,EAAE,EAAG,IAAIA,EAAM,KAAM,EAAG,EAAE,EAC7C,IAAIA,EAAM,KAAM,EAAG,EAAE,EAAG,IAAIA,EAAM,QAAW,EAAG,EAAE,EAClD,IAAIA,EAAM,QAAW,EAAG,EAAE,EAAG,IAAIA,EAAM,QAAW,EAAG,EAAE,EACvD,IAAIA,EAAM,IAAK,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,EAAG,EAAE,EAC7C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC9C,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EACzD,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EACnD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EACrD,IAAIA,EAAM,QAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,EACxD,IAAIA,EAAM,aAAkB,GAAI,CAAC,EAAG,IAAIA,EAAM,IAAK,GAAI,EAAE,EACzD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC7C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAW,GAAI,EAAE,EACnD,IAAIA,EAAM,QAAW,GAAI,EAAE,EAAG,IAAIA,EAAM,IAAK,GAAI,EAAE,EACnD,IAAIA,EAAM,OAAU,GAAI,EAAE,EAAG,IAAIA,EAAM,OAAU,GAAI,EAAE,CACzD,EACAc,EAAO,CACL,IAAId,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,EAAG,CAAC,EAC9C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,MAAO,EAAG,CAAC,EAC9C,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAAG,IAAIA,EAAM,SAAY,EAAG,CAAC,EACnD,IAAIA,EAAM,SAAY,EAAG,CAAC,EAAG,IAAIA,EAAM,IAAK,GAAI,EAAE,EAClD,IAAIA,EAAM,KAAM,EAAG,EAAE,EAAG,IAAIA,EAAM,MAAO,EAAG,EAAE,EAC9C,IAAIA,EAAM,KAAM,EAAG,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EAC/C,IAAIA,EAAM,QAAW,EAAG,EAAE,EAAG,IAAIA,EAAM,QAAW,EAAG,EAAE,EACvD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAS,GAAI,EAAE,EACpD,IAAIA,EAAM,SAAU,GAAI,EAAE,EAAG,IAAIA,EAAM,WAAc,GAAI,EAAE,EAC3D,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EAChD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EAClD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAY,GAAI,EAAE,EACvD,IAAIA,EAAM,SAAY,GAAI,EAAE,EAAG,IAAIA,EAAM,MAAO,GAAI,EAAE,EACtD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAS,GAAI,EAAE,EACpD,IAAIA,EAAM,OAAQ,GAAI,EAAE,EAAG,IAAIA,EAAM,QAAS,GAAI,EAAE,EACpD,IAAIA,EAAM,UAAa,GAAI,EAAE,EAAG,IAAIA,EAAM,UAAa,GAAI,EAAE,EAC7D,IAAIA,EAAM,QAAS,GAAI,EAAE,EAAG,IAAIA,EAAM,SAAU,GAAI,EAAE,EACtD,IAAIA,EAAM,WAAc,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EACtD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAY,GAAI,CAAC,CAC3D,EACAe,EAAM,CACJ,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,EAChE,EACAC,EAAMC,EAAM,IAAIhB,EAClB,KAAK,WAAa,SAASiB,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,GAAiB,CACxB,IAAIC,EAAMH,EAAI,OACZI,EAEF,GADAL,EAAOC,EAAI,MACPA,EAAI,YAAYF,EAAK,GAAI,GAAG,EAC9B,OAAa,CAEX,GADAM,EAAMJ,EAAI,OACNA,EAAI,aAAaF,EAAK,GAAI,GAAG,EAAG,CAClCE,EAAI,OAASI,EACRJ,EAAI,WAAWd,EAAK,CAAC,IACxBc,EAAI,OAASI,EACTA,EAAMJ,EAAI,OACZA,EAAI,UAERD,EAAOC,EAAI,OACX,MACF,CAEA,GADAA,EAAI,OAASI,EACTA,GAAOJ,EAAI,MAAO,CACpBD,EAAOK,EACP,MACF,CACAJ,EAAI,QACN,CAGF,GADAA,EAAI,OAASG,EACTH,EAAI,aAAaF,EAAK,GAAI,GAAG,EAAG,CAClC,KAAO,CAACE,EAAI,YAAYF,EAAK,GAAI,GAAG,GAAG,CACrC,GAAIE,EAAI,QAAUA,EAAI,MACpB,OACFA,EAAI,QACN,CACAD,EAAOC,EAAI,MACb,CACF,CAEA,SAASK,GAAO,CACd,OAAON,GAAQC,EAAI,MACrB,CAEA,SAASM,GAAa,CACpB,IAAIC,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAab,EAAK,CAAC,EAC/BoB,IACFP,EAAI,IAAMA,EAAI,OACVK,EAAK,GACP,OAAQE,EAAW,CACjB,IAAK,GACHP,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,KACJ,CAGN,CAEA,SAASQ,GAAW,CAClB,IAAIL,EAAMH,EAAI,MAAQA,EAAI,OAC1B,OAAKA,EAAI,aAAaZ,EAAK,EAAE,GAE7BY,EAAI,OAASA,EAAI,MAAQG,EAClB,IAFE,EAGX,CAEA,SAASM,GAAa,CACpB,GAAIT,EAAI,OAASA,EAAI,eAAgB,CACnCA,EAAI,SACJA,EAAI,IAAMA,EAAI,OACd,IAAIU,EAAIV,EAAI,OAAS,EACjBA,EAAI,gBAAkBU,GAAKA,GAAKV,EAAI,QACtCA,EAAI,OAASU,EACbV,EAAI,IAAMU,EACVV,EAAI,UAAU,EAElB,CACF,CAEA,SAASW,GAAY,CACnB,IAAIJ,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaX,EAAK,CAAC,EAC/BkB,IACFP,EAAI,IAAMA,EAAI,OACVK,EAAK,GAAG,CACV,IAAIE,GAAa,GAAKA,GAAa,IAC7B,CAACC,EAAS,EACZ,OACJR,EAAI,UAAU,EACdS,EAAW,CACb,CAEJ,CAEA,SAASG,GAAS,CAChBZ,EAAI,IAAMA,EAAI,OACVA,EAAI,aAAaV,EAAK,EAAE,IAC1BU,EAAI,IAAMA,EAAI,OACVK,EAAK,IACPL,EAAI,UAAU,EACdM,EAAW,GAGjB,CAEA,SAASO,GAAiB,CACxB,IAAIN,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaT,EAAK,CAAC,EAC/BgB,IACFP,EAAI,IAAMA,EAAI,OACVK,EAAK,GACP,OAAQE,EAAW,CACjB,IAAK,GACHP,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACL,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,KACJ,CAGN,CAEA,SAASc,GAAe,CACtB,IAAIP,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaR,EAAK,CAAC,EAC/Be,IACFP,EAAI,IAAMA,EAAI,OACVK,EAAK,GACP,OAAQE,EAAW,CACjB,IAAK,GACL,IAAK,GACHP,EAAI,UAAU,EACd,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,KACJ,CAGN,CAEA,SAASe,GAAY,CACnB,IAAIR,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaP,EAAK,CAAC,EAC/Bc,IACFP,EAAI,IAAMA,EAAI,OACVK,EAAK,GAAG,CACV,IAAIE,GAAa,GAAKA,GAAa,IAC7B,CAACC,EAAS,EACZ,OACJR,EAAI,UAAU,EACdS,EAAW,CACb,CAEJ,CAEA,SAASO,GAAW,CAClB,IAAIT,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaN,EAAK,CAAC,EAC/Ba,IACFP,EAAI,IAAMA,EAAI,OACVK,EAAK,GACP,OAAQE,EAAW,CACjB,IAAK,GACHP,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,GACHA,EAAI,UAAU,EACd,KACJ,CAGN,CAEA,SAASiB,GAAU,CACjB,IAAIV,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaL,EAAK,EAAE,EAChCY,IACFP,EAAI,IAAMA,EAAI,OACVK,EAAK,GACP,OAAQE,EAAW,CACjB,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,GACHP,EAAI,UAAU,EACd,MACF,IAAK,GACL,IAAK,GACL,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACL,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,KACJ,CAGN,CAEA,SAASkB,GAAe,CACtB,IAAIX,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaJ,EAAM,EAAE,EACjCW,IACFP,EAAI,IAAMA,EAAI,OACVK,EAAK,GACP,OAAQE,EAAW,CACjB,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACHP,EAAI,UAAU,EACd,MACF,IAAK,GACL,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACL,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACHA,EAAI,WAAW,GAAG,EAClB,KACJ,CAGN,CAEA,SAASmB,GAAe,CACtB,IAAIZ,EAGJ,GAFAP,EAAI,IAAMA,EAAI,OACdO,EAAYP,EAAI,aAAaH,EAAM,EAAE,EACjCU,IACFP,EAAI,IAAMA,EAAI,OACVK,EAAK,GACP,OAAQE,EAAW,CACjB,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACHP,EAAI,UAAU,EACd,MACF,IAAK,GACL,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACL,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACHA,EAAI,WAAW,GAAG,EAClB,KACJ,CAGN,CACA,KAAK,KAAO,UAAW,CACrB,IAAIG,EAAMH,EAAI,OACd,OAAAE,EAAe,EACfF,EAAI,eAAiBG,EACrBH,EAAI,OAASA,EAAI,MACjBW,EAAU,EACVX,EAAI,OAASA,EAAI,MACjBY,EAAO,EACPZ,EAAI,OAASA,EAAI,MACjBa,EAAe,EACfb,EAAI,OAASA,EAAI,MACjBc,EAAa,EACbd,EAAI,OAASA,EAAI,MACjBe,EAAU,EACVf,EAAI,OAASA,EAAI,MACjBiB,EAAQ,EACRjB,EAAI,OAASA,EAAI,MACjBkB,EAAa,EACblB,EAAI,OAASA,EAAI,MACjBmB,EAAa,EACbnB,EAAI,OAASA,EAAI,MACjBgB,EAAS,EACF,EACT,CACF,EAGF,OAAO,SAASI,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASnB,EAAM,CACjC,OAAAhB,EAAG,WAAWgB,CAAI,EAClBhB,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWmC,CAAK,EACnBnC,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,21CAAsoC,MAAM,GAAG,CAAC,EAErsCA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_hu", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6", "a_7", "a_8", "a_9", "a_10", "a_11", "g_v", "I_p1", "sbp", "word", "r_mark_regions", "v_1", "v_2", "r_R1", "r_v_ending", "among_var", "r_double", "r_undouble", "c", "r_instrum", "r_case", "r_case_special", "r_case_other", "r_factive", "r_plural", "r_owned", "r_sing_owner", "r_plur_owner", "token"]}