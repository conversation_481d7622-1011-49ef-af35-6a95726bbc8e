<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class DecelerationTimingParameters | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class DecelerationTimingParameters | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_DecelerationTimingParameters.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.DecelerationTimingParameters%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.DecelerationTimingParameters">



  <h1 id="DrawnUi_Draw_DecelerationTimingParameters" data-uid="DrawnUi.Draw.DecelerationTimingParameters" class="text-break">
Class DecelerationTimingParameters  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class DecelerationTimingParameters : ITimingParameters</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">DecelerationTimingParameters</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="DrawnUi.Draw.ITimingParameters.html">ITimingParameters</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_DecelerationTimingParameters__ctor_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.#ctor*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters__ctor_System_Single_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.#ctor(System.Single,System.Single,System.Single,System.Single)">
  DecelerationTimingParameters(float, float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L13"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DecelerationTimingParameters(float initialValue, float initialVelocity, float decelerationRate, float threshold)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>initialValue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>initialVelocity</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>decelerationRate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>threshold</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Draw_DecelerationTimingParameters__ctor_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.#ctor*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters__ctor_System_Single_System_Single_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single)">
  DecelerationTimingParameters(float, float, float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L35"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Creates timing parameters specifically for scrolling to a target position in a specified time</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DecelerationTimingParameters(float currentValue, float targetValue, float durationSecs, float decelerationRate, float threshold = 0.1)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>currentValue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>The current position/value</p>
</dd>
    <dt><code>targetValue</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>The target position/value to scroll to</p>
</dd>
    <dt><code>durationSecs</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>The desired duration in seconds</p>
</dd>
    <dt><code>decelerationRate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>The deceleration rate (between 0 and 1)</p>
</dd>
    <dt><code>threshold</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd><p>The threshold for considering motion stopped</p>
</dd>
  </dl>












  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Draw_DecelerationTimingParameters_DecelerationK_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.DecelerationK*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_DecelerationK" data-uid="DrawnUi.Draw.DecelerationTimingParameters.DecelerationK">
  DecelerationK
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L10"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float DecelerationK { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_DecelerationTimingParameters_DecelerationRate_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.DecelerationRate*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_DecelerationRate" data-uid="DrawnUi.Draw.DecelerationTimingParameters.DecelerationRate">
  DecelerationRate
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L9"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float DecelerationRate { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_DecelerationTimingParameters_Destination_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.Destination*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_Destination" data-uid="DrawnUi.Draw.DecelerationTimingParameters.Destination">
  Destination
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L83"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Destination { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_DecelerationTimingParameters_DurationSecs_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.DurationSecs*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_DurationSecs" data-uid="DrawnUi.Draw.DecelerationTimingParameters.DurationSecs">
  DurationSecs
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L91"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float DurationSecs { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_DecelerationTimingParameters_InitialValue_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.InitialValue*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_InitialValue" data-uid="DrawnUi.Draw.DecelerationTimingParameters.InitialValue">
  InitialValue
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float InitialValue { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_DecelerationTimingParameters_InitialVelocity_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.InitialVelocity*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_InitialVelocity" data-uid="DrawnUi.Draw.DecelerationTimingParameters.InitialVelocity">
  InitialVelocity
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float InitialVelocity { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Draw_DecelerationTimingParameters_Threshold_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.Threshold*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_Threshold" data-uid="DrawnUi.Draw.DecelerationTimingParameters.Threshold">
  Threshold
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Threshold { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Draw_DecelerationTimingParameters_DurationToValue_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.DurationToValue*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_DurationToValue_System_Single_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.DurationToValue(System.Single)">
  DurationToValue(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L119"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double DurationToValue(float value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_DecelerationTimingParameters_ValueAt_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.ValueAt*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_ValueAt_System_Single_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.ValueAt(System.Single)">
  ValueAt(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L103"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ValueAt(float offsetSecs)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>offsetSecs</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_DecelerationTimingParameters_VelocityAt_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.VelocityAt*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_VelocityAt_System_Double_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.VelocityAt(System.Double)">
  VelocityAt(double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L113"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float VelocityAt(double time)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>time</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_DecelerationTimingParameters_VelocityTo_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.VelocityTo*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_VelocityTo_System_Single_System_Single_System_Double_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.VelocityTo(System.Single,System.Single,System.Double)">
  VelocityTo(float, float, double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L128"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float VelocityTo(float startingPoint, float targetPoint, double time)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>startingPoint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>targetPoint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>time</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Draw_DecelerationTimingParameters_VelocityToZero_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.VelocityToZero*"></a>

  <h3 id="DrawnUi_Draw_DecelerationTimingParameters_VelocityToZero_System_Single_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.DecelerationTimingParameters.VelocityToZero(System.Single,System.Single,System.Single,System.Single)">
  VelocityToZero(float, float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L135"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float VelocityToZero(float startingPoint, float targetPoint, float maxTimeSecs = 0, float epsilon = 1E-06)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>startingPoint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>targetPoint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>maxTimeSecs</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>epsilon</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Animations/Parameters/DecelerationTimingParameters.cs/#L5" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
