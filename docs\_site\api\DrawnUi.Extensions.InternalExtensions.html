<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class InternalExtensions | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class InternalExtensions | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Extensions_InternalExtensions.md&amp;value=---%0Auid%3A%20DrawnUi.Extensions.InternalExtensions%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Extensions.InternalExtensions">



  <h1 id="DrawnUi_Extensions_InternalExtensions" data-uid="DrawnUi.Extensions.InternalExtensions" class="text-break">
Class InternalExtensions  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Extensions.html">Extensions</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class InternalExtensions</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">InternalExtensions</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Extensions_InternalExtensions_Clamp_" data-uid="DrawnUi.Extensions.InternalExtensions.Clamp*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_Clamp_System_Double_System_Double_System_Double_" data-uid="DrawnUi.Extensions.InternalExtensions.Clamp(System.Double,System.Double,System.Double)">
  Clamp(double, double, double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L162"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double Clamp(this double self, double min, double max)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>self</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
    <dt><code>min</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
    <dt><code>max</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_Clamp_" data-uid="DrawnUi.Extensions.InternalExtensions.Clamp*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_Clamp_System_Int32_System_Int32_System_Int32_" data-uid="DrawnUi.Extensions.InternalExtensions.Clamp(System.Int32,System.Int32,System.Int32)">
  Clamp(int, int, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L181"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int Clamp(this int self, int min, int max)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>self</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>min</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
    <dt><code>max</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_Clamp_" data-uid="DrawnUi.Extensions.InternalExtensions.Clamp*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_Clamp_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Extensions.InternalExtensions.Clamp(System.Single,System.Single,System.Single)">
  Clamp(float, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L143"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Clamp(this float self, float min, float max)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>self</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>min</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>max</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_Clone_" data-uid="DrawnUi.Extensions.InternalExtensions.Clone*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_Clone_SkiaSharp_SKRect_" data-uid="DrawnUi.Extensions.InternalExtensions.Clone(SkiaSharp.SKRect)">
  Clone(SKRect)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L133"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKRect Clone(this SKRect rect)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rect</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_ContainsInclusive_" data-uid="DrawnUi.Extensions.InternalExtensions.ContainsInclusive*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_ContainsInclusive_SkiaSharp_SKRect_SkiaSharp_SKPoint_" data-uid="DrawnUi.Extensions.InternalExtensions.ContainsInclusive(SkiaSharp.SKRect,SkiaSharp.SKPoint)">
  ContainsInclusive(SKRect, SKPoint)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L21"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The default Skia method is returning false if point is on the bounds, We correct this by custom function.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool ContainsInclusive(this SKRect rect, SKPoint point)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rect</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>point</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_ContainsInclusive_" data-uid="DrawnUi.Extensions.InternalExtensions.ContainsInclusive*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_ContainsInclusive_SkiaSharp_SKRect_System_Single_System_Single_" data-uid="DrawnUi.Extensions.InternalExtensions.ContainsInclusive(SkiaSharp.SKRect,System.Single,System.Single)">
  ContainsInclusive(SKRect, float, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L30"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The default Skia method is returning false if point is on the bounds, We correct this by custom function.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool ContainsInclusive(this SKRect rect, float x, float y)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rect</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>x</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>y</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_" data-uid="DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_" data-uid="DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)">
  DisposeControlAndChildren(IView)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L55"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DisposeControlAndChildren(this IView view)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Extensions_InternalExtensions_FindMauiContext_" data-uid="DrawnUi.Extensions.InternalExtensions.FindMauiContext*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_" data-uid="DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)">
  FindMauiContext(Element, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/InternalExtensions.Maui.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IMauiContext? FindMauiContext(this Element element, bool fallbackToAppMauiContext = false)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>element</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a></dt>
    <dd></dd>
    <dt><code>fallbackToAppMauiContext</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.imauicontext">IMauiContext</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_FromPlatform_" data-uid="DrawnUi.Extensions.InternalExtensions.FromPlatform*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_FromPlatform_Microsoft_Maui_Controls_Shapes_Geometry_SkiaSharp_SKPath_SkiaSharp_SKRect_System_Single_" data-uid="DrawnUi.Extensions.InternalExtensions.FromPlatform(Microsoft.Maui.Controls.Shapes.Geometry,SkiaSharp.SKPath,SkiaSharp.SKRect,System.Single)">
  FromPlatform(Geometry, SKPath, SKRect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/InternalExtensions.Maui.cs/#L68"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void FromPlatform(this Geometry geometry, SKPath path, SKRect destination, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>geometry</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.shapes.geometry">Geometry</a></dt>
    <dd></dd>
    <dt><code>path</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpath">SKPath</a></dt>
    <dd></dd>
    <dt><code>destination</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Extensions_InternalExtensions_FromPlatform_" data-uid="DrawnUi.Extensions.InternalExtensions.FromPlatform*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_FromPlatform_Microsoft_Maui_Controls_Shapes_Geometry_SkiaSharp_SKPath_System_Single_" data-uid="DrawnUi.Extensions.InternalExtensions.FromPlatform(Microsoft.Maui.Controls.Shapes.Geometry,SkiaSharp.SKPath,System.Single)">
  FromPlatform(Geometry, SKPath, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/InternalExtensions.Maui.cs/#L74"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKPath FromPlatform(this Geometry geometry, SKPath path, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>geometry</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.shapes.geometry">Geometry</a></dt>
    <dd></dd>
    <dt><code>path</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpath">SKPath</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpath">SKPath</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_FromPlatform_" data-uid="DrawnUi.Extensions.InternalExtensions.FromPlatform*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_" data-uid="DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)">
  FromPlatform(object)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/InternalExtensions.Maui.cs/#L51"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaShadow FromPlatform(this object platform)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>platform</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_GetItemAtIndex_" data-uid="DrawnUi.Extensions.InternalExtensions.GetItemAtIndex*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_GetItemAtIndex__1_System_Collections_Generic_LinkedList___0__System_Int32_" data-uid="DrawnUi.Extensions.InternalExtensions.GetItemAtIndex``1(System.Collections.Generic.LinkedList{``0},System.Int32)">
  GetItemAtIndex&lt;T&gt;(LinkedList&lt;T&gt;, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L38"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T GetItemAtIndex&lt;T&gt;(this LinkedList&lt;T&gt; linkedStack, int index)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>linkedStack</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.linkedlist-1">LinkedList</a>&lt;T&gt;</dt>
    <dd></dd>
    <dt><code>index</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><span class="xref">T</span></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Type Parameters</h4>
  <dl class="parameters">
    <dt><code>T</code></dt>
    <dd></dd>
  </dl>










  <a id="DrawnUi_Extensions_InternalExtensions_GetParentsPath_" data-uid="DrawnUi.Extensions.InternalExtensions.GetParentsPath*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_" data-uid="DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)">
  GetParentsPath(Element)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/InternalExtensions.Maui.cs/#L25"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IEnumerable&lt;Element&gt; GetParentsPath(this Element self)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>self</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_IntersectsWith_" data-uid="DrawnUi.Extensions.InternalExtensions.IntersectsWith*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_IntersectsWith_SkiaSharp_SKRect_SkiaSharp_SKRect_SkiaSharp_SKPoint_" data-uid="DrawnUi.Extensions.InternalExtensions.IntersectsWith(SkiaSharp.SKRect,SkiaSharp.SKRect,SkiaSharp.SKPoint)">
  IntersectsWith(SKRect, SKRect, SKPoint)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IntersectsWith(this SKRect rect, SKRect with, SKPoint offset)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rect</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>with</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
    <dt><code>offset</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_IsEven_" data-uid="DrawnUi.Extensions.InternalExtensions.IsEven*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_IsEven_System_Int32_" data-uid="DrawnUi.Extensions.InternalExtensions.IsEven(System.Int32)">
  IsEven(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L138"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsEven(this int value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_ToDegrees_" data-uid="DrawnUi.Extensions.InternalExtensions.ToDegrees*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_ToDegrees_System_Single_" data-uid="DrawnUi.Extensions.InternalExtensions.ToDegrees(System.Single)">
  ToDegrees(float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/InternalExtensions.Maui.cs/#L46"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Radians to degrees</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float ToDegrees(this float radians)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>radians</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_ToSKRect_" data-uid="DrawnUi.Extensions.InternalExtensions.ToSKRect*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_ToSKRect_Microsoft_Maui_Graphics_Rect_System_Single_" data-uid="DrawnUi.Extensions.InternalExtensions.ToSKRect(Microsoft.Maui.Graphics.Rect,System.Single)">
  ToSKRect(Rect, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/InternalExtensions.Maui.cs/#L102"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SKRect ToSKRect(this Rect rect, float scale)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>rect</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect">Rect</a></dt>
    <dd></dd>
    <dt><code>scale</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_WithCancellation_" data-uid="DrawnUi.Extensions.InternalExtensions.WithCancellation*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_WithCancellation_System_Threading_Tasks_Task_System_Threading_CancellationToken_" data-uid="DrawnUi.Extensions.InternalExtensions.WithCancellation(System.Threading.Tasks.Task,System.Threading.CancellationToken)">
  WithCancellation(Task, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L112"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Task WithCancellation(this Task task, CancellationToken cancellationToken)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>task</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
    <dt><code>cancellationToken</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Extensions_InternalExtensions_WithCancellation_" data-uid="DrawnUi.Extensions.InternalExtensions.WithCancellation*"></a>

  <h3 id="DrawnUi_Extensions_InternalExtensions_WithCancellation__1_System_Threading_Tasks_Task___0__System_Threading_CancellationToken_" data-uid="DrawnUi.Extensions.InternalExtensions.WithCancellation``1(System.Threading.Tasks.Task{``0},System.Threading.CancellationToken)">
  WithCancellation&lt;T&gt;(Task&lt;T&gt;, CancellationToken)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L123"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Task&lt;T&gt; WithCancellation&lt;T&gt;(this Task&lt;T&gt; task, CancellationToken cancellationToken)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>task</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;T&gt;</dt>
    <dd></dd>
    <dt><code>cancellationToken</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken">CancellationToken</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;T&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Type Parameters</h4>
  <dl class="parameters">
    <dt><code>T</code></dt>
    <dd></dd>
  </dl>











</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/InternalExtensions.cs/#L5" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
