<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Namespace DrawnUi.Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Namespace DrawnUi.Controls | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Controls">

  <h1 id="DrawnUi_Controls" data-uid="DrawnUi.Controls" class="text-break">Namespace DrawnUi.Controls</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>

    <h3 id="classes">
Classes
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.AnimatedFramesRenderer.html">AnimatedFramesRenderer</a></dt>
      <dd><p>Base class for playing frames. Subclass to play spritesheets, gifs, custom animations etc.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.ContentWithBackdrop.html">ContentWithBackdrop</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.GifAnimation.html">GifAnimation</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.GridLayout.html">GridLayout</a></dt>
      <dd><p>Helper class for SkiaLayout Type = LayoutType.Grid</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.MauiEditor.html">MauiEditor</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.MauiEditorHandler.html">MauiEditorHandler</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.MauiEntry.html">MauiEntry</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.MauiEntryHandler.html">MauiEntryHandler</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.RadioButtons.html">RadioButtons</a></dt>
      <dd><p>Manages radio button groups, ensuring only one button is selected per group.
Supports grouping by parent control or by string name.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.ScrollPickerLabelContainer.html">ScrollPickerLabelContainer</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.ScrollPickerWheel.html">ScrollPickerWheel</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaCarousel.html">SkiaCarousel</a></dt>
      <dd><p>A specialized scroll control designed for creating swipeable carousels with automatic snapping to items.
Supports data binding through ItemsSource and ItemTemplate, peek effects with SidesOffset, and smooth transitions.
Ideal for image galleries, tab interfaces, and any swipeable content display.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaDecoratedGrid.html">SkiaDecoratedGrid</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaDrawer.html">SkiaDrawer</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaDrawnCell.html">SkiaDrawnCell</a></dt>
      <dd><p>Base ISkiaCell implementation</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaDynamicDrawnCell.html">SkiaDynamicDrawnCell</a></dt>
      <dd><p>This cell can watch binding context property changing</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaGif.html">SkiaGif</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaLottie.html">SkiaLottie</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.html">SkiaLottie.ColorEqualityComparer</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaMauiEditor.html">SkiaMauiEditor</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaMauiEntry.html">SkiaMauiEntry</a></dt>
      <dd><p>Used to draw maui element over a skia canvas.
Positions elelement using drawnUi layout and sometimes just renders element bitmap snapshot instead of displaying the real element, for example, when scrolling/animating.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaMediaImage.html">SkiaMediaImage</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaRadioButton.html">SkiaRadioButton</a></dt>
      <dd><p>Switch-like control, can include any content inside. It's aither you use default content (todo templates?..)
or can include any content inside, and properties will by applied by convention to a SkiaShape with Tag <code>Frame</code>, SkiaShape with Tag <code>Thumb</code>. At the same time you can override ApplyProperties() and apply them to your content yourself.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a></dt>
      <dd><p>A Canvas with Navigation capabilities</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.ModalWrapper.html">SkiaShell.ModalWrapper</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.NavigationLayer-1.html">SkiaShell.NavigationLayer&lt;T&gt;</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.PageInStack.html">SkiaShell.PageInStack</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.ParsedRoute.html">SkiaShell.ParsedRoute</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.PopupWrapper.html">SkiaShell.PopupWrapper</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.ShellCurrentRoute.html">SkiaShell.ShellCurrentRoute</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.ShellStackChild.html">SkiaShell.ShellStackChild</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.TypeRouteFactory.html">SkiaShell.TypeRouteFactory</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShellNavigatedArgs.html">SkiaShellNavigatedArgs</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShellNavigatingArgs.html">SkiaShellNavigatingArgs</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaSpinner.html">SkiaSpinner</a></dt>
      <dd><p>A wheel-of-names spinner control that displays items in a circular arrangement
and allows spinning to select an item through gesture interaction.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaSprite.html">SkiaSprite</a></dt>
      <dd><p>Renders animated sprite sheets by subclassing AnimatedFramesRenderer</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaTabsSelector.html">SkiaTabsSelector</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaTabsSelector.TabEntry.html">SkiaTabsSelector.TabEntry</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaViewSwitcher.html">SkiaViewSwitcher</a></dt>
      <dd><p>Display and hide views, eventually animating them</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html">SkiaViewSwitcher.NavigationStackEntry</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaWheelPicker.html">SkiaWheelPicker</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaWheelPickerCell.html">SkiaWheelPickerCell</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaWheelScroll.html">SkiaWheelScroll</a></dt>
      <dd><p>A specialized scroll view that displays items in a 3D wheel-like arrangement</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaWheelShape.html">SkiaWheelShape</a></dt>
      <dd><p>Custom SkiaShape that positions children in a circular arrangement around the wheel circumference.
Handles rotation and positioning calculations for the spinner wheel.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaWheelStack.html">SkiaWheelStack</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.WheelCellInfo.html">WheelCellInfo</a></dt>
      <dd></dd>
    </dl>
    <h3 id="interfaces">
Interfaces
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.ISmartNative.html">ISmartNative</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.IWheelPickerCell.html">IWheelPickerCell</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.IHandleGoBack.html">SkiaShell.IHandleGoBack</a></dt>
      <dd></dd>
    </dl>
    <h3 id="enums">
Enums
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.DrawerDirection.html">DrawerDirection</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Controls.NavigationSource.html">NavigationSource</a></dt>
      <dd></dd>
    </dl>


</article>

        <div class="contribution d-print-none">
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
