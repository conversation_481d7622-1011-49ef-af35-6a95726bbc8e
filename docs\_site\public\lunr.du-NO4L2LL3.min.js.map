{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.du.js"], "sourcesContent": ["/*!\n * Lunr languages, `Dutch` language\n * https://github.com/Mihai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    console.warn(\"[Lunr Languages] Please use the \\\"nl\\\" instead of the \\\"du\\\". The \\\"nl\\\" code is the standard code for Dutch language, and \\\"du\\\" will be removed in the next major versions.\");\n\n    /* register specific locale function */\n    lunr.du = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.du.trimmer,\n        lunr.du.stopWordFilter,\n        lunr.du.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.du.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.du.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.du.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.du.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.du.trimmer, 'trimmer-du');\n\n    /* lunr stemmer function */\n    lunr.du.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function DutchStemmer() {\n          var a_0 = [new Among(\"\", -1, 6), new Among(\"\\u00E1\", 0, 1),\n              new Among(\"\\u00E4\", 0, 1), new Among(\"\\u00E9\", 0, 2),\n              new Among(\"\\u00EB\", 0, 2), new Among(\"\\u00ED\", 0, 3),\n              new Among(\"\\u00EF\", 0, 3), new Among(\"\\u00F3\", 0, 4),\n              new Among(\"\\u00F6\", 0, 4), new Among(\"\\u00FA\", 0, 5),\n              new Among(\"\\u00FC\", 0, 5)\n            ],\n            a_1 = [new Among(\"\", -1, 3),\n              new Among(\"I\", 0, 2), new Among(\"Y\", 0, 1)\n            ],\n            a_2 = [\n              new Among(\"dd\", -1, -1), new Among(\"kk\", -1, -1),\n              new Among(\"tt\", -1, -1)\n            ],\n            a_3 = [new Among(\"ene\", -1, 2),\n              new Among(\"se\", -1, 3), new Among(\"en\", -1, 2),\n              new Among(\"heden\", 2, 1), new Among(\"s\", -1, 3)\n            ],\n            a_4 = [\n              new Among(\"end\", -1, 1), new Among(\"ig\", -1, 2),\n              new Among(\"ing\", -1, 1), new Among(\"lijk\", -1, 3),\n              new Among(\"baar\", -1, 4), new Among(\"bar\", -1, 5)\n            ],\n            a_5 = [\n              new Among(\"aa\", -1, -1), new Among(\"ee\", -1, -1),\n              new Among(\"oo\", -1, -1), new Among(\"uu\", -1, -1)\n            ],\n            g_v = [17, 65,\n              16, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128\n            ],\n            g_v_I = [1, 0, 0,\n              17, 65, 16, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128\n            ],\n            g_v_j = [\n              17, 67, 16, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128\n            ],\n            I_p2, I_p1, B_e_found, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function r_prelude() {\n            var among_var, v_1 = sbp.cursor,\n              v_2, v_3;\n            while (true) {\n              sbp.bra = sbp.cursor;\n              among_var = sbp.find_among(a_0, 11);\n              if (among_var) {\n                sbp.ket = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"a\");\n                    continue;\n                  case 2:\n                    sbp.slice_from(\"e\");\n                    continue;\n                  case 3:\n                    sbp.slice_from(\"i\");\n                    continue;\n                  case 4:\n                    sbp.slice_from(\"o\");\n                    continue;\n                  case 5:\n                    sbp.slice_from(\"u\");\n                    continue;\n                  case 6:\n                    if (sbp.cursor >= sbp.limit)\n                      break;\n                    sbp.cursor++;\n                    continue;\n                }\n              }\n              break;\n            }\n            sbp.cursor = v_1;\n            sbp.bra = v_1;\n            if (sbp.eq_s(1, \"y\")) {\n              sbp.ket = sbp.cursor;\n              sbp.slice_from(\"Y\");\n            } else\n              sbp.cursor = v_1;\n            while (true) {\n              v_2 = sbp.cursor;\n              if (sbp.in_grouping(g_v, 97, 232)) {\n                v_3 = sbp.cursor;\n                sbp.bra = v_3;\n                if (sbp.eq_s(1, \"i\")) {\n                  sbp.ket = sbp.cursor;\n                  if (sbp.in_grouping(g_v, 97, 232)) {\n                    sbp.slice_from(\"I\");\n                    sbp.cursor = v_2;\n                  }\n                } else {\n                  sbp.cursor = v_3;\n                  if (sbp.eq_s(1, \"y\")) {\n                    sbp.ket = sbp.cursor;\n                    sbp.slice_from(\"Y\");\n                    sbp.cursor = v_2;\n                  } else if (habr1(v_2))\n                    break;\n                }\n              } else if (habr1(v_2))\n                break;\n            }\n          }\n\n          function habr1(v_1) {\n            sbp.cursor = v_1;\n            if (v_1 >= sbp.limit)\n              return true;\n            sbp.cursor++;\n            return false;\n          }\n\n          function r_mark_regions() {\n            I_p1 = sbp.limit;\n            I_p2 = I_p1;\n            if (!habr2()) {\n              I_p1 = sbp.cursor;\n              if (I_p1 < 3)\n                I_p1 = 3;\n              if (!habr2())\n                I_p2 = sbp.cursor;\n            }\n          }\n\n          function habr2() {\n            while (!sbp.in_grouping(g_v, 97, 232)) {\n              if (sbp.cursor >= sbp.limit)\n                return true;\n              sbp.cursor++;\n            }\n            while (!sbp.out_grouping(g_v, 97, 232)) {\n              if (sbp.cursor >= sbp.limit)\n                return true;\n              sbp.cursor++;\n            }\n            return false;\n          }\n\n          function r_postlude() {\n            var among_var;\n            while (true) {\n              sbp.bra = sbp.cursor;\n              among_var = sbp.find_among(a_1, 3);\n              if (among_var) {\n                sbp.ket = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"y\");\n                    break;\n                  case 2:\n                    sbp.slice_from(\"i\");\n                    break;\n                  case 3:\n                    if (sbp.cursor >= sbp.limit)\n                      return;\n                    sbp.cursor++;\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_R1() {\n            return I_p1 <= sbp.cursor;\n          }\n\n          function r_R2() {\n            return I_p2 <= sbp.cursor;\n          }\n\n          function r_undouble() {\n            var v_1 = sbp.limit - sbp.cursor;\n            if (sbp.find_among_b(a_2, 3)) {\n              sbp.cursor = sbp.limit - v_1;\n              sbp.ket = sbp.cursor;\n              if (sbp.cursor > sbp.limit_backward) {\n                sbp.cursor--;\n                sbp.bra = sbp.cursor;\n                sbp.slice_del();\n              }\n            }\n          }\n\n          function r_e_ending() {\n            var v_1;\n            B_e_found = false;\n            sbp.ket = sbp.cursor;\n            if (sbp.eq_s_b(1, \"e\")) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                v_1 = sbp.limit - sbp.cursor;\n                if (sbp.out_grouping_b(g_v, 97, 232)) {\n                  sbp.cursor = sbp.limit - v_1;\n                  sbp.slice_del();\n                  B_e_found = true;\n                  r_undouble();\n                }\n              }\n            }\n          }\n\n          function r_en_ending() {\n            var v_1;\n            if (r_R1()) {\n              v_1 = sbp.limit - sbp.cursor;\n              if (sbp.out_grouping_b(g_v, 97, 232)) {\n                sbp.cursor = sbp.limit - v_1;\n                if (!sbp.eq_s_b(3, \"gem\")) {\n                  sbp.cursor = sbp.limit - v_1;\n                  sbp.slice_del();\n                  r_undouble();\n                }\n              }\n            }\n          }\n\n          function r_standard_suffix() {\n            var among_var, v_1 = sbp.limit - sbp.cursor,\n              v_2, v_3, v_4, v_5, v_6;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_3, 5);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              switch (among_var) {\n                case 1:\n                  if (r_R1())\n                    sbp.slice_from(\"heid\");\n                  break;\n                case 2:\n                  r_en_ending();\n                  break;\n                case 3:\n                  if (r_R1() && sbp.out_grouping_b(g_v_j, 97, 232))\n                    sbp.slice_del();\n                  break;\n              }\n            }\n            sbp.cursor = sbp.limit - v_1;\n            r_e_ending();\n            sbp.cursor = sbp.limit - v_1;\n            sbp.ket = sbp.cursor;\n            if (sbp.eq_s_b(4, \"heid\")) {\n              sbp.bra = sbp.cursor;\n              if (r_R2()) {\n                v_2 = sbp.limit - sbp.cursor;\n                if (!sbp.eq_s_b(1, \"c\")) {\n                  sbp.cursor = sbp.limit - v_2;\n                  sbp.slice_del();\n                  sbp.ket = sbp.cursor;\n                  if (sbp.eq_s_b(2, \"en\")) {\n                    sbp.bra = sbp.cursor;\n                    r_en_ending();\n                  }\n                }\n              }\n            }\n            sbp.cursor = sbp.limit - v_1;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_4, 6);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              switch (among_var) {\n                case 1:\n                  if (r_R2()) {\n                    sbp.slice_del();\n                    v_3 = sbp.limit - sbp.cursor;\n                    sbp.ket = sbp.cursor;\n                    if (sbp.eq_s_b(2, \"ig\")) {\n                      sbp.bra = sbp.cursor;\n                      if (r_R2()) {\n                        v_4 = sbp.limit - sbp.cursor;\n                        if (!sbp.eq_s_b(1, \"e\")) {\n                          sbp.cursor = sbp.limit - v_4;\n                          sbp.slice_del();\n                          break;\n                        }\n                      }\n                    }\n                    sbp.cursor = sbp.limit - v_3;\n                    r_undouble();\n                  }\n                  break;\n                case 2:\n                  if (r_R2()) {\n                    v_5 = sbp.limit - sbp.cursor;\n                    if (!sbp.eq_s_b(1, \"e\")) {\n                      sbp.cursor = sbp.limit - v_5;\n                      sbp.slice_del();\n                    }\n                  }\n                  break;\n                case 3:\n                  if (r_R2()) {\n                    sbp.slice_del();\n                    r_e_ending();\n                  }\n                  break;\n                case 4:\n                  if (r_R2())\n                    sbp.slice_del();\n                  break;\n                case 5:\n                  if (r_R2() && B_e_found)\n                    sbp.slice_del();\n                  break;\n              }\n            }\n            sbp.cursor = sbp.limit - v_1;\n            if (sbp.out_grouping_b(g_v_I, 73, 232)) {\n              v_6 = sbp.limit - sbp.cursor;\n              if (sbp.find_among_b(a_5, 4) && sbp.out_grouping_b(g_v, 97, 232)) {\n                sbp.cursor = sbp.limit - v_6;\n                sbp.ket = sbp.cursor;\n                if (sbp.cursor > sbp.limit_backward) {\n                  sbp.cursor--;\n                  sbp.bra = sbp.cursor;\n                  sbp.slice_del();\n                }\n              }\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_prelude();\n            sbp.cursor = v_1;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            r_standard_suffix();\n            sbp.cursor = sbp.limit_backward;\n            r_postlude();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.du.stemmer, 'stemmer-du');\n\n    lunr.du.stopWordFilter = lunr.generateStopWordFilter(' aan al alles als altijd andere ben bij daar dan dat de der deze die dit doch doen door dus een eens en er ge geen geweest haar had heb hebben heeft hem het hier hij hoe hun iemand iets ik in is ja je kan kon kunnen maar me meer men met mij mijn moet na naar niet niets nog nu of om omdat onder ons ook op over reeds te tegen toch toen tot u uit uw van veel voor want waren was wat werd wezen wie wil worden wordt zal ze zelf zich zij zijn zo zonder zou'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.du.stopWordFilter, 'stopWordFilter-du');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAG1H,QAAQ,KAAK,uKAA+K,EAG5LA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAAwB,CAC/B,IAAIC,EAAM,CAAC,IAAIH,EAAM,GAAI,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,EACrD,IAAIA,EAAM,OAAU,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,EACnD,IAAIA,EAAM,OAAU,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,EACnD,IAAIA,EAAM,OAAU,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,EACnD,IAAIA,EAAM,OAAU,EAAG,CAAC,EAAG,IAAIA,EAAM,OAAU,EAAG,CAAC,EACnD,IAAIA,EAAM,OAAU,EAAG,CAAC,CAC1B,EACAI,EAAM,CAAC,IAAIJ,EAAM,GAAI,GAAI,CAAC,EACxB,IAAIA,EAAM,IAAK,EAAG,CAAC,EAAG,IAAIA,EAAM,IAAK,EAAG,CAAC,CAC3C,EACAK,EAAM,CACJ,IAAIL,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,CACxB,EACAM,EAAM,CAAC,IAAIN,EAAM,MAAO,GAAI,CAAC,EAC3B,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC7C,IAAIA,EAAM,QAAS,EAAG,CAAC,EAAG,IAAIA,EAAM,IAAK,GAAI,CAAC,CAChD,EACAO,EAAM,CACJ,IAAIP,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,CAClD,EACAQ,EAAM,CACJ,IAAIR,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,EAC/C,IAAIA,EAAM,KAAM,GAAI,EAAE,EAAG,IAAIA,EAAM,KAAM,GAAI,EAAE,CACjD,EACAS,EAAM,CAAC,GAAI,GACT,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC7C,EACAC,EAAQ,CAAC,EAAG,EAAG,EACb,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GACrD,EACAC,EAAQ,CACN,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GACrD,EACAC,EAAMC,EAAMC,EAAWC,EAAM,IAAId,EACnC,KAAK,WAAa,SAASe,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,GAAY,CAGnB,QAFIC,EAAWC,EAAMJ,EAAI,OACvBK,EAAKC,IACM,CAGX,GAFAN,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,WAAWZ,EAAK,EAAE,EAC9Be,EAEF,OADAH,EAAI,IAAMA,EAAI,OACNG,EAAW,CACjB,IAAK,GACHH,EAAI,WAAW,GAAG,EAClB,SACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,SACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,SACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,SACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,SACF,IAAK,GACH,GAAIA,EAAI,QAAUA,EAAI,MACpB,MACFA,EAAI,SACJ,QACJ,CAEF,KACF,CAQA,IAPAA,EAAI,OAASI,EACbJ,EAAI,IAAMI,EACNJ,EAAI,KAAK,EAAG,GAAG,GACjBA,EAAI,IAAMA,EAAI,OACdA,EAAI,WAAW,GAAG,GAElBA,EAAI,OAASI,IAGb,GADAC,EAAML,EAAI,OACNA,EAAI,YAAYN,EAAK,GAAI,GAAG,GAG9B,GAFAY,EAAMN,EAAI,OACVA,EAAI,IAAMM,EACNN,EAAI,KAAK,EAAG,GAAG,EACjBA,EAAI,IAAMA,EAAI,OACVA,EAAI,YAAYN,EAAK,GAAI,GAAG,IAC9BM,EAAI,WAAW,GAAG,EAClBA,EAAI,OAASK,WAGfL,EAAI,OAASM,EACTN,EAAI,KAAK,EAAG,GAAG,EACjBA,EAAI,IAAMA,EAAI,OACdA,EAAI,WAAW,GAAG,EAClBA,EAAI,OAASK,UACJE,EAAMF,CAAG,EAClB,cAEKE,EAAMF,CAAG,EAClB,KAEN,CAEA,SAASE,EAAMH,EAAK,CAElB,OADAJ,EAAI,OAASI,EACTA,GAAOJ,EAAI,MACN,IACTA,EAAI,SACG,GACT,CAEA,SAASQ,GAAiB,CACxBV,EAAOE,EAAI,MACXH,EAAOC,EACFW,EAAM,IACTX,EAAOE,EAAI,OACPF,EAAO,IACTA,EAAO,GACJW,EAAM,IACTZ,EAAOG,EAAI,QAEjB,CAEA,SAASS,GAAQ,CACf,KAAO,CAACT,EAAI,YAAYN,EAAK,GAAI,GAAG,GAAG,CACrC,GAAIM,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,KAAO,CAACA,EAAI,aAAaN,EAAK,GAAI,GAAG,GAAG,CACtC,GAAIM,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASU,GAAa,CAEpB,QADIP,IAIF,GAFAH,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,WAAWX,EAAK,CAAC,EAC7Bc,EAEF,OADAH,EAAI,IAAMA,EAAI,OACNG,EAAW,CACjB,IAAK,GACHH,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACH,GAAIA,EAAI,QAAUA,EAAI,MACpB,OACFA,EAAI,SACJ,KACJ,CAGN,CAEA,SAASW,GAAO,CACd,OAAOb,GAAQE,EAAI,MACrB,CAEA,SAASY,GAAO,CACd,OAAOf,GAAQG,EAAI,MACrB,CAEA,SAASa,GAAa,CACpB,IAAIT,EAAMJ,EAAI,MAAQA,EAAI,OACtBA,EAAI,aAAaV,EAAK,CAAC,IACzBU,EAAI,OAASA,EAAI,MAAQI,EACzBJ,EAAI,IAAMA,EAAI,OACVA,EAAI,OAASA,EAAI,iBACnBA,EAAI,SACJA,EAAI,IAAMA,EAAI,OACdA,EAAI,UAAU,GAGpB,CAEA,SAASc,GAAa,CACpB,IAAIV,EACJL,EAAY,GACZC,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,GAAG,IACnBA,EAAI,IAAMA,EAAI,OACVW,EAAK,IACPP,EAAMJ,EAAI,MAAQA,EAAI,OAClBA,EAAI,eAAeN,EAAK,GAAI,GAAG,IACjCM,EAAI,OAASA,EAAI,MAAQI,EACzBJ,EAAI,UAAU,EACdD,EAAY,GACZc,EAAW,IAInB,CAEA,SAASE,GAAc,CACrB,IAAIX,EACAO,EAAK,IACPP,EAAMJ,EAAI,MAAQA,EAAI,OAClBA,EAAI,eAAeN,EAAK,GAAI,GAAG,IACjCM,EAAI,OAASA,EAAI,MAAQI,EACpBJ,EAAI,OAAO,EAAG,KAAK,IACtBA,EAAI,OAASA,EAAI,MAAQI,EACzBJ,EAAI,UAAU,EACda,EAAW,IAInB,CAEA,SAASG,GAAoB,CAC3B,IAAIb,EAAWC,EAAMJ,EAAI,MAAQA,EAAI,OACnCK,EAAKC,EAAKW,EAAKC,EAAKC,EAGtB,GAFAnB,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,aAAaT,EAAK,CAAC,EAC/BY,EAEF,OADAH,EAAI,IAAMA,EAAI,OACNG,EAAW,CACjB,IAAK,GACCQ,EAAK,GACPX,EAAI,WAAW,MAAM,EACvB,MACF,IAAK,GACHe,EAAY,EACZ,MACF,IAAK,GACCJ,EAAK,GAAKX,EAAI,eAAeJ,EAAO,GAAI,GAAG,GAC7CI,EAAI,UAAU,EAChB,KACJ,CAwBF,GAtBAA,EAAI,OAASA,EAAI,MAAQI,EACzBU,EAAW,EACXd,EAAI,OAASA,EAAI,MAAQI,EACzBJ,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,MAAM,IACtBA,EAAI,IAAMA,EAAI,OACVY,EAAK,IACPP,EAAML,EAAI,MAAQA,EAAI,OACjBA,EAAI,OAAO,EAAG,GAAG,IACpBA,EAAI,OAASA,EAAI,MAAQK,EACzBL,EAAI,UAAU,EACdA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACde,EAAY,MAKpBf,EAAI,OAASA,EAAI,MAAQI,EACzBJ,EAAI,IAAMA,EAAI,OACdG,EAAYH,EAAI,aAAaR,EAAK,CAAC,EAC/BW,EAEF,OADAH,EAAI,IAAMA,EAAI,OACNG,EAAW,CACjB,IAAK,GACH,GAAIS,EAAK,EAAG,CAIV,GAHAZ,EAAI,UAAU,EACdM,EAAMN,EAAI,MAAQA,EAAI,OACtBA,EAAI,IAAMA,EAAI,OACVA,EAAI,OAAO,EAAG,IAAI,IACpBA,EAAI,IAAMA,EAAI,OACVY,EAAK,IACPK,EAAMjB,EAAI,MAAQA,EAAI,OAClB,CAACA,EAAI,OAAO,EAAG,GAAG,IAAG,CACvBA,EAAI,OAASA,EAAI,MAAQiB,EACzBjB,EAAI,UAAU,EACd,KACF,CAGJA,EAAI,OAASA,EAAI,MAAQM,EACzBO,EAAW,CACb,CACA,MACF,IAAK,GACCD,EAAK,IACPM,EAAMlB,EAAI,MAAQA,EAAI,OACjBA,EAAI,OAAO,EAAG,GAAG,IACpBA,EAAI,OAASA,EAAI,MAAQkB,EACzBlB,EAAI,UAAU,IAGlB,MACF,IAAK,GACCY,EAAK,IACPZ,EAAI,UAAU,EACdc,EAAW,GAEb,MACF,IAAK,GACCF,EAAK,GACPZ,EAAI,UAAU,EAChB,MACF,IAAK,GACCY,EAAK,GAAKb,GACZC,EAAI,UAAU,EAChB,KACJ,CAEFA,EAAI,OAASA,EAAI,MAAQI,EACrBJ,EAAI,eAAeL,EAAO,GAAI,GAAG,IACnCwB,EAAMnB,EAAI,MAAQA,EAAI,OAClBA,EAAI,aAAaP,EAAK,CAAC,GAAKO,EAAI,eAAeN,EAAK,GAAI,GAAG,IAC7DM,EAAI,OAASA,EAAI,MAAQmB,EACzBnB,EAAI,IAAMA,EAAI,OACVA,EAAI,OAASA,EAAI,iBACnBA,EAAI,SACJA,EAAI,IAAMA,EAAI,OACdA,EAAI,UAAU,IAItB,CACA,KAAK,KAAO,UAAW,CACrB,IAAII,EAAMJ,EAAI,OACd,OAAAE,EAAU,EACVF,EAAI,OAASI,EACbI,EAAe,EACfR,EAAI,eAAiBI,EACrBJ,EAAI,OAASA,EAAI,MACjBgB,EAAkB,EAClBhB,EAAI,OAASA,EAAI,eACjBU,EAAW,EACJ,EACT,CACF,EAGF,OAAO,SAASU,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASnB,EAAM,CACjC,OAAAd,EAAG,WAAWc,CAAI,EAClBd,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWiC,CAAK,EACnBjC,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,wcAAwc,MAAM,GAAG,CAAC,EAEvgBA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_du", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "g_v", "g_v_I", "g_v_j", "I_p2", "I_p1", "B_e_found", "sbp", "word", "r_prelude", "among_var", "v_1", "v_2", "v_3", "habr1", "r_mark_regions", "habr2", "r_postlude", "r_R1", "r_R2", "r_undouble", "r_e_ending", "r_en_ending", "r_standard_suffix", "v_4", "v_5", "v_6", "token"]}