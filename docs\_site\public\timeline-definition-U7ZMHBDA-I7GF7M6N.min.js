import{Da as Q,P as ot,R as ct,S as ht,Z as dt,d as it,e as st,f as at,h as s,i as lt,ia as V,j as S}from"./chunk-U3SD26FK.min.js";import"./chunk-OSRY5VT3.min.js";var X=function(){var n=s(function(m,i,a,c){for(a=a||{},c=m.length;c--;a[m[c]]=i);return a},"o"),t=[6,8,10,11,12,14,16,17,20,21],e=[1,9],l=[1,10],r=[1,11],d=[1,12],h=[1,13],g=[1,16],f=[1,17],u={trace:s(function(){},"trace"),yy:{},symbols_:{error:2,start:3,timeline:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,title:11,acc_title:12,acc_title_value:13,acc_descr:14,acc_descr_value:15,acc_descr_multiline_value:16,section:17,period_statement:18,event_statement:19,period:20,event:21,$accept:0,$end:1},terminals_:{2:"error",4:"timeline",6:"EOF",8:"SPACE",10:"NEWLINE",11:"title",12:"acc_title",13:"acc_title_value",14:"acc_descr",15:"acc_descr_value",16:"acc_descr_multiline_value",17:"section",20:"period",21:"event"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[18,1],[19,1]],performAction:s(function(i,a,c,p,y,o,E){var b=o.length-1;switch(y){case 1:return o[b-1];case 2:this.$=[];break;case 3:o[b-1].push(o[b]),this.$=o[b-1];break;case 4:case 5:this.$=o[b];break;case 6:case 7:this.$=[];break;case 8:p.getCommonDb().setDiagramTitle(o[b].substr(6)),this.$=o[b].substr(6);break;case 9:this.$=o[b].trim(),p.getCommonDb().setAccTitle(this.$);break;case 10:case 11:this.$=o[b].trim(),p.getCommonDb().setAccDescription(this.$);break;case 12:p.addSection(o[b].substr(8)),this.$=o[b].substr(8);break;case 15:p.addTask(o[b],0,""),this.$=o[b];break;case 16:p.addEvent(o[b].substr(2)),this.$=o[b];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},n(t,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:e,12:l,14:r,16:d,17:h,18:14,19:15,20:g,21:f},n(t,[2,7],{1:[2,1]}),n(t,[2,3]),{9:18,11:e,12:l,14:r,16:d,17:h,18:14,19:15,20:g,21:f},n(t,[2,5]),n(t,[2,6]),n(t,[2,8]),{13:[1,19]},{15:[1,20]},n(t,[2,11]),n(t,[2,12]),n(t,[2,13]),n(t,[2,14]),n(t,[2,15]),n(t,[2,16]),n(t,[2,4]),n(t,[2,9]),n(t,[2,10])],defaultActions:{},parseError:s(function(i,a){if(a.recoverable)this.trace(i);else{var c=new Error(i);throw c.hash=a,c}},"parseError"),parse:s(function(i){var a=this,c=[0],p=[],y=[null],o=[],E=this.table,b="",L=0,A=0,F=0,et=2,M=1,_=o.slice.call(arguments,1),k=Object.create(this.lexer),T={yy:{}};for(var P in this.yy)Object.prototype.hasOwnProperty.call(this.yy,P)&&(T.yy[P]=this.yy[P]);k.setInput(i,T.yy),T.yy.lexer=k,T.yy.parser=this,typeof k.yylloc>"u"&&(k.yylloc={});var C=k.yylloc;o.push(C);var U=k.options&&k.options.ranges;typeof T.yy.parseError=="function"?this.parseError=T.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function It(I){c.length=c.length-2*I,y.length=y.length-I,o.length=o.length-I}s(It,"popStack");function nt(){var I;return I=p.pop()||k.lex()||M,typeof I!="number"&&(I instanceof Array&&(p=I,I=p.pop()),I=a.symbols_[I]||I),I}s(nt,"lex");for(var w,Z,H,N,Jt,J,z={},O,$,rt,j;;){if(H=c[c.length-1],this.defaultActions[H]?N=this.defaultActions[H]:((w===null||typeof w>"u")&&(w=nt()),N=E[H]&&E[H][w]),typeof N>"u"||!N.length||!N[0]){var K="";j=[];for(O in E[H])this.terminals_[O]&&O>et&&j.push("'"+this.terminals_[O]+"'");k.showPosition?K="Parse error on line "+(L+1)+`:
`+k.showPosition()+`
Expecting `+j.join(", ")+", got '"+(this.terminals_[w]||w)+"'":K="Parse error on line "+(L+1)+": Unexpected "+(w==M?"end of input":"'"+(this.terminals_[w]||w)+"'"),this.parseError(K,{text:k.match,token:this.terminals_[w]||w,line:k.yylineno,loc:C,expected:j})}if(N[0]instanceof Array&&N.length>1)throw new Error("Parse Error: multiple actions possible at state: "+H+", token: "+w);switch(N[0]){case 1:c.push(w),y.push(k.yytext),o.push(k.yylloc),c.push(N[1]),w=null,Z?(w=Z,Z=null):(A=k.yyleng,b=k.yytext,L=k.yylineno,C=k.yylloc,F>0&&F--);break;case 2:if($=this.productions_[N[1]][1],z.$=y[y.length-$],z._$={first_line:o[o.length-($||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-($||1)].first_column,last_column:o[o.length-1].last_column},U&&(z._$.range=[o[o.length-($||1)].range[0],o[o.length-1].range[1]]),J=this.performAction.apply(z,[b,A,L,T.yy,N[1],y,o].concat(_)),typeof J<"u")return J;$&&(c=c.slice(0,-1*$*2),y=y.slice(0,-1*$),o=o.slice(0,-1*$)),c.push(this.productions_[N[1]][0]),y.push(z.$),o.push(z._$),rt=E[c[c.length-2]][c[c.length-1]],c.push(rt);break;case 3:return!0}}return!0},"parse")},x=function(){var m={EOF:1,parseError:s(function(a,c){if(this.yy.parser)this.yy.parser.parseError(a,c);else throw new Error(a)},"parseError"),setInput:s(function(i,a){return this.yy=a||this.yy||{},this._input=i,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:s(function(){var i=this._input[0];this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i;var a=i.match(/(?:\r\n?|\n).*/g);return a?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},"input"),unput:s(function(i){var a=i.length,c=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-a),this.offset-=a;var p=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var y=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===p.length?this.yylloc.first_column:0)+p[p.length-c.length].length-c[0].length:this.yylloc.first_column-a},this.options.ranges&&(this.yylloc.range=[y[0],y[0]+this.yyleng-a]),this.yyleng=this.yytext.length,this},"unput"),more:s(function(){return this._more=!0,this},"more"),reject:s(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:s(function(i){this.unput(this.match.slice(i))},"less"),pastInput:s(function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:s(function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:s(function(){var i=this.pastInput(),a=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+a+"^"},"showPosition"),test_match:s(function(i,a){var c,p,y;if(this.options.backtrack_lexer&&(y={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(y.yylloc.range=this.yylloc.range.slice(0))),p=i[0].match(/(?:\r\n?|\n).*/g),p&&(this.yylineno+=p.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:p?p[p.length-1].length-p[p.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+i[0].length},this.yytext+=i[0],this.match+=i[0],this.matches=i,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(i[0].length),this.matched+=i[0],c=this.performAction.call(this,this.yy,this,a,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var o in y)this[o]=y[o];return!1}return!1},"test_match"),next:s(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var i,a,c,p;this._more||(this.yytext="",this.match="");for(var y=this._currentRules(),o=0;o<y.length;o++)if(c=this._input.match(this.rules[y[o]]),c&&(!a||c[0].length>a[0].length)){if(a=c,p=o,this.options.backtrack_lexer){if(i=this.test_match(c,y[o]),i!==!1)return i;if(this._backtrack){a=!1;continue}else return!1}else if(!this.options.flex)break}return a?(i=this.test_match(a,y[p]),i!==!1?i:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:s(function(){var a=this.next();return a||this.lex()},"lex"),begin:s(function(a){this.conditionStack.push(a)},"begin"),popState:s(function(){var a=this.conditionStack.length-1;return a>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:s(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:s(function(a){return a=this.conditionStack.length-1-Math.abs(a||0),a>=0?this.conditionStack[a]:"INITIAL"},"topState"),pushState:s(function(a){this.begin(a)},"pushState"),stateStackSize:s(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:s(function(a,c,p,y){var o=y;switch(p){case 0:break;case 1:break;case 2:return 10;case 3:break;case 4:break;case 5:return 4;case 6:return 11;case 7:return this.begin("acc_title"),12;break;case 8:return this.popState(),"acc_title_value";break;case 9:return this.begin("acc_descr"),14;break;case 10:return this.popState(),"acc_descr_value";break;case 11:this.begin("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 17;case 15:return 21;case 16:return 20;case 17:return 6;case 18:return"INVALID"}},"anonymous"),rules:[/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:timeline\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:section\s[^:\n]+)/i,/^(?::\s[^:\n]+)/i,/^(?:[^#:\n]+)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18],inclusive:!0}}};return m}();u.lexer=x;function v(){this.yy={}}return s(v,"Parser"),v.prototype=u,u.Parser=v,new v}();X.parser=X;var Nt=X,gt={};lt(gt,{addEvent:()=>wt,addSection:()=>kt,addTask:()=>_t,addTaskOrg:()=>St,clear:()=>xt,default:()=>Lt,getCommonDb:()=>mt,getSections:()=>bt,getTasks:()=>vt});var W="",ft=0,Y=[],G=[],B=[],mt=s(()=>ct,"getCommonDb"),xt=s(function(){Y.length=0,G.length=0,W="",B.length=0,ht()},"clear"),kt=s(function(n){W=n,Y.push(n)},"addSection"),bt=s(function(){return Y},"getSections"),vt=s(function(){let n=ut(),t=100,e=0;for(;!n&&e<t;)n=ut(),e++;return G.push(...B),G},"getTasks"),_t=s(function(n,t,e){let l={id:ft++,section:W,type:W,task:n,score:t||0,events:e?[e]:[]};B.push(l)},"addTask"),wt=s(function(n){B.find(e=>e.id===ft-1).events.push(n)},"addEvent"),St=s(function(n){let t={section:W,type:W,description:n,task:n,classes:[]};G.push(t)},"addTaskOrg"),ut=s(function(){let n=s(function(e){return B[e].processed},"compileTask"),t=!0;for(let[e,l]of B.entries())n(e),t=t&&l.processed;return t},"compileTasks"),Lt={clear:xt,getCommonDb:mt,addSection:kt,getSections:bt,getTasks:vt,addTask:_t,addTaskOrg:St,addEvent:wt},Mt=12,q=s(function(n,t){let e=n.append("rect");return e.attr("x",t.x),e.attr("y",t.y),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("width",t.width),e.attr("height",t.height),e.attr("rx",t.rx),e.attr("ry",t.ry),t.class!==void 0&&e.attr("class",t.class),e},"drawRect"),$t=s(function(n,t){let l=n.append("circle").attr("cx",t.cx).attr("cy",t.cy).attr("class","face").attr("r",15).attr("stroke-width",2).attr("overflow","visible"),r=n.append("g");r.append("circle").attr("cx",t.cx-15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666"),r.append("circle").attr("cx",t.cx+15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666");function d(f){let u=Q().startAngle(Math.PI/2).endAngle(3*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);f.append("path").attr("class","mouth").attr("d",u).attr("transform","translate("+t.cx+","+(t.cy+2)+")")}s(d,"smile");function h(f){let u=Q().startAngle(3*Math.PI/2).endAngle(5*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);f.append("path").attr("class","mouth").attr("d",u).attr("transform","translate("+t.cx+","+(t.cy+7)+")")}s(h,"sad");function g(f){f.append("line").attr("class","mouth").attr("stroke",2).attr("x1",t.cx-5).attr("y1",t.cy+7).attr("x2",t.cx+5).attr("y2",t.cy+7).attr("class","mouth").attr("stroke-width","1px").attr("stroke","#666")}return s(g,"ambivalent"),t.score>3?d(r):t.score<3?h(r):g(r),l},"drawFace"),Pt=s(function(n,t){let e=n.append("circle");return e.attr("cx",t.cx),e.attr("cy",t.cy),e.attr("class","actor-"+t.pos),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("r",t.r),e.class!==void 0&&e.attr("class",e.class),t.title!==void 0&&e.append("title").text(t.title),e},"drawCircle"),Et=s(function(n,t){let e=t.text.replace(/<br\s*\/?>/gi," "),l=n.append("text");l.attr("x",t.x),l.attr("y",t.y),l.attr("class","legend"),l.style("text-anchor",t.anchor),t.class!==void 0&&l.attr("class",t.class);let r=l.append("tspan");return r.attr("x",t.x+t.textMargin*2),r.text(e),l},"drawText"),At=s(function(n,t){function e(r,d,h,g,f){return r+","+d+" "+(r+h)+","+d+" "+(r+h)+","+(d+g-f)+" "+(r+h-f*1.2)+","+(d+g)+" "+r+","+(d+g)}s(e,"genPoints");let l=n.append("polygon");l.attr("points",e(t.x,t.y,50,20,7)),l.attr("class","labelBox"),t.y=t.y+t.labelMargin,t.x=t.x+.5*t.labelMargin,Et(n,t)},"drawLabel"),Ct=s(function(n,t,e){let l=n.append("g"),r=D();r.x=t.x,r.y=t.y,r.fill=t.fill,r.width=e.width,r.height=e.height,r.class="journey-section section-type-"+t.num,r.rx=3,r.ry=3,q(l,r),Tt(e)(t.text,l,r.x,r.y,r.width,r.height,{class:"journey-section section-type-"+t.num},e,t.colour)},"drawSection"),pt=-1,Ht=s(function(n,t,e){let l=t.x+e.width/2,r=n.append("g");pt++;let d=300+5*30;r.append("line").attr("id","task"+pt).attr("x1",l).attr("y1",t.y).attr("x2",l).attr("y2",d).attr("class","task-line").attr("stroke-width","1px").attr("stroke-dasharray","4 2").attr("stroke","#666"),$t(r,{cx:l,cy:300+(5-t.score)*30,score:t.score});let h=D();h.x=t.x,h.y=t.y,h.fill=t.fill,h.width=e.width,h.height=e.height,h.class="task task-type-"+t.num,h.rx=3,h.ry=3,q(r,h),Tt(e)(t.task,r,h.x,h.y,h.width,h.height,{class:"task"},e,t.colour)},"drawTask"),Rt=s(function(n,t){q(n,{x:t.startx,y:t.starty,width:t.stopx-t.startx,height:t.stopy-t.starty,fill:t.fill,class:"rect"}).lower()},"drawBackgroundRect"),Ft=s(function(){return{x:0,y:0,fill:void 0,"text-anchor":"start",width:100,height:100,textMargin:0,rx:0,ry:0}},"getTextObj"),D=s(function(){return{x:0,y:0,width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),Tt=function(){function n(r,d,h,g,f,u,x,v){let m=d.append("text").attr("x",h+f/2).attr("y",g+u/2+5).style("font-color",v).style("text-anchor","middle").text(r);l(m,x)}s(n,"byText");function t(r,d,h,g,f,u,x,v,m){let{taskFontSize:i,taskFontFamily:a}=v,c=r.split(/<br\s*\/?>/gi);for(let p=0;p<c.length;p++){let y=p*i-i*(c.length-1)/2,o=d.append("text").attr("x",h+f/2).attr("y",g).attr("fill",m).style("text-anchor","middle").style("font-size",i).style("font-family",a);o.append("tspan").attr("x",h+f/2).attr("dy",y).text(c[p]),o.attr("y",g+u/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),l(o,x)}}s(t,"byTspan");function e(r,d,h,g,f,u,x,v){let m=d.append("switch"),a=m.append("foreignObject").attr("x",h).attr("y",g).attr("width",f).attr("height",u).attr("position","fixed").append("xhtml:div").style("display","table").style("height","100%").style("width","100%");a.append("div").attr("class","label").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(r),t(r,m,h,g,f,u,x,v),l(a,x)}s(e,"byFo");function l(r,d){for(let h in d)h in d&&r.attr(h,d[h])}return s(l,"_setTextAttrs"),function(r){return r.textPlacement==="fo"?e:r.textPlacement==="old"?n:t}}(),zt=s(function(n){n.append("defs").append("marker").attr("id","arrowhead").attr("refX",5).attr("refY",2).attr("markerWidth",6).attr("markerHeight",4).attr("orient","auto").append("path").attr("d","M 0,0 V 4 L6,2 Z")},"initGraphics");function tt(n,t){n.each(function(){var e=V(this),l=e.text().split(/(\s+|<br>)/).reverse(),r,d=[],h=1.1,g=e.attr("y"),f=parseFloat(e.attr("dy")),u=e.text(null).append("tspan").attr("x",0).attr("y",g).attr("dy",f+"em");for(let x=0;x<l.length;x++)r=l[l.length-1-x],d.push(r),u.text(d.join(" ").trim()),(u.node().getComputedTextLength()>t||r==="<br>")&&(d.pop(),u.text(d.join(" ").trim()),r==="<br>"?d=[""]:d=[r],u=e.append("tspan").attr("x",0).attr("y",g).attr("dy",h+"em").text(r))})}s(tt,"wrap");var Vt=s(function(n,t,e,l){let r=e%Mt-1,d=n.append("g");t.section=r,d.attr("class",(t.class?t.class+" ":"")+"timeline-node "+("section-"+r));let h=d.append("g"),g=d.append("g"),u=g.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(tt,t.width).node().getBBox(),x=l.fontSize?.replace?l.fontSize.replace("px",""):l.fontSize;return t.height=u.height+x*1.1*.5+t.padding,t.height=Math.max(t.height,t.maxHeight),t.width=t.width+2*t.padding,g.attr("transform","translate("+t.width/2+", "+t.padding/2+")"),Bt(h,t,r,l),t},"drawNode"),Wt=s(function(n,t,e){let l=n.append("g"),d=l.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(tt,t.width).node().getBBox(),h=e.fontSize?.replace?e.fontSize.replace("px",""):e.fontSize;return l.remove(),d.height+h*1.1*.5+t.padding},"getVirtualNodeHeight"),Bt=s(function(n,t,e){n.append("path").attr("id","node-"+t.id).attr("class","node-bkg node-"+t.type).attr("d",`M0 ${t.height-5} v${-t.height+2*5} q0,-5 5,-5 h${t.width-2*5} q5,0 5,5 v${t.height-5} H0 Z`),n.append("line").attr("class","node-line-"+e).attr("x1",0).attr("y1",t.height).attr("x2",t.width).attr("y2",t.height)},"defaultBkg"),R={drawRect:q,drawCircle:Pt,drawSection:Ct,drawText:Et,drawLabel:At,drawTask:Ht,drawBackgroundRect:Rt,getTextObj:Ft,getNoteRect:D,initGraphics:zt,drawNode:Vt,getVirtualNodeHeight:Wt},Ot=s(function(n,t,e,l){let r=dt(),d=r.leftMargin??50;S.debug("timeline",l.db);let h=r.securityLevel,g;h==="sandbox"&&(g=V("#i"+t));let u=(h==="sandbox"?V(g.nodes()[0].contentDocument.body):V("body")).select("#"+t);u.append("g");let x=l.db.getTasks(),v=l.db.getCommonDb().getDiagramTitle();S.debug("task",x),R.initGraphics(u);let m=l.db.getSections();S.debug("sections",m);let i=0,a=0,c=0,p=0,y=50+d,o=50;p=50;let E=0,b=!0;m.forEach(function(M){let _={number:E,descr:M,section:E,width:150,padding:20,maxHeight:i},k=R.getVirtualNodeHeight(u,_,r);S.debug("sectionHeight before draw",k),i=Math.max(i,k+20)});let L=0,A=0;S.debug("tasks.length",x.length);for(let[M,_]of x.entries()){let k={number:M,descr:_,section:_.section,width:150,padding:20,maxHeight:a},T=R.getVirtualNodeHeight(u,k,r);S.debug("taskHeight before draw",T),a=Math.max(a,T+20),L=Math.max(L,_.events.length);let P=0;for(let C of _.events){let U={descr:C,section:_.section,number:_.section,width:150,padding:20,maxHeight:50};P+=R.getVirtualNodeHeight(u,U,r)}A=Math.max(A,P)}S.debug("maxSectionHeight before draw",i),S.debug("maxTaskHeight before draw",a),m&&m.length>0?m.forEach(M=>{let _=x.filter(C=>C.section===M),k={number:E,descr:M,section:E,width:200*Math.max(_.length,1)-50,padding:20,maxHeight:i};S.debug("sectionNode",k);let T=u.append("g"),P=R.drawNode(T,k,E,r);S.debug("sectionNode output",P),T.attr("transform",`translate(${y}, ${p})`),o+=i+50,_.length>0&&yt(u,_,E,y,o,a,r,L,A,i,!1),y+=200*Math.max(_.length,1),o=p,E++}):(b=!1,yt(u,x,E,y,o,a,r,L,A,i,!0));let F=u.node().getBBox();S.debug("bounds",F),v&&u.append("text").text(v).attr("x",F.width/2-d).attr("font-size","4ex").attr("font-weight","bold").attr("y",20),c=b?i+a+150:a+100,u.append("g").attr("class","lineWrapper").append("line").attr("x1",d).attr("y1",c).attr("x2",F.width+3*d).attr("y2",c).attr("stroke-width",4).attr("stroke","black").attr("marker-end","url(#arrowhead)"),ot(void 0,u,r.timeline?.padding??50,r.timeline?.useMaxWidth??!1)},"draw"),yt=s(function(n,t,e,l,r,d,h,g,f,u,x){for(let v of t){let m={descr:v.task,section:e,number:e,width:150,padding:20,maxHeight:d};S.debug("taskNode",m);let i=n.append("g").attr("class","taskWrapper"),c=R.drawNode(i,m,e,h).height;if(S.debug("taskHeight after draw",c),i.attr("transform",`translate(${l}, ${r})`),d=Math.max(d,c),v.events){let p=n.append("g").attr("class","lineWrapper"),y=d;r+=100,y=y+jt(n,v.events,e,l,r,h),r-=100,p.append("line").attr("x1",l+190/2).attr("y1",r+d).attr("x2",l+190/2).attr("y2",r+d+(x?d:u)+f+120).attr("stroke-width",2).attr("stroke","black").attr("marker-end","url(#arrowhead)").attr("stroke-dasharray","5,5")}l=l+200,x&&!h.timeline?.disableMulticolor&&e++}r=r-10},"drawTasks"),jt=s(function(n,t,e,l,r,d){let h=0,g=r;r=r+100;for(let f of t){let u={descr:f,section:e,number:e,width:150,padding:20,maxHeight:50};S.debug("eventNode",u);let x=n.append("g").attr("class","eventWrapper"),m=R.drawNode(x,u,e,d).height;h=h+m,x.attr("transform",`translate(${l}, ${r})`),r=r+10+m}return r=g,h},"drawEvents"),Gt={setConf:s(()=>{},"setConf"),draw:Ot},qt=s(n=>{let t="";for(let e=0;e<n.THEME_COLOR_LIMIT;e++)n["lineColor"+e]=n["lineColor"+e]||n["cScaleInv"+e],it(n["lineColor"+e])?n["lineColor"+e]=st(n["lineColor"+e],20):n["lineColor"+e]=at(n["lineColor"+e],20);for(let e=0;e<n.THEME_COLOR_LIMIT;e++){let l=""+(17-3*e);t+=`
    .section-${e-1} rect, .section-${e-1} path, .section-${e-1} circle, .section-${e-1} path  {
      fill: ${n["cScale"+e]};
    }
    .section-${e-1} text {
     fill: ${n["cScaleLabel"+e]};
    }
    .node-icon-${e-1} {
      font-size: 40px;
      color: ${n["cScaleLabel"+e]};
    }
    .section-edge-${e-1}{
      stroke: ${n["cScale"+e]};
    }
    .edge-depth-${e-1}{
      stroke-width: ${l};
    }
    .section-${e-1} line {
      stroke: ${n["cScaleInv"+e]} ;
      stroke-width: 3;
    }

    .lineWrapper line{
      stroke: ${n["cScaleLabel"+e]} ;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return t},"genSections"),Ut=s(n=>`
  .edge {
    stroke-width: 3;
  }
  ${qt(n)}
  .section-root rect, .section-root path, .section-root circle  {
    fill: ${n.git0};
  }
  .section-root text {
    fill: ${n.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .eventWrapper  {
   filter: brightness(120%);
  }
`,"getStyles"),Zt=Ut,Dt={db:gt,renderer:Gt,parser:Nt,styles:Zt};export{Dt as diagram};
//# sourceMappingURL=timeline-definition-U7ZMHBDA-I7GF7M6N.min.js.map
