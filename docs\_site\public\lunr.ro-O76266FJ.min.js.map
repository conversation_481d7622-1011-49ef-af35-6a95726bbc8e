{"version": 3, "sources": ["../../node_modules/lunr-languages/lunr.ro.js"], "sourcesContent": ["/*!\n * Lunr languages, `Romanian` language\n * https://github.com/<PERSON>hai<PERSON>alentin/lunr-languages\n *\n * Copyright 2014, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n/*!\n * based on\n * Snowball JavaScript Library v0.3\n * http://code.google.com/p/urim/\n * http://snowball.tartarus.org/\n *\n * Copyright 2010, <PERSON><PERSON>\n * http://www.mozilla.org/MPL/\n */\n\n/**\n * export the module via AMD, CommonJS or as a browser global\n * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n */\n;\n(function(root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory)\n  } else if (typeof exports === 'object') {\n    /**\n     * Node. Does not work with strict CommonJS, but\n     * only CommonJS-like environments that support module.exports,\n     * like Node.\n     */\n    module.exports = factory()\n  } else {\n    // Browser globals (root is window)\n    factory()(root.lunr);\n  }\n}(this, function() {\n  /**\n   * Just return a value to define the module export.\n   * This example returns an object, but the module\n   * can return a function as the exported value.\n   */\n  return function(lunr) {\n    /* throw error if lunr is not yet included */\n    if ('undefined' === typeof lunr) {\n      throw new Error('Lunr is not present. Please include / require Lunr before this script.');\n    }\n\n    /* throw error if lunr stemmer support is not yet included */\n    if ('undefined' === typeof lunr.stemmerSupport) {\n      throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');\n    }\n\n    /* register specific locale function */\n    lunr.ro = function() {\n      this.pipeline.reset();\n      this.pipeline.add(\n        lunr.ro.trimmer,\n        lunr.ro.stopWordFilter,\n        lunr.ro.stemmer\n      );\n\n      // for lunr version 2\n      // this is necessary so that every searched word is also stemmed before\n      // in lunr <= 1 this is not needed, as it is done using the normal pipeline\n      if (this.searchPipeline) {\n        this.searchPipeline.reset();\n        this.searchPipeline.add(lunr.ro.stemmer)\n      }\n    };\n\n    /* lunr trimmer function */\n    lunr.ro.wordCharacters = \"A-Za-z\\xAA\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02B8\\u02E0-\\u02E4\\u1D00-\\u1D25\\u1D2C-\\u1D5C\\u1D62-\\u1D65\\u1D6B-\\u1D77\\u1D79-\\u1DBE\\u1E00-\\u1EFF\\u2071\\u207F\\u2090-\\u209C\\u212A\\u212B\\u2132\\u214E\\u2160-\\u2188\\u2C60-\\u2C7F\\uA722-\\uA787\\uA78B-\\uA7AD\\uA7B0-\\uA7B7\\uA7F7-\\uA7FF\\uAB30-\\uAB5A\\uAB5C-\\uAB64\\uFB00-\\uFB06\\uFF21-\\uFF3A\\uFF41-\\uFF5A\";\n    lunr.ro.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.ro.wordCharacters);\n\n    lunr.Pipeline.registerFunction(lunr.ro.trimmer, 'trimmer-ro');\n\n    /* lunr stemmer function */\n    lunr.ro.stemmer = (function() {\n      /* create the wrapped stemmer object */\n      var Among = lunr.stemmerSupport.Among,\n        SnowballProgram = lunr.stemmerSupport.SnowballProgram,\n        st = new function RomanianStemmer() {\n          var a_0 = [new Among(\"\", -1, 3), new Among(\"I\", 0, 1), new Among(\"U\", 0, 2)],\n            a_1 = [\n              new Among(\"ea\", -1, 3), new Among(\"a\\u0163ia\", -1, 7),\n              new Among(\"aua\", -1, 2), new Among(\"iua\", -1, 4),\n              new Among(\"a\\u0163ie\", -1, 7), new Among(\"ele\", -1, 3),\n              new Among(\"ile\", -1, 5), new Among(\"iile\", 6, 4),\n              new Among(\"iei\", -1, 4), new Among(\"atei\", -1, 6),\n              new Among(\"ii\", -1, 4), new Among(\"ului\", -1, 1),\n              new Among(\"ul\", -1, 1), new Among(\"elor\", -1, 3),\n              new Among(\"ilor\", -1, 4), new Among(\"iilor\", 14, 4)\n            ],\n            a_2 = [\n              new Among(\"icala\", -1, 4), new Among(\"iciva\", -1, 4),\n              new Among(\"ativa\", -1, 5), new Among(\"itiva\", -1, 6),\n              new Among(\"icale\", -1, 4), new Among(\"a\\u0163iune\", -1, 5),\n              new Among(\"i\\u0163iune\", -1, 6), new Among(\"atoare\", -1, 5),\n              new Among(\"itoare\", -1, 6), new Among(\"\\u0103toare\", -1, 5),\n              new Among(\"icitate\", -1, 4), new Among(\"abilitate\", -1, 1),\n              new Among(\"ibilitate\", -1, 2), new Among(\"ivitate\", -1, 3),\n              new Among(\"icive\", -1, 4), new Among(\"ative\", -1, 5),\n              new Among(\"itive\", -1, 6), new Among(\"icali\", -1, 4),\n              new Among(\"atori\", -1, 5), new Among(\"icatori\", 18, 4),\n              new Among(\"itori\", -1, 6), new Among(\"\\u0103tori\", -1, 5),\n              new Among(\"icitati\", -1, 4), new Among(\"abilitati\", -1, 1),\n              new Among(\"ivitati\", -1, 3), new Among(\"icivi\", -1, 4),\n              new Among(\"ativi\", -1, 5), new Among(\"itivi\", -1, 6),\n              new Among(\"icit\\u0103i\", -1, 4), new Among(\"abilit\\u0103i\", -1, 1),\n              new Among(\"ivit\\u0103i\", -1, 3),\n              new Among(\"icit\\u0103\\u0163i\", -1, 4),\n              new Among(\"abilit\\u0103\\u0163i\", -1, 1),\n              new Among(\"ivit\\u0103\\u0163i\", -1, 3), new Among(\"ical\", -1, 4),\n              new Among(\"ator\", -1, 5), new Among(\"icator\", 35, 4),\n              new Among(\"itor\", -1, 6), new Among(\"\\u0103tor\", -1, 5),\n              new Among(\"iciv\", -1, 4), new Among(\"ativ\", -1, 5),\n              new Among(\"itiv\", -1, 6), new Among(\"ical\\u0103\", -1, 4),\n              new Among(\"iciv\\u0103\", -1, 4), new Among(\"ativ\\u0103\", -1, 5),\n              new Among(\"itiv\\u0103\", -1, 6)\n            ],\n            a_3 = [new Among(\"ica\", -1, 1),\n              new Among(\"abila\", -1, 1), new Among(\"ibila\", -1, 1),\n              new Among(\"oasa\", -1, 1), new Among(\"ata\", -1, 1),\n              new Among(\"ita\", -1, 1), new Among(\"anta\", -1, 1),\n              new Among(\"ista\", -1, 3), new Among(\"uta\", -1, 1),\n              new Among(\"iva\", -1, 1), new Among(\"ic\", -1, 1),\n              new Among(\"ice\", -1, 1), new Among(\"abile\", -1, 1),\n              new Among(\"ibile\", -1, 1), new Among(\"isme\", -1, 3),\n              new Among(\"iune\", -1, 2), new Among(\"oase\", -1, 1),\n              new Among(\"ate\", -1, 1), new Among(\"itate\", 17, 1),\n              new Among(\"ite\", -1, 1), new Among(\"ante\", -1, 1),\n              new Among(\"iste\", -1, 3), new Among(\"ute\", -1, 1),\n              new Among(\"ive\", -1, 1), new Among(\"ici\", -1, 1),\n              new Among(\"abili\", -1, 1), new Among(\"ibili\", -1, 1),\n              new Among(\"iuni\", -1, 2), new Among(\"atori\", -1, 1),\n              new Among(\"osi\", -1, 1), new Among(\"ati\", -1, 1),\n              new Among(\"itati\", 30, 1), new Among(\"iti\", -1, 1),\n              new Among(\"anti\", -1, 1), new Among(\"isti\", -1, 3),\n              new Among(\"uti\", -1, 1), new Among(\"i\\u015Fti\", -1, 3),\n              new Among(\"ivi\", -1, 1), new Among(\"it\\u0103i\", -1, 1),\n              new Among(\"o\\u015Fi\", -1, 1), new Among(\"it\\u0103\\u0163i\", -1, 1),\n              new Among(\"abil\", -1, 1), new Among(\"ibil\", -1, 1),\n              new Among(\"ism\", -1, 3), new Among(\"ator\", -1, 1),\n              new Among(\"os\", -1, 1), new Among(\"at\", -1, 1),\n              new Among(\"it\", -1, 1), new Among(\"ant\", -1, 1),\n              new Among(\"ist\", -1, 3), new Among(\"ut\", -1, 1),\n              new Among(\"iv\", -1, 1), new Among(\"ic\\u0103\", -1, 1),\n              new Among(\"abil\\u0103\", -1, 1), new Among(\"ibil\\u0103\", -1, 1),\n              new Among(\"oas\\u0103\", -1, 1), new Among(\"at\\u0103\", -1, 1),\n              new Among(\"it\\u0103\", -1, 1), new Among(\"ant\\u0103\", -1, 1),\n              new Among(\"ist\\u0103\", -1, 3), new Among(\"ut\\u0103\", -1, 1),\n              new Among(\"iv\\u0103\", -1, 1)\n            ],\n            a_4 = [new Among(\"ea\", -1, 1),\n              new Among(\"ia\", -1, 1), new Among(\"esc\", -1, 1),\n              new Among(\"\\u0103sc\", -1, 1), new Among(\"ind\", -1, 1),\n              new Among(\"\\u00E2nd\", -1, 1), new Among(\"are\", -1, 1),\n              new Among(\"ere\", -1, 1), new Among(\"ire\", -1, 1),\n              new Among(\"\\u00E2re\", -1, 1), new Among(\"se\", -1, 2),\n              new Among(\"ase\", 10, 1), new Among(\"sese\", 10, 2),\n              new Among(\"ise\", 10, 1), new Among(\"use\", 10, 1),\n              new Among(\"\\u00E2se\", 10, 1), new Among(\"e\\u015Fte\", -1, 1),\n              new Among(\"\\u0103\\u015Fte\", -1, 1), new Among(\"eze\", -1, 1),\n              new Among(\"ai\", -1, 1), new Among(\"eai\", 19, 1),\n              new Among(\"iai\", 19, 1), new Among(\"sei\", -1, 2),\n              new Among(\"e\\u015Fti\", -1, 1), new Among(\"\\u0103\\u015Fti\", -1, 1),\n              new Among(\"ui\", -1, 1), new Among(\"ezi\", -1, 1),\n              new Among(\"\\u00E2i\", -1, 1), new Among(\"a\\u015Fi\", -1, 1),\n              new Among(\"se\\u015Fi\", -1, 2), new Among(\"ase\\u015Fi\", 29, 1),\n              new Among(\"sese\\u015Fi\", 29, 2), new Among(\"ise\\u015Fi\", 29, 1),\n              new Among(\"use\\u015Fi\", 29, 1),\n              new Among(\"\\u00E2se\\u015Fi\", 29, 1), new Among(\"i\\u015Fi\", -1, 1),\n              new Among(\"u\\u015Fi\", -1, 1), new Among(\"\\u00E2\\u015Fi\", -1, 1),\n              new Among(\"a\\u0163i\", -1, 2), new Among(\"ea\\u0163i\", 38, 1),\n              new Among(\"ia\\u0163i\", 38, 1), new Among(\"e\\u0163i\", -1, 2),\n              new Among(\"i\\u0163i\", -1, 2), new Among(\"\\u00E2\\u0163i\", -1, 2),\n              new Among(\"ar\\u0103\\u0163i\", -1, 1),\n              new Among(\"ser\\u0103\\u0163i\", -1, 2),\n              new Among(\"aser\\u0103\\u0163i\", 45, 1),\n              new Among(\"seser\\u0103\\u0163i\", 45, 2),\n              new Among(\"iser\\u0103\\u0163i\", 45, 1),\n              new Among(\"user\\u0103\\u0163i\", 45, 1),\n              new Among(\"\\u00E2ser\\u0103\\u0163i\", 45, 1),\n              new Among(\"ir\\u0103\\u0163i\", -1, 1),\n              new Among(\"ur\\u0103\\u0163i\", -1, 1),\n              new Among(\"\\u00E2r\\u0103\\u0163i\", -1, 1), new Among(\"am\", -1, 1),\n              new Among(\"eam\", 54, 1), new Among(\"iam\", 54, 1),\n              new Among(\"em\", -1, 2), new Among(\"asem\", 57, 1),\n              new Among(\"sesem\", 57, 2), new Among(\"isem\", 57, 1),\n              new Among(\"usem\", 57, 1), new Among(\"\\u00E2sem\", 57, 1),\n              new Among(\"im\", -1, 2), new Among(\"\\u00E2m\", -1, 2),\n              new Among(\"\\u0103m\", -1, 2), new Among(\"ar\\u0103m\", 65, 1),\n              new Among(\"ser\\u0103m\", 65, 2), new Among(\"aser\\u0103m\", 67, 1),\n              new Among(\"seser\\u0103m\", 67, 2), new Among(\"iser\\u0103m\", 67, 1),\n              new Among(\"user\\u0103m\", 67, 1),\n              new Among(\"\\u00E2ser\\u0103m\", 67, 1),\n              new Among(\"ir\\u0103m\", 65, 1), new Among(\"ur\\u0103m\", 65, 1),\n              new Among(\"\\u00E2r\\u0103m\", 65, 1), new Among(\"au\", -1, 1),\n              new Among(\"eau\", 76, 1), new Among(\"iau\", 76, 1),\n              new Among(\"indu\", -1, 1), new Among(\"\\u00E2ndu\", -1, 1),\n              new Among(\"ez\", -1, 1), new Among(\"easc\\u0103\", -1, 1),\n              new Among(\"ar\\u0103\", -1, 1), new Among(\"ser\\u0103\", -1, 2),\n              new Among(\"aser\\u0103\", 84, 1), new Among(\"seser\\u0103\", 84, 2),\n              new Among(\"iser\\u0103\", 84, 1), new Among(\"user\\u0103\", 84, 1),\n              new Among(\"\\u00E2ser\\u0103\", 84, 1), new Among(\"ir\\u0103\", -1, 1),\n              new Among(\"ur\\u0103\", -1, 1), new Among(\"\\u00E2r\\u0103\", -1, 1),\n              new Among(\"eaz\\u0103\", -1, 1)\n            ],\n            a_5 = [new Among(\"a\", -1, 1),\n              new Among(\"e\", -1, 1), new Among(\"ie\", 1, 1),\n              new Among(\"i\", -1, 1), new Among(\"\\u0103\", -1, 1)\n            ],\n            g_v = [17, 65,\n              16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 32, 0, 0, 4\n            ],\n            B_standard_suffix_removed, I_p2, I_p1, I_pV, sbp = new SnowballProgram();\n          this.setCurrent = function(word) {\n            sbp.setCurrent(word);\n          };\n          this.getCurrent = function() {\n            return sbp.getCurrent();\n          };\n\n          function habr1(c1, c2) {\n            if (sbp.eq_s(1, c1)) {\n              sbp.ket = sbp.cursor;\n              if (sbp.in_grouping(g_v, 97, 259))\n                sbp.slice_from(c2);\n            }\n          }\n\n          function r_prelude() {\n            var v_1, v_2;\n            while (true) {\n              v_1 = sbp.cursor;\n              if (sbp.in_grouping(g_v, 97, 259)) {\n                v_2 = sbp.cursor;\n                sbp.bra = v_2;\n                habr1(\"u\", \"U\");\n                sbp.cursor = v_2;\n                habr1(\"i\", \"I\");\n              }\n              sbp.cursor = v_1;\n              if (sbp.cursor >= sbp.limit) {\n                break;\n              }\n              sbp.cursor++;\n            }\n          }\n\n          function habr2() {\n            if (sbp.out_grouping(g_v, 97, 259)) {\n              while (!sbp.in_grouping(g_v, 97, 259)) {\n                if (sbp.cursor >= sbp.limit)\n                  return true;\n                sbp.cursor++;\n              }\n              return false;\n            }\n            return true;\n          }\n\n          function habr3() {\n            if (sbp.in_grouping(g_v, 97, 259)) {\n              while (!sbp.out_grouping(g_v, 97, 259)) {\n                if (sbp.cursor >= sbp.limit)\n                  return true;\n                sbp.cursor++;\n              }\n            }\n            return false;\n          }\n\n          function habr4() {\n            var v_1 = sbp.cursor,\n              v_2, v_3;\n            if (sbp.in_grouping(g_v, 97, 259)) {\n              v_2 = sbp.cursor;\n              if (habr2()) {\n                sbp.cursor = v_2;\n                if (!habr3()) {\n                  I_pV = sbp.cursor;\n                  return;\n                }\n              } else {\n                I_pV = sbp.cursor;\n                return;\n              }\n            }\n            sbp.cursor = v_1;\n            if (sbp.out_grouping(g_v, 97, 259)) {\n              v_3 = sbp.cursor;\n              if (habr2()) {\n                sbp.cursor = v_3;\n                if (sbp.in_grouping(g_v, 97, 259) && sbp.cursor < sbp.limit)\n                  sbp.cursor++;\n              }\n              I_pV = sbp.cursor;\n            }\n          }\n\n          function habr5() {\n            while (!sbp.in_grouping(g_v, 97, 259)) {\n              if (sbp.cursor >= sbp.limit)\n                return false;\n              sbp.cursor++;\n            }\n            while (!sbp.out_grouping(g_v, 97, 259)) {\n              if (sbp.cursor >= sbp.limit)\n                return false;\n              sbp.cursor++;\n            }\n            return true;\n          }\n\n          function r_mark_regions() {\n            var v_1 = sbp.cursor;\n            I_pV = sbp.limit;\n            I_p1 = I_pV;\n            I_p2 = I_pV;\n            habr4();\n            sbp.cursor = v_1;\n            if (habr5()) {\n              I_p1 = sbp.cursor;\n              if (habr5())\n                I_p2 = sbp.cursor;\n            }\n          }\n\n          function r_postlude() {\n            var among_var;\n            while (true) {\n              sbp.bra = sbp.cursor;\n              among_var = sbp.find_among(a_0, 3);\n              if (among_var) {\n                sbp.ket = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"i\");\n                    continue;\n                  case 2:\n                    sbp.slice_from(\"u\");\n                    continue;\n                  case 3:\n                    if (sbp.cursor >= sbp.limit)\n                      break;\n                    sbp.cursor++;\n                    continue;\n                }\n              }\n              break;\n            }\n          }\n\n          function r_RV() {\n            return I_pV <= sbp.cursor;\n          }\n\n          function r_R1() {\n            return I_p1 <= sbp.cursor;\n          }\n\n          function r_R2() {\n            return I_p2 <= sbp.cursor;\n          }\n\n          function r_step_0() {\n            var among_var, v_1;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_1, 16);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                    sbp.slice_from(\"a\");\n                    break;\n                  case 3:\n                    sbp.slice_from(\"e\");\n                    break;\n                  case 4:\n                    sbp.slice_from(\"i\");\n                    break;\n                  case 5:\n                    v_1 = sbp.limit - sbp.cursor;\n                    if (!sbp.eq_s_b(2, \"ab\")) {\n                      sbp.cursor = sbp.limit - v_1;\n                      sbp.slice_from(\"i\");\n                    }\n                    break;\n                  case 6:\n                    sbp.slice_from(\"at\");\n                    break;\n                  case 7:\n                    sbp.slice_from(\"a\\u0163i\");\n                    break;\n                }\n              }\n            }\n          }\n\n          function r_combo_suffix() {\n            var among_var, v_1 = sbp.limit - sbp.cursor;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_2, 46);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R1()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_from(\"abil\");\n                    break;\n                  case 2:\n                    sbp.slice_from(\"ibil\");\n                    break;\n                  case 3:\n                    sbp.slice_from(\"iv\");\n                    break;\n                  case 4:\n                    sbp.slice_from(\"ic\");\n                    break;\n                  case 5:\n                    sbp.slice_from(\"at\");\n                    break;\n                  case 6:\n                    sbp.slice_from(\"it\");\n                    break;\n                }\n                B_standard_suffix_removed = true;\n                sbp.cursor = sbp.limit - v_1;\n                return true;\n              }\n            }\n            return false;\n          }\n\n          function r_standard_suffix() {\n            var among_var, v_1;\n            B_standard_suffix_removed = false;\n            while (true) {\n              v_1 = sbp.limit - sbp.cursor;\n              if (!r_combo_suffix()) {\n                sbp.cursor = sbp.limit - v_1;\n                break;\n              }\n            }\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_3, 62);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_R2()) {\n                switch (among_var) {\n                  case 1:\n                    sbp.slice_del();\n                    break;\n                  case 2:\n                    if (sbp.eq_s_b(1, \"\\u0163\")) {\n                      sbp.bra = sbp.cursor;\n                      sbp.slice_from(\"t\");\n                    }\n                    break;\n                  case 3:\n                    sbp.slice_from(\"ist\");\n                    break;\n                }\n                B_standard_suffix_removed = true;\n              }\n            }\n          }\n\n          function r_verb_suffix() {\n            var among_var, v_1, v_2;\n            if (sbp.cursor >= I_pV) {\n              v_1 = sbp.limit_backward;\n              sbp.limit_backward = I_pV;\n              sbp.ket = sbp.cursor;\n              among_var = sbp.find_among_b(a_4, 94);\n              if (among_var) {\n                sbp.bra = sbp.cursor;\n                switch (among_var) {\n                  case 1:\n                    v_2 = sbp.limit - sbp.cursor;\n                    if (!sbp.out_grouping_b(g_v, 97, 259)) {\n                      sbp.cursor = sbp.limit - v_2;\n                      if (!sbp.eq_s_b(1, \"u\"))\n                        break;\n                    }\n                    case 2:\n                      sbp.slice_del();\n                      break;\n                }\n              }\n              sbp.limit_backward = v_1;\n            }\n          }\n\n          function r_vowel_suffix() {\n            var among_var;\n            sbp.ket = sbp.cursor;\n            among_var = sbp.find_among_b(a_5, 5);\n            if (among_var) {\n              sbp.bra = sbp.cursor;\n              if (r_RV() && among_var == 1)\n                sbp.slice_del();\n            }\n          }\n          this.stem = function() {\n            var v_1 = sbp.cursor;\n            r_prelude();\n            sbp.cursor = v_1;\n            r_mark_regions();\n            sbp.limit_backward = v_1;\n            sbp.cursor = sbp.limit;\n            r_step_0();\n            sbp.cursor = sbp.limit;\n            r_standard_suffix();\n            sbp.cursor = sbp.limit;\n            if (!B_standard_suffix_removed) {\n              sbp.cursor = sbp.limit;\n              r_verb_suffix();\n              sbp.cursor = sbp.limit;\n            }\n            r_vowel_suffix();\n            sbp.cursor = sbp.limit_backward;\n            r_postlude();\n            return true;\n          }\n        };\n\n      /* and return a function that stems a word for the current locale */\n      return function(token) {\n        // for lunr version 2\n        if (typeof token.update === \"function\") {\n          return token.update(function(word) {\n            st.setCurrent(word);\n            st.stem();\n            return st.getCurrent();\n          })\n        } else { // for lunr version <= 1\n          st.setCurrent(token);\n          st.stem();\n          return st.getCurrent();\n        }\n      }\n    })();\n\n    lunr.Pipeline.registerFunction(lunr.ro.stemmer, 'stemmer-ro');\n\n    lunr.ro.stopWordFilter = lunr.generateStopWordFilter('acea aceasta această aceea acei aceia acel acela acele acelea acest acesta aceste acestea aceşti aceştia acolo acord acum ai aia aibă aici al ale alea altceva altcineva am ar are asemenea asta astea astăzi asupra au avea avem aveţi azi aş aşadar aţi bine bucur bună ca care caut ce cel ceva chiar cinci cine cineva contra cu cum cumva curând curînd când cât câte câtva câţi cînd cît cîte cîtva cîţi că căci cărei căror cărui către da dacă dar datorită dată dau de deci deja deoarece departe deşi din dinaintea dintr- dintre doi doilea două drept după dă ea ei el ele eram este eu eşti face fata fi fie fiecare fii fim fiu fiţi frumos fără graţie halbă iar ieri la le li lor lui lângă lîngă mai mea mei mele mereu meu mi mie mine mult multă mulţi mulţumesc mâine mîine mă ne nevoie nici nicăieri nimeni nimeri nimic nişte noastre noastră noi noroc nostru nouă noştri nu opt ori oricare orice oricine oricum oricând oricât oricînd oricît oriunde patra patru patrulea pe pentru peste pic poate pot prea prima primul prin puţin puţina puţină până pînă rog sa sale sau se spate spre sub sunt suntem sunteţi sută sînt sîntem sînteţi să săi său ta tale te timp tine toate toată tot totuşi toţi trei treia treilea tu tăi tău un una unde undeva unei uneia unele uneori unii unor unora unu unui unuia unul vi voastre voastră voi vostru vouă voştri vreme vreo vreun vă zece zero zi zice îi îl îmi împotriva în  înainte înaintea încotro încât încît între întrucât întrucît îţi ăla ălea ăsta ăstea ăştia şapte şase şi ştiu ţi ţie'.split(' '));\n\n    lunr.Pipeline.registerFunction(lunr.ro.stopWordFilter, 'stopWordFilter-ro');\n  };\n}))"], "mappings": "4CAAA,IAAAA,EAAAC,EAAA,CAAAC,EAAAC,IAAA,EAsBC,SAASC,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IAEzC,OAAOA,CAAO,EACL,OAAOH,GAAY,SAM5BC,EAAO,QAAUE,EAAQ,EAGzBA,EAAQ,EAAED,EAAK,IAAI,CAEvB,GAAEF,EAAM,UAAW,CAMjB,OAAO,SAASI,EAAM,CAEpB,GAAoB,OAAOA,EAAvB,IACF,MAAM,IAAI,MAAM,wEAAwE,EAI1F,GAAoB,OAAOA,EAAK,eAA5B,IACF,MAAM,IAAI,MAAM,wGAAwG,EAI1HA,EAAK,GAAK,UAAW,CACnB,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,IACZA,EAAK,GAAG,QACRA,EAAK,GAAG,eACRA,EAAK,GAAG,OACV,EAKI,KAAK,iBACP,KAAK,eAAe,MAAM,EAC1B,KAAK,eAAe,IAAIA,EAAK,GAAG,OAAO,EAE3C,EAGAA,EAAK,GAAG,eAAiB,yUACzBA,EAAK,GAAG,QAAUA,EAAK,eAAe,gBAAgBA,EAAK,GAAG,cAAc,EAE5EA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAG5DA,EAAK,GAAG,QAAW,UAAW,CAE5B,IAAIC,EAAQD,EAAK,eAAe,MAC9BE,EAAkBF,EAAK,eAAe,gBACtCG,EAAK,IAAI,UAA2B,CAClC,IAAIC,EAAM,CAAC,IAAIH,EAAM,GAAI,GAAI,CAAC,EAAG,IAAIA,EAAM,IAAK,EAAG,CAAC,EAAG,IAAIA,EAAM,IAAK,EAAG,CAAC,CAAC,EACzEI,EAAM,CACJ,IAAIJ,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EACpD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,YAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACrD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,EAAG,CAAC,EAC/C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,CACpD,EACAK,EAAM,CACJ,IAAIL,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,cAAe,GAAI,CAAC,EACzD,IAAIA,EAAM,cAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EAC1D,IAAIA,EAAM,SAAU,GAAI,CAAC,EAAG,IAAIA,EAAM,cAAe,GAAI,CAAC,EAC1D,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EACzD,IAAIA,EAAM,YAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAW,GAAI,CAAC,EACzD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAW,GAAI,CAAC,EACrD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAc,GAAI,CAAC,EACxD,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EACzD,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACrD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,cAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,gBAAiB,GAAI,CAAC,EACjE,IAAIA,EAAM,cAAe,GAAI,CAAC,EAC9B,IAAIA,EAAM,oBAAqB,GAAI,CAAC,EACpC,IAAIA,EAAM,sBAAuB,GAAI,CAAC,EACtC,IAAIA,EAAM,oBAAqB,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC9D,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,EACnD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EACtD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAc,GAAI,CAAC,EACvD,IAAIA,EAAM,aAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAc,GAAI,CAAC,EAC7D,IAAIA,EAAM,aAAc,GAAI,CAAC,CAC/B,EACAM,EAAM,CAAC,IAAIN,EAAM,MAAO,GAAI,CAAC,EAC3B,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EACnD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAS,GAAI,CAAC,EAClD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACjD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EACrD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EACrD,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,kBAAmB,GAAI,CAAC,EAChE,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EACjD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC7C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC9C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC9C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,CAAC,EACnD,IAAIA,EAAM,aAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAc,GAAI,CAAC,EAC7D,IAAIA,EAAM,YAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,CAAC,EAC1D,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EAC1D,IAAIA,EAAM,YAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,CAAC,EAC1D,IAAIA,EAAM,WAAY,GAAI,CAAC,CAC7B,EACAO,EAAM,CAAC,IAAIP,EAAM,KAAM,GAAI,CAAC,EAC1B,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC9C,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACpD,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EACpD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EACnD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAChD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,SAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EAC1D,IAAIA,EAAM,iBAAkB,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC1D,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC9C,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,YAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,iBAAkB,GAAI,CAAC,EAChE,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC9C,IAAIA,EAAM,QAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,CAAC,EACxD,IAAIA,EAAM,YAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAc,GAAI,CAAC,EAC5D,IAAIA,EAAM,cAAe,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAc,GAAI,CAAC,EAC9D,IAAIA,EAAM,aAAc,GAAI,CAAC,EAC7B,IAAIA,EAAM,gBAAmB,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,CAAC,EAChE,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,cAAiB,GAAI,CAAC,EAC9D,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EAC1D,IAAIA,EAAM,YAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,CAAC,EAC1D,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,cAAiB,GAAI,CAAC,EAC9D,IAAIA,EAAM,kBAAmB,GAAI,CAAC,EAClC,IAAIA,EAAM,mBAAoB,GAAI,CAAC,EACnC,IAAIA,EAAM,oBAAqB,GAAI,CAAC,EACpC,IAAIA,EAAM,qBAAsB,GAAI,CAAC,EACrC,IAAIA,EAAM,oBAAqB,GAAI,CAAC,EACpC,IAAIA,EAAM,oBAAqB,GAAI,CAAC,EACpC,IAAIA,EAAM,uBAA0B,GAAI,CAAC,EACzC,IAAIA,EAAM,kBAAmB,GAAI,CAAC,EAClC,IAAIA,EAAM,kBAAmB,GAAI,CAAC,EAClC,IAAIA,EAAM,qBAAwB,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EAC/D,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAC/C,IAAIA,EAAM,QAAS,GAAI,CAAC,EAAG,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAClD,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EACtD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,QAAW,GAAI,CAAC,EAClD,IAAIA,EAAM,UAAW,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EACzD,IAAIA,EAAM,aAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,cAAe,GAAI,CAAC,EAC9D,IAAIA,EAAM,eAAgB,GAAI,CAAC,EAAG,IAAIA,EAAM,cAAe,GAAI,CAAC,EAChE,IAAIA,EAAM,cAAe,GAAI,CAAC,EAC9B,IAAIA,EAAM,iBAAoB,GAAI,CAAC,EACnC,IAAIA,EAAM,YAAa,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EAC3D,IAAIA,EAAM,eAAkB,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,GAAI,CAAC,EACzD,IAAIA,EAAM,MAAO,GAAI,CAAC,EAAG,IAAIA,EAAM,MAAO,GAAI,CAAC,EAC/C,IAAIA,EAAM,OAAQ,GAAI,CAAC,EAAG,IAAIA,EAAM,UAAa,GAAI,CAAC,EACtD,IAAIA,EAAM,KAAM,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAc,GAAI,CAAC,EACrD,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,YAAa,GAAI,CAAC,EAC1D,IAAIA,EAAM,aAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,cAAe,GAAI,CAAC,EAC9D,IAAIA,EAAM,aAAc,GAAI,CAAC,EAAG,IAAIA,EAAM,aAAc,GAAI,CAAC,EAC7D,IAAIA,EAAM,gBAAmB,GAAI,CAAC,EAAG,IAAIA,EAAM,WAAY,GAAI,CAAC,EAChE,IAAIA,EAAM,WAAY,GAAI,CAAC,EAAG,IAAIA,EAAM,cAAiB,GAAI,CAAC,EAC9D,IAAIA,EAAM,YAAa,GAAI,CAAC,CAC9B,EACAQ,EAAM,CAAC,IAAIR,EAAM,IAAK,GAAI,CAAC,EACzB,IAAIA,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,KAAM,EAAG,CAAC,EAC3C,IAAIA,EAAM,IAAK,GAAI,CAAC,EAAG,IAAIA,EAAM,SAAU,GAAI,CAAC,CAClD,EACAS,EAAM,CAAC,GAAI,GACT,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,EAAG,CAC1D,EACAC,EAA2BC,EAAMC,EAAMC,EAAMC,EAAM,IAAIb,EACzD,KAAK,WAAa,SAASc,EAAM,CAC/BD,EAAI,WAAWC,CAAI,CACrB,EACA,KAAK,WAAa,UAAW,CAC3B,OAAOD,EAAI,WAAW,CACxB,EAEA,SAASE,EAAMC,EAAIC,EAAI,CACjBJ,EAAI,KAAK,EAAGG,CAAE,IAChBH,EAAI,IAAMA,EAAI,OACVA,EAAI,YAAYL,EAAK,GAAI,GAAG,GAC9BK,EAAI,WAAWI,CAAE,EAEvB,CAEA,SAASC,GAAY,CAEnB,QADIC,EAAKC,EAEPD,EAAMN,EAAI,OACNA,EAAI,YAAYL,EAAK,GAAI,GAAG,IAC9BY,EAAMP,EAAI,OACVA,EAAI,IAAMO,EACVL,EAAM,IAAK,GAAG,EACdF,EAAI,OAASO,EACbL,EAAM,IAAK,GAAG,GAEhBF,EAAI,OAASM,EACT,EAAAN,EAAI,QAAUA,EAAI,QAGtBA,EAAI,QAER,CAEA,SAASQ,GAAQ,CACf,GAAIR,EAAI,aAAaL,EAAK,GAAI,GAAG,EAAG,CAClC,KAAO,CAACK,EAAI,YAAYL,EAAK,GAAI,GAAG,GAAG,CACrC,GAAIK,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CACA,MAAO,EACT,CAEA,SAASS,GAAQ,CACf,GAAIT,EAAI,YAAYL,EAAK,GAAI,GAAG,EAC9B,KAAO,CAACK,EAAI,aAAaL,EAAK,GAAI,GAAG,GAAG,CACtC,GAAIK,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CAEF,MAAO,EACT,CAEA,SAASU,GAAQ,CACf,IAAIJ,EAAMN,EAAI,OACZO,EAAKI,EACP,GAAIX,EAAI,YAAYL,EAAK,GAAI,GAAG,EAE9B,GADAY,EAAMP,EAAI,OACNQ,EAAM,GAER,GADAR,EAAI,OAASO,EACT,CAACE,EAAM,EAAG,CACZV,EAAOC,EAAI,OACX,MACF,MACK,CACLD,EAAOC,EAAI,OACX,MACF,CAEFA,EAAI,OAASM,EACTN,EAAI,aAAaL,EAAK,GAAI,GAAG,IAC/BgB,EAAMX,EAAI,OACNQ,EAAM,IACRR,EAAI,OAASW,EACTX,EAAI,YAAYL,EAAK,GAAI,GAAG,GAAKK,EAAI,OAASA,EAAI,OACpDA,EAAI,UAERD,EAAOC,EAAI,OAEf,CAEA,SAASY,GAAQ,CACf,KAAO,CAACZ,EAAI,YAAYL,EAAK,GAAI,GAAG,GAAG,CACrC,GAAIK,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,KAAO,CAACA,EAAI,aAAaL,EAAK,GAAI,GAAG,GAAG,CACtC,GAAIK,EAAI,QAAUA,EAAI,MACpB,MAAO,GACTA,EAAI,QACN,CACA,MAAO,EACT,CAEA,SAASa,GAAiB,CACxB,IAAIP,EAAMN,EAAI,OACdD,EAAOC,EAAI,MACXF,EAAOC,EACPF,EAAOE,EACPW,EAAM,EACNV,EAAI,OAASM,EACTM,EAAM,IACRd,EAAOE,EAAI,OACPY,EAAM,IACRf,EAAOG,EAAI,QAEjB,CAEA,SAASc,GAAa,CAEpB,QADIC,IACS,CAGX,GAFAf,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,WAAWX,EAAK,CAAC,EAC7B0B,EAEF,OADAf,EAAI,IAAMA,EAAI,OACNe,EAAW,CACjB,IAAK,GACHf,EAAI,WAAW,GAAG,EAClB,SACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,SACF,IAAK,GACH,GAAIA,EAAI,QAAUA,EAAI,MACpB,MACFA,EAAI,SACJ,QACJ,CAEF,KACF,CACF,CAEA,SAASgB,GAAO,CACd,OAAOjB,GAAQC,EAAI,MACrB,CAEA,SAASiB,GAAO,CACd,OAAOnB,GAAQE,EAAI,MACrB,CAEA,SAASkB,GAAO,CACd,OAAOrB,GAAQG,EAAI,MACrB,CAEA,SAASmB,GAAW,CAClB,IAAIJ,EAAWT,EAGf,GAFAN,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,aAAaV,EAAK,EAAE,EAChCyB,IACFf,EAAI,IAAMA,EAAI,OACViB,EAAK,GACP,OAAQF,EAAW,CACjB,IAAK,GACHf,EAAI,UAAU,EACd,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHA,EAAI,WAAW,GAAG,EAClB,MACF,IAAK,GACHM,EAAMN,EAAI,MAAQA,EAAI,OACjBA,EAAI,OAAO,EAAG,IAAI,IACrBA,EAAI,OAASA,EAAI,MAAQM,EACzBN,EAAI,WAAW,GAAG,GAEpB,MACF,IAAK,GACHA,EAAI,WAAW,IAAI,EACnB,MACF,IAAK,GACHA,EAAI,WAAW,UAAU,EACzB,KACJ,CAGN,CAEA,SAASoB,GAAiB,CACxB,IAAIL,EAAWT,EAAMN,EAAI,MAAQA,EAAI,OAGrC,GAFAA,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,aAAaT,EAAK,EAAE,EAChCwB,IACFf,EAAI,IAAMA,EAAI,OACViB,EAAK,GAAG,CACV,OAAQF,EAAW,CACjB,IAAK,GACHf,EAAI,WAAW,MAAM,EACrB,MACF,IAAK,GACHA,EAAI,WAAW,MAAM,EACrB,MACF,IAAK,GACHA,EAAI,WAAW,IAAI,EACnB,MACF,IAAK,GACHA,EAAI,WAAW,IAAI,EACnB,MACF,IAAK,GACHA,EAAI,WAAW,IAAI,EACnB,MACF,IAAK,GACHA,EAAI,WAAW,IAAI,EACnB,KACJ,CACA,OAAAJ,EAA4B,GAC5BI,EAAI,OAASA,EAAI,MAAQM,EAClB,EACT,CAEF,MAAO,EACT,CAEA,SAASe,GAAoB,CAC3B,IAAIN,EAAWT,EAEf,IADAV,EAA4B,KAG1B,GADAU,EAAMN,EAAI,MAAQA,EAAI,OAClB,CAACoB,EAAe,EAAG,CACrBpB,EAAI,OAASA,EAAI,MAAQM,EACzB,KACF,CAIF,GAFAN,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,aAAaR,EAAK,EAAE,EAChCuB,IACFf,EAAI,IAAMA,EAAI,OACVkB,EAAK,GAAG,CACV,OAAQH,EAAW,CACjB,IAAK,GACHf,EAAI,UAAU,EACd,MACF,IAAK,GACCA,EAAI,OAAO,EAAG,QAAQ,IACxBA,EAAI,IAAMA,EAAI,OACdA,EAAI,WAAW,GAAG,GAEpB,MACF,IAAK,GACHA,EAAI,WAAW,KAAK,EACpB,KACJ,CACAJ,EAA4B,EAC9B,CAEJ,CAEA,SAAS0B,GAAgB,CACvB,IAAIP,EAAWT,EAAKC,EACpB,GAAIP,EAAI,QAAUD,EAAM,CAKtB,GAJAO,EAAMN,EAAI,eACVA,EAAI,eAAiBD,EACrBC,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,aAAaP,EAAK,EAAE,EAChCsB,EAEF,OADAf,EAAI,IAAMA,EAAI,OACNe,EAAW,CACjB,IAAK,GAEH,GADAR,EAAMP,EAAI,MAAQA,EAAI,OAClB,CAACA,EAAI,eAAeL,EAAK,GAAI,GAAG,IAClCK,EAAI,OAASA,EAAI,MAAQO,EACrB,CAACP,EAAI,OAAO,EAAG,GAAG,GACpB,MAEJ,IAAK,GACHA,EAAI,UAAU,EACd,KACN,CAEFA,EAAI,eAAiBM,CACvB,CACF,CAEA,SAASiB,GAAiB,CACxB,IAAIR,EACJf,EAAI,IAAMA,EAAI,OACde,EAAYf,EAAI,aAAaN,EAAK,CAAC,EAC/BqB,IACFf,EAAI,IAAMA,EAAI,OACVgB,EAAK,GAAKD,GAAa,GACzBf,EAAI,UAAU,EAEpB,CACA,KAAK,KAAO,UAAW,CACrB,IAAIM,EAAMN,EAAI,OACd,OAAAK,EAAU,EACVL,EAAI,OAASM,EACbO,EAAe,EACfb,EAAI,eAAiBM,EACrBN,EAAI,OAASA,EAAI,MACjBmB,EAAS,EACTnB,EAAI,OAASA,EAAI,MACjBqB,EAAkB,EAClBrB,EAAI,OAASA,EAAI,MACZJ,IACHI,EAAI,OAASA,EAAI,MACjBsB,EAAc,EACdtB,EAAI,OAASA,EAAI,OAEnBuB,EAAe,EACfvB,EAAI,OAASA,EAAI,eACjBc,EAAW,EACJ,EACT,CACF,EAGF,OAAO,SAASU,EAAO,CAErB,OAAI,OAAOA,EAAM,QAAW,WACnBA,EAAM,OAAO,SAASvB,EAAM,CACjC,OAAAb,EAAG,WAAWa,CAAI,EAClBb,EAAG,KAAK,EACDA,EAAG,WAAW,CACvB,CAAC,GAEDA,EAAG,WAAWoC,CAAK,EACnBpC,EAAG,KAAK,EACDA,EAAG,WAAW,EAEzB,CACF,EAAG,EAEHH,EAAK,SAAS,iBAAiBA,EAAK,GAAG,QAAS,YAAY,EAE5DA,EAAK,GAAG,eAAiBA,EAAK,uBAAuB,4+DAA++C,MAAM,GAAG,CAAC,EAE9iDA,EAAK,SAAS,iBAAiBA,EAAK,GAAG,eAAgB,mBAAmB,CAC5E,CACF,CAAC", "names": ["require_lunr_ro", "__commonJSMin", "exports", "module", "root", "factory", "lunr", "Among", "SnowballProgram", "st", "a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "g_v", "B_standard_suffix_removed", "I_p2", "I_p1", "I_pV", "sbp", "word", "habr1", "c1", "c2", "r_prelude", "v_1", "v_2", "habr2", "habr3", "habr4", "v_3", "habr5", "r_mark_regions", "r_postlude", "among_var", "r_RV", "r_R1", "r_R2", "r_step_0", "r_combo_suffix", "r_standard_suffix", "r_verb_suffix", "r_vowel_suffix", "token"]}