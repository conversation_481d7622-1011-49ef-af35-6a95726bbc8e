# DrawnUi.Maui

**A lightweight library for .NET MAUI built on top of SkiaSharp, to layout and draw your UI on a Skia canvas.**

* Provides infrastructure to create and render drawn controls with gestures and animations, comes with some pre-built controls.  
* Profits from hardware-accelerated rendering on iOS • MacCatalyst • Android • Windows  
* Free to use under the MIT license, a nuget package is available.
* To consume inside a usual MAUI app - wrap drawn controls inside `Canvas` views.
* To create a totally drawn apps with just one `Canvas` as root view - `SkiaShell`, `SkiaViewSwitcher` are provided for navigation with modals, popups, toasts etc.
* Drawn controls are virtual: no native views/handlers created, UI-thread is not required to accessed and modify them.
 

## 🚀 Live Examples

- **[Engine Demo](https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo)** - A totally drawn app demo with recycled cells, camera etc
- **[Space Shooter Game](https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter)** - Arcade game etude built with DrawnUi
- **[Shaders Carousel](https://github.com/taublast/ShadersCarousel/)** - A totally drawn app with making use of SkiaSharp v3 shaders
- **[Sandbox project](https://github.com/taublast/DrawnUi.Maui/tree/main/src/Maui/Samples/Sandbox)** - Experiment with pre-built drawn controls and more

---

## 🎯 Onboarding

**Q: Drawn? What is the difference between DrawnUi and other drawn frameworks?**  
A: Not really comparable since DrawnUI is just a library for **.NET MAUI**, aiming to draw UI instead of using native views.

**Q: Why avoid native UI?**  
A: Rather a freedom choice to draw what you want and how you see it. It can also be more performant to draw a complex UI on just one canvas instead of using many native views.

**Q: Do I need to know how to draw on a canvas??**  
A: No, you can start by using prebuilt drawn controls and customize them. All controls are initially designed to be subclassed, customized, and almost every method is virtual. 

**Q: Can I still use XAML?**  
A: Yes you can use both XAML and code-behind to create your UI.  

**Q: Can I avoid using XAML at all costs?**  
A: Yes feel free to use code-behind to create your UI, up to using background thread to access and modify drawn controls properties.

**Q: How do I create custom controls with DrawnUI?**  
A: Inherit from `SkiaControl` for basic controls or `SkiaLayout` for containers etc. Override the `Paint` method to draw with SkiaSharp.

**Q: Can I embed native MAUI controls inside DrawnUI?**  
A: Yes! Use `SkiaMauiElement` to embed native MAUI controls like WebView inside your DrawnUI canvas. This allows you to combine the best of both worlds.

**Q: Possible to make games with DrawnUI?**  
A: Well, since you draw, why not just draw a game instead of a business app. DrawnUI comes with gaming helpers and custom accelerated platform views to assure a smooth display-synched rendering.

### Advanced

**Q: How do I set SkiaImage source?**  
A:  Set from source or an existing image:  
Option A: Place images in `Resources/Raw` folder: `<draw:SkiaImage Source="baboon.jpg" />` .  
Option B: Set images directly: `mySkiaImage.SetImageInternal(skiaImage)`.

**Q: Can I use MAUI's default `Images` folder?**  
A: No.

**Q: How do I prevent touch events from passing through overlapping controls?**  
A: Use the `BlockGesturesBelow="True"` property on the top control. Note that `InputTransparent` makes the control itself avoid gestures, not controls below.

**Q: How do I internally rebuild the ItemsSource?**  
A: Directly call `layout.ApplyItemsSource()`.


---

**Can't find the answer to your question?** → **[Ask in GitHub Discussions](https://github.com/taublast/DrawnUi/discussions)** - The community is here to help!

## 💡More Help

- **[FAQ](fluent-extensions.md#troubleshooting)** - Frequently asked questions and answers
- **[Troubleshooting](fluent-extensions.md#troubleshooting)** - Common issues and solutions
- **[GitHub Issues](https://github.com/taublast/DrawnUi.Maui/issues)** - Report bugs or ask questions
- **[Background Article](https://taublast.github.io/posts/MauiJuly/)** - Why DrawnUi was created

---

**Ready to get started?** → **[Install and Setup Guide](getting-started.md)**