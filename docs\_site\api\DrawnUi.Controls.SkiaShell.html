<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaShell | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaShell | DrawnUi Documentation ">
      
      <meta name="description" content="A Canvas with Navigation capabilities">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_SkiaShell.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.SkiaShell%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Controls.SkiaShell">



  <h1 id="DrawnUi_Controls_SkiaShell" data-uid="DrawnUi.Controls.SkiaShell" class="text-break">
Class SkiaShell  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.PopupWrapper.cs/#L3"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Controls.html">Controls</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>A Canvas with Navigation capabilities</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SkiaShell : BasePageReloadable, INotifyPropertyChanged, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, ILayout, IPageController, IVisualElementController, IElementController, IElementConfiguration&lt;Page&gt;, ISafeAreaView, ITitledElement, IToolbarElement, IContentView, IPadding, ICrossPlatformLayout, IHotReloadableView, IView, IElement, ITransform, IReplaceableView, IDisposable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement">StyleableElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement">NavigableElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement">VisualElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page">Page</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage">TemplatedPage</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage">ContentPage</a></div>
      <div><a class="xref" href="DrawnUi.Views.DrawnUiBasePage.html">DrawnUiBasePage</a></div>
      <div><a class="xref" href="DrawnUi.Views.BasePageReloadable.html">BasePageReloadable</a></div>
      <div><span class="xref">SkiaShell</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider">IEffectControlProvider</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement">IToolTipElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement">IContextFlyoutElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable">IAnimatable</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ilayout">ILayout</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ipagecontroller">IPageController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller">IVisualElementController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller">IElementController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1">IElementConfiguration</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page">Page</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.isafeareaview">ISafeAreaView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ititledelement">ITitledElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itoolbarelement">IToolbarElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontentview">IContentView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ipadding">IPadding</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icrossplatformlayout">ICrossPlatformLayout</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ihotreloadableview">IHotReloadableView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement">IElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform">ITransform</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ireplaceableview">IReplaceableView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_CountReloads">BasePageReloadable.CountReloads</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_ReloadUi_System_Type___">BasePageReloadable.ReloadUi(Type[])</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_Build">BasePageReloadable.Build()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_Dispose">BasePageReloadable.Dispose()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_IsDisposed">BasePageReloadable.IsDisposed</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Views.DrawnUiBasePage.html#DrawnUi_Views_DrawnUiBasePage_KeyboardResized_System_Double_">DrawnUiBasePage.KeyboardResized(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Views.DrawnUiBasePage.html#DrawnUi_Views_DrawnUiBasePage_OnKeyboardResized_System_Double_">DrawnUiBasePage.OnKeyboardResized(double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.contentproperty">ContentPage.ContentProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.hidesoftinputontappedproperty">ContentPage.HideSoftInputOnTappedProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.onbindingcontextchanged">ContentPage.OnBindingContextChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.measureoverride">ContentPage.MeasureOverride(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.layoutchildren">ContentPage.LayoutChildren(double, double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.arrangeoverride">ContentPage.ArrangeOverride(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.invalidatemeasureoverride">ContentPage.InvalidateMeasureOverride()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.content">ContentPage.Content</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.hidesoftinputontapped">ContentPage.HideSoftInputOnTapped</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.controltemplateproperty">TemplatedPage.ControlTemplateProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.onapplytemplate">TemplatedPage.OnApplyTemplate()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.onchildremoved">TemplatedPage.OnChildRemoved(Element, int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.gettemplatechild">TemplatedPage.GetTemplateChild(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.controltemplate">TemplatedPage.ControlTemplate</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.busysetsignalname">Page.BusySetSignalName</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.alertsignalname">Page.AlertSignalName</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.promptsignalname">Page.PromptSignalName</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.actionsheetsignalname">Page.ActionSheetSignalName</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.backgroundimagesourceproperty">Page.BackgroundImageSourceProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.isbusyproperty">Page.IsBusyProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.paddingproperty">Page.PaddingProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.titleproperty">Page.TitleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.iconimagesourceproperty">Page.IconImageSourceProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayactionsheet#microsoft-maui-controls-page-displayactionsheet(system-string-system-string-system-string-system-string())">Page.DisplayActionSheet(string, string, string, params string[])</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayactionsheet#microsoft-maui-controls-page-displayactionsheet(system-string-system-string-system-string-microsoft-maui-flowdirection-system-string())">Page.DisplayActionSheet(string, string, string, FlowDirection, params string[])</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string)">Page.DisplayAlert(string, string, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-system-string)">Page.DisplayAlert(string, string, string, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-microsoft-maui-flowdirection)">Page.DisplayAlert(string, string, string, FlowDirection)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-system-string-microsoft-maui-flowdirection)">Page.DisplayAlert(string, string, string, string, FlowDirection)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displaypromptasync">Page.DisplayPromptAsync(string, string, string, string, string, int, Keyboard, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.forcelayout">Page.ForceLayout()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.sendbackbuttonpressed">Page.SendBackButtonPressed()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onappearing">Page.OnAppearing()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onbackbuttonpressed">Page.OnBackButtonPressed()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onchildmeasureinvalidated#microsoft-maui-controls-page-onchildmeasureinvalidated(system-object-system-eventargs)">Page.OnChildMeasureInvalidated(object, EventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.ondisappearing">Page.OnDisappearing()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onparentset">Page.OnParentSet()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onsizeallocated">Page.OnSizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.updatechildrenlayout">Page.UpdateChildrenLayout()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.on">Page.On&lt;T&gt;()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatedto">Page.OnNavigatedTo(NavigatedToEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatingfrom">Page.OnNavigatingFrom(NavigatingFromEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatedfrom">Page.OnNavigatedFrom(NavigatedFromEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.getparentwindow">Page.GetParentWindow()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.backgroundimagesource">Page.BackgroundImageSource</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.iconimagesource">Page.IconImageSource</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.isbusy">Page.IsBusy</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.padding">Page.Padding</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.title">Page.Title</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.toolbaritems">Page.ToolbarItems</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.menubaritems">Page.MenuBarItems</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.layoutchanged">Page.LayoutChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.appearing">Page.Appearing</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.disappearing">Page.Disappearing</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.navigatedto">Page.NavigatedTo</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.navigatingfrom">Page.NavigatingFrom</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.navigatedfrom">Page.NavigatedFrom</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.navigationproperty">VisualElement.NavigationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.styleproperty">VisualElement.StyleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparentproperty">VisualElement.InputTransparentProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledproperty">VisualElement.IsEnabledProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.xproperty">VisualElement.XProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.yproperty">VisualElement.YProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorxproperty">VisualElement.AnchorXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchoryproperty">VisualElement.AnchorYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationxproperty">VisualElement.TranslationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationyproperty">VisualElement.TranslationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthproperty">VisualElement.WidthProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightproperty">VisualElement.HeightProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationproperty">VisualElement.RotationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationxproperty">VisualElement.RotationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationyproperty">VisualElement.RotationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleproperty">VisualElement.ScaleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalexproperty">VisualElement.ScaleXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleyproperty">VisualElement.ScaleYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clipproperty">VisualElement.ClipProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visualproperty">VisualElement.VisualProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisibleproperty">VisualElement.IsVisibleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacityproperty">VisualElement.OpacityProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolorproperty">VisualElement.BackgroundColorProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundproperty">VisualElement.BackgroundProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviorsproperty">VisualElement.BehaviorsProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggersproperty">VisualElement.TriggersProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequestproperty">VisualElement.WidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequestproperty">VisualElement.HeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequestproperty">VisualElement.MinimumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequestproperty">VisualElement.MinimumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequestproperty">VisualElement.MaximumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequestproperty">VisualElement.MaximumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocusedproperty">VisualElement.IsFocusedProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirectionproperty">VisualElement.FlowDirectionProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.windowproperty">VisualElement.WindowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadowproperty">VisualElement.ShadowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindexproperty">VisualElement.ZIndexProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin">VisualElement.BatchBegin()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit">VisualElement.BatchCommit()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus">VisualElement.Focus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)">VisualElement.Measure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)">VisualElement.Measure(double, double, MeasureFlags)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus">VisualElement.Unfocus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure">VisualElement.InvalidateMeasure()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded">VisualElement.OnChildAdded(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered">VisualElement.OnChildrenReordered()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure">VisualElement.OnMeasure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated">VisualElement.SizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.changevisualstate">VisualElement.ChangeVisualState()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty">VisualElement.RefreshIsEnabledProperty()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange">VisualElement.Arrange(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout">VisualElement.Layout(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor">VisualElement.MapBackgroundColor(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource">VisualElement.MapBackgroundImageSource(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visual">VisualElement.Visual</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirection">VisualElement.FlowDirection</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.window">VisualElement.Window</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorx">VisualElement.AnchorX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchory">VisualElement.AnchorY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolor">VisualElement.BackgroundColor</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.background">VisualElement.Background</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviors">VisualElement.Behaviors</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.bounds">VisualElement.Bounds</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.height">VisualElement.Height</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequest">VisualElement.HeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparent">VisualElement.InputTransparent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabled">VisualElement.IsEnabled</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledcore">VisualElement.IsEnabledCore</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocused">VisualElement.IsFocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisible">VisualElement.IsVisible</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequest">VisualElement.MinimumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequest">VisualElement.MinimumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequest">VisualElement.MaximumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequest">VisualElement.MaximumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacity">VisualElement.Opacity</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotation">VisualElement.Rotation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationx">VisualElement.RotationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationy">VisualElement.RotationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scale">VisualElement.Scale</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalex">VisualElement.ScaleX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaley">VisualElement.ScaleY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationx">VisualElement.TranslationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationy">VisualElement.TranslationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggers">VisualElement.Triggers</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.width">VisualElement.Width</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequest">VisualElement.WidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.x">VisualElement.X</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.y">VisualElement.Y</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clip">VisualElement.Clip</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.resources">VisualElement.Resources</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.frame">VisualElement.Frame</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.handler">VisualElement.Handler</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadow">VisualElement.Shadow</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindex">VisualElement.ZIndex</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.desiredsize">VisualElement.DesiredSize</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isloaded">VisualElement.IsLoaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.childrenreordered">VisualElement.ChildrenReordered</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focused">VisualElement.Focused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureinvalidated">VisualElement.MeasureInvalidated</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizechanged">VisualElement.SizeChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocused">VisualElement.Unfocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.loaded">VisualElement.Loaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unloaded">VisualElement.Unloaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.navigation">NavigableElement.Navigation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.style">StyleableElement.Style</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.styleclass">StyleableElement.StyleClass</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.class">StyleableElement.class</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty">Element.AutomationIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty">Element.ClassIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild">Element.InsertLogicalChild(int, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild">Element.AddLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild">Element.RemoveLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren">Element.ClearLogicalChildren()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname">Element.FindByName(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource">Element.RemoveDynamicResource(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource">Element.SetDynamicResource(BindableProperty, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged">Element.OnPropertyChanged(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging">Element.OnParentChanging(ParentChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged">Element.OnParentChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging">Element.OnHandlerChanging(HandlerChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree">Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren">Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid">Element.AutomationId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid">Element.ClassId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects">Element.Effects</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id">Element.Id</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid">Element.StyleId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent">Element.Parent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded">Element.ChildAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved">Element.ChildRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded">Element.DescendantAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved">Element.DescendantRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging">Element.ParentChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged">Element.ParentChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">Element.HandlerChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">Element.HandlerChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty">BindableObject.BindingContextProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)">BindableObject.ClearValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.ClearValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue">BindableObject.GetValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset">BindableObject.IsSet(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding">BindableObject.RemoveBinding(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding">BindableObject.SetBinding(BindableProperty, BindingBase)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings">BindableObject.ApplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging">BindableObject.OnPropertyChanging(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings">BindableObject.UnapplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)">BindableObject.SetValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)">BindableObject.SetValue(BindablePropertyKey, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)">BindableObject.CoerceValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.CoerceValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher">BindableObject.Dispatcher</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext">BindableObject.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged">BindableObject.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging">BindableObject.PropertyChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged">BindableObject.BindingContextChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__">FluentExtensions.AssignNative&lt;T&gt;(T, out T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_">StaticResourcesExtensions.FindParent&lt;T&gt;(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_">InternalExtensions.FindMauiContext(Element, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_">InternalExtensions.GetParentsPath(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_">StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_">InternalExtensions.DisposeControlAndChildren(IView)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Controls_SkiaShell__ctor_" data-uid="DrawnUi.Controls.SkiaShell.#ctor*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell__ctor" data-uid="DrawnUi.Controls.SkiaShell.#ctor">
  SkiaShell()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L195"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaShell()</code></pre>
  </div>













  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Controls_SkiaShell_LockLayers" data-uid="DrawnUi.Controls.SkiaShell.LockLayers">
  LockLayers
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1153"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SemaphoreSlim LockLayers</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.semaphoreslim">SemaphoreSlim</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_LockNavigation" data-uid="DrawnUi.Controls.SkiaShell.LockNavigation">
  LockNavigation
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1154"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SemaphoreSlim LockNavigation</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.semaphoreslim">SemaphoreSlim</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_LogEnabled" data-uid="DrawnUi.Controls.SkiaShell.LogEnabled">
  LogEnabled
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L190"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool LogEnabled</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_PopupBackgroundColor" data-uid="DrawnUi.Controls.SkiaShell.PopupBackgroundColor">
  PopupBackgroundColor
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L181"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Default background tint for freezing popups/modals etc</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color PopupBackgroundColor</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_PopupsAnimationSpeed" data-uid="DrawnUi.Controls.SkiaShell.PopupsAnimationSpeed">
  PopupsAnimationSpeed
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L188"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float PopupsAnimationSpeed</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_PopupsBackgroundBlur" data-uid="DrawnUi.Controls.SkiaShell.PopupsBackgroundBlur">
  PopupsBackgroundBlur
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L186"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Default background blur amount for freezing popups/modals etc</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float PopupsBackgroundBlur</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_PopupsCancelAnimationsAfterMs" data-uid="DrawnUi.Controls.SkiaShell.PopupsCancelAnimationsAfterMs">
  PopupsCancelAnimationsAfterMs
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L189"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int PopupsCancelAnimationsAfterMs</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_RouteProperty" data-uid="DrawnUi.Controls.SkiaShell.RouteProperty">
  RouteProperty
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1998"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty RouteProperty</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_Services" data-uid="DrawnUi.Controls.SkiaShell.Services">
  Services
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2206"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected readonly IServiceProvider Services</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.iserviceprovider">IServiceProvider</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_ToastBackgroundColor" data-uid="DrawnUi.Controls.SkiaShell.ToastBackgroundColor">
  ToastBackgroundColor
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L171"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color ToastBackgroundColor</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_ToastTextColor" data-uid="DrawnUi.Controls.SkiaShell.ToastTextColor">
  ToastTextColor
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L172"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color ToastTextColor</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_ToastTextFont" data-uid="DrawnUi.Controls.SkiaShell.ToastTextFont">
  ToastTextFont
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L173"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string ToastTextFont</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_ToastTextFontWeight" data-uid="DrawnUi.Controls.SkiaShell.ToastTextFontWeight">
  ToastTextFontWeight
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L174"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int ToastTextFontWeight</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_ToastTextMargins" data-uid="DrawnUi.Controls.SkiaShell.ToastTextMargins">
  ToastTextMargins
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L176"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double ToastTextMargins</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_ToastTextSize" data-uid="DrawnUi.Controls.SkiaShell.ToastTextSize">
  ToastTextSize
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L175"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double ToastTextSize</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_ZIndexModals" data-uid="DrawnUi.Controls.SkiaShell.ZIndexModals">
  ZIndexModals
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L191"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int ZIndexModals</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_ZIndexPopups" data-uid="DrawnUi.Controls.SkiaShell.ZIndexPopups">
  ZIndexPopups
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L192"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int ZIndexPopups</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell_ZIndexToasts" data-uid="DrawnUi.Controls.SkiaShell.ZIndexToasts">
  ZIndexToasts
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L193"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int ZIndexToasts</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell__lockLayers" data-uid="DrawnUi.Controls.SkiaShell._lockLayers">
  _lockLayers
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1360"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected object _lockLayers</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell__rootRoute" data-uid="DrawnUi.Controls.SkiaShell._rootRoute">
  _rootRoute
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2205"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected string _rootRoute</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Controls_SkiaShell__topmost" data-uid="DrawnUi.Controls.SkiaShell._topmost">
  _topmost
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1359"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SkiaControl _topmost</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Controls_SkiaShell_BottomInsets_" data-uid="DrawnUi.Controls.SkiaShell.BottomInsets*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_BottomInsets" data-uid="DrawnUi.Controls.SkiaShell.BottomInsets">
  BottomInsets
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2554"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double BottomInsets { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_Buffer_" data-uid="DrawnUi.Controls.SkiaShell.Buffer*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_Buffer" data-uid="DrawnUi.Controls.SkiaShell.Buffer">
  Buffer
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2608"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Can use to pass items as models between viewmodels</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Dictionary&lt;string, object&gt; Buffer { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_Canvas_" data-uid="DrawnUi.Controls.SkiaShell.Canvas*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_Canvas" data-uid="DrawnUi.Controls.SkiaShell.Canvas">
  Canvas
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1773"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Canvas Canvas { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Views.Canvas.html">Canvas</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_CurrentRoute_" data-uid="DrawnUi.Controls.SkiaShell.CurrentRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_CurrentRoute" data-uid="DrawnUi.Controls.SkiaShell.CurrentRoute">
  CurrentRoute
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2358"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaShell.ShellCurrentRoute CurrentRoute { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.ShellCurrentRoute.html">ShellCurrentRoute</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_CurrentRouteAuto_" data-uid="DrawnUi.Controls.SkiaShell.CurrentRouteAuto*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_CurrentRouteAuto" data-uid="DrawnUi.Controls.SkiaShell.CurrentRouteAuto">
  CurrentRouteAuto
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L906"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string CurrentRouteAuto { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_FreezingModals_" data-uid="DrawnUi.Controls.SkiaShell.FreezingModals*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_FreezingModals" data-uid="DrawnUi.Controls.SkiaShell.FreezingModals">
  FreezingModals
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1165"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public List&lt;SkiaControl&gt; FreezingModals { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_FrozenLayers_" data-uid="DrawnUi.Controls.SkiaShell.FrozenLayers*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_FrozenLayers" data-uid="DrawnUi.Controls.SkiaShell.FrozenLayers">
  FrozenLayers
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L67"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Dictionary&lt;SkiaControl, SkiaControl&gt; FrozenLayers { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>, <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_Initialized_" data-uid="DrawnUi.Controls.SkiaShell.Initialized*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_Initialized" data-uid="DrawnUi.Controls.SkiaShell.Initialized">
  Initialized
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1896"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Initialized { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_MenuItems_" data-uid="DrawnUi.Controls.SkiaShell.MenuItems*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_MenuItems" data-uid="DrawnUi.Controls.SkiaShell.MenuItems">
  MenuItems
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2599"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ObservableCollection&lt;MenuPageItem&gt; MenuItems { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1">ObservableCollection</a>&lt;<span class="xref">MenuPageItem</span>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_ModalStack_" data-uid="DrawnUi.Controls.SkiaShell.ModalStack*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ModalStack" data-uid="DrawnUi.Controls.SkiaShell.ModalStack">
  ModalStack
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2142"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IReadOnlyList&lt;Page&gt; ModalStack { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page">Page</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_NavigationLayout_" data-uid="DrawnUi.Controls.SkiaShell.NavigationLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_NavigationLayout" data-uid="DrawnUi.Controls.SkiaShell.NavigationLayout">
  NavigationLayout
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L218"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The main control that pushes pages, switches tabs etc</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaViewSwitcher NavigationLayout { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Controls.SkiaViewSwitcher.html">SkiaViewSwitcher</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_NavigationStack_" data-uid="DrawnUi.Controls.SkiaShell.NavigationStack*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_NavigationStack" data-uid="DrawnUi.Controls.SkiaShell.NavigationStack">
  NavigationStack
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2151"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IReadOnlyList&lt;Page&gt; NavigationStack { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page">Page</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_NavigationStackModals_" data-uid="DrawnUi.Controls.SkiaShell.NavigationStackModals*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_NavigationStackModals" data-uid="DrawnUi.Controls.SkiaShell.NavigationStackModals">
  NavigationStackModals
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L71"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LinkedList&lt;SkiaShell.PageInStack&gt; NavigationStackModals { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.linkedlist-1">LinkedList</a>&lt;<a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.PageInStack.html">PageInStack</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_NavigationStackScreens_" data-uid="DrawnUi.Controls.SkiaShell.NavigationStackScreens*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_NavigationStackScreens" data-uid="DrawnUi.Controls.SkiaShell.NavigationStackScreens">
  NavigationStackScreens
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L70"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LinkedList&lt;SkiaShell.PageInStack&gt; NavigationStackScreens { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.linkedlist-1">LinkedList</a>&lt;<a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.PageInStack.html">PageInStack</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_OnShellGoBack_" data-uid="DrawnUi.Controls.SkiaShell.OnShellGoBack*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnShellGoBack" data-uid="DrawnUi.Controls.SkiaShell.OnShellGoBack">
  OnShellGoBack
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L534"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaShell.IHandleGoBack OnShellGoBack { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.IHandleGoBack.html">IHandleGoBack</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_OrderedRoute_" data-uid="DrawnUi.Controls.SkiaShell.OrderedRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OrderedRoute" data-uid="DrawnUi.Controls.SkiaShell.OrderedRoute">
  OrderedRoute
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2189"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string OrderedRoute { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_Popups_" data-uid="DrawnUi.Controls.SkiaShell.Popups*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_Popups" data-uid="DrawnUi.Controls.SkiaShell.Popups">
  Popups
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L69"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaShell.NavigationLayer&lt;SkiaControl&gt; Popups { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.NavigationLayer-1.html">NavigationLayer</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_RootLayout_" data-uid="DrawnUi.Controls.SkiaShell.RootLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_RootLayout" data-uid="DrawnUi.Controls.SkiaShell.RootLayout">
  RootLayout
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L223"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Use this for covering everything in a modal way, precisely tabs</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl RootLayout { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_ShellLayout_" data-uid="DrawnUi.Controls.SkiaShell.ShellLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ShellLayout" data-uid="DrawnUi.Controls.SkiaShell.ShellLayout">
  ShellLayout
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L213"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl ShellLayout { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_StatusBarHeight_" data-uid="DrawnUi.Controls.SkiaShell.StatusBarHeight*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_StatusBarHeight" data-uid="DrawnUi.Controls.SkiaShell.StatusBarHeight">
  StatusBarHeight
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2584"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double StatusBarHeight { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_Toasts_" data-uid="DrawnUi.Controls.SkiaShell.Toasts*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_Toasts" data-uid="DrawnUi.Controls.SkiaShell.Toasts">
  Toasts
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L68"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaShell.NavigationLayer&lt;SkiaControl&gt; Toasts { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.NavigationLayer-1.html">NavigationLayer</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Controls_SkiaShell_TopInsets_" data-uid="DrawnUi.Controls.SkiaShell.TopInsets*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_TopInsets" data-uid="DrawnUi.Controls.SkiaShell.TopInsets">
  TopInsets
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2569"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double TopInsets { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Controls_SkiaShell_AddToCurrentRouteNodes_" data-uid="DrawnUi.Controls.SkiaShell.AddToCurrentRouteNodes*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_AddToCurrentRouteNodes_System_String_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.AddToCurrentRouteNodes(System.String,DrawnUi.Draw.SkiaControl)">
  AddToCurrentRouteNodes(string, SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L879"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void AddToCurrentRouteNodes(string registered, SkiaControl created)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>registered</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>created</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_ApplyShellPropertiesToCanvas_" data-uid="DrawnUi.Controls.SkiaShell.ApplyShellPropertiesToCanvas*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ApplyShellPropertiesToCanvas" data-uid="DrawnUi.Controls.SkiaShell.ApplyShellPropertiesToCanvas">
  ApplyShellPropertiesToCanvas()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1756"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ApplyShellPropertiesToCanvas()</code></pre>
  </div>













  <a id="DrawnUi_Controls_SkiaShell_AwaitLayersLock_" data-uid="DrawnUi.Controls.SkiaShell.AwaitLayersLock*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_AwaitLayersLock_System_String_" data-uid="DrawnUi.Controls.SkiaShell.AwaitLayersLock(System.String)">
  AwaitLayersLock(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1717"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual Task AwaitLayersLock(string caller = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>caller</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_AwaitNavigationLock_" data-uid="DrawnUi.Controls.SkiaShell.AwaitNavigationLock*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_AwaitNavigationLock_System_String_" data-uid="DrawnUi.Controls.SkiaShell.AwaitNavigationLock(System.String)">
  AwaitNavigationLock(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1703"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual Task AwaitNavigationLock(string caller = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>caller</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_BuildRoute_" data-uid="DrawnUi.Controls.SkiaShell.BuildRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_BuildRoute_System_String_System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.BuildRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
  BuildRoute(string, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2209"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string BuildRoute(string host, IDictionary&lt;string, object&gt; arguments = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>host</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_CanFreezeLayout_" data-uid="DrawnUi.Controls.SkiaShell.CanFreezeLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_CanFreezeLayout" data-uid="DrawnUi.Controls.SkiaShell.CanFreezeLayout">
  CanFreezeLayout()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1139"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Override this if you have custom navigation layers and custom logic to decide if we can unfreeze background.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual bool CanFreezeLayout()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_CanUnfreezeLayout_" data-uid="DrawnUi.Controls.SkiaShell.CanUnfreezeLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_CanUnfreezeLayout" data-uid="DrawnUi.Controls.SkiaShell.CanUnfreezeLayout">
  CanUnfreezeLayout()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1148"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Override this if you have custom navigation layers and custom logic to decide if we can unfreeze background.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual bool CanUnfreezeLayout()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_CloseAllPopups_" data-uid="DrawnUi.Controls.SkiaShell.CloseAllPopups*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_CloseAllPopups" data-uid="DrawnUi.Controls.SkiaShell.CloseAllPopups">
  CloseAllPopups()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1420"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task CloseAllPopups()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_CloseAllToasts_" data-uid="DrawnUi.Controls.SkiaShell.CloseAllToasts*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_CloseAllToasts" data-uid="DrawnUi.Controls.SkiaShell.CloseAllToasts">
  CloseAllToasts()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1635"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task CloseAllToasts()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_ClosePopupAsync_" data-uid="DrawnUi.Controls.SkiaShell.ClosePopupAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ClosePopupAsync_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.ClosePopupAsync(DrawnUi.Draw.SkiaControl,System.Boolean)">
  ClosePopupAsync(SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1440"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task ClosePopupAsync(SkiaControl popup, bool animated)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>popup</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_ClosePopupAsync_" data-uid="DrawnUi.Controls.SkiaShell.ClosePopupAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ClosePopupAsync_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.ClosePopupAsync(System.Boolean)">
  ClosePopupAsync(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1431"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Close topmost popup</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task ClosePopupAsync(bool animated)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_CreateModalDrawer_" data-uid="DrawnUi.Controls.SkiaShell.CreateModalDrawer*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_CreateModalDrawer_System_Boolean_System_Boolean_System_Boolean_Microsoft_Maui_Graphics_Color_" data-uid="DrawnUi.Controls.SkiaShell.CreateModalDrawer(System.Boolean,System.Boolean,System.Boolean,Microsoft.Maui.Graphics.Color)">
  CreateModalDrawer(bool, bool, bool, Color)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L703"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual SkiaShell.ModalWrapper CreateModalDrawer(bool useGestures, bool animated, bool willFreeze, Color backgroundColor)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>useGestures</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>willFreeze</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>backgroundColor</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.ModalWrapper.html">ModalWrapper</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_Dispose_" data-uid="DrawnUi.Controls.SkiaShell.Dispose*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_Dispose_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.Dispose(System.Boolean)">
  Dispose(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L33"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void Dispose(bool isDisposing)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>isDisposing</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_ExecuteActionRoute_" data-uid="DrawnUi.Controls.SkiaShell.ExecuteActionRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ExecuteActionRoute_System_String_" data-uid="DrawnUi.Controls.SkiaShell.ExecuteActionRoute(System.String)">
  ExecuteActionRoute(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1944"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool ExecuteActionRoute(string route)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_FormatRoute_" data-uid="DrawnUi.Controls.SkiaShell.FormatRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_FormatRoute_System_Collections_Generic_List_System_String__" data-uid="DrawnUi.Controls.SkiaShell.FormatRoute(System.Collections.Generic.List{System.String})">
  FormatRoute(List&lt;string&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2109"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string FormatRoute(List&lt;string&gt; segments)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>segments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_FormatRoute_" data-uid="DrawnUi.Controls.SkiaShell.FormatRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_FormatRoute_System_String_" data-uid="DrawnUi.Controls.SkiaShell.FormatRoute(System.String)">
  FormatRoute(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2115"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string FormatRoute(string route)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_FreezeRootLayout_" data-uid="DrawnUi.Controls.SkiaShell.FreezeRootLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_FreezeRootLayout_DrawnUi_Draw_SkiaControl_SkiaSharp_SKImage_System_Boolean_Microsoft_Maui_Graphics_Color_System_Single_" data-uid="DrawnUi.Controls.SkiaShell.FreezeRootLayout(DrawnUi.Draw.SkiaControl,SkiaSharp.SKImage,System.Boolean,Microsoft.Maui.Graphics.Color,System.Single)">
  FreezeRootLayout(SkiaControl, SKImage, bool, Color, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1274"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task FreezeRootLayout(SkiaControl control, SKImage screenshot, bool animated, Color tintScreenshot, float blurScreenshot)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>screenshot</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skimage">SKImage</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>tintScreenshot</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
    <dt><code>blurScreenshot</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_FreezeRootLayout_" data-uid="DrawnUi.Controls.SkiaShell.FreezeRootLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_FreezeRootLayout_DrawnUi_Draw_SkiaControl_System_Boolean_Microsoft_Maui_Graphics_Color_System_Single_" data-uid="DrawnUi.Controls.SkiaShell.FreezeRootLayout(DrawnUi.Draw.SkiaControl,System.Boolean,Microsoft.Maui.Graphics.Color,System.Single)">
  FreezeRootLayout(SkiaControl, bool, Color, float)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1250"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Freezes layout below the overlay: takes screenshot of RootLayout, places it over, then hides RootLayout to avoid rendering it. Can override</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task FreezeRootLayout(SkiaControl control, bool animated, Color tintScreenshot, float blurScreenshot)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>tintScreenshot</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
    <dt><code>blurScreenshot</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_FreezeRootLayoutInternal_" data-uid="DrawnUi.Controls.SkiaShell.FreezeRootLayoutInternal*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_FreezeRootLayoutInternal_DrawnUi_Draw_SkiaControl_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.FreezeRootLayoutInternal(DrawnUi.Draw.SkiaControl,DrawnUi.Draw.SkiaControl,System.Boolean)">
  FreezeRootLayoutInternal(SkiaControl, SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1298"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task FreezeRootLayoutInternal(SkiaControl control, SkiaControl screenshot, bool animated)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>screenshot</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GetOrCreateContent_" data-uid="DrawnUi.Controls.SkiaShell.GetOrCreateContent*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GetOrCreateContent_System_String_" data-uid="DrawnUi.Controls.SkiaShell.GetOrCreateContent(System.String)">
  GetOrCreateContent(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1966"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BindableObject GetOrCreateContent(string route)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GetOrCreateContentSetArguments_" data-uid="DrawnUi.Controls.SkiaShell.GetOrCreateContentSetArguments*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GetOrCreateContentSetArguments__1_System_String_System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.GetOrCreateContentSetArguments``1(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
  GetOrCreateContentSetArguments&lt;T&gt;(string, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2334"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual T GetOrCreateContentSetArguments&lt;T&gt;(string part, IDictionary&lt;string, object&gt; arguments) where T : BindableObject</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>part</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><span class="xref">T</span></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Type Parameters</h4>
  <dl class="parameters">
    <dt><code>T</code></dt>
    <dd></dd>
  </dl>










  <a id="DrawnUi_Controls_SkiaShell_GetTopModal_" data-uid="DrawnUi.Controls.SkiaShell.GetTopModal*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GetTopModal" data-uid="DrawnUi.Controls.SkiaShell.GetTopModal">
  GetTopModal()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L241"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetTopModal()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GetTopPopup_" data-uid="DrawnUi.Controls.SkiaShell.GetTopPopup*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GetTopPopup" data-uid="DrawnUi.Controls.SkiaShell.GetTopPopup">
  GetTopPopup()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L227"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetTopPopup()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GetTopmostView_" data-uid="DrawnUi.Controls.SkiaShell.GetTopmostView*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GetTopmostView" data-uid="DrawnUi.Controls.SkiaShell.GetTopmostView">
  GetTopmostView()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L302"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the topmost visible view:
if no popups and modals are open then return NavigationLayout
otherwise return the topmost popup or modal
depending which ZIndexModals or ZIndexPopups is higher.
If view is inside a shell wrapper will return just the view.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetTopmostView()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GetTopmostViewInternal_" data-uid="DrawnUi.Controls.SkiaShell.GetTopmostViewInternal*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GetTopmostViewInternal" data-uid="DrawnUi.Controls.SkiaShell.GetTopmostViewInternal">
  GetTopmostViewInternal()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L266"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the topmost visible view:
if no popups and modals are open then return NavigationLayout
otherwise return the topmost popup or modal
depending which ZIndexModals or ZIndexPopups is higher.
If pushed view is inside a shell wrapper will return the wrapper.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetTopmostViewInternal()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GoBack_" data-uid="DrawnUi.Controls.SkiaShell.GoBack*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GoBack_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.GoBack(System.Boolean)">
  GoBack(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L536"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual bool GoBack(bool animate)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>animate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GoBackDefault_" data-uid="DrawnUi.Controls.SkiaShell.GoBackDefault*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GoBackDefault_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.GoBackDefault(System.Boolean)">
  GoBackDefault(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L553"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool GoBackDefault(bool animate)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>animate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GoBackInRoute_" data-uid="DrawnUi.Controls.SkiaShell.GoBackInRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GoBackInRoute_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.GoBackInRoute(System.Boolean)">
  GoBackInRoute(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L587"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>This will not affect popups</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Task&lt;SkiaControl&gt; GoBackInRoute(bool animate)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>animate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GoToAsync_" data-uid="DrawnUi.Controls.SkiaShell.GoToAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GoToAsync_Microsoft_Maui_Controls_ShellNavigationState_" data-uid="DrawnUi.Controls.SkiaShell.GoToAsync(Microsoft.Maui.Controls.ShellNavigationState)">
  GoToAsync(ShellNavigationState)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2200"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task GoToAsync(ShellNavigationState state)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>state</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.shellnavigationstate">ShellNavigationState</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_GoToAsync_" data-uid="DrawnUi.Controls.SkiaShell.GoToAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_GoToAsync_Microsoft_Maui_Controls_ShellNavigationState_System_Nullable_System_Boolean__System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.GoToAsync(Microsoft.Maui.Controls.ShellNavigationState,System.Nullable{System.Boolean},System.Collections.Generic.IDictionary{System.String,System.Object})">
  GoToAsync(ShellNavigationState, bool?, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2368"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Navigate to a registered route. Arguments will be taken from query string of can be passed as parameter. You can receive them by implementing IQueryAttributable or using attribute [QueryProperty] in the page itsself or in the ViewModel that must be the screen's BindingContext upon registered screen instatiation.
Animate can be specified otherwise will use it from Shell.PresentationMode attached property. This property will be also used for pushing as page, modal etc.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task GoToAsync(ShellNavigationState state, bool? animate = null, IDictionary&lt;string, object&gt; arguments = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>state</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.shellnavigationstate">ShellNavigationState</a></dt>
    <dd></dd>
    <dt><code>animate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a>?</dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_HasTopmostModalBindingContext_" data-uid="DrawnUi.Controls.SkiaShell.HasTopmostModalBindingContext*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_HasTopmostModalBindingContext__1" data-uid="DrawnUi.Controls.SkiaShell.HasTopmostModalBindingContext``1">
  HasTopmostModalBindingContext&lt;T&gt;()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1742"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool HasTopmostModalBindingContext&lt;T&gt;()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Type Parameters</h4>
  <dl class="parameters">
    <dt><code>T</code></dt>
    <dd></dd>
  </dl>










  <a id="DrawnUi_Controls_SkiaShell_ImportNavigationLayout_" data-uid="DrawnUi.Controls.SkiaShell.ImportNavigationLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ImportNavigationLayout_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.ImportNavigationLayout(System.Boolean)">
  ImportNavigationLayout(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1869"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void ImportNavigationLayout(bool replace = false)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>replace</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_ImportRootLayout_" data-uid="DrawnUi.Controls.SkiaShell.ImportRootLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ImportRootLayout_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.ImportRootLayout(System.Boolean)">
  ImportRootLayout(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1832"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void ImportRootLayout(bool replace = false)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>replace</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_Initialize_" data-uid="DrawnUi.Controls.SkiaShell.Initialize*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_Initialize_System_String_" data-uid="DrawnUi.Controls.SkiaShell.Initialize(System.String)">
  Initialize(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1898"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void Initialize(string route)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_NotifyAndCheckCanNavigate_" data-uid="DrawnUi.Controls.SkiaShell.NotifyAndCheckCanNavigate*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_NotifyAndCheckCanNavigate_DrawnUi_Draw_SkiaControl_System_String_DrawnUi_Controls_NavigationSource_" data-uid="DrawnUi.Controls.SkiaShell.NotifyAndCheckCanNavigate(DrawnUi.Draw.SkiaControl,System.String,DrawnUi.Controls.NavigationSource)">
  NotifyAndCheckCanNavigate(SkiaControl, string, NavigationSource)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L119"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual bool NotifyAndCheckCanNavigate(SkiaControl view, string route, NavigationSource source)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>view</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>source</code> <a class="xref" href="DrawnUi.Controls.NavigationSource.html">NavigationSource</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_OnCurrentRouteChanged_" data-uid="DrawnUi.Controls.SkiaShell.OnCurrentRouteChanged*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnCurrentRouteChanged" data-uid="DrawnUi.Controls.SkiaShell.OnCurrentRouteChanged">
  OnCurrentRouteChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L128"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnCurrentRouteChanged()</code></pre>
  </div>













  <a id="DrawnUi_Controls_SkiaShell_OnDisposing_" data-uid="DrawnUi.Controls.SkiaShell.OnDisposing*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnDisposing" data-uid="DrawnUi.Controls.SkiaShell.OnDisposing">
  OnDisposing()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L45"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnDisposing()</code></pre>
  </div>













  <a id="DrawnUi_Controls_SkiaShell_OnHandlerChanged_" data-uid="DrawnUi.Controls.SkiaShell.OnHandlerChanged*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnHandlerChanged" data-uid="DrawnUi.Controls.SkiaShell.OnHandlerChanged">
  OnHandlerChanged()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L54"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>When overridden in a derived class, should raise the <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">HandlerChanged</a> event.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnHandlerChanged()</code></pre>
  </div>









  <h4 class="section" id="DrawnUi_Controls_SkiaShell_OnHandlerChanged_remarks">Remarks</h4>
  <div class="markdown level1 remarks"><p>It is the implementor's responsibility to raise the <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">HandlerChanged</a> event.</p>
</div>




  <a id="DrawnUi_Controls_SkiaShell_OnLayersChanged_" data-uid="DrawnUi.Controls.SkiaShell.OnLayersChanged*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnLayersChanged_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.OnLayersChanged(DrawnUi.Draw.SkiaControl)">
  OnLayersChanged(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1367"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Setup _topmost and send OnAppeared / OnDisappeared to views.
Occurs when layers configuration changes,
some layer go visible, some not, some are added, some are removed.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnLayersChanged(SkiaControl dissapeared = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>dissapeared</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_OnLayoutInvalidated_" data-uid="DrawnUi.Controls.SkiaShell.OnLayoutInvalidated*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnLayoutInvalidated" data-uid="DrawnUi.Controls.SkiaShell.OnLayoutInvalidated">
  OnLayoutInvalidated()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2542"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnLayoutInvalidated()</code></pre>
  </div>













  <a id="DrawnUi_Controls_SkiaShell_OnModalStackChanged_" data-uid="DrawnUi.Controls.SkiaShell.OnModalStackChanged*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnModalStackChanged_System_Int32_" data-uid="DrawnUi.Controls.SkiaShell.OnModalStackChanged(System.Int32)">
  OnModalStackChanged(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L73"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnModalStackChanged(int count)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>count</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_OnNavigated_" data-uid="DrawnUi.Controls.SkiaShell.OnNavigated*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnNavigated_DrawnUi_Controls_SkiaShellNavigatedArgs_" data-uid="DrawnUi.Controls.SkiaShell.OnNavigated(DrawnUi.Controls.SkiaShellNavigatedArgs)">
  OnNavigated(SkiaShellNavigatedArgs)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L105"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnNavigated(SkiaShellNavigatedArgs e)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>e</code> <a class="xref" href="DrawnUi.Controls.SkiaShellNavigatedArgs.html">SkiaShellNavigatedArgs</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_OnNavigating_" data-uid="DrawnUi.Controls.SkiaShell.OnNavigating*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnNavigating_DrawnUi_Controls_SkiaShellNavigatingArgs_" data-uid="DrawnUi.Controls.SkiaShell.OnNavigating(DrawnUi.Controls.SkiaShellNavigatingArgs)">
  OnNavigating(SkiaShellNavigatingArgs)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L112"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnNavigating(SkiaShellNavigatingArgs e)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>e</code> <a class="xref" href="DrawnUi.Controls.SkiaShellNavigatingArgs.html">SkiaShellNavigatingArgs</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_OnPagesStackChanged_" data-uid="DrawnUi.Controls.SkiaShell.OnPagesStackChanged*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnPagesStackChanged_System_Int32_" data-uid="DrawnUi.Controls.SkiaShell.OnPagesStackChanged(System.Int32)">
  OnPagesStackChanged(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L85"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnPagesStackChanged(int count)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>count</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_OnPopupsStackChanged_" data-uid="DrawnUi.Controls.SkiaShell.OnPopupsStackChanged*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnPopupsStackChanged_System_Int32_" data-uid="DrawnUi.Controls.SkiaShell.OnPopupsStackChanged(System.Int32)">
  OnPopupsStackChanged(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L81"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnPopupsStackChanged(int count)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>count</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_OnStarted_" data-uid="DrawnUi.Controls.SkiaShell.OnStarted*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnStarted" data-uid="DrawnUi.Controls.SkiaShell.OnStarted">
  OnStarted()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1931"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnStarted()</code></pre>
  </div>













  <a id="DrawnUi_Controls_SkiaShell_OnToastsStackChanged_" data-uid="DrawnUi.Controls.SkiaShell.OnToastsStackChanged*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OnToastsStackChanged_System_Int32_" data-uid="DrawnUi.Controls.SkiaShell.OnToastsStackChanged(System.Int32)">
  OnToastsStackChanged(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L77"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void OnToastsStackChanged(int count)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>count</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_OpenPopupAsync_" data-uid="DrawnUi.Controls.SkiaShell.OpenPopupAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_OpenPopupAsync_DrawnUi_Draw_SkiaControl_System_Boolean_System_Boolean_System_Boolean_Microsoft_Maui_Graphics_Color_System_Nullable_SkiaSharp_SKPoint__" data-uid="DrawnUi.Controls.SkiaShell.OpenPopupAsync(DrawnUi.Draw.SkiaControl,System.Boolean,System.Boolean,System.Boolean,Microsoft.Maui.Graphics.Color,System.Nullable{SkiaSharp.SKPoint})">
  OpenPopupAsync(SkiaControl, bool, bool, bool, Color, SKPoint?)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1488"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Pass pixelsScaleInFrom you you want popup to animate appearing from a specific point instead of screen center.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task&lt;SkiaControl&gt; OpenPopupAsync(SkiaControl content, bool animated = true, bool closeWhenBackgroundTapped = true, bool showOverlay = true, Color backgroundColor = null, SKPoint? pixelsScaleInFrom = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>content</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>closeWhenBackgroundTapped</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>showOverlay</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>backgroundColor</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
    <dt><code>pixelsScaleInFrom</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpoint">SKPoint</a>?</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_ParseRoute_" data-uid="DrawnUi.Controls.SkiaShell.ParseRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ParseRoute_System_String_" data-uid="DrawnUi.Controls.SkiaShell.ParseRoute(System.String)">
  ParseRoute(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2310"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaShell.ParsedRoute ParseRoute(string route)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.ParsedRoute.html">ParsedRoute</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_ParseState_" data-uid="DrawnUi.Controls.SkiaShell.ParseState*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ParseState_Microsoft_Maui_Controls_ShellNavigationState_" data-uid="DrawnUi.Controls.SkiaShell.ParseState(Microsoft.Maui.Controls.ShellNavigationState)">
  ParseState(ShellNavigationState)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2299"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaShell.ParsedRoute ParseState(ShellNavigationState state)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>state</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.shellnavigationstate">ShellNavigationState</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.ParsedRoute.html">ParsedRoute</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PopAsync_" data-uid="DrawnUi.Controls.SkiaShell.PopAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PopAsync_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.PopAsync(System.Boolean)">
  PopAsync(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L607"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Returns the page so you could dispose it if needed. Uses ViewSwitcher.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task&lt;BindableObject&gt; PopAsync(bool animated = true)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PopModalAsync_" data-uid="DrawnUi.Controls.SkiaShell.PopModalAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PopModalAsync_DrawnUi_Controls_SkiaShell_PageInStack_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.PopModalAsync(DrawnUi.Controls.SkiaShell.PageInStack,System.Boolean)">
  PopModalAsync(PageInStack, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1082"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task&lt;BindableObject&gt; PopModalAsync(SkiaShell.PageInStack inStack, bool animated)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>inStack</code> <a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.PageInStack.html">PageInStack</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PopModalAsync_" data-uid="DrawnUi.Controls.SkiaShell.PopModalAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PopModalAsync_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.PopModalAsync(DrawnUi.Draw.SkiaControl,System.Boolean)">
  PopModalAsync(SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L980"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Task&lt;SkiaControl&gt; PopModalAsync(SkiaControl modal, bool animated)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>modal</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PopModalAsync_" data-uid="DrawnUi.Controls.SkiaShell.PopModalAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PopModalAsync_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.PopModalAsync(System.Boolean)">
  PopModalAsync(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L943"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task&lt;BindableObject&gt; PopModalAsync(bool animated)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PopTabToRoot_" data-uid="DrawnUi.Controls.SkiaShell.PopTabToRoot*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PopTabToRoot" data-uid="DrawnUi.Controls.SkiaShell.PopTabToRoot">
  PopTabToRoot()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1130"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task PopTabToRoot()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PopToRootAsync_" data-uid="DrawnUi.Controls.SkiaShell.PopToRootAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PopToRootAsync" data-uid="DrawnUi.Controls.SkiaShell.PopToRootAsync">
  PopToRootAsync()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2137"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task PopToRootAsync()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PresentAsync_" data-uid="DrawnUi.Controls.SkiaShell.PresentAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PresentAsync_System_String_System_Nullable_System_Boolean__System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.PresentAsync(System.String,System.Nullable{System.Boolean},System.Collections.Generic.IDictionary{System.String,System.Object})">
  PresentAsync(string, bool?, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L381"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task&lt;SkiaControl&gt; PresentAsync(string registered, bool? animate, IDictionary&lt;string, object&gt; arguments = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>registered</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>animate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a>?</dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PushAsync_" data-uid="DrawnUi.Controls.SkiaShell.PushAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PushAsync_Microsoft_Maui_Controls_BindableObject_System_Boolean_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.PushAsync(Microsoft.Maui.Controls.BindableObject,System.Boolean,System.Boolean)">
  PushAsync(BindableObject, bool, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L479"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Uses ViewSwitcher to push a view on the canvas, into the current tab if any.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task PushAsync(BindableObject page, bool animated = true, bool notify = true)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>page</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>notify</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PushAsync_" data-uid="DrawnUi.Controls.SkiaShell.PushAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PushAsync_System_String_System_Boolean_System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.PushAsync(System.String,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
  PushAsync(string, bool, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L514"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Uses ViewSwitcher to push a view on the canvas, into the current tab if any. We can use a route with arguments to instantiate the view instead of passing an instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task PushAsync(string registered, bool animated = true, IDictionary&lt;string, object&gt; arguments = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>registered</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PushModalAsync_" data-uid="DrawnUi.Controls.SkiaShell.PushModalAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PushModalAsync_Microsoft_Maui_Controls_BindableObject_System_Boolean_System_Boolean_System_Boolean_System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.PushModalAsync(Microsoft.Maui.Controls.BindableObject,System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
  PushModalAsync(BindableObject, bool, bool, bool, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L727"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Creates a SkiaDrawer opening over the RootLayout with the passed content. Override this method to create your own implementation.
Default freezing background is True, control with frozenLayerBackgroundParameters parameter.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task&lt;SkiaControl&gt; PushModalAsync(BindableObject page, bool useGestures, bool animated = true, bool freezeBackground = true, IDictionary&lt;string, object&gt; arguments = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>page</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>useGestures</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>freezeBackground</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PushModalAsync_" data-uid="DrawnUi.Controls.SkiaShell.PushModalAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PushModalAsync_System_String_System_Boolean_System_Boolean_System_Boolean_System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.PushModalAsync(System.String,System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
  PushModalAsync(string, bool, bool, bool, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L925"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task PushModalAsync(string registered, bool useGestures, bool animated = true, bool freezeBackground = true, IDictionary&lt;string, object&gt; arguments = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>registered</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>useGestures</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>freezeBackground</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PushModalRoute_" data-uid="DrawnUi.Controls.SkiaShell.PushModalRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PushModalRoute_DrawnUi_Draw_SkiaControl_System_String_System_Nullable_System_Boolean__" data-uid="DrawnUi.Controls.SkiaShell.PushModalRoute(DrawnUi.Draw.SkiaControl,System.String,System.Nullable{System.Boolean})">
  PushModalRoute(SkiaControl, string, bool?)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L440"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual Task&lt;SkiaControl&gt; PushModalRoute(SkiaControl skia, string route, bool? animate)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>skia</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>animate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a>?</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_PushRegisteredPageAsync_" data-uid="DrawnUi.Controls.SkiaShell.PushRegisteredPageAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_PushRegisteredPageAsync_System_String_System_Boolean_System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.PushRegisteredPageAsync(System.String,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
  PushRegisteredPageAsync(string, bool, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L446"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task PushRegisteredPageAsync(string registered, bool animate, IDictionary&lt;string, object&gt; arguments = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>registered</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>animate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_RegisterActionRoute_" data-uid="DrawnUi.Controls.SkiaShell.RegisterActionRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_RegisterActionRoute_System_String_System_Action_" data-uid="DrawnUi.Controls.SkiaShell.RegisterActionRoute(System.String,System.Action)">
  RegisterActionRoute(string, Action)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1936"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RegisterActionRoute(string route, Action switchToTab)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>switchToTab</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action">Action</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_RegisterRoute_" data-uid="DrawnUi.Controls.SkiaShell.RegisterRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_RegisterRoute_System_String_DrawnUi_Controls_SkiaShell_TypeRouteFactory_" data-uid="DrawnUi.Controls.SkiaShell.RegisterRoute(System.String,DrawnUi.Controls.SkiaShell.TypeRouteFactory)">
  RegisterRoute(string, TypeRouteFactory)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2120"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RegisterRoute(string route, SkiaShell.TypeRouteFactory factory)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>factory</code> <a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.TypeRouteFactory.html">TypeRouteFactory</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_RegisterRoute_" data-uid="DrawnUi.Controls.SkiaShell.RegisterRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_RegisterRoute_System_String_System_Type_" data-uid="DrawnUi.Controls.SkiaShell.RegisterRoute(System.String,System.Type)">
  RegisterRoute(string, Type)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1961"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RegisterRoute(string route, Type type)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>route</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>type</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.type">Type</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_RemoveAsync_" data-uid="DrawnUi.Controls.SkiaShell.RemoveAsync*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_RemoveAsync_DrawnUi_Draw_SkiaControl_System_Nullable_System_Boolean__System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.RemoveAsync(DrawnUi.Draw.SkiaControl,System.Nullable{System.Boolean},System.Collections.Generic.IDictionary{System.String,System.Object})">
  RemoveAsync(SkiaControl, bool?, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L324"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task&lt;SkiaControl&gt; RemoveAsync(SkiaControl modal, bool? animate, IDictionary&lt;string, object&gt; arguments = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>modal</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>animate</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a>?</dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_RemoveFromCurrentRouteNodes_" data-uid="DrawnUi.Controls.SkiaShell.RemoveFromCurrentRouteNodes*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_RemoveFromCurrentRouteNodes_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.RemoveFromCurrentRouteNodes(DrawnUi.Draw.SkiaControl)">
  RemoveFromCurrentRouteNodes(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L888"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected bool RemoveFromCurrentRouteNodes(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_ReplaceRootLayout_" data-uid="DrawnUi.Controls.SkiaShell.ReplaceRootLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ReplaceRootLayout_DrawnUi_Draw_ISkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.ReplaceRootLayout(DrawnUi.Draw.ISkiaControl)">
  ReplaceRootLayout(ISkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1808"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void ReplaceRootLayout(ISkiaControl newLayout)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>newLayout</code> <a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_ReplaceShellLayout_" data-uid="DrawnUi.Controls.SkiaShell.ReplaceShellLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ReplaceShellLayout_DrawnUi_Draw_ISkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.ReplaceShellLayout(DrawnUi.Draw.ISkiaControl)">
  ReplaceShellLayout(ISkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1778"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void ReplaceShellLayout(ISkiaControl newLayout)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>newLayout</code> <a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_Reset_" data-uid="DrawnUi.Controls.SkiaShell.Reset*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_Reset" data-uid="DrawnUi.Controls.SkiaShell.Reset">
  Reset()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1882"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void Reset()</code></pre>
  </div>













  <a id="DrawnUi_Controls_SkiaShell_SetArguments_" data-uid="DrawnUi.Controls.SkiaShell.SetArguments*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetArguments_Microsoft_Maui_Controls_BindableObject_System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.SetArguments(Microsoft.Maui.Controls.BindableObject,System.Collections.Generic.IDictionary{System.String,System.Object})">
  SetArguments(BindableObject, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2266"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void SetArguments(BindableObject page, IDictionary&lt;string, object&gt; arguments)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>page</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_SetCurrentRouteNodes_" data-uid="DrawnUi.Controls.SkiaShell.SetCurrentRouteNodes*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetCurrentRouteNodes_System_Collections_Generic_List_DrawnUi_Controls_SkiaShell_ShellStackChild__" data-uid="DrawnUi.Controls.SkiaShell.SetCurrentRouteNodes(System.Collections.Generic.List{DrawnUi.Controls.SkiaShell.ShellStackChild})">
  SetCurrentRouteNodes(List&lt;ShellStackChild&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L872"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void SetCurrentRouteNodes(List&lt;SkiaShell.ShellStackChild&gt; children)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>children</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a>.<a class="xref" href="DrawnUi.Controls.SkiaShell.ShellStackChild.html">ShellStackChild</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_SetFrozenLayerVisibility_" data-uid="DrawnUi.Controls.SkiaShell.SetFrozenLayerVisibility*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetFrozenLayerVisibility_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.SetFrozenLayerVisibility(DrawnUi.Draw.SkiaControl,System.Boolean)">
  SetFrozenLayerVisibility(SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1173"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Display or hide the background scrrenshot assotiated with an overlay control</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Task SetFrozenLayerVisibility(SkiaControl control, bool isVisible)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>isVisible</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_SetRoot_" data-uid="DrawnUi.Controls.SkiaShell.SetRoot*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetRoot_System_String_System_Boolean_System_Collections_Generic_IDictionary_System_String_System_Object__" data-uid="DrawnUi.Controls.SkiaShell.SetRoot(System.String,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
  SetRoot(string, bool, IDictionary&lt;string, object&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2241"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Returns true if was replaced</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual SkiaControl SetRoot(string host, bool replace, IDictionary&lt;string, object&gt; arguments = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>host</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>replace</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
    <dt><code>arguments</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2">IDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>








  <h4 class="section">Exceptions</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.exception">Exception</a></dt>
    <dd></dd>
  </dl>



  <a id="DrawnUi_Controls_SkiaShell_SetRoute_" data-uid="DrawnUi.Controls.SkiaShell.SetRoute*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetRoute_Microsoft_Maui_Controls_BindableObject_System_String_" data-uid="DrawnUi.Controls.SkiaShell.SetRoute(Microsoft.Maui.Controls.BindableObject,System.String)">
  SetRoute(BindableObject, string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1993"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetRoute(BindableObject obj, string value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>obj</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></dt>
    <dd></dd>
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_SetupAppeared_" data-uid="DrawnUi.Controls.SkiaShell.SetupAppeared*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetupAppeared_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.SetupAppeared(DrawnUi.Draw.SkiaControl)">
  SetupAppeared(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1394"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void SetupAppeared(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_SetupDisappeared_" data-uid="DrawnUi.Controls.SkiaShell.SetupDisappeared*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetupDisappeared_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.SetupDisappeared(DrawnUi.Draw.SkiaControl)">
  SetupDisappeared(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1406"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void SetupDisappeared(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_SetupFrozenLayersVisibility_" data-uid="DrawnUi.Controls.SkiaShell.SetupFrozenLayersVisibility*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetupFrozenLayersVisibility_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.SetupFrozenLayersVisibility(DrawnUi.Draw.SkiaControl)">
  SetupFrozenLayersVisibility(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1156"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected void SetupFrozenLayersVisibility(SkiaControl except)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>except</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_SetupModalsVisibility_" data-uid="DrawnUi.Controls.SkiaShell.SetupModalsVisibility*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetupModalsVisibility_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.SetupModalsVisibility(DrawnUi.Draw.SkiaControl)">
  SetupModalsVisibility(SkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1324"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task&lt;bool&gt; SetupModalsVisibility(SkiaControl control)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a>&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_SetupRoot_" data-uid="DrawnUi.Controls.SkiaShell.SetupRoot*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_SetupRoot_DrawnUi_Draw_ISkiaControl_" data-uid="DrawnUi.Controls.SkiaShell.SetupRoot(DrawnUi.Draw.ISkiaControl)">
  SetupRoot(ISkiaControl)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2229"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Main control inside RootLayout</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void SetupRoot(ISkiaControl shellLayout)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>shellLayout</code> <a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_ShowToast_" data-uid="DrawnUi.Controls.SkiaShell.ShowToast*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ShowToast_DrawnUi_Draw_SkiaControl_System_Int32_" data-uid="DrawnUi.Controls.SkiaShell.ShowToast(DrawnUi.Draw.SkiaControl,System.Int32)">
  ShowToast(SkiaControl, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1641"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ShowToast(SkiaControl content, int msShowTime = 4000)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>content</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>msShowTime</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_ShowToast_" data-uid="DrawnUi.Controls.SkiaShell.ShowToast*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_ShowToast_System_String_System_Int32_" data-uid="DrawnUi.Controls.SkiaShell.ShowToast(System.String,System.Int32)">
  ShowToast(string, int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1724"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void ShowToast(string text, int msShowTime = 4000)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>text</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>msShowTime</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_UnfreezeRootLayout_" data-uid="DrawnUi.Controls.SkiaShell.UnfreezeRootLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_UnfreezeRootLayout_DrawnUi_Draw_SkiaControl_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.UnfreezeRootLayout(DrawnUi.Draw.SkiaControl,System.Boolean)">
  UnfreezeRootLayout(SkiaControl, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1198"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>pass who frozen the layout</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual Task UnfreezeRootLayout(SkiaControl control, bool animated)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Controls_SkiaShell_UnlockLayers_" data-uid="DrawnUi.Controls.SkiaShell.UnlockLayers*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_UnlockLayers_System_String_" data-uid="DrawnUi.Controls.SkiaShell.UnlockLayers(System.String)">
  UnlockLayers(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1710"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void UnlockLayers(string caller = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>caller</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_UnlockNavigation_" data-uid="DrawnUi.Controls.SkiaShell.UnlockNavigation*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_UnlockNavigation_System_String_" data-uid="DrawnUi.Controls.SkiaShell.UnlockNavigation(System.String)">
  UnlockNavigation(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L1696"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void UnlockNavigation(string caller = null)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>caller</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Controls_SkiaShell_UpdateLayout_" data-uid="DrawnUi.Controls.SkiaShell.UpdateLayout*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_UpdateLayout" data-uid="DrawnUi.Controls.SkiaShell.UpdateLayout">
  UpdateLayout()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2537"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UpdateLayout()</code></pre>
  </div>













  <a id="DrawnUi_Controls_SkiaShell_WrapScreenshot_" data-uid="DrawnUi.Controls.SkiaShell.WrapScreenshot*"></a>

  <h3 id="DrawnUi_Controls_SkiaShell_WrapScreenshot_DrawnUi_Draw_SkiaControl_SkiaSharp_SKImage_Microsoft_Maui_Graphics_Color_System_Single_System_Boolean_" data-uid="DrawnUi.Controls.SkiaShell.WrapScreenshot(DrawnUi.Draw.SkiaControl,SkiaSharp.SKImage,Microsoft.Maui.Graphics.Color,System.Single,System.Boolean)">
  WrapScreenshot(SkiaControl, SKImage, Color, float, bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L655"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Override this to create your own image with your own effect of the screenshot to be placed under modal controls. Default is image with Darken Effect.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual SkiaImage WrapScreenshot(SkiaControl control, SKImage screenshot, Color tint, float blur, bool animated)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>control</code> <a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></dt>
    <dd></dd>
    <dt><code>screenshot</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skimage">SKImage</a></dt>
    <dd></dd>
    <dt><code>tint</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></dt>
    <dd></dd>
    <dt><code>blur</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></dt>
    <dd></dd>
    <dt><code>animated</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Draw.SkiaImage.html">SkiaImage</a></dt>
    <dd></dd>
  </dl>











  <h2 class="section" id="events">Events
</h2>



  <h3 id="DrawnUi_Controls_SkiaShell_ModalStackChanged" data-uid="DrawnUi.Controls.SkiaShell.ModalStackChanged">
  ModalStackChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L92"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;int&gt; ModalStackChanged</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Controls_SkiaShell_Navigated" data-uid="DrawnUi.Controls.SkiaShell.Navigated">
  Navigated
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L97"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;SkiaShellNavigatedArgs&gt; Navigated</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="DrawnUi.Controls.SkiaShellNavigatedArgs.html">SkiaShellNavigatedArgs</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Controls_SkiaShell_Navigating" data-uid="DrawnUi.Controls.SkiaShell.Navigating">
  Navigating
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L98"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;SkiaShellNavigatingArgs&gt; Navigating</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="DrawnUi.Controls.SkiaShellNavigatingArgs.html">SkiaShellNavigatingArgs</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Controls_SkiaShell_OnRotation" data-uid="DrawnUi.Controls.SkiaShell.OnRotation">
  OnRotation
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2597"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;RotationEventArgs&gt; OnRotation</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<span class="xref">RotationEventArgs</span>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Controls_SkiaShell_PagesStackChanged" data-uid="DrawnUi.Controls.SkiaShell.PagesStackChanged">
  PagesStackChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L95"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;int&gt; PagesStackChanged</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Controls_SkiaShell_PopupsStackChanged" data-uid="DrawnUi.Controls.SkiaShell.PopupsStackChanged">
  PopupsStackChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L94"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;int&gt; PopupsStackChanged</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Controls_SkiaShell_RouteChanged" data-uid="DrawnUi.Controls.SkiaShell.RouteChanged">
  RouteChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L99"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler RouteChanged</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Controls_SkiaShell_TabReselected" data-uid="DrawnUi.Controls.SkiaShell.TabReselected">
  TabReselected
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L2598"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;IndexArgs&gt; TabReselected</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<span class="xref">IndexArgs</span>&gt;</dt>
    <dd></dd>
  </dl>








  <h3 id="DrawnUi_Controls_SkiaShell_ToastsStackChanged" data-uid="DrawnUi.Controls.SkiaShell.ToastsStackChanged">
  ToastsStackChanged
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs/#L93"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler&lt;int&gt; ToastsStackChanged</code></pre>
  </div>






  <h4 class="section">Event Type</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler-1">EventHandler</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a>&gt;</dt>
    <dd></dd>
  </dl>








</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/Navigation/SkiaShell.PopupWrapper.cs/#L3" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
